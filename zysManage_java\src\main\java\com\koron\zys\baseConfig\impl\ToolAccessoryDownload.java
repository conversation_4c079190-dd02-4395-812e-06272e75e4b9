package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.ToolAccessoryBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.ToolAccessoryMapper;

import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

/**
 * @author: lrk
 * @date: 2022-08-16 11:42
 * @description:
 */
public class ToolAccessoryDownload implements ServerInterface {

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            ToolAccessoryBean bean = JsonUtils.objectToPojo(req.getData(), ToolAccessoryBean.class);
            if(bean.getId() == null){
                return MessageBean.create(Constant.MESSAGE_INT_FAIL, "参数id不能为空", null);
            }
            ToolAccessoryMapper mapper = factory.getMapper(ToolAccessoryMapper.class,"_default");
            mapper.updateCount(bean.getId());
            return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "修改成功", null);
        } catch (Exception e) {
            logger.error("更新工具页附件下载次数失败", e.getMessage(), e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, e.getMessage(), null);
        }
    }
}
