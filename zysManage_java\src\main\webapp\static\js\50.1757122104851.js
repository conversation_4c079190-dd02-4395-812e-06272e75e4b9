webpackJsonp([50],{NL0r:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=s("yJVD"),o={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"processConfigAdd"},[s("el-form",{ref:"processConfigAddForm",staticClass:"formBill-One",attrs:{model:e.modelData,rules:e.rules,"label-width":"100px",inline:!0}},[s("el-form-item",{attrs:{label:"单据类型：",prop:"receiptType"}},[s("el-select",{attrs:{placeholder:"请选择"},model:{value:e.modelData.receiptType,callback:function(t){e.$set(e.modelData,"receiptType",t)},expression:"modelData.receiptType"}},e._l(e.receiptTypeData,function(e){return s("el-option",{key:e.receiptNo,attrs:{label:e.receiptName,value:e.receiptNo}})}),1)],1),e._v(" "),s("el-form-item",{attrs:{label:"流程编号：",prop:"processCode"}},[s("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.modelData.processCode,callback:function(t){e.$set(e.modelData,"processCode",t)},expression:"modelData.processCode"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"流程名称：",prop:"processName"}},[s("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.modelData.processName,callback:function(t){e.$set(e.modelData,"processName",t)},expression:"modelData.processName"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"回调接口：",prop:"workflowInterface"}},[s("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.modelData.workflowInterface,callback:function(t){e.$set(e.modelData,"workflowInterface",t)},expression:"modelData.workflowInterface"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"状态：",prop:"status"}},[s("el-select",{attrs:{placeholder:"请选择"},model:{value:e.modelData.status,callback:function(t){e.$set(e.modelData,"status",t)},expression:"modelData.status"}},[s("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),s("el-option",{attrs:{label:"禁用",value:0}})],1)],1)],1),e._v(" "),s("div",{attrs:{id:"workflowDiv"}})],1)},staticRenderFns:[]},r={name:"processConfig",components:{processConfigAdd:s("VU/8")({name:"processConfigAdd",data:function(){return{receiptTypeData:[],modelData:{id:"",processCode:"",workflowInterface:"",processName:"",receiptType:"",status:"",tenantId:"",companyNo:"",processInstanceId:"",processState:""},formData:{id:"",processCode:"",workflowInterface:"",processName:"",receiptType:"",status:"",tenantId:"",companyNo:"",processInstanceId:"",processState:""},rules:{processCode:[{required:!0,message:"请输入流程标识",trigger:"blur"}],processName:[{required:!0,message:"请输入流程名称",trigger:"blur"}],receiptType:[{required:!0,message:"请输入单据类型",trigger:"blur"}]}}},mounted:function(){this.getBusinessData()},methods:{resetForm:function(){this.$refs.processConfigAddForm.resetFields()},getBusinessData:function(){var e=this;this.$api.fetch({params:{busicode:"ReceiptList",data:{page:1,pageCount:5e4}}}).then(function(t){e.receiptTypeData=t.list})},submitForm:function(e,t,s){var a=this,o=this,r={};this.$refs[e].validate(function(e){if(!e)return!1;o.modelData=o.common.handleData(o.modelData,o.formData),r="添加"===t?{busicode:"processConfigAdd",data:a.modelData}:{busicode:"processConfigUpdate",data:a.modelData},a.$api.fetch({params:r}).then(function(e){o.$message({showClose:!0,message:"保存成功",type:"success"}),o.$parent.getData(),o.$parent.closeDialog()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.modelData,"get","processConfigAdd",this.$parent.closeDialog)},editData:function(e){this.modelData=e}}},o,!1,null,null,null).exports,autoTree:a.a},data:function(){return{tableShow:!0,maxHeight:0,selServicesData:[],processStateData:[],tableQuery:{receiptName:"",companyNo:""},formData:{companyNo:"",id:"",processCode:"",platId:"",processName:"",receiptType:"",status:"",tenantId:"",processInstanceId:"",processState:""},crumbsData:{titleList:[{title:"水司配置",path:"/waterSet"},{title:"审批流程",method:function(){window.histroy.back()}},{title:"根目录",method:function(){window.histroy.back()}}]},processUrl:"",processConfigShow:!0,processConfigAddVisible:!1,treeDatas:{tree:[{shortName:"根目录",id:"2",children:[]}],defaultProps:{label:"shortName",children:"children"},inputProp:{showSearch:!1,Inp_placeholder:"请输入节点"},sendTreeProp:["code","shortName","districtArr","children","companyNo","group","isLeaf","isParent","parent","sonData"],rootName:"根目录",defaultOpen:{nodeKey:"id"}},treeParantId:""}},mounted:function(){var e=this;this.getProcessUrl(),this.getTreeDatas(),this.$nextTick(function(){e.common.changeTable(e,".processConfig",[".processConfig .toolbar"])})},methods:{formatStatus:function(e){return 1===e.status?"启用":"禁用"},formatProcessStatus:function(e){for(var t=0;t<this.processStateData.length;t++)if(e.processState==this.processStateData[t].nodeCode)return this.processStateData[t].nodeName;return e.processState},appAdd:function(e){var t=this;if(this.processConfigShow=!1,this.processConfigAddVisible=!0,"add"===e)this.$refs.processConfigAdd.editData({id:"",processCode:"",platId:"",processName:"",receiptType:"",status:"",tenantId:"",processInstanceId:"",processState:"",companyNo:this.tableQuery.companyNo}),this.formData.companyNo=this.tableQuery.companyNo,this.$set(this.crumbsData.titleList,"3",{title:"添加",method:function(){window.histroy.back()}}),this.common.chargeObjectEqual(this,this.formData,"set","processConfigAdd");else{this.$set(this.crumbsData.titleList,"3",{title:"编辑",method:function(){window.histroy.back()}});var s=this,a={busicode:"processConfigQuery",data:{id:e.row.id,companyNo:s.tableQuery.companyNo}};this.$api.fetch({params:a}).then(function(e){e.companyNo=s.tableQuery.companyNo,t.$refs.processConfigAdd.editData(e),t.common.chargeObjectEqual(t,e,"set","processConfigAdd")})}},getProcessUrl:function(){var e=this;this.$api.fetch({params:{busicode:"ProcessUrl",data:{}}}).then(function(t){e.processUrl=t,console.log(e.processUrl)})},design:function(e){var t=this;this.$api.fetch({params:{busicode:"ProcessSecret",data:{}}}).then(function(s){window.open(t.processUrl+"/workFlowDesignSSO.htm?secret="+s+"&account=zxy&appCode=css2&setCode="+e.row.setCode+"&processCode="+e.row.processCode)})},search:function(){this.tableQuery.page=1,this.getData()},indexMethod:function(e){return e+1},getData:function(){var e=this;e.common.getProcessStateData("BASE_PROCESS_CONFIG",e.tableQuery.companyNo,e,function(t){e.processStateData=t;var s={busicode:"ProcessConfigList",data:e.tableQuery};e.$api.fetch({params:s}).then(function(t){e.selServicesData=t,e.common.changeTable(e,".processConfig .kl-table",[])}).catch(function(t){e.common.changeTable(e,".processConfig .kl-table",[])})})},getTreeDatas:function(){var e=this,t=this;this.$api.fetch({params:{busicode:"CompanyNameList",data:{}}}).then(function(s){t.treeDatas.tree[0].children=s,t.tableQuery.companyNo=s[0].companyNo,e.$set(e.crumbsData.titleList,"2",{title:s[0].shortName,method:function(){window.histroy.back()}}),t.getData()})},refreshNode:function(e){var t=this,s={busicode:"ProcessConfigNodeRefresh",data:{processCode:e.row.processCode,companyNo:t.tableQuery.companyNo}};this.$api.fetch({params:s}).then(function(e){t.$message({showClose:!0,message:"执行成功",type:"success"})})},closeDialog:function(){this.processConfigShow=!0,this.processConfigAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.processConfigAdd.handleClose()},close:function(){this.printStyleShow=!1},backTreeData:function(e){if("根目录"!==e.shortName){this.tableQuery.companyNo=e.companyNo,this.$set(this.crumbsData.titleList,"2",{title:e.shortName,method:function(){window.histroy.back()}}),this.getData()}},submitForm:function(e){var t=this.crumbsData.titleList[3].title;this.$refs.processConfigAdd.submitForm(e,t,this.tableQuery.companyNo)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},i={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"processConfig"},[s("div",{staticClass:"main-content"},[s("div",{staticClass:"bread-contain"},[s("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:e.processConfigAddVisible,expression:"processConfigAddVisible"}],staticClass:"bread-contain-right"},[s("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("processConfigAddForm")}}},[e._v("保存")]),e._v(" "),s("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:e.processConfigShow,expression:"processConfigShow"}],staticClass:"company-content"},[s("div",{staticClass:"company-left"},[s("auto-tree",{attrs:{treeData:e.treeDatas},on:{sendTreeData:e.backTreeData}})],1),e._v(" "),s("div",{staticClass:"kl-table company-right"},[s("div",{staticClass:"toolbar"},[s("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.tableQuery,size:"mini"}},[s("div",{staticClass:"toolbar-left",staticStyle:{width:"80%"}},[s("el-form-item",{attrs:{label:"单据类型："}},[s("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.receiptName,callback:function(t){e.$set(e.tableQuery,"receiptName",t)},expression:"tableQuery.receiptName"}})],1),e._v(" "),s("el-form-item",{staticClass:"button-group"},[s("el-button",{staticClass:"compile",attrs:{type:"primary el-icon-search",size:"mini"},on:{click:e.search}})],1)],1)])],1),e._v(" "),e.tableShow?s("el-table",{attrs:{stripe:"",border:"",data:e.selServicesData,"max-height":e.maxHeight}},[s("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),s("el-table-column",{attrs:{prop:"receiptName",label:"单据类型",width:"280"}}),e._v(" "),s("el-table-column",{attrs:{prop:"processCode",label:"流程编号",width:"280"}}),e._v(" "),s("el-table-column",{attrs:{prop:"setCode",label:"模板组编号",width:"280"}}),e._v(" "),s("el-table-column",{attrs:{label:"操作",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{attrs:{type:"text"},on:{click:function(s){return e.design(t)}}},[e._v("设计")]),e._v(" "),s("span",{staticStyle:{color:"#e6e6e6"}},[e._v("|")]),e._v(" "),s("el-button",{attrs:{type:"text"},on:{click:function(s){return e.refreshNode(t)}}},[e._v("刷新节点")])]}}],null,!1,2672590165)})],1):e._e()],1)]),e._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:e.processConfigAddVisible,expression:"processConfigAddVisible"}],staticStyle:{height:"92%",overflow:"auto"}},[s("processConfigAdd",{ref:"processConfigAdd"})],1)])])},staticRenderFns:[]};var n=s("VU/8")(r,i,!1,function(e){s("VCeR")},null,null);t.default=n.exports},VCeR:function(e,t){}});