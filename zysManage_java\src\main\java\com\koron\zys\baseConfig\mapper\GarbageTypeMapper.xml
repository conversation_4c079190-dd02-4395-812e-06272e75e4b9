<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.koron.zys.baseConfig.mapper.GarbageTypeMapper">
	<select id="list"
		parameterType="com.koron.zys.baseConfig.bean.GarbageTypeQueryBean"
		resultType="com.koron.zys.baseConfig.bean.GarbageTypeBean">
		select * from base_garbate_config
		where 1=1 
		<if test=" garbageTypeName !='' and garbageTypeName != null ">
			and garbage_type_name like concat('%',#{garbageTypeName},'%') 
		</if>
		<if test=" garbageTypeValue !='' and garbageTypeValue != null ">
			and garbage_type_value like concat('%',#{garbageTypeValue},'%')  
		</if>
		<if test=" status !='' and status != null ">
			and status = #{status}
		</if>
	</select>
	<insert id="add" 
		parameterType="com.koron.zys.baseConfig.bean.GarbageTypeBean">
	insert into base_garbate_config(id,garbage_type_name,garbage_type_value,status,comments,create_time,
		create_name,create_account,update_time,update_name,update_account)
	values(#{id},#{garbageTypeName},#{garbageTypeValue},#{status},#{comments},#{createTime},
		#{createName},#{createAccount},#{updateTime},#{updateName},#{updateAccount})
	</insert>
	<update id="update"
		parameterType="com.koron.zys.baseConfig.bean.GarbageTypeBean">
		update base_garbate_config set 
			garbage_Type_name=#{garbageTypeName},
			garbage_Type_value=#{garbageTypeValue},
			status=#{status},
			comments=#{comments},
			create_time=#{createTime},
			create_name=#{createName},
			create_account=#{createAccount},
			update_time=#{updateTime},
			update_name=#{updateName},
			update_account=#{updateAccount}
		where id=#{id}
	</update>
	<delete id="delete" parameterType="String">
		delete from base_garbate_config where id=#{id}
	</delete>
</mapper>