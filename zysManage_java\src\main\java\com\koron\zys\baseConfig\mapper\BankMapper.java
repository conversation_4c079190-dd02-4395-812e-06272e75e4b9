package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.koron.zys.baseConfig.bean.BankBean;
import com.koron.zys.baseConfig.queryBean.BankQueryBean;
import com.koron.zys.baseConfig.vo.BankVO;
import com.koron.zys.baseConfig.vo.SelectVO;
import com.koron.zys.serviceManage.bean.SelectBean;
import com.koron.zys.serviceManage.bean.TreeBean;

/**
 * 银行信息
 *
 * <AUTHOR>
 */
public interface BankMapper {

    /**
     * 新增
     *
     * @param bean
     * @return
     */
    public Integer saveBank(BankBean bean);

    /**
     * 更新
     *
     * @param bean
     * @return
     */
    public Integer updateBank(BankBean bean);

    /**
     * 查询
     *
     * @param bean
     * @return
     */
    public List<BankVO> findBank(BankQueryBean bean);

//    /**
//     * 查询省份
//     *
//     * @param bean
//     * @return
//     */
//    public List<SelectBean> SelectProvince();

    /**
     * 树结构
     *
     * @param
     * @return
     */
    public List<TreeBean> findBankByTree();

    /**
     * 下拉框
     *
     * @return
     */
    public List<SelectBean> findBankBySelect();

//	/**
//	 * 按ID查询
//	 * @param bean
//	 * @return
//	 */
//	public BankBean findBankById(BankBean bean);

    /**
     * 根据id查Bank数据
     *
     * @param id
     * @return
     */
    @Select("select * from BASE_BANK where id = #{id}")
    public BankBean findBankById(@Param("id") String id);

    /**
     * 根据id查Bank数据
     *
     * @param bankNumber
     * @return
     */
    @Select("select * from BASE_BANK where bank_number = #{bankNumber}")
    public BankBean findBankByBankNumber(@Param("bankNumber") String bankNumber);
    
    /**
     * 根据name查Bank数据
     *
     * @param id
     * @return
     */
    @Select("select * from BASE_BANK where bank_name = #{bankName}")
    public BankBean findBankByName(@Param("bankName") String bankName);
    /**
     * 根据code查Bank数据
     *
     * @param bankNo
     * @return
     */
    public SelectBean findBankByCode(@Param("bankNo") String bankNo);

    /**
     * 根据某个code下最大子级代号
     *
     * @param bankNo
     * @return
     */
    public String finBankMAXChild(@Param("bankNo") String bankNo);

    /**
     * 根据某个code下最大子级代号
     *
     * @param bankNo
     * @return
     */
    public String findMaxChild(@Param("bankNo") String bankNo);

    /**
     * 下拉框
     *
     * @return
     */
    List<SelectVO> bankSelect();

    @Select("select a.* " +
            "from BASE_BANK a left join BASE_BANK b " +
            "on a.parent_id=b.id " +
            "where a.status =1 and  a.bank_no LIKE CONCAT(#{bankNo},'%')")
    List<BankBean> getChild(@Param("bankNo") String bankNo);
}
