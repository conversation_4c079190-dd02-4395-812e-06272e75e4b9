package com.koron.zys.baseConfig.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

public class PjQueryItemSelect implements ServerInterface {
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			// 获取下拉框
			List<Map<String, String>> list = new ArrayList<Map<String, String>>();
			Map<String, String> map = new HashMap<String, String>();
			map.put("1", "工程编号");
			map.put("2", "报装地址");
			map.put("3", "用户名称");
			map.put("4", "客户证件号码");
			map.put("5", "手机号码");
			map.put("6", "经办人电话");
			map.put("7", "经办人名称");
			map.put("8", "经办人证件号码");
			list.add(map);
			info.setData(list);
			return info;
		} catch (Exception e) {
			logger.error("工程查询项查询失败" + e.getMessage(), e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "工程查询项查询失败", null);
		}

	}

}
