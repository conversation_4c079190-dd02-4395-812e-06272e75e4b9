package com.koron.common.bean;


public class TarantBean {
	
	/**
	 * id
	 */
	private Integer id;

	/**
	 * 名称
	 */
	private String name;
	
	
	/**
	 * 编码
	 */
	private String code;
	
	/**
	 * 简称
	 */
	private String abbr;
	
	/**
	 * 存储分支
	 */
	private String configBranch;
	
	
	/**
	 * 更新时间
	 */
	private String lastupdate;
	
	
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getAbbr() {
		return abbr;
	}

	public void setAbbr(String abbr) {
		this.abbr = abbr;
	}

	public String getConfigBranch() {
		return configBranch;
	}

	public void setConfigBranch(String configBranch) {
		this.configBranch = configBranch;
	}

	public String getLastupdate() {
		return lastupdate;
	}

	public void setLastupdate(String lastupdate) {
		this.lastupdate = lastupdate;
	}

	@Override
	public String toString() {
		return "TarantBean [id=" + id + ", name=" + name + ", code=" + code + ", abbr=" + abbr + ", configBranch="
				+ configBranch + ", lastupdate=" + lastupdate + "]";
	}
	
}
