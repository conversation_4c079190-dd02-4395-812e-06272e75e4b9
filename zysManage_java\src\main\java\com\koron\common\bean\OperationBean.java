package com.koron.common.bean;

import java.io.Serializable;

/**
 * 操作的实体bean
 */
public class OperationBean implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 操作的主键id
	 */
	private Integer id;
	
	private Integer parentId;
	
	/**
	 * 操作名称
	 */
	private String name;
	/**
	 * 操作的code编码
	 */
	private String code;
	/**
	 * 参数
	 */
	private String param;
	/**
	 * 顺序号,用来形成树形结构
	 */
	private Long seq;
	/**
	 * 父级掩码位数
	 */
	private Integer parentMask;	
	/**
	 * 掩码位数
	 */
	private Integer mask;
	/**
	 * 子级掩码位数
	 */
	private Integer childMask;
	
	/**
	 * app
	 */
	private String app;
	
	public String getApp() {
		return app;
	}
	public void setApp(String app) {
		this.app = app;
	}
	public Integer getParentId() {
		return parentId;
	}
	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getParam() {
		return param;
	}
	public void setParam(String param) {
		this.param = param;
	}
	public Long getSeq() {
		return seq;
	}
	public void setSeq(Long seq) {
		this.seq = seq;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	public Integer getParentMask() {
		return parentMask;
	}
	public void setParentMask(Integer parentMask) {
		this.parentMask = parentMask;
	}
	public Integer getMask() {
		return mask;
	}
	public void setMask(Integer mask) {
		this.mask = mask;
	}
	public Integer getChildMask() {
		return childMask;
	}
	public void setChildMask(Integer childMask) {
		this.childMask = childMask;
	}
	
	@Override
	public String toString() {
		return "OperationBean [id=" + id + ", parentId=" + parentId + ", name=" + name + ", code=" + code + ", param="
				+ param + ", seq=" + seq + ", parentMask=" + parentMask + ", mask=" + mask + ", childMask=" + childMask
				+ "]";
	}

	

}
