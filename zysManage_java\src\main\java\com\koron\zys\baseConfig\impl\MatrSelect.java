package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.vo.MatrSelectVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.MatrCodeMapper;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import java.util.List;

/**
 * 物料编码下拉框
 * 
 * <AUTHOR>
 *
 */
public class MatrSelect implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			MatrCodeMapper mapper = factory.getMapper(MatrCodeMapper.class, "_default");
			// 获取下拉框
			List<MatrSelectVO> list = mapper.matrSelect();
			info.setData(list);
			return info;
		} catch (Exception e) {
			logger.error("物料编码查询失败" + e.getMessage(), e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "物料编码查询失败", null);
		}

	}
}
