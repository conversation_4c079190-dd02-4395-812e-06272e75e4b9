package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BaseInvoiceTypeBean;
import com.koron.zys.baseConfig.mapper.BaseInvoiceTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;

public class BaseInvoiceTypeAdd implements ServerInterface {

	@Override
	@ValidationKey(clazz = BaseInvoiceTypeBean.class,method = "insert")
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			BaseInvoiceTypeBean bean = JsonUtils.objectToPojo(req.getData(), BaseInvoiceTypeBean.class);
			BaseInvoiceTypeMapper mapper = factory.getMapper(BaseInvoiceTypeMapper.class);
			bean.setCreateInfo(userInfo);
			mapper.insert(bean);
			return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "保存成功", void.class);
		} catch (Exception e) {
			logger.error("非法参数,票据类型保存失败", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数,票据类型保存失败", void.class);
		}
	}

}
