package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.koron.zys.baseConfig.bean.BaseReceiptAccessoryBean;
import com.koron.zys.baseConfig.queryBean.BaseReceiptAccessoryQueryBean;

public interface BaseReceiptAccessoryMapper {
	
	List<BaseReceiptAccessoryBean> selectList(BaseReceiptAccessoryQueryBean query);
	
	List<BaseReceiptAccessoryBean> selectListByCtmNo(BaseReceiptAccessoryQueryBean query);
	
	BaseReceiptAccessoryBean selectById(String id);
	
	int insert(BaseReceiptAccessoryBean bean);
	
	int update(BaseReceiptAccessoryBean bean);
	
	int deleteById(String id);
	
	void updateAccessoryReceiptId(@Param("id") String id,@Param("tempId") String tempId);
	
	int selectReceiptByUserNo(BaseReceiptAccessoryQueryBean query);
	
	int isHaveUserNo(String receiptType);
	int deleteByAccessoryNo(BaseReceiptAccessoryBean bean);
}	
