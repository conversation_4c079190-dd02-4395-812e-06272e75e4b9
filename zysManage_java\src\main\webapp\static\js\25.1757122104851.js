webpackJsonp([25],{Mril:function(e,t){},TCmE:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"printBusinessManAdd"},[i("el-form",{ref:"formDataprintBusinessManAdd",staticClass:"formprintBusinessManAdd",attrs:{model:e.formDataService,rules:e.rules,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"打印业务编号：",prop:"code"}},[i("el-input",{model:{value:e.formDataService.code,callback:function(t){e.$set(e.formDataService,"code",t)},expression:"formDataService.code"}})],1),e._v(" "),i("el-form-item",{staticClass:"mark",attrs:{label:"打印业务名称：",prop:"name"}},[i("el-input",{model:{value:e.formDataService.name,callback:function(t){e.$set(e.formDataService,"name",t)},expression:"formDataService.name"}})],1),e._v(" "),i("el-form-item",{staticClass:"remark",attrs:{label:"打印业务描述：",prop:"remark"}},[i("el-input",{attrs:{type:"textarea",autosize:{minRows:2,maxRows:4},maxlength:"500",clearable:"","show-word-limit":""},model:{value:e.formDataService.remark,callback:function(t){e.$set(e.formDataService,"remark",t)},expression:"formDataService.remark"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"子系统："}},[i("el-button-group",[i("el-button",{attrs:{size:"mini",type:"info",icon:"el-icon-minus"},on:{click:e.delRow}}),e._v(" "),i("el-button",{attrs:{size:"mini",type:"info",icon:"el-icon-plus"},on:{click:e.addRow}})],1)],1)],1),e._v(" "),i("div",{staticClass:"kl-table form-table"},[e.tableShow?i("el-table",{ref:"multipleTable",staticClass:"tableTSer",staticStyle:{width:"100%"},attrs:{data:e.formDataService.fieldsList,border:"","max-height":e.maxHeight,"highlight-current-row":!0},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),i("el-table-column",{attrs:{prop:"fieldCode",label:"字段标识","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{staticClass:"inputClass",staticStyle:{width:"100%"},attrs:{"controls-position":"right"},model:{value:e.formDataService.fieldsList[t.$index].fieldCode,callback:function(i){e.$set(e.formDataService.fieldsList[t.$index],"fieldCode",i)},expression:"formDataService.fieldsList[scope.$index].fieldCode"}})]}}],null,!1,2293153011)}),e._v(" "),i("el-table-column",{attrs:{prop:"fieldName",label:"字段名称","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-input",{staticClass:"inputClass",staticStyle:{width:"100%"},attrs:{"controls-position":"right"},model:{value:e.formDataService.fieldsList[t.$index].fieldName,callback:function(i){e.$set(e.formDataService.fieldsList[t.$index],"fieldName",i)},expression:"formDataService.fieldsList[scope.$index].fieldName"}})]}}],null,!1,4065693561)}),e._v(" "),i("el-table-column",{attrs:{prop:"fieldType",label:"字段类型","min-width":"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-select",{attrs:{placeholder:"请选择字段类型"},model:{value:e.formDataService.fieldsList[t.$index].fieldType,callback:function(i){e.$set(e.formDataService.fieldsList[t.$index],"fieldType",i)},expression:"formDataService.fieldsList[scope.$index].fieldType"}},e._l(e.fieldTypesData,function(e){return i("el-option",{key:e.type,attrs:{label:e.name,value:e.type}})}),1)]}}],null,!1,2150353068)})],1):e._e()],1)],1)},staticRenderFns:[]};var s={name:"printBusinessMan",components:{printBusinessManAdd:i("VU/8")({name:"printBusinessManAdd",data:function(){return{formDataService:{id:"",code:"",name:"",remark:"",fieldsList:[]},fieldTypesData:[{type:1,name:"数字"},{type:2,name:"字符串"},{type:13,name:"图片"}],rules:{code:[{required:!0,message:"请输入打印业务编号",trigger:"blur"}],name:[{required:!0,message:"请输入打印业务名称",trigger:"blur"}]},multipleSelection:"",maxHeight:0,tableShow:!1}},mounted:function(){var e=this;this.$nextTick(function(){e.common.changeTable(e,".printBusinessManAdd",[".printBusinessManAdd .formprintBusinessManAdd"])})},methods:{handleSelectionChange:function(e){this.multipleSelection=e},delRow:function(){for(var e in this.formDataService.fieldsList)if(0===this.multipleSelection.length)this.$message({showClose:!0,message:"请选择要移除的数据"});else for(var t in this.multipleSelection)this.formDataService.fieldsList[e]==this.multipleSelection[t]&&this.formDataService.fieldsList.splice(e,1)},addRow:function(){this.formDataService.fieldsList.push({fieldCode:"",fieldName:"",fieldType:""})},delAll:function(){this.formDataService.fieldsList=[]},submitForm:function(e,t){var i=this;if(this.formDataService.fieldsList.length>0)for(var a=0;a<this.formDataService.fieldsList.length;a++){if(""==this.formDataService.fieldsList[a].fieldCode)return void this.$message({type:"warning",message:"第"+(a+1)+"行字段标识不能为空"});if(""==this.formDataService.fieldsList[a].fieldName)return void this.$message({type:"warning",message:"第"+(a+1)+"行字段名称不能为空"});if(""==this.formDataService.fieldsList[a].fieldType)return void this.$message({type:"warning",message:"第"+(a+1)+"行字段标类型不能为空"})}var s=this,n={};this.$refs[e].validate(function(e){if(!e)return!1;n="添加"===t?{busicode:"PrintBusinessAdd",data:i.formDataService}:{busicode:"PrintBusinessUpdate",data:i.formDataService},i.$api.fetch({method:"post",params:n}).then(function(e){s.$message({showClose:!0,message:"保存成功",type:"success"}),s.$parent.getData(),s.$parent.handleClose(),s.formDataService.fieldsList=[]})})},handleClose:function(){this.common.chargeObjectEqual(this,this.formDataService,"get","printBusinessForm",this.boforeClose)},boforeClose:function(){this.$parent.handleClose()},editData:function(e,t){this.formDataService=e,this.formDataService.fieldsList||(this.formDataService.fieldsList=[])}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},a,!1,function(e){i("nsoA")},null,null).exports},data:function(){return{tableShow:!1,tableQuery:{name:"",code:"",page:1,pageCount:50},maxHeight:0,selServicesData:{},printBusinessManAddVisible:!1,printBusinessManShow:!0,crumbsData:{titleList:[{title:"基础配置",path:"/baseInfo"},{title:"打印模板",method:function(){window.histroy.back()}}]},formData:{id:"",code:"",name:"",remark:""},formDataService:{}}},mounted:function(){this.getData()},methods:{getData:function(){var e=this,t={busicode:"PrintBusinessList",data:this.tableQuery};this.$api.fetch({params:t}).then(function(t){e.selServicesData=t,e.common.changeTable(e,".printBusinessMan .kl-table",[".printBusinessMan .toolbar",".printBusinessMan .block"])})},search:function(){this.tableQuery.page=1,this.getData()},appAdd:function(e){var t=this;this.printBusinessManAddVisible=!0,this.printBusinessManShow=!1,this.$nextTick(function(){if("add"===e)t.$set(t.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),t.$refs.printBusinessManAdd.editData({code:"",name:"",remark:"",fieldsList:[]}),t.common.chargeObjectEqual(t,t.formData,"set","printBusinessForm");else{t.$set(t.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var i=t,a={busicode:"PrintBusinessQuery",data:{id:e.row.id}};t.$api.fetch({params:a}).then(function(e){i.formDataService=e,i.$refs.printBusinessManAdd.editData(i.formDataService,"edit"),i.common.chargeObjectEqual(i,e,"set","printBusinessForm")})}})},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.getData()},handleCurrentChange:function(e){this.tableQuery.page=e,this.getData()},handleClose:function(){this.printBusinessManAddVisible=!1,this.printBusinessManShow=!0,this.crumbsData.titleList.pop()},close:function(){this.$refs.printBusinessManAdd.handleClose()},submitForm:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.printBusinessManAdd.submitForm(e,t)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},n={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"printBusinessMan"},[i("div",{staticClass:"main-content"},[i("div",{staticClass:"bread-contain"},[i("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.printBusinessManShow,expression:"printBusinessManShow"}],staticClass:"bread-contain-right"},[i("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.printBusinessManAddVisible,expression:"printBusinessManAddVisible"}],staticClass:"bread-contain-right"},[i("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("formDataprintBusinessManAdd")}}},[e._v("保存")]),e._v(" "),i("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.close}},[e._v("返回")])],1)],1),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.printBusinessManShow,expression:"printBusinessManShow"}],staticClass:"kl-table"},[i("div",{staticClass:"toolbar"},[i("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.tableQuery,size:"mini"}},[i("div",{staticClass:"toolbar-left"},[i("el-form-item",{attrs:{label:"打印业务编号："}},[i("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.code,callback:function(t){e.$set(e.tableQuery,"code",t)},expression:"tableQuery.code"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"打印业务名称："}},[i("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.name,callback:function(t){e.$set(e.tableQuery,"name",t)},expression:"tableQuery.name"}})],1),e._v(" "),i("el-form-item",{staticClass:"button-group"},[i("el-button",{staticClass:"compile",attrs:{type:"primary el-icon-search",size:"mini"},on:{click:e.search}})],1)],1),e._v(" "),i("div",{staticClass:"toolbar-right"})])],1),e._v(" "),e.tableShow?i("el-table",{staticStyle:{width:"100%"},attrs:{stripe:"",center:"",border:"",data:e.selServicesData.list,"max-height":e.maxHeight}},[i("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),i("el-table-column",{attrs:{prop:"code",label:"业务编码",width:"200"}}),e._v(" "),i("el-table-column",{attrs:{prop:"name",label:"业务名称","show-overflow-tooltip":""}}),e._v(" "),i("el-table-column",{attrs:{prop:"remark",label:"业务描述"}}),e._v(" "),i("el-table-column",{attrs:{label:"操作",width:"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{type:"text"},on:{click:function(i){return e.appAdd(t)}}},[e._v("修改")])]}}],null,!1,1896694690)})],1):e._e(),e._v(" "),i("div",{staticClass:"block"},[i("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.selServicesData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),e.printBusinessManAddVisible?i("div",{staticClass:"printBusinessAdd"},[i("printBusinessManAdd",{ref:"printBusinessManAdd"})],1):e._e()])])},staticRenderFns:[]};var r=i("VU/8")(s,n,!1,function(e){i("Mril")},null,null);t.default=r.exports},nsoA:function(e,t){}});