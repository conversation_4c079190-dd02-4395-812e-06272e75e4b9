package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CostInvoiceBean;
import com.koron.zys.baseConfig.mapper.CostInvoiceMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
/**
 * 短信模板-添加
 * <AUTHOR>
 */
public class CostInvoiceAdd implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(CostInvoiceAdd.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			CostInvoiceMapper mapper = factory.getMapper(CostInvoiceMapper.class);
			//清空
			mapper.deleteAll();
			//插入新记录
			List<CostInvoiceBean> beans = JsonUtils.jsonToList(JsonUtils.objectToJson(req.getData()), CostInvoiceBean.class);
			if(beans == null || beans.size() == 0) {
				factory.close();
				return MessageBean.create(Constant.NOT_NULL, "参数为空", void.class);
			}
			for (CostInvoiceBean bean : beans) {
				bean.setId(new ObjectId().toHexString());
				bean.setStatus("1");
				bean.setCreateName(userInfo.getUserInfo().getName());
				bean.setCreateAccount(userInfo.getUserInfo().getAcount());
				bean.setCreateTime(CommonUtils.getCurrentTime());
				mapper.insert(bean);
			}
		} catch (Exception e) {
			factory.close();
			logger.error("操作失败", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
}