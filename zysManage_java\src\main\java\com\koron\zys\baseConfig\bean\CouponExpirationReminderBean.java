package com.koron.zys.baseConfig.bean;

/**
 *
 * 优惠到期提醒
 * */
public class CouponExpirationReminderBean extends BaseBean{

    /**
     * 到期前天数
     */
    private Integer daysBeforeExpiration;
    /**
     * 通知方式，1为微信，2为短信，12为微信+短信
     */
    private Integer noticeWay;

    /**
     * 通知起始时间
     */
    private String beginTime;
    /**
     * 通知结束时间
     */
    private String endTime;

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getDaysBeforeExpiration() {
        return daysBeforeExpiration;
    }

    public void setDaysBeforeExpiration(Integer daysBeforeExpiration) {
        this.daysBeforeExpiration = daysBeforeExpiration;
    }

    public Integer getNoticeWay() {
        return noticeWay;
    }

    public void setNoticeWay(Integer noticeWay) {
        this.noticeWay = noticeWay;
    }
}
