package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CostNameBean;
import com.koron.zys.baseConfig.mapper.CostMapper;
import com.koron.zys.baseConfig.queryBean.CostQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 *费用名称下拉框
 * <AUTHOR>
 */
public class CostNameSelect implements ServerInterface{
	
	private static Logger logger = LoggerFactory.getLogger(CostNameSelect.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);

		try {
			CostQueryBean Bean = JsonUtils.objectToPojo(req.getData(), CostQueryBean.class);
			CostMapper mapper = factory.getMapper(CostMapper.class);		
			List<CostNameBean>  list = mapper.selectCostNameList();			
			info.setData(list);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}
