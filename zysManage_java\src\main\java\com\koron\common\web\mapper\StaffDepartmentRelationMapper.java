package com.koron.common.web.mapper;

import com.koron.common.web.bean.StaffDepartmentRelationBean;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StaffDepartmentRelationMapper {

	/**
	 * 删除所有的员工部门关系表所有信息
	 * @return
	 */
	@Delete("DELETE FROM tblstaff_department")
	public int deleteAll();

	/**
	 * 删除通过用户账号
	 * @param userCode
	 * @return
	 */
	@Delete("DELETE FROM tblstaff_department WHERE user_code = #{userCode} AND department_code = #{deptCode}")
	int deleteByUserAndDept(@Param("userCode") String userCode, @Param("deptCode")  String deptCode);

	/**
	 * 删除通过用户账号
	 * @param userCode
	 * @return
	 */
	@Delete("DELETE FROM tblstaff_department WHERE user_code = #{userCode} ")
	int deleteByUser(@Param("userCode") String userCode);

	/**
	 * 批量插入员工信息
	 * @param list
	 * @return
	 */
	public int batchInsertStaffDepartment(List<StaffDepartmentRelationBean> list);

	int insertStaffDepartment(StaffDepartmentRelationBean relation);

}
