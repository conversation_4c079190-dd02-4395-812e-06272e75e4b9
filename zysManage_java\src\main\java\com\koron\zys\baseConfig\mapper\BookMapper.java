package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.koron.zys.baseConfig.bean.BookBean;
import com.koron.zys.baseConfig.queryBean.BookQueryBean;

public interface BookMapper {

	List<BookBean> select(BookQueryBean bean);

	BookBean query(@Param("id") String id);

	BookBean queryByBookNo(@Param("bookNo") String bookNo);
	
	int insert(BookBean bean);
	
	int update(BookBean bean);
	
}
