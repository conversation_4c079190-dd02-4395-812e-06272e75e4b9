package com.koron.zys.baseConfig.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.WaterTypeMapper;
import com.koron.zys.baseConfig.queryBean.WaterTypeQueryBean;
import com.koron.zys.baseConfig.vo.WaterTypeVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.SelectBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

/**
 * 用水类型-列表初始化
 *
 * <AUTHOR>
 */
public class WaterTypeList implements ServerInterface {
    private static Logger logger = LoggerFactory.getLogger(WaterTypeList.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        @SuppressWarnings("rawtypes")
        MessageBean<HashMap> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", HashMap.class);
        try {
            WaterTypeMapper mapper = factory.getMapper(WaterTypeMapper.class);
            WaterTypeQueryBean bean = JsonUtils.objectToPojo(req.getData(), WaterTypeQueryBean.class);
//            bean.setIsLeaf(-1);//查所有数据
            PageHelper.startPage(bean.getPage(), bean.getPageCount());
            List<WaterTypeVO> beans = mapper.findWaterType(bean);
            HashMap<String, Object> map = new HashMap<>();
            map.put("pageInfo", new PageInfo<>(beans));
            //找出所有上级目录，显示分级导航
            ArrayList<HashMap<String, String>> navList = new ArrayList<>();
            recParent(mapper, bean.getWaterTypeNo(), navList);
            map.put("navData", navList);
            info.setCode(Constant.MESSAGE_INT_SUCCESS);
            info.setDescription("success");
            info.setData(map);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_DBFAIL);
            info.setDescription("数据库异常");
            logger.error("数据库异常", e);
            factory.close(false);
        }
        return info;
    }

    /**
     * 递归查询每一级目录
     *
     * @param mapper
     * @param code
     * @param navList
     */
    private void recParent(WaterTypeMapper mapper, String code, ArrayList<HashMap<String, String>> navList) {
        if (code.length() > 0) {
            SelectBean selb = mapper.findWaterTypeByCode(code);
            HashMap<String, String> m = new HashMap<>();
            m.put("code", code);
            m.put("name", selb.getName());
            //存储id和parentId
            m.put("id", selb.getId());
            m.put("parentId", selb.getParentId());
            navList.add(0, m);
            recParent(mapper, code.substring(0, code.length() - 2), navList);
        }
    }

}
