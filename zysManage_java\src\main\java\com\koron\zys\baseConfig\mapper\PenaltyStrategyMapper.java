package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.baseConfig.bean.PenaltyBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.koron.zys.baseConfig.bean.PenaltyStrategyBean;
import com.koron.zys.baseConfig.queryBean.PenaltyQueryBean;
import com.koron.zys.baseConfig.vo.PenaltyVO;
import com.koron.zys.baseConfig.vo.SelectVO;



public interface PenaltyStrategyMapper {

	/**
	 * 查询列表
	 *
	 * @return
	 */
	List<PenaltyVO> selectPenaltyList(PenaltyQueryBean penaltyStrategyQueryBean);

	/**
	 * 根据id查询
	 * @param penaltyId
	 * @return
	 */
	PenaltyBean selectPenaltyById(@Param("id") String id);

	/**
	 * 根据id查询
	 * @param penaltyId
	 * @return
	 */
	PenaltyStrategyBean selectPenaltyStrategyById(@Param("id") String id);

	/**
	 * 根据id查询
	 * @param penaltyId
	 * @return
	 */
	List<PenaltyStrategyBean> selectPenaltyStrategyListByPenaltyId(@Param("penaltyId") String penaltyId);

	/**
	 * 添加
	 *
	 * @param penaltyBean
	 * @return
	 */
	void insertPenalty(PenaltyBean penaltyBean);

	/**
	 * 添加
	 *
	 * @param penaltyStrategyBean
	 * @return
	 */
	void insertPenaltyStrategy(PenaltyStrategyBean penaltyStrategyBean);

	/**
	 * 校验字段内容重复
	 */
	@Select("select count(*) from BASE_PENALTY_STRATEGY where ${key} = #{val}")
	Integer check(@Param("key") String key, @Param("val") String val);

	/**
	 * 校验字段内容重复-排除当前记录
	 */
	@Select("select count(*) from BASE_PENALTY_STRATEGY where ${key} = #{val} and id <> #{id}")
	Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);

	/**
	 * 修改
	 *
	 * @param penaltyBean
	 * @return
	 */
	Integer updatePenalty(PenaltyBean penaltyBean);

	/**
	 * 修改
	 *
	 * @param penaltyStrategyBean
	 * @return
	 */
	Integer updatePenaltyStrategy(PenaltyStrategyBean penaltyStrategyBean);

	/**
	 * 查询列表
	 * @return
	 */
	List<SelectVO>  selectStrategyNameList();


}
