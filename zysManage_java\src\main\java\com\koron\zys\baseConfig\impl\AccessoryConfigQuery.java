package com.koron.zys.baseConfig.impl;

import java.util.ArrayList;
import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.AccessoryConfigBean;
import com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean;
import com.koron.zys.baseConfig.bean.PubReceiptBean;
import com.koron.zys.baseConfig.mapper.BaseAccessoryCofingMapper;
import com.koron.zys.baseConfig.mapper.PubReceiptMapper;
import com.koron.zys.baseConfig.queryBean.BaseAccessoryConfigQueryBean;
import com.koron.zys.baseConfig.queryBean.PubReceiptQueryBean;
import com.koron.zys.serviceManage.bean.AccessoryTypeBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.AccessoryTypeMapper;
import com.koron.util.Constant;

public class AccessoryConfigQuery implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub
		 @SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		 
		BaseAccessoryCofingMapper mapper = factory.getMapper(BaseAccessoryCofingMapper.class);
		List<BaseAccessoryConfigBean> list = mapper.selectList1(new BaseAccessoryConfigQueryBean());
		
		
		//获取列表上单据类型数据
		PubReceiptMapper pubmapper = factory.getMapper(PubReceiptMapper.class);
		PubReceiptQueryBean pub =  new PubReceiptQueryBean();
		pub.setStartAccess("1");
		List<PubReceiptBean> publist = pubmapper.selectPubReceiptList(pub);
		//附件类型数据
		AccessoryTypeMapper acmapper = factory.getMapper(AccessoryTypeMapper.class,"_default");
		List<AccessoryTypeBean> aclist = acmapper.selectAccessoryType();
		//按顺序初始化前端需要的数据
		List<List<AccessoryConfigBean>> resultlist = new ArrayList<List<AccessoryConfigBean>>();
		for (PubReceiptBean pubbean : publist) {
			List<AccessoryConfigBean> nextlist = new ArrayList<AccessoryConfigBean>();
			for (AccessoryTypeBean acbean : aclist) {
				AccessoryConfigBean acfbean = new AccessoryConfigBean();
				acfbean.setAccessoryNo(acbean.getAccessoryNo());
				acfbean.setReceiptNo(pubbean.getReceiptNo());
				acfbean.setIsUse("false");
				acfbean.setIsChecked("false");
				//按照列表生生成基础BaseAccessoryConfigBean
				for (BaseAccessoryConfigBean bean : list) {
					//同个单据类型同个附件类型的重新设置
					if(bean.getAccessoryNo().equals(acfbean.getAccessoryNo())&&
							bean.getReceiptType().equals(acfbean.getReceiptNo())) {
						if(bean.getRequiredFlag()==1) {
							acfbean.setIsChecked("true");
						}
						acfbean.setIsUse("true");
					}
				}
				nextlist.add(acfbean);
			}
			resultlist.add(nextlist);
		}
		info.setData(resultlist);
		return info;
	}

}
