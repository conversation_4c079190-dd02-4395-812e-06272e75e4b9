package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;

/**
 * 缴费通知精灵规格实体类
 */
public class BasePayMentElfBean extends BaseBean {
    /**
     * 通知方式
     */
	@Check(name = "通知方式", notNull = true, min = 1)
    private Integer noticeWay;
    /**
     * 通知起始时间
     */
    private String beginTime;
    /**
     * 通知结束时间
     */
    private String endTime;

    public Integer getNoticeWay() {
        return noticeWay;
    }

    public void setNoticeWay(Integer noticeWay) {
        this.noticeWay = noticeWay;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
