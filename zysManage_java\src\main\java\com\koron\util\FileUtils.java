package com.koron.util;

import com.koron.zys.ApplicationConfig;

import java.io.*;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class FileUtils {

    public static File toZip(List<File> files, File zipFile) {
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))){
            int len;
            for (File file : files) {
                byte[] buf = new byte[1024];
                zos.putNextEntry(new ZipEntry(file.getName()));
                try(InputStream in = new FileInputStream(file)){
                    while ((len = in.read(buf)) != -1){
                        zos.write(buf, 0, len);
                    }
                };
            }
            return zipFile;
        } catch (Exception e) {
            throw new RuntimeException("文件压缩失败，原因", e);
        }
    }

    public static File templateZipFile(String folderName) throws IOException {
        String filepath = ApplicationConfig.getUploadTempDir();
        File file =  new File(ApplicationConfig.getUploadTempDir());
        if(!file.exists()) {
            file.mkdir();
        }
        filepath = filepath + File.separator + folderName;
        file =  new File(filepath);
        if(!file.exists()) {
            file.createNewFile();
        }
        return file;
    }

    public static File templateZipFolder(String folderName) {
        String filepath = ApplicationConfig.getUploadTempDir();
        File file =  new File(ApplicationConfig.getUploadTempDir());
        if(!file.exists()) {
            file.mkdir();
        }
        filepath = filepath + File.separator + folderName;
        file =  new File(filepath);
        if(!file.exists()) {
            file.mkdir();
        }
        return file;
    }
}
