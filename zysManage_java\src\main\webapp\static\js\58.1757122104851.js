webpackJsonp([58],{NuXb:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=a("mvHQ"),o=a.n(l),n=a("bOdI"),i=a.n(n),r={name:"ContractFilling",data:function(){var t;return t={isCheckPage:!1,dictionaryData:{},crumbsData:{titleList:[{title:"系统监控",path:"/systemMan"},{title:"合同填报统计",method:function(){window.histroy.back()}}]},tableShow:!1,maxHeight:0,tableQuery:{contractDateBegin:"",contractDateEnd:"",belongArea:"",waterCompany:"",projectType:"",businessType:"",projectStatus:"",needSignContractFlag:"",page:1,pageCount:50},companyAreaSelectList:[],companyList:[]},i()(t,"dictionaryData",{}),i()(t,"tableData",""),i()(t,"exportData",""),t},mounted:function(){var t=this;this.getCompanyList(),this.getCompanyAreaSelectList(),this.getDictionarySelect(),this.getData(),this.$nextTick(function(){t.common.changeTable(t,".ContractFilling .kl-table",[".ContractFilling .toolbar",".ContractFilling .block"])}),eventBus.$emit("secondMenuShow","secondMenuShow1")},methods:{exportExcelList:function(){if(""!=this.exportData){var t={busicode:"ContractFillExport2",data:this.exportData,token:localStorage.getItem("token"),sysType:"002"};window.open(this.common.getExportExcelIp()+"/zysManage/exportExcel.api?json="+encodeURI(o()(t)))}else this.$message.warning("请先查询数据")},getCompanyAreaSelectList:function(){var t=this;this.$api.fetch({params:{busicode:"CompanyAreaSelect",data:{}}}).then(function(e){console.log(e),t.companyAreaSelectList=e})},getDictionarySelect:function(){var t=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"CONTRACT_BUSINESS_TYPE_1,CONTRACT_BUSINESS_TYPE_2,CONTRACT_PROJECT_TYPE,NEED_SIGN_CONTRACT_FLAG,CONTRACT_PROJECT_STATUS"}}).then(function(e){e.CONTRACT_BUSINESS_TYPE=e.CONTRACT_BUSINESS_TYPE_1.concat(e.CONTRACT_BUSINESS_TYPE_2),t.$set(t,"dictionaryData",e)}).catch(function(e){t.$set(t,"dictionaryData",[])})},getCompanyList:function(){var t=this;this.$api.fetch({params:{busicode:"CompanySelect",data:{}}}).then(function(e){t.companyList=e})},getData:function(){var t=this,e={busicode:"ContractFillSelect",data:this.tableQuery};this.$api.fetch({params:e}).then(function(e){console.log(e,"res"),t.tableData=e,t.exportData=JSON.parse(o()(t.tableQuery)),t.common.changeTable(t,".ContractFilling .kl-table",[".ContractFilling .toolbar",".ContractFilling .block"])})},demand:function(){this.tableQuery.page=1,this.getData()},indexMethod:function(t){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(t+1)},handleSizeChange:function(t){this.tableQuery.pageCount=t,this.tableQuery.page=1,this.getData()},handleCurrentChange:function(t){this.tableQuery.page=t,this.getData()}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},s={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"ContractFilling"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:t.crumbsData}}),t._v(" "),a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:t.exportExcelList}},[t._v("导出")])],1)],1),t._v(" "),a("div",{staticClass:"kl-table"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,size:"mini","label-width":"80px"}},[a("el-form-item",{staticClass:"col-fir-level accountPeriod",attrs:{label:"签约日期:"}},[a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"开始日期"},model:{value:t.tableQuery.beginContractDate,callback:function(e){t.$set(t.tableQuery,"beginContractDate",e)},expression:"tableQuery.beginContractDate"}}),t._v(" "),a("span",{staticClass:"separator"},[t._v("-")]),t._v(" "),a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"结束日期"},model:{value:t.tableQuery.endContractDate,callback:function(e){t.$set(t.tableQuery,"endContractDate",e)},expression:"tableQuery.endContractDate"}})],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"所属片区:"}},[a("el-select",{attrs:{clearable:"",placeholder:"全部"},model:{value:t.tableQuery.belongArea,callback:function(e){t.$set(t.tableQuery,"belongArea",e)},expression:"tableQuery.belongArea"}},t._l(t.companyAreaSelectList,function(t){return a("el-option",{key:t.code,attrs:{label:t.name,value:t.code}})}),1)],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"水司名称:"}},[a("el-select",{attrs:{clearable:"",placeholder:"全部"},model:{value:t.tableQuery.waterCompany,callback:function(e){t.$set(t.tableQuery,"waterCompany",e)},expression:"tableQuery.waterCompany"}},t._l(t.companyList,function(t,e){return a("el-option",{key:e,attrs:{value:t.id,label:t.name}})}),1)],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"项目类型:"}},[a("el-select",{attrs:{clearable:"",placeholder:"全部"},model:{value:t.tableQuery.projectType,callback:function(e){t.$set(t.tableQuery,"projectType",e)},expression:"tableQuery.projectType"}},t._l(t.dictionaryData.CONTRACT_PROJECT_TYPE,function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"业务类型:"}},[a("el-select",{attrs:{clearable:"",placeholder:"全部"},model:{value:t.tableQuery.businessType,callback:function(e){t.$set(t.tableQuery,"businessType",e)},expression:"tableQuery.businessType"}},t._l(t.dictionaryData.CONTRACT_BUSINESS_TYPE,function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"项目状态:"}},[a("el-select",{attrs:{clearable:"",placeholder:"全部"},model:{value:t.tableQuery.projectStatus,callback:function(e){t.$set(t.tableQuery,"projectStatus",e)},expression:"tableQuery.projectStatus"}},t._l(t.dictionaryData.CONTRACT_PROJECT_STATUS,function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"是否需要签订合同:","label-width":"140px"}},[a("el-select",{attrs:{clearable:"",placeholder:"全部"},model:{value:t.tableQuery.needSignContractFlag,callback:function(e){t.$set(t.tableQuery,"needSignContractFlag",e)},expression:"tableQuery.needSignContractFlag"}},t._l(t.dictionaryData.NEED_SIGN_CONTRACT_FLAG,function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",[a("el-button",{staticClass:"compile",attrs:{type:"primary el-icon-search",size:"mini"},on:{click:t.demand}})],1)],1)],1),t._v(" "),a("div",{staticClass:"kl-table",style:{height:t.maxHeight+"px"}},[a("el-table",{attrs:{stripe:"",center:"",border:"","max-height":t.maxHeight,data:t.tableData.list}},[a("el-table-column",{attrs:{fixed:"left",type:"index",width:"80",label:"NO.",index:t.indexMethod}}),t._v(" "),a("el-table-column",{attrs:{prop:"belongAreaName","min-width":"150",label:"所属片区","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"waterCompanyName","min-width":"120",label:"水司名称","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"projectName","min-width":"180",label:"项目名称","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"projectTypeName","min-width":"180",label:"项目类型","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"businessTypeName","min-width":"120",label:"业务类型","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"needSignContractFlagName","min-width":"120",label:"是否需要签订合同","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"projectStatusName","min-width":"120",label:"项目状态","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"contractDate","min-width":"120",label:"签约日期","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"usPayee","min-width":"150",label:"我方收款主体","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"otherPayee","min-width":"150",label:"对方付款主体","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"taxIncludeAmount","min-width":"150",label:"项目含税总金额(万元)",align:"right"}}),t._v(" "),a("el-table-column",{attrs:{prop:"serviceTerm","min-width":"120",label:"合同服务年限(年)","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"investTypeName","min-width":"120",label:"投资类型","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"usInvestAmount","min-width":"150",label:"我方投资额(万元)",align:"right"}}),t._v(" "),a("el-table-column",{attrs:{prop:"otherInvestAmount","min-width":"150",label:"对方投资额(万元)",align:"right"}}),t._v(" "),a("el-table-column",{attrs:{prop:"irr","min-width":"130",label:"IRR(%)",align:"right"}}),t._v(" "),a("el-table-column",{attrs:{prop:"grossProfitRate","min-width":"120",label:"毛利率(%)",align:"right"}}),t._v(" "),a("el-table-column",{attrs:{prop:"salePerson","min-width":"120",label:"销售人员"}}),t._v(" "),a("el-table-column",{attrs:{prop:"createName","min-width":"120",label:"填报人"}}),t._v(" "),a("el-table-column",{attrs:{prop:"createTime","min-width":"120",label:"填报时间"}}),t._v(" "),a("el-table-column",{attrs:{prop:"remark","min-width":"150",label:"备注","show-overflow-tooltip":""}})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":t.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":t.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)])])},staticRenderFns:[]};var c=a("VU/8")(r,s,!1,function(t){a("ZLdU")},null,null);e.default=c.exports},ZLdU:function(t,e){}});