package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.PenaltyBean;
import com.koron.zys.baseConfig.bean.PenaltyStrategyBean;
import com.koron.zys.baseConfig.mapper.PenaltyStrategyMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

/**
 * 违约金策略-编辑
 * 
 * <AUTHOR>
 *
 */
public class PenaltyStrategyUpdate implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(PenaltyStrategyUpdate.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			PenaltyStrategyMapper mapper = factory.getMapper(PenaltyStrategyMapper.class);
			PenaltyBean bean = JsonUtils.objectToPojo(req.getData(), PenaltyBean.class);
			if (StringUtils.isNullOrEmpty(bean.getId())) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
			}
			bean.setUpdateName(userInfo.getUserInfo().getName());
			bean.setUpdateTime(CommonUtils.getCurrentTime());
			mapper.updatePenalty(bean);
			for (PenaltyStrategyBean penaltyStrategyBean : bean.getList()) {
				penaltyStrategyBean.setUpdateName(userInfo.getUserInfo().getName());
				penaltyStrategyBean.setUpdateTime(CommonUtils.getCurrentTime());
				mapper.updatePenaltyStrategy(penaltyStrategyBean);
			}
		} catch (Exception e) {
			logger.error("非法参数", e);
			factory.close(false);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
}