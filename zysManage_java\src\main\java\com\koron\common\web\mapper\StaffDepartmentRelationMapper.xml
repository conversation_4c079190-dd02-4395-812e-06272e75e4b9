<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.common.web.mapper.StaffDepartmentRelationMapper">

	<!--批量新增用户信息-->
	<insert id="batchInsertStaffDepartment" parameterType="com.koron.common.web.bean.StaffDepartmentRelationBean">
		INSERT IGNORE INTO tblstaff_department	(
		user_code,
		department_code
		) VALUES
		<foreach collection="list" item="staff" separator=",">
			(
			#{staff.userCode,jdbcType=VARCHAR},
			#{staff.departmentCode,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--批量新增用户信息-->
	<insert id="insertStaffDepartment" parameterType="com.koron.common.web.bean.StaffDepartmentRelationBean">
		INSERT IGNORE INTO tblstaff_department	(
		user_code,
		department_code
		) VALUES

			(
			#{userCode,jdbcType=VARCHAR},
			#{departmentCode,jdbcType=VARCHAR}
			)

	</insert>
</mapper>