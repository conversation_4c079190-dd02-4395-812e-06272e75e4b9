package com.koron.common.bean;

import java.util.List;


public class PubDepartmentBean {
    /**
    * id
    */
    private String id;

    /**
    * 名称
    */
    private String name;

    /**
    * 简称
    */
    private String shortName;

    /**
    * 父节点id
    */
    private String parentId;

    /**
    * 序号
    */
    private Long seq;

    /**
    * 水司编号
    */
    private String groupCode;

    /**
    * 外键
    */
    private String code;

    List<PubDepartmentBean> children;

    public List<PubDepartmentBean> getChildren() {
        return children;
    }

    public void setChildren(List<PubDepartmentBean> children) {
        this.children = children;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Long getSeq() {
        return seq;
    }

    public void setSeq(Long seq) {
        this.seq = seq;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}