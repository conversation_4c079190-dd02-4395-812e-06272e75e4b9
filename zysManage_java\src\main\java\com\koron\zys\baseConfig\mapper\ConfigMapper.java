package com.koron.zys.baseConfig.mapper;

import com.koron.zys.baseConfig.bean.ConfigBean;
import com.koron.zys.baseConfig.queryBean.ConfigQueryBean;
import com.koron.zys.baseConfig.vo.ConfigVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ConfigMapper {
	
	/**
	 * 根据名称获取
	 * 
	 * @return
	 */
	List<ConfigVO> selectConfigByNames(@Param("names") String... names);
	
	/**
	 * 查询列表
	 * 
	 * @return
	 */
	List<ConfigVO> selectConfigList(ConfigQueryBean configQueryBean);
	
	/**
	 * 根据id查询
	 * @param configId
	 * @return
	 */
	ConfigBean selectConfigById(@Param("configId") String configId);
	/**
	 * 根据name查询
	 * @param configId
	 * @return
	 */
	String  getConfigValueByName(@Param("name") String name);
	String  getConfigValueByNameOn(@Param("name") String name);
	/**
	 * 添加
	 * 
	 * @param ConfigBean
	 * @return
	 */
	void insertConfig(ConfigBean configBean);
	/**
	 * 校验字段内容重复
	 */
	@Select("select count(*) from BASE_CONFIG where ${key} = #{val}")
	Integer check(@Param("key") String key, @Param("val") String val);
	
	/**
	 * 校验字段内容重复-排除当前记录
	 */
	@Select("select count(*) from BASE_CONFIG where ${key} = #{val} and config_id <> #{id}")
	Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);
	
	/**
	 * 修改
	 * 
	 * @param configBean
	 * @return
	 */
	Integer updateConfig(ConfigBean configBean);

	Integer	updateConfigValueByName(@Param("configName")String configName,@Param("configValue") String configValue);

}
