package com.koron.zys.baseConfig.bean;

/**
 * 单据类型
 * <AUTHOR>
 *
 */
public class PubReceiptBean extends BaseBean{
	
	private String receiptId;
	
	private String receiptNo;
	
	private String receiptName;
	
	private String workflowInterface;
	
	private String url;
	
	private String processCode;
	
	private String setCode;
	
	private String startAccess;
	
	public String getSetCode() {
		return setCode;
	}

	public void setSetCode(String setCode) {
		this.setCode = setCode;
	}

	public String getProcessCode() {
		return processCode;
	}

	public void setProcessCode(String processCode) {
		this.processCode = processCode;
	}

	public String getWorkflowInterface() {
		return workflowInterface;
	}

	public String getUrl() {
		return url;
	}

	public void setWorkflowInterface(String workflowInterface) {
		this.workflowInterface = workflowInterface;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getReceiptNo() {
		return receiptNo;
	}

	public String getReceiptName() {
		return receiptName;
	}

	public String getReceiptId() {
		return receiptId;
	}

	public void setReceiptId(String receiptId) {
		this.receiptId = receiptId;
	}

	public void setReceiptNo(String receiptNo) {
		this.receiptNo = receiptNo;
	}

	public void setReceiptName(String receiptName) {
		this.receiptName = receiptName;
	}

	public String getStartAccess() {
		return startAccess;
	}

	public void setStartAccess(String startAccess) {
		this.startAccess = startAccess;
	}

}
