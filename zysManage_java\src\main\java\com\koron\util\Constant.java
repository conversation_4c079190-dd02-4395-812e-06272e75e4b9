package com.koron.util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.koron.ebs.util.field.EnumElement;

import com.koron.common.bean.DefineFieldBean;
import com.koron.common.bean.StaffBean;
import com.koron.common.web.bean.DepartmentBean;

public class Constant {
	/**
	 * 默认数据源的schema和password
	 */
	public static final String SYSTEM = "system";
	// redis缓存中，数据的存放key ------------- start
	// 前面三个，存放的都是(“token”，“”，“json”)这样的格式,获取中间的某一个resource.hget(token, "")；
	/**
	 * 用户信息
	 */
	public static final String USER_INFO = "userInfo";
	
	public static final String ADMIN_NMAE = "管理员";
	
	public static final String SUPER_ADMIN_NMAE = "超级管理员";
	
	public static final String MASTER = "_M";
	
	public static final String SLAVE = "_S";
	/**
	 * 用户当前登录水司信息
	 */
	public static final String SELECT_WATERCODE = "selectWaterCode";
	/**
	 * 用户有权限的水司信息
	 */
	public static final String WATER_CODE = "waterCode";
	/**
	 * 服务管理 ("serverManage","servicecode","json")
	 */
	public static final String SERVER_MANAGE = "serverManage";
	/**
	 * 数据库服务器（“dbServer”，“serverCode”，“json”)
	 */
	public static final String DB_SERVER = "dbServer";
	/**
	 * 子系统 ("childSystem","systemCode","json")
	 */
	public static final String CHILD_SYSTEM = "childSystem";

	/**
	 * 子模块 ("subModule","subModuleCode","json")
	 */
	public static final String SUB_MODULE = "subModule";
	// redis缓存中，数据的存放key ------------ end

	/**
	 * 登录用户在SESSION里的储存KEY
	 */
	public static final String USER = "_user";
	/**
	 * 登陆微信号在SESSION里的储存KEY
	 */
	public static final String OPENID = "_openid";
	/**
	 * 验证码在Session里的存储KEY
	 */
	public static final String VALIDATECODE = "_validate_code";
	// ////////////////////////接口错误说明/////////////
	/**
	 * 操作成功
	 */
	public static final int MESSAGE_INT_SUCCESS = 0;
	/**
	 * 操作失败
	 */
	public static final int MESSAGE_INT_FAIL = 1;
	/**
	 * 未登录
	 */
	public static final int MESSAGE_INT_NOLOGIN = 10000;
	/**
	 * 无模块、操作的权限
	 */
	public static final int MESSAGE_INT_NOMODULE = 10001;
	/**
	 * 密码错误
	 */
	public static final int MESSAGE_INT_PWDERROR = 11002;
	/**
	 * 职员信息不存在
	 */
	public static final int MESSAGE_INT_STAFF_NO_EXIST = 11003;
	/**
	 * 账号已停用
	 */
	public static final int MESSAGE_INT_STAFF_DISABLE = 11004;
	/**
	 * 数据信息为空
	 */
	public static final int NOT_NULL = 11005;
	/**
	 * 无欠费
	 */
	public static final int NOT_ARREARAGE = 11006;

	/**
	 * 欠费未缴
	 */
	public static final int ARREARS_NOT_PAID = 11007;

	/**
	 * 未写卡
	 */
	public static final int NOT_WRITE_CARD = 11008;

	/**
	 * 不允许购水
	 */
	public static final int NOT_ALLOW_BUY_WATER = 11009;

	///////////////// 调用中台接口错误码//////////////////////////

	/**
	 * 请求不能为空
	 */
	public static final int REQUEST_IS_NULL = 10010;

	/**
	 * 加密认证不能为空
	 */
	public static final int REQUEST_SECRET_NULL = 10011;

	/**
	 * 指令超时，请校准服务器时间
	 */
	public static final int REQUEST_OUTTIME = 10012;

	/**
	 * 指令加密信息不一致，请确认加密方式是否合法
	 */
	public static final int SECRET_IS_DIFFERENCE = 10013;

	/**
	 * 请求信息解密錯誤
	 */
	public static final int REQUEST_DECODE_ERROR = 10014;

	///////////////// 调用中台接口错误码//////////////////////////

	/**
	 * 分配100个错误码给文件转换
	 */
	public static final int MESSAGE_INT_FILE_TRANSFER_ERROR = 20001;
	/**
	 * 分配100个错误吗给数据库
	 */
	public static final int MESSAGE_DBFAIL = 20101;

	/**
	 * 自定义表单增加辅助单位功能(在前台要自动加上辅助功能的脚本)
	 */
	public static final int HAS_ASSIST = 1000;

	/**
	 * 服务器异常
	 */
	public static final int SERVER_EXCEPTION = 11201;

	/**
	 * 无权限
	 */
	public static final int NO_AUTH = 11202;
	
	public static final String APP_CUSTOMER = "002";
	
	public static final String APP_DEVOPS = "001";
	
	public static final String APP_ANALYSIS = "003";

	// 默认的水司CODE 用于运维平台
	public static final String DEFAULT_GROUP_CODE = "C000000";

	/**
	 * 服务已被禁用
	 */
	public static final int SERVER_DISABLE = 112003;

	/**
	 * redis服务连接错误
	 */
	public static final int REDISF_SERVER_LINK_ERROR = 11203;

	/**
	 * redis数据格式异常
	 */
	public static final int REDISF_DATA_FORMAT_EXCEPTION = 11204;
	/**
	 * 非法参数
	 */
	public static final int ILLEGAL_PARAMETER = 11205;
	/**
	 * 无操作权限
	 */
	public static final int NO_AUTHORITY = 11206;
	/**
	 * 数据库没有当前服务相关的数据信息
	 */
	public static final int NO_DATA = 11207;
	/**
	 * IP地址不在允许返回内
	 */
	public static final int IP_NOT_ALLOW = 11208;
	/**
	 * 表示有权限
	 */
	public static final int HAVE_AUTHORITY = 11209;
	/**
	 * 没有子系统权限
	 */
	public static final int NO_SUB_SYSTEM_AUTHORITY = 11210;
	/**
	 * admin账号不允许删除
	 */
	public static final int ADMIN_NOT_DEL = 11211;

	// ////////////////////////接口错误说明/////////////

	/**
	 * 自定义层别缓存
	 */
	public static final HashMap<String, Integer> layerCache = new HashMap<>();
	/**
	 * 自定义层别下的所有字段
	 */
	public static final HashMap<Integer, List<DefineFieldBean>> layerFieldCache = new HashMap<>();
	/**
	 * 自定义字段缓存
	 */
	public final static HashMap<String, DefineFieldBean> fieldCache = new HashMap<>();
	/**
	 * 枚举缓存
	 */
	public final static HashMap<String, EnumElement<Object>> enumCache = new HashMap<>();

	public static final Map<String, StaffBean> STAFF = new HashMap<>();
	public static final Map<String, DepartmentBean> DEPARTMENT = new HashMap<>();
}
