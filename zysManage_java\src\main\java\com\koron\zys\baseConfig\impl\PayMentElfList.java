package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BasePayMentElfBean;
import com.koron.zys.baseConfig.mapper.PayMentElfMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;


/**
 * 缴费通知精灵-列表
 *
 * <AUTHOR>
 */
public class PayMentElfList implements ServerInterface {
    private static Logger logger = LoggerFactory.getLogger(PayMentElfList.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        @SuppressWarnings("rawtypes")
        MessageBean<BasePayMentElfBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", BasePayMentElfBean.class);
        try {
            PayMentElfMapper mapper = factory.getMapper(PayMentElfMapper.class);
            BasePayMentElfBean basePayMentElfBean = mapper.selectPayMentElf();
            info.setCode(Constant.MESSAGE_INT_SUCCESS);
            info.setDescription("success");
            info.setData(basePayMentElfBean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription("操作失败");
            logger.error("操作失败", e);
        }
        return info;
    }
}
