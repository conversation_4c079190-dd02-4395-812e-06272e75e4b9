package com.koron.zys.baseConfig.bean;

/**
 * 水表供应商
 *
 * <AUTHOR>
 */
public class MeterFactoryBean extends BaseBean {

    private String factoryName;

    private String factoryFullName;

    private String factoryAddr;

    private String linkMan;

    private String linkTel;

    private String comments;

    private Integer status;

    private Integer sortNo;

    public String getFactoryName() {
        return factoryName;
    }

    public void setFactoryName(String factoryName) {
        this.factoryName = factoryName;
    }

    public String getFactoryFullName() {
        return factoryFullName;
    }

    public void setFactoryFullName(String factoryFullName) {
        this.factoryFullName = factoryFullName;
    }

    public String getFactoryAddr() {
        return factoryAddr;
    }

    public void setFactoryAddr(String factoryAddr) {
        this.factoryAddr = factoryAddr;
    }

    public String getLinkMan() {
        return linkMan;
    }

    public void setLinkMan(String linkMan) {
        this.linkMan = linkMan;
    }

    public String getLinkTel() {
        return linkTel;
    }

    public void setLinkTel(String linkTel) {
        this.linkTel = linkTel;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

}
