package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.AlipayConfigMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import java.util.List;
import java.util.Map;

/**
 * 数据字典统一查询接口（支持多个一起查询，逗号分隔）
 * <AUTHOR>
 *
 */
public class AlipayCompanyNoData implements ServerInterface{
	
	private static Logger logger = LoggerFactory.getLogger(AlipayCompanyNoData.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			AlipayConfigMapper mapper = factory.getMapper(AlipayConfigMapper.class);
			List<Map<String,String>> result = mapper.selectCompanyNo();
			info.setData(result);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
	
}

