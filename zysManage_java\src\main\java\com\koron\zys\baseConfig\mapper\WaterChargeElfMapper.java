package com.koron.zys.baseConfig.mapper;

import com.koron.zys.baseConfig.bean.WaterChargeElfBean;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;


public interface WaterChargeElfMapper {
    @Delete("delete from base_water_charge_elf")
    Integer deleteAllWaterChargeElf();

    Integer insertWaterChargeElf(WaterChargeElfBean waterChargeElfBean);

    @Select("select * from base_water_charge_elf")
    WaterChargeElfBean selectWaterChargeElf();
}
