package com.koron.common.bean.query;

public class StaffQueryBean extends BaseQueryBean{
	
	private Integer id;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 职位
	 */
	private String position;
	/**
	 * 职员编号
	 */
	private String code;
	/**
	 * 状态 0停用 1启用
	 * 
	 * @see #STATUS_ENABLE
	 * @see #STATUS_DISABLE
	 */
	private Integer status;
	/**
	 * 电话号码
	 */
	private String phone;

	/**
	 * 手机号码
	 */
	private String mobile;
	/**
	 * 邮箱
	 */
	private String email;
	/**
	 * 部门
	 */
	private String departmentName;
	private Long seq;
	/**
	 * 级别
	 */
	private Integer level;
	/**
	 * 登录人ID
	 */
	private String loginid;
	
	/**
	 * 登录名
	 */
	private String loginname;
	/**
	 * 性别
	 */
	private Integer sex;
	/**
	 * 证件号码
	 */
	private String idcard;
	/**
	 * 职员权重
	 */
	private Integer weight;
	
	//图标路径
	private String photo;

	//openid（云之家）
	private String openid;

	//用户ID（云之家）
	private String userid;

	//最后更新时间
	private String lastupdate;

	//部门编号
	private String departmentCode;

	public Integer getId() {
		return id;
	}

	public String getName() {
		return name;
	}

	public String getPosition() {
		return position;
	}

	public String getCode() {
		return code;
	}

	public Integer getStatus() {
		return status;
	}

	public String getPhone() {
		return phone;
	}

	public String getMobile() {
		return mobile;
	}

	public String getEmail() {
		return email;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public Long getSeq() {
		return seq;
	}

	public Integer getLevel() {
		return level;
	}

	public String getLoginid() {
		return loginid;
	}

	public String getLoginname() {
		return loginname;
	}

	public Integer getSex() {
		return sex;
	}

	public String getIdcard() {
		return idcard;
	}

	public Integer getWeight() {
		return weight;
	}

	public String getPhoto() {
		return photo;
	}

	public String getOpenid() {
		return openid;
	}

	public String getUserid() {
		return userid;
	}

	public String getLastupdate() {
		return lastupdate;
	}

	public String getDepartmentCode() {
		return departmentCode;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	public void setSeq(Long seq) {
		this.seq = seq;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public void setLoginid(String loginid) {
		this.loginid = loginid;
	}

	public void setLoginname(String loginname) {
		this.loginname = loginname;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public void setIdcard(String idcard) {
		this.idcard = idcard;
	}

	public void setWeight(Integer weight) {
		this.weight = weight;
	}

	public void setPhoto(String photo) {
		this.photo = photo;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public void setLastupdate(String lastupdate) {
		this.lastupdate = lastupdate;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}
	
}
