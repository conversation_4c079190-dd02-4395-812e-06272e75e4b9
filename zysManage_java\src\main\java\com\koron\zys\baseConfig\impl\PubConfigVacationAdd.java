package com.koron.zys.baseConfig.impl;

import com.koron.zys.baseConfig.queryBean.ConfigVacationQueryBean;
import com.koron.util.Tools;
import com.mysql.cj.util.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ConfigVacationBean;
import com.koron.zys.baseConfig.mapper.ConfigVacationMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

/**
 * 假期设置-添加
 *
 * <AUTHOR>
 */
public class PubConfigVacationAdd implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(PubConfigVacationAdd.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            ConfigVacationMapper mapper = factory.getMapper(ConfigVacationMapper.class, "_default");
            ConfigVacationBean bean = JsonUtils.objectToPojo(req.getData(), ConfigVacationBean.class);
            // 校验字段重复
            if (mapper.check("vacation_name", bean.getVacationName()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "假期名称：" + bean.getVacationName() + "的信息已存在。", void.class);
            }
            if (StringUtils.isNullOrEmpty(bean.getVacationBeginDate()) || StringUtils.isNullOrEmpty(bean.getVacationEndDate())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "假期开始日期和结束日期不能为空。", void.class);
            }
            ConfigVacationQueryBean configVacationQueryBean = new ConfigVacationQueryBean();
            configVacationQueryBean.setVacationBeginDate(bean.getVacationBeginDate());
            configVacationQueryBean.setVacationEndDate(bean.getVacationEndDate());
            Integer counts = mapper.checkConfigVacation(configVacationQueryBean);
            if (counts > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "假期日期已存在。", void.class);
            }
            if (StringUtils.isNullOrEmpty(bean.getId())) {
                bean.setId(Tools.getObjectId());
            }
            bean.setGroupCode(userInfo.getCurWaterCode());
            bean.setCreateInfo(userInfo);
            mapper.insertConfigVacation(bean);
        } catch (Exception e) {
            logger.error("假期设置添加失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "假期设置添加失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }


}