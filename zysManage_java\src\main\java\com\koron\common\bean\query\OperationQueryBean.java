package com.koron.common.bean.query;


/**
 * 搜索操作的条件
 * <AUTHOR>
 *
 */
public class OperationQueryBean extends BaseQueryBean {
	/**
	 * 角色
	 */
	private String roleCode;
	/**
	 * 关键字
	 */
	private String key;
	/**
	 * 名称
	 */
	private String opName;
	/**
	 * 类型
	 */
	private Integer type;
	
	/**
	 * 租户code
	*/
	private String tenantCode;
	
	/**
	 * 应用
	*/
	private String app;

	public String getRoleCode() {
		return roleCode;
	}

	public String getKey() {
		return key;
	}

	public String getOpName() {
		return opName;
	}

	public Integer getType() {
		return type;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public String getApp() {
		return app;
	}

	public void setRoleCode(String roleCode) {
		this.roleCode = roleCode;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public void setOpName(String opName) {
		this.opName = opName;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public void setApp(String app) {
		this.app = app;
	}
	
}
