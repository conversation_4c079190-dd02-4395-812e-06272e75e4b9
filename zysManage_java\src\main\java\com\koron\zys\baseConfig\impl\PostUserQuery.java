package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.PubSysUserBean;
import com.koron.zys.baseConfig.mapper.PubSysUserMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 岗位用户查询
 * <AUTHOR>
 *
 */
public class PostUserQuery implements ServerInterface{

	private static Logger logger = LoggerFactory.getLogger(PostUserQuery.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "查询成功", List.class);
		try { 
			String post = JsonUtils.objectToPojo(req.getData(), String.class);
			PubSysUserMapper mapper = factory.getMapper(PubSysUserMapper.class);
			List<PubSysUserBean> users = mapper.selectPubSysUserByPost(post);
			info.setData(users);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}
