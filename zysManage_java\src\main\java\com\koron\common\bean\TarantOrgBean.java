package com.koron.common.bean;

public class TarantOrgBean {
	
	/**
	 * id
	 */
	private Integer id;
	
	/**
	 * 租户编码
	 */
	private String tarantCode;
	
	/**
	 * 组织架构编码
	 */
	private String code;
	
	/**
	 * 组织架构名称
	 */
	private String name;
	
	/**
	 * 组织架构树形存储类型
	 */
	private Integer treeType;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getTarantCode() {
		return tarantCode;
	}

	public void setTarantCode(String tarantCode) {
		this.tarantCode = tarantCode;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getTreeType() {
		return treeType;
	}

	public void setTreeType(Integer treeType) {
		this.treeType = treeType;
	}

	@Override
	public String toString() {
		return "TarantOrgBean [id=" + id + ", tarantCode=" + tarantCode + ", code=" + code + ", name=" + name
				+ ", treeType=" + treeType + "]";
	}

}
