package com.koron.zys.baseConfig.impl;

import java.util.ArrayList;
import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BaseOtherElfBean;
import com.koron.zys.baseConfig.mapper.BaseOtherElfMapper;
import com.koron.zys.baseConfig.vo.BaseOtherElfVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
/**
 * 2020-06-11
 * <AUTHOR>
 *
 */
public class BaseOtherElfAdd implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub
		
		try {
			BaseOtherElfVO bean = JsonUtils.objectToPojo(req.getData(), BaseOtherElfVO.class);
			BaseOtherElfMapper mapper = factory.getMapper(BaseOtherElfMapper.class);

			bean.setNoticeStaff1(bean.getNoticeStaff1().replace("，", ","));
			bean.setNoticeStaff2(bean.getNoticeStaff2().replace("，", ","));
			bean.setNoticeStaff3(bean.getNoticeStaff3().replace("，", ","));
			bean.setNoticeStaff4(bean.getNoticeStaff4().replace("，", ","));
			
			String[] staff1=bean.getNoticeStaff1().split(","),
					staff2=bean.getNoticeStaff2().split(","),
					staff3=bean.getNoticeStaff3().split(","),
					staff4=bean.getNoticeStaff4().split(",");
			List<BaseOtherElfBean> list = new ArrayList<BaseOtherElfBean>();
			for (String str : staff1) {
				list.add(new BaseOtherElfBean("1",str,userInfo));
			}
			for (String str : staff2) {
				list.add(new BaseOtherElfBean("2",str,userInfo));
			}
			for (String str : staff3) {
				list.add(new BaseOtherElfBean("3",str,userInfo));
			}
			for (String str : staff4) {
				list.add(new BaseOtherElfBean("4",str,userInfo));
			}
			mapper.delBaseOtherElf();
			mapper.addBaseOtherElf(list);
		} catch (Exception e) {
			 factory.close(false);
	         logger.error("其它精灵保存失败", e);
	         return MessageBean.create(Constant.MESSAGE_INT_FAIL, "其它精灵保存失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
