webpackJsonp([10],{NWlX:function(a,e){},X4JQ:function(a,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l={render:function(){var a=this,e=a.$createElement,t=a._self._c||e;return t("div",{staticClass:"mgCompanyEdit"},[t("el-form",{ref:"mgCompanyEditForm",staticClass:"formBill-Two",attrs:{inline:!0,size:"mini",model:a.formData,"label-width":"150px"}},[t("el-form-item",{attrs:{label:"水司编号：",prop:"groupCode"}},[t("el-select",{attrs:{placeholder:"请选择水司编号"},on:{change:a.groupCodeChange},model:{value:a.formData.groupCode,callback:function(e){a.$set(a.formData,"groupCode",e)},expression:"formData.groupCode"}},a._l(a.CompanyNoData,function(a){return t("el-option",{key:a.companyNo,attrs:{label:a.companyNo,value:a.companyNo}})}),1)],1),a._v(" "),t("el-form-item",{attrs:{label:"水司简称：",prop:"groupName"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入水司简称"},model:{value:a.formData.groupName,callback:function(e){a.$set(a.formData,"groupName",e)},expression:"formData.groupName"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"水司全称：",prop:"groupFullName"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入水司全称"},model:{value:a.formData.groupFullName,callback:function(e){a.$set(a.formData,"groupFullName",e)},expression:"formData.groupFullName"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"所属省：",prop:"province"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入所属省"},model:{value:a.formData.province,callback:function(e){a.$set(a.formData,"province",e)},expression:"formData.province"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"所属市：",prop:"city"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入所属市"},model:{value:a.formData.city,callback:function(e){a.$set(a.formData,"city",e)},expression:"formData.city"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"所属地区：",prop:"area"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入所属地区"},model:{value:a.formData.area,callback:function(e){a.$set(a.formData,"area",e)},expression:"formData.area"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"营收版本：",prop:"version"}},[t("el-select",{attrs:{placeholder:"请选择营收版本"},model:{value:a.formData.version,callback:function(e){a.$set(a.formData,"version",e)},expression:"formData.version"}},[t("el-option",{attrs:{label:"老客服",value:"1"}}),a._v(" "),t("el-option",{attrs:{label:"客服2.0",value:"2"}}),a._v(" "),t("el-option",{attrs:{label:"客服2.1",value:"4"}}),a._v(" "),t("el-option",{attrs:{label:"其他系统",value:"3"}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"旧接口平台地址：",prop:"url"}},[t("el-input",{attrs:{maxlength:"100",clearable:"",placeholder:"请输入旧接口平台地址"},model:{value:a.formData.url,callback:function(e){a.$set(a.formData,"url",e)},expression:"formData.url"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"数据库url：",prop:"dbUrl"}},[t("el-input",{attrs:{maxlength:"100",clearable:"",placeholder:"请输入数据库url"},model:{value:a.formData.dbUrl,callback:function(e){a.$set(a.formData,"dbUrl",e)},expression:"formData.dbUrl"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"数据库用户名：",prop:"dbUserName"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入数据库用户名"},model:{value:a.formData.dbUserName,callback:function(e){a.$set(a.formData,"dbUserName",e)},expression:"formData.dbUserName"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"数据库密码：",prop:"dbPassword"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入数据库密码"},model:{value:a.formData.dbPassword,callback:function(e){a.$set(a.formData,"dbPassword",e)},expression:"formData.dbPassword"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"是否开通微信：",prop:"hasWxHall"}},[t("el-select",{attrs:{placeholder:"请选择是否开通微信"},model:{value:a.formData.hasWxHall,callback:function(e){a.$set(a.formData,"hasWxHall",e)},expression:"formData.hasWxHall"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"是否开通网厅：",prop:"hasWebHall"}},[t("el-select",{attrs:{placeholder:"请选择是否开通网厅"},model:{value:a.formData.hasWebHall,callback:function(e){a.$set(a.formData,"hasWebHall",e)},expression:"formData.hasWebHall"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"是否开通智慧营业厅：",prop:"hasWisdomHall"}},[t("el-select",{attrs:{placeholder:"请选择是否开通智慧营业厅"},model:{value:a.formData.hasWisdomHall,callback:function(e){a.$set(a.formData,"hasWisdomHall",e)},expression:"formData.hasWisdomHall"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"老客服水司编号：",prop:"oldGroupCode"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入老客服水司编号"},model:{value:a.formData.oldGroupCode,callback:function(e){a.$set(a.formData,"oldGroupCode",e)},expression:"formData.oldGroupCode"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"水价图片url：",prop:"waterPricePic"}},[t("el-input",{attrs:{maxlength:"200",clearable:"",placeholder:"请输入水价图片url"},model:{value:a.formData.waterPricePic,callback:function(e){a.$set(a.formData,"waterPricePic",e)},expression:"formData.waterPricePic"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"发票信息url：",prop:"invoiceUrl"}},[t("el-input",{attrs:{maxlength:"100",clearable:"",placeholder:"请输入发票信息url"},model:{value:a.formData.invoiceUrl,callback:function(e){a.$set(a.formData,"invoiceUrl",e)},expression:"formData.invoiceUrl"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"水司水印图片url：",prop:"markPic"}},[t("el-input",{attrs:{maxlength:"255",clearable:"",placeholder:"请输入水司水印图片url"},model:{value:a.formData.markPic,callback:function(e){a.$set(a.formData,"markPic",e)},expression:"formData.markPic"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"账单excel模板url：",prop:"billExcel"}},[t("el-input",{attrs:{maxlength:"255",clearable:"",placeholder:"请输入账单excel模板url"},model:{value:a.formData.billExcel,callback:function(e){a.$set(a.formData,"billExcel",e)},expression:"formData.billExcel"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"坐标经度：",prop:"longitude"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入坐标经度"},model:{value:a.formData.longitude,callback:function(e){a.$set(a.formData,"longitude",e)},expression:"formData.longitude"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"坐标纬度：",prop:"latitude"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入坐标纬度"},model:{value:a.formData.latitude,callback:function(e){a.$set(a.formData,"latitude",e)},expression:"formData.latitude"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"微信公众号appId：",prop:"appId"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入微信公众号appId"},model:{value:a.formData.appId,callback:function(e){a.$set(a.formData,"appId",e)},expression:"formData.appId"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"微信公众号appSecret：",prop:"appSecret"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入微信公众号appSecret"},model:{value:a.formData.appSecret,callback:function(e){a.$set(a.formData,"appSecret",e)},expression:"formData.appSecret"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"微信公众号aesKey：",prop:"aesKey"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入微信公众号aesKey"},model:{value:a.formData.aesKey,callback:function(e){a.$set(a.formData,"aesKey",e)},expression:"formData.aesKey"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"微信公众号token：",prop:"token"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入微信公众号token"},model:{value:a.formData.token,callback:function(e){a.$set(a.formData,"token",e)},expression:"formData.token"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"微信商户号mchId：",prop:"mchId"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入微信商户号mchId"},model:{value:a.formData.mchId,callback:function(e){a.$set(a.formData,"mchId",e)},expression:"formData.mchId"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"微信商户号mchKey：",prop:"mchKey"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入微信商户号mchKey"},model:{value:a.formData.mchKey,callback:function(e){a.$set(a.formData,"mchKey",e)},expression:"formData.mchKey"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"微信商户号certPath：",prop:"certPath"}},[t("el-input",{attrs:{maxlength:"255",clearable:"",placeholder:"请输入微信商户号certPath"},model:{value:a.formData.certPath,callback:function(e){a.$set(a.formData,"certPath",e)},expression:"formData.certPath"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"固定电话区号：",prop:"telAreaCode"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入固定电话区号"},model:{value:a.formData.telAreaCode,callback:function(e){a.$set(a.formData,"telAreaCode",e)},expression:"formData.telAreaCode"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"是否缴当前月账单：",prop:"isCurrentBill"}},[t("el-select",{attrs:{placeholder:"请选择是否缴当前月账单"},model:{value:a.formData.isCurrentBill,callback:function(e){a.$set(a.formData,"isCurrentBill",e)},expression:"formData.isCurrentBill"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"缴费凭证excel模板url：",prop:"payVoucherExcel"}},[t("el-input",{attrs:{maxlength:"255",clearable:"",placeholder:"请输入缴费凭证excel模板url"},model:{value:a.formData.payVoucherExcel,callback:function(e){a.$set(a.formData,"payVoucherExcel",e)},expression:"formData.payVoucherExcel"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"是否开通预存：",prop:"isPrestore"}},[t("el-select",{attrs:{placeholder:"请选择是否开通预存"},model:{value:a.formData.isPrestore,callback:function(e){a.$set(a.formData,"isPrestore",e)},expression:"formData.isPrestore"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"是否维护：",prop:"isMaintain"}},[t("el-select",{attrs:{placeholder:"请选择是否维护"},model:{value:a.formData.isMaintain,callback:function(e){a.$set(a.formData,"isMaintain",e)},expression:"formData.isMaintain"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"维护时间：",prop:"maintainTime"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入维护时间"},model:{value:a.formData.maintainTime,callback:function(e){a.$set(a.formData,"maintainTime",e)},expression:"formData.maintainTime"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"是否启用日结：",prop:"billDailyStatus"}},[t("el-select",{attrs:{placeholder:"请选择是否启用日结"},model:{value:a.formData.billDailyStatus,callback:function(e){a.$set(a.formData,"billDailyStatus",e)},expression:"formData.billDailyStatus"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"日结时间：",prop:"billDailyTime"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入日结时间，格式：17:00:00/18:30:00"},model:{value:a.formData.billDailyTime,callback:function(e){a.$set(a.formData,"billDailyTime",e)},expression:"formData.billDailyTime"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"是否启用月结：",prop:"billMonthlyStatus"}},[t("el-select",{attrs:{placeholder:"请选择是否启用月结"},model:{value:a.formData.billMonthlyStatus,callback:function(e){a.$set(a.formData,"billMonthlyStatus",e)},expression:"formData.billMonthlyStatus"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"月结时间：",prop:"billMonthlyTime"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入月结时间，格式：2020-12-31 22:00:00/2021-01-01 02:00:00"},model:{value:a.formData.billMonthlyTime,callback:function(e){a.$set(a.formData,"billMonthlyTime",e)},expression:"formData.billMonthlyTime"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"所有付款方式都支持预存：",prop:"isAllPayWayPrestore"}},[t("el-select",{attrs:{placeholder:"请选择是否所有付款方式都支持预存"},model:{value:a.formData.isAllPayWayPrestore,callback:function(e){a.$set(a.formData,"isAllPayWayPrestore",e)},expression:"formData.isAllPayWayPrestore"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"增值税用户能否缴费：",prop:"isAddedTaxPay"}},[t("el-select",{attrs:{placeholder:"请选择月增值税用户能否缴费"},model:{value:a.formData.isAddedTaxPay,callback:function(e){a.$set(a.formData,"isAddedTaxPay",e)},expression:"formData.isAddedTaxPay"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"是否允许托收方式缴费：",prop:"isTuoPay"}},[t("el-select",{attrs:{placeholder:"请选择是否允许托收方式缴费"},model:{value:a.formData.isTuoPay,callback:function(e){a.$set(a.formData,"isTuoPay",e)},expression:"formData.isTuoPay"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"用户绑定时是否校验用户名：",prop:"isCheckName"}},[t("el-select",{attrs:{placeholder:"请选择用户绑定时是否校验用户名"},model:{value:a.formData.isCheckName,callback:function(e){a.$set(a.formData,"isCheckName",e)},expression:"formData.isCheckName"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"增值税用户能否开票：",prop:"isAddedTaxInvoice"}},[t("el-select",{attrs:{placeholder:"请选择增值税用户能否开票"},model:{value:a.formData.isAddedTaxInvoice,callback:function(e){a.$set(a.formData,"isAddedTaxInvoice",e)},expression:"formData.isAddedTaxInvoice"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"是否使用接口访问水司：",prop:"interfaceAccess"}},[t("el-select",{attrs:{placeholder:"请选择是否使用接口访问水司"},model:{value:a.formData.interfaceAccess,callback:function(e){a.$set(a.formData,"interfaceAccess",e)},expression:"formData.interfaceAccess"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"银行水司编号：",prop:"bankGroupCode"}},[t("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入银行水司编号"},model:{value:a.formData.bankGroupCode,callback:function(e){a.$set(a.formData,"bankGroupCode",e)},expression:"formData.bankGroupCode"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"是否独立运营：",prop:"isSelfOpera"}},[t("el-select",{attrs:{placeholder:"请选择是否独立运营"},model:{value:a.formData.isSelfOpera,callback:function(e){a.$set(a.formData,"isSelfOpera",e)},expression:"formData.isSelfOpera"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"公司类型：",prop:"companyType"}},[t("el-select",{attrs:{placeholder:"请选择公司类型"},model:{value:a.formData.companyType,callback:function(e){a.$set(a.formData,"companyType",e)},expression:"formData.companyType"}},[t("el-option",{attrs:{label:"子公司",value:2}}),a._v(" "),t("el-option",{attrs:{label:"主公司",value:1}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"账单分组类型：",prop:"billGroupType"}},[t("el-select",{attrs:{placeholder:"请选择账单分组类型"},model:{value:a.formData.billGroupType,callback:function(e){a.$set(a.formData,"billGroupType",e)},expression:"formData.billGroupType"}},[t("el-option",{attrs:{label:"按账期分组",value:1}}),a._v(" "),t("el-option",{attrs:{label:"按抄表id，红冲标志，价格id分组",value:2}})],1)],1),a._v(" "),t("el-form-item",{attrs:{label:"可选择月份缴费：",prop:"monthsltFlag"}},[t("el-select",{attrs:{placeholder:"请选择可选择月份缴费"},model:{value:a.formData.monthsltFlag,callback:function(e){a.$set(a.formData,"monthsltFlag",e)},expression:"formData.monthsltFlag"}},[t("el-option",{attrs:{label:"否",value:0}}),a._v(" "),t("el-option",{attrs:{label:"是",value:1}})],1)],1)],1)],1)},staticRenderFns:[]};var o={components:{mgCompanyEdit:t("VU/8")({name:"mgCompanyEdit",data:function(){return{formData:{id:"",groupCode:"",groupName:"",groupFullName:"",province:"",city:"",area:"",version:"",url:"",dbUrl:"",dbUserName:"",dbPassword:"",hasWxHall:"",hasWebHall:"",hasWisdomHall:"",oldGroupCode:"",waterPricePic:"",invoiceUrl:"",markPic:"",billExcel:"",longitude:"",latitude:"",appId:"",appSecret:"",aesKey:"",token:"",mchId:"",mchKey:"",certPath:"",telAreaCode:"",isCurrentBill:"",payVoucherExcel:"",isPrestore:"",billDailyStatus:"",billDailyTime:"",billMonthlyStatus:"",billMonthlyTime:"",isAddedTaxPay:"",isAllPayWayPrestore:"",isCheckName:"",isMaintain:"",maintainTime:"",isAddedTaxInvoice:"",interfaceAccess:"",bankGroupCode:"",isSelfOpera:"",companyType:"",billGroupType:"",isTuoPay:"",monthsltFlag:""},CompanyNoData:[]}},mounted:function(){this.getData()},methods:{groupCodeChange:function(a){var e=this;this.CompanyNoData.forEach(function(t){t.companyNo==a&&(e.formData.groupName=t.shortName)})},getData:function(){var a=this;this.$api.fetch({params:{busicode:"CompanyNameList"}}).then(function(e){a.$set(a,"CompanyNoData",e)})},resetForm:function(){this.$refs.mgCompanyEditForm.resetFields()},submitForm:function(){var a=this,e={busicode:"MgCompanyUpdate",data:this.formData};this.$api.fetch({params:e}).then(function(e){a.$message({showClose:!0,message:"保存成功",type:"success"})})},editData:function(a){this.formData=a}}},l,!1,function(a){t("NWlX")},"data-v-cb4a3810",null).exports,autoTree:t("yJVD").a},name:"mgCompany",data:function(){return{EditVisible:!1,formData:{id:"",companyNo:"",orgNo:"",orgName:"",orgEnShortName:"",freePay:"",sftpIp:"",sftpPort:"",sftpUserName:"",lastDate:"",ruleType:"",ruleValue:""},treeDatas:{tree:[{shortName:"根目录",id:"2",companyNo:"",children:[]}],defaultProps:{label:"shortName",children:"children"},inputProp:{showSearch:!1,Inp_placeholder:"请输入节点"},sendTreeProp:["code","shortName","districtArr","children","companyNo","group","isLeaf","isParent","parent","sonData"],rootName:"根目录",defaultOpen:{nodeKey:"id"}},crumbsData:{titleList:[{title:"租户管理",path:"/tenant"},{title:"中台租户信息",method:function(){window.histroy.back()}}]},maxHeight:0,tableShow:!1,tableData:[],tableQuery:{groupCode:""},CompanyNoData:[]}},mounted:function(){this.getTreeDatas(),this.getData()},methods:{query:function(){var a=this,e={busicode:"MgCompanyQuery",data:{groupCode:this.tableQuery.groupCode}};this.$api.fetch({params:e}).then(function(e){a.$refs.mgCompanyEdit.editData(e)})},getTreeDatas:function(){var a=this,e=this;this.$api.fetch({params:{busicode:"CompanyNameList",data:{}}}).then(function(t){e.treeDatas.tree[0].children=t,e.tableQuery.groupCode=t[0].companyNo,a.$set(a.crumbsData.titleList,"2",{title:t[0].shortName,method:function(){window.histroy.back()}})})},backTreeData:function(a){if(this.$refs.mgCompanyEdit.resetForm(),"根目录"!==a.shortName){this.tableQuery.groupCode=a.companyNo,this.$set(this.crumbsData.titleList,"2",{title:a.shortName,method:function(){window.histroy.back()}}),this.query()}},submitForm:function(){this.$refs.mgCompanyEdit.submitForm()}}},r={render:function(){var a=this,e=a.$createElement,t=a._self._c||e;return t("div",{staticClass:"mgCompany"},[t("div",{staticClass:"bread-contain"},[t("publicCrumbs",{attrs:{crumbsData:a.crumbsData}}),a._v(" "),t("div",{staticClass:"bread-contain-right"},[t("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return a.submitForm()}}},[a._v("保存")])],1)],1),a._v(" "),t("div",{staticClass:"company-content"},[t("div",{staticClass:"company-left"},[t("auto-tree",{attrs:{treeData:a.treeDatas},on:{sendTreeData:a.backTreeData}})],1),a._v(" "),t("div",{staticClass:"kl-table company-right"},[t("mgCompanyEdit",{ref:"mgCompanyEdit"})],1)])])},staticRenderFns:[]};var s=t("VU/8")(o,r,!1,function(a){t("iy7T")},null,null);e.default=s.exports},iy7T:function(a,e){}});