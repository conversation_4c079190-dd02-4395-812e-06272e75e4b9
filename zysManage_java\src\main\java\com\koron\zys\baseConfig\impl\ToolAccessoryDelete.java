package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.ToolAccessoryBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.ToolAccessoryMapper;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import java.util.Optional;

/**
 * @author: lrk
 * @date: 2022-04-22 16:19
 * @description:
 */
public class ToolAccessoryDelete implements ServerInterface {
    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            ToolAccessoryBean bean = JsonUtils.objectToPojo(req.getData(), ToolAccessoryBean.class);
            Optional.ofNullable(bean).filter(x -> x.getId() != null).orElseThrow(() -> new Exception("删除附件id参数不能为空"));
            ToolAccessoryMapper mapper = factory.getMapper(ToolAccessoryMapper.class,"_default");
            mapper.delToolAccessoryById(bean.getId());
            return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "删除成功", null);
        } catch (Exception e) {
            logger.error("工具页附件新增失败", e.getMessage(), e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "删除失败", null);
        }
    }
}
