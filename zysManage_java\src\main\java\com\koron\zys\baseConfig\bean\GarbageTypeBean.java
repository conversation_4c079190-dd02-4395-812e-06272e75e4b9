package com.koron.zys.baseConfig.bean;

public class GarbageTypeBean extends BaseBean{
	private String garbageTypeName;
	private String garbageTypeValue;
	private String status;
	private String statusName;
	private String comments;
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public String getGarbageTypeName() {
		return garbageTypeName;
	}
	public void setGarbageTypeName(String garbageTypeName) {
		this.garbageTypeName = garbageTypeName;
	}
	public String getGarbageTypeValue() {
		return garbageTypeValue;
	}
	public void setGarbageTypeValue(String garbageTypeValue) {
		this.garbageTypeValue = garbageTypeValue;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getStatusName() {
		return statusName;
	}
	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}
	
	
}
