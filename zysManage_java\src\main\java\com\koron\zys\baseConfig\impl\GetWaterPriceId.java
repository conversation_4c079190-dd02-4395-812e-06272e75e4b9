package com.koron.zys.baseConfig.impl;

import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.WaterPriceMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 根据用水类型获取用水价格Id
 * <AUTHOR>
 *
 */
public class GetWaterPriceId implements ServerInterface {
	
private static Logger logger = LoggerFactory.getLogger(GetWaterPriceId.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<HashMap> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", HashMap.class);

		try { 
			@SuppressWarnings("unchecked")
			Map<String,String> useWaterType = JsonUtils.objectToPojo(req.getData(), Map.class);
			WaterPriceMapper mapper = factory.getMapper(WaterPriceMapper.class);
			if(useWaterType==null||StringUtils.isEmpty(useWaterType.get("id"))) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id不能为空", void.class);
			}
			//查询用水价格信息
			String  id = mapper.getWaterPriceIdByType(useWaterType.get("id"));
			HashMap<String,String> map=new HashMap<String,String>();
			map.put("id", id);
			info.setData( map);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}
