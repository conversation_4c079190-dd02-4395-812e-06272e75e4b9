package com.koron.zys.baseConfig.impl;

import java.util.List;

import com.koron.zys.baseConfig.bean.MatrCodeBean;
import com.koron.zys.baseConfig.bean.UseMatrTemplateBean;
import com.koron.zys.baseConfig.bean.UseMatrTemplateListBean;
import com.koron.zys.baseConfig.mapper.UseMatrTemplateMapper;
import com.koron.zys.baseConfig.queryBean.UseMatrTemplateQueryBean;
import com.koron.zys.serviceManage.mapper.MatrCodeMapper;
import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 用料模板-编辑初始化
 *
 * <AUTHOR>
 */
public class UseMatrTemplateQuery implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(UseMatrTemplateQuery.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        @SuppressWarnings("rawtypes")
        MessageBean<UseMatrTemplateBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", UseMatrTemplateBean.class);
        try {
            UseMatrTemplateQueryBean bean = JsonUtils.objectToPojo(req.getData(), UseMatrTemplateQueryBean.class);
            MatrCodeMapper matrMapper = factory.getMapper(MatrCodeMapper.class, "_default");
            UseMatrTemplateMapper mapper = factory.getMapper(UseMatrTemplateMapper.class);
            if (StringUtils.isEmpty(bean.getTemplateId())){
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "模板id不能为空", void.class);
            }
            // 查询用料模板信息
            UseMatrTemplateBean useMatrTemplateBean = mapper.getMatrTemplate(bean.getTemplateId());
            // 查询用料模板明细信息
            List<UseMatrTemplateListBean> useMatrTemplateList = mapper.getMatrTemplateListByTemplateId(bean.getTemplateId());
            // 获取材料信息
            for (UseMatrTemplateListBean useMatrTemplateListBean : useMatrTemplateList) {
                MatrCodeBean matrCodeBean = matrMapper.selectMatrTemplateByNo(useMatrTemplateListBean.getMatrNo());
                if(matrCodeBean != null) {
                    useMatrTemplateListBean.setMatrId(matrCodeBean.getId());
                    useMatrTemplateListBean.setMatrName(matrCodeBean.getMatrName());
                    useMatrTemplateListBean.setMatrMode(matrCodeBean.getMatrMode());
                    useMatrTemplateListBean.setMatrUnit(matrCodeBean.getMatrUnit());
                }
            }
            useMatrTemplateBean.setUseMatrTemplateList(useMatrTemplateList);
            info.setData(useMatrTemplateBean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }
}
