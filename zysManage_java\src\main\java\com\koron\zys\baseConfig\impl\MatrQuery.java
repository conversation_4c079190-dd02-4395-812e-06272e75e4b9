package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MatrBean;
import com.koron.zys.baseConfig.mapper.MatrMapper;
import com.koron.zys.baseConfig.queryBean.MatrQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 材料价格-编辑初始化
 *
 * <AUTHOR>
 */
public class MatrQuery implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MatrQuery.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        @SuppressWarnings("rawtypes")
        MessageBean<MatrBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", MatrBean.class);

        try {
            MatrMapper mapper = factory.getMapper(MatrMapper.class);
            MatrQueryBean bean = JsonUtils.objectToPojo(req.getData(), MatrQueryBean.class);
            if (StringUtils.isEmpty(bean.getMatrNo())){
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "材料编码不能为空", void.class);
            }
            MatrBean matrBean = mapper.selectMatrByMatrNo(bean.getMatrNo());
            info.setData(matrBean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }
}
