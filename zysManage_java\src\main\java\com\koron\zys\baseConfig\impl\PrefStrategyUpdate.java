package com.koron.zys.baseConfig.impl;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.PrefStrategyBean;
import com.koron.zys.baseConfig.bean.PrefStrategyDetailBean;
import com.koron.zys.baseConfig.mapper.PrefStrategyMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;
import com.mysql.cj.util.StringUtils;

/**
 * 优惠策略-编辑
 *
 * <AUTHOR>
 */
public class PrefStrategyUpdate implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(PrefStrategyUpdate.class);

    @Override
    @ValidationKey(clazz = PrefStrategyBean.class,method = "update")
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            PrefStrategyMapper mapper = factory.getMapper(PrefStrategyMapper.class);
            PrefStrategyBean bean = JsonUtils.objectToPojo(req.getData(), PrefStrategyBean.class);
            // 校验字段重复
            if (mapper.check2("strategy_name", bean.getStrategyName(), bean.getId()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "优惠策略名称：" + bean.getStrategyName() + "的信息已存在。",
                        void.class);
            }
            if (StringUtils.isNullOrEmpty(bean.getId())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
            }
            bean.setUpdateInfo(userInfo);
            // 删除修改前的明细数据
            mapper.delPrefStrategyDetail(bean.getId());
            // 前端传过来的修改之后的数据
            List<PrefStrategyDetailBean> prefStrategyList = bean.getPrefStrategyList();
            if (null == prefStrategyList || prefStrategyList.size() == 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "优惠明细内容不能为空！", void.class);
            }
            // 判断费用名称是否重复
            Set<String> set = new HashSet<>();
            for (PrefStrategyDetailBean ladderBean : prefStrategyList) {
                if (ladderBean.getCostId() == null || ladderBean.getMaxRange() == null || ladderBean.getMinRange() == null || ladderBean.getPrefValue() == null || ladderBean.getPrefWay() == null) {
                    return MessageBean.create(Constant.ILLEGAL_PARAMETER, "优惠明细内容不能为空！", void.class);
                }
                if (!set.add(ladderBean.getCostId())) {
                    return MessageBean.create(Constant.ILLEGAL_PARAMETER,"存在重复的费用名称。", void.class);
                }
                ladderBean.setCreateInfo(userInfo);
                ladderBean.setPrefStrategyId(bean.getId());
            }
            mapper.insertPrefStrategyDetailList(prefStrategyList);// 批量添加列表的数据
            mapper.updatePrefStrategy(bean); // 修改主表信息
        } catch (Exception e) {
            factory.close(false);
            logger.error("优惠策略修改失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "优惠策略修改失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}