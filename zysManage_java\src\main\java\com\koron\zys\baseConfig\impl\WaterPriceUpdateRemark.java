package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.WaterPriceBean;
import com.koron.zys.baseConfig.mapper.WaterPriceMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;

public class WaterPriceUpdateRemark implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub
		try {
			WaterPriceMapper mapper = factory.getMapper(WaterPriceMapper.class);
			WaterPriceBean bean = JsonUtils.objectToPojo(req.getData(), WaterPriceBean.class);
			if (StringUtils.isNullOrEmpty(bean.getId())) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
			}
			if (StringUtils.isNullOrEmpty(bean.getRemark())) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "描述不能为空", void.class);
			}
			mapper.updateWaterPriceRemark(bean);
			MessageBean<WaterPriceBean> ret = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", WaterPriceBean.class);
			ret.setData(bean);
			return ret;
		} catch (Exception e) {
			factory.close(false);
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
	}

}
