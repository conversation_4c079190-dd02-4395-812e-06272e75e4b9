package com.koron.zys.baseConfig.impl;

import java.util.ArrayList;
import java.util.List;

import com.koron.zys.baseConfig.bean.UseMatrTemplateListBean;
import com.koron.zys.baseConfig.mapper.UseMatrTemplateMapper;
import com.koron.zys.baseConfig.queryBean.UseMatrTemplateQueryBean;
import com.koron.zys.baseConfig.vo.MatrTemplateSelectVO;
import com.koron.zys.baseConfig.vo.UseMatrTemplateVO;
import com.koron.zys.serviceManage.mapper.MatrCodeMapper;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MatrCodeBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

/**
 * 领料模板下拉框
 * <AUTHOR>
 * 2020年3月31日
 */
public class GetMatrTemplateSelect implements ServerInterface  {
	
	private static Logger logger = LoggerFactory.getLogger(GetMatrTemplateSelect.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			//获取mapper实例
			MatrCodeMapper matrMapper = factory.getMapper(MatrCodeMapper.class, "_default");
			UseMatrTemplateMapper mapper = factory.getMapper(UseMatrTemplateMapper.class);
			
			List<MatrTemplateSelectVO> res = new ArrayList<MatrTemplateSelectVO>();
			//查询模板列表
			UseMatrTemplateQueryBean matrTempQueryBean = new UseMatrTemplateQueryBean();
			matrTempQueryBean.setStatus(1);
			List<UseMatrTemplateVO> list = mapper.selectUseMatrTemplate(matrTempQueryBean);
			for (UseMatrTemplateVO vo : list) {
				///查询模板列表
				List<UseMatrTemplateListBean> matrs = mapper.getMatrTemplateListByTemplateId(vo.getId());
				for (UseMatrTemplateListBean m : matrs) {
					//查询材料信息
					MatrCodeBean matr = matrMapper.selectMatrTemplateByNo(m.getMatrNo());
					if(matr != null) {
						m.setMatrId(matr.getId());
						m.setMatrName(matr.getMatrName());
						m.setMatrMode(matr.getMatrMode());
						m.setMatrUnit(matr.getMatrUnit());
						//判断定价表为空取默认值
						if(m.getMatrPrice() == null || m.getMatrPrice() == 0) {
							m.setMatrPrice(matr.getMatrPrice());
						}
						m.setMatrMoney(m.getMatrPrice()*m.getMatrNum());
					}
				}
				MatrTemplateSelectVO selectVO = new MatrTemplateSelectVO();
				selectVO.setTemplateId(vo.getId());
				selectVO.setTemplateName(vo.getTemplateName());
				selectVO.setMatrs(matrs);
				res.add(selectVO);
			}
			//输出结果
			info.setData(res);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}