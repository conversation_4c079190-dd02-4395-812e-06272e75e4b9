webpackJsonp([36],{YQ7Z:function(e,t){},bpYE:function(e,t){},gMLm:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"childSystemAdd"},[s("el-form",{ref:"childSystemAddruleForm",staticClass:"formBill-One",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px",inline:!0}},[s("el-form-item",{attrs:{label:"子系统编号：",prop:"systemCode"}},[s("el-input",{attrs:{maxlength:"20",clearable:""},model:{value:e.ruleForm.systemCode,callback:function(t){e.$set(e.ruleForm,"systemCode",t)},expression:"ruleForm.systemCode"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"子系统名称：",prop:"systemName"}},[s("el-input",{attrs:{maxlength:"20",clearable:""},model:{value:e.ruleForm.systemName,callback:function(t){e.$set(e.ruleForm,"systemName",t)},expression:"ruleForm.systemName"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"状态：",prop:"status"}},[s("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[s("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),s("el-option",{attrs:{label:"禁用",value:2}})],1)],1),s("br"),e._v(" "),s("el-form-item",{attrs:{label:"接口允许IP："}},[s("el-input",{attrs:{type:"textarea",maxlength:"500",clearable:"","show-word-limit":"",rows:3,placeholder:"可用逗号（,）、横杆（-）、星号（*）等分隔符，如***********-***********00,*************,192.168.4.*"},model:{value:e.ruleForm.allowIp,callback:function(t){e.$set(e.ruleForm,"allowIp",t)},expression:"ruleForm.allowIp"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"接口禁止IP："}},[s("el-input",{attrs:{type:"textarea",maxlength:"500",clearable:"","show-word-limit":"",rows:3,placeholder:"可用逗号（,）、横杆（-）、星号（*）等分隔符，如***********-***********00,*************,192.168.4.*"},model:{value:e.ruleForm.forbidIp,callback:function(t){e.$set(e.ruleForm,"forbidIp",t)},expression:"ruleForm.forbidIp"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"描述："}},[s("el-input",{attrs:{type:"textarea",maxlength:"500",clearable:"","show-word-limit":""},model:{value:e.ruleForm.comments,callback:function(t){e.$set(e.ruleForm,"comments",t)},expression:"ruleForm.comments"}})],1)],1)],1)},staticRenderFns:[]};var l={name:"childSystem",components:{childSystemAdd:s("VU/8")({name:"childSystemAdd",data:function(){return{databaseData:[],ruleForm:{systemCode:"",systemName:"",status:1,allowIp:"",forbidIp:"",comments:""},rules:{systemCode:[{required:!0,message:"请输入子系统编号",trigger:"blur"}],systemName:[{required:!0,message:"请输入子系统名称",trigger:"blur"}],status:[{required:!0,message:"请输入状态",trigger:"blur"}]}}},mounted:function(){},methods:{resetForm:function(){this.$refs.childSystemAddruleForm.resetFields()},submitForm:function(e,t){var s=this,a=this,l={};this.$refs[e].validate(function(e){if(!e)return!1;l="添加"===t?{busicode:"TSubSystemAdd",data:s.ruleForm}:{busicode:"TSubSystemUpdate",data:s.ruleForm},s.$api.fetch({params:l}).then(function(e){a.$message({showClose:!0,message:"保存成功",type:"success"}),a.$parent.selectTSubSystem(),a.$parent.closeDialog(),s.resetForm()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","childSystemAdd",this.$parent.closeDialog)},editData:function(e){this.ruleForm=e}}},a,!1,function(e){s("bpYE")},null,null).exports},data:function(){return{tableShow:!1,tableQuery:{page:1,pageCount:20},maxHeight:0,appServerData:[],formData:{systemCode:"",systemName:"",status:1,allowIp:"",forbidIp:"",comments:""},crumbsData:{titleList:[{title:"系统管理",path:"/systemMan"},{title:"子系统管理",method:function(){window.histroy.back()}}]},childSystemShow:!0,childSystemAddVisible:!1}},mounted:function(){this.selectTSubSystem()},methods:{formatStatus:function(e){return 1===e.status?"启用":"禁用"},appAdd:function(e){var t=this;if("add"===e)this.$set(this.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),this.$refs.childSystemAdd.editData({systemCode:"",systemName:"",status:1,allowIp:"",forbidIp:"",comments:""}),this.common.chargeObjectEqual(this,this.formData,"set","childSystemAdd");else{this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var s={busicode:"TSubSystemList",data:{subsystemId:e.row.subsystemId}};this.$api.fetch({params:s}).then(function(e){t.$refs.childSystemAdd.editData(e[0]),t.common.chargeObjectEqual(t,e[0],"set","childSystemAdd")})}this.childSystemShow=!1,this.childSystemAddVisible=!0},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(e){this.tableQuery.page=e,this.selectTSubSystem()},selectTSubSystem:function(){var e=this;this.$api.fetch({params:{busicode:"TSubSystemList",data:{}}}).then(function(t){e.$set(e.appServerData,"list",t),e.common.changeTable(e,".childSystem .kl-table",[])})},closeDialog:function(){this.childSystemShow=!0,this.childSystemAddVisible=!1,this.crumbsData.titleList.pop(),this.$refs.childSystemAdd.resetForm()},handleClose:function(){this.$refs.childSystemAdd.handleClose()},submitForm:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.childSystemAdd.submitForm(e,t)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},i={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"childSystem"},[s("div",{staticClass:"main-content"},[s("div",{staticClass:"bread-contain"},[s("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:e.childSystemShow,expression:"childSystemShow"}],staticClass:"bread-contain-right"},[s("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:e.childSystemAddVisible,expression:"childSystemAddVisible"}],staticClass:"bread-contain-right"},[s("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("childSystemAddruleForm")}}},[e._v("保存")]),e._v(" "),s("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:e.childSystemShow,expression:"childSystemShow"}],staticClass:"kl-table"},[e.tableShow?s("el-table",{attrs:{stripe:"",border:"",data:e.appServerData.list,"max-height":e.maxHeight}},[s("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),s("el-table-column",{attrs:{prop:"systemCode",label:"子系统编号","min-width":"100"}}),e._v(" "),s("el-table-column",{attrs:{prop:"systemName",label:"子系统名称","min-width":"100"}}),e._v(" "),s("el-table-column",{attrs:{prop:"allowIp",label:"接口允许IP","min-width":"100"}}),e._v(" "),s("el-table-column",{attrs:{prop:"forbidIp",label:"接口禁止IP","min-width":"100"}}),e._v(" "),s("el-table-column",{attrs:{prop:"status",formatter:e.formatStatus,"min-width":"80",label:"状态"}}),e._v(" "),s("el-table-column",{attrs:{prop:"comments",label:"描述","min-width":"150"}}),e._v(" "),s("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{attrs:{type:"text"},on:{click:function(s){return e.appAdd(t)}}},[e._v("编辑")])]}}],null,!1,1191321586)})],1):e._e()],1),e._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:e.childSystemAddVisible,expression:"childSystemAddVisible"}]},[s("childSystemAdd",{ref:"childSystemAdd"})],1)])])},staticRenderFns:[]};var r=s("VU/8")(l,i,!1,function(e){s("YQ7Z")},null,null);t.default=r.exports}});