webpackJsonp([34],{LsLR:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"postManEdit"},[a("el-form",{ref:"postManEditForm",staticClass:"formBill-One",attrs:{inline:!0,size:"mini",rules:t.rules,model:t.formData,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"岗位编号：",prop:"postNo"}},[a("el-input",{attrs:{placeholder:"岗位编号",maxlength:"50",clearable:""},model:{value:t.formData.postNo,callback:function(e){t.$set(t.formData,"postNo",e)},expression:"formData.postNo"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"岗位名称：",prop:"postName"}},[a("el-input",{attrs:{placeholder:"岗位名称",maxlength:"50",clearable:""},model:{value:t.formData.postName,callback:function(e){t.$set(t.formData,"postName",e)},expression:"formData.postName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"排序号：",prop:"sortNo"}},[a("el-input",{attrs:{placeholder:"排序号",maxlength:"6",clearable:""},model:{value:t.formData.sortNo,callback:function(e){t.$set(t.formData,"sortNo",t._n(e))},expression:"formData.sortNo"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"状态：",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态"},model:{value:t.formData.status,callback:function(e){t.$set(t.formData,"status",e)},expression:"formData.status"}},[a("el-option",{attrs:{label:"停用",value:0}}),t._v(" "),a("el-option",{attrs:{label:"启用",value:1}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"备注："}},[a("el-input",{attrs:{type:"textarea",rows:3,"show-word-limit":"",maxlength:"150",placeholder:"备注",clearable:""},model:{value:t.formData.comments,callback:function(e){t.$set(t.formData,"comments",e)},expression:"formData.comments"}})],1)],1)],1)},staticRenderFns:[]};var o={name:"postMan",components:{postManEdit:a("VU/8")({name:"postManEdit",data:function(){return{formData:{id:"",postNo:"",postName:"",tenantId:"",status:"",comments:"",sortNo:""},rules:{postNo:[{required:!0,message:"请输入岗位编号",trigger:"blur"}],postName:[{required:!0,message:"请输入岗位名称",trigger:"blur"}],status:[{required:!0,message:"请输入状态",trigger:"blur"}],sortNo:[{required:!0,message:"请输入排序号",trigger:"blur"},{type:"number",message:"排序号必须为数字值"}]}}},methods:{resetForm:function(){this.$refs.postManEditformData.resetFields()},submitForm:function(t){var e=this,a={};a="postAdd"===t?{busicode:"PostAdd",data:this.formData}:{busicode:"PostUpdate",data:this.formData},this.$api.fetch({params:a}).then(function(t){e.$message({showClose:!0,message:"保存成功",type:"success"}),e.$parent.init(),e.$parent.closeDialog()})},editData:function(t){this.formData=t}}},s,!1,function(t){a("mwgJ")},"data-v-50373a62",null).exports},data:function(){return{tableData:[],tableQuery:{page:1,pageCount:10,id:"",postName:"",status:""},EditVisible:!1,formData:{postNo:"",postName:"",tenantId:"",sortNo:"",status:"",comments:""},crumbsData:{titleList:[{title:"基础配置",path:"/baseInfo"},{title:"岗位管理",method:function(){window.histroy.back()}}]},tableShow:!1,maxHeight:0,formType:""}},mounted:function(){var t=this;eventBus.$emit("asideMenuShow","basicsMenuShow1"),this.init(),this.$nextTick(function(){t.common.changeTable(t,".postMan .kl-table",[])})},methods:{init:function(){var t=this,e=this,a={busicode:"PostList",data:this.tableQuery};this.$api.fetch({params:a}).then(function(a){e.tableData=a,t.$nextTick(function(){e.common.changeTable(t,".postManIndex",[".postManIndex .toolbar",".postManIndex .block"])})})},add:function(t){var e=this;if(this.EditVisible=!0,"add"===t)this.formType="postAdd",this.$refs.postManEdit.editData({postNo:"",postName:"",sortNo:"",status:"",comments:""});else{this.formType="postUpdate";var a={busicode:"PostQuery",data:{id:t.row.id}};this.$api.fetch({params:a}).then(function(t){e.$refs.postManEdit.editData(t)})}},search:function(){this.tableQuery.page=1,this.init()},indexMethod:function(t){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(t+1)},handleSizeChange:function(t){this.tableQuery.pageCount=t,this.tableQuery.page=1,this.init()},handleCurrentChange:function(t){this.tableQuery.page=t,this.init()},closeDialog:function(){this.EditVisible=!1},submitForm:function(){this.$refs.postManEdit.submitForm(this.formType)}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},i={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"postMan"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:t.crumbsData}}),t._v(" "),t.EditVisible?a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.submitForm()}}},[t._v("保存")]),t._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:t.closeDialog}},[t._v("返回")])],1):a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-plus",type:"primary"},on:{click:function(e){return t.add("add")}}},[t._v("添加")])],1)],1),t._v(" "),a("postManEdit",{directives:[{name:"show",rawName:"v-show",value:t.EditVisible,expression:"EditVisible"}],ref:"postManEdit"}),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.EditVisible,expression:"!EditVisible"}],staticClass:"postManIndex"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,size:"mini",model:t.formData}},[a("el-form-item",{staticClass:"form-left"},[a("el-form-item",{attrs:{label:"状态："}},[a("el-select",{attrs:{placeholder:"请选择状态"},model:{value:t.tableQuery.status,callback:function(e){t.$set(t.tableQuery,"status",e)},expression:"tableQuery.status"}},[a("el-option",{attrs:{label:"全部",value:""}}),t._v(" "),a("el-option",{attrs:{label:"启用",value:1}}),t._v(" "),a("el-option",{attrs:{label:"停用",value:0}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"岗位名称："}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入岗位名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search.apply(null,arguments)}},model:{value:t.tableQuery.postName,callback:function(e){t.$set(t.tableQuery,"postName",e)},expression:"tableQuery.postName"}})],1),t._v(" "),a("el-form-item",[a("el-button",{staticClass:"searchBtn",attrs:{icon:"el-icon-search"},on:{click:t.search}})],1)],1)],1)],1),t._v(" "),a("div",{staticClass:"kl-table"},[t.tableShow?a("el-table",{staticClass:"wuserInfo-table",attrs:{"max-height":t.maxHeight,stripe:"",border:"",data:t.tableData.list}},[a("el-table-column",{attrs:{type:"index",label:"NO.",index:t.indexMethod}}),t._v(" "),a("el-table-column",{attrs:{prop:"postNo",label:"岗位编号"}}),t._v(" "),a("el-table-column",{attrs:{prop:"postName",label:"岗位名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"sortNo",label:"排序号"}}),t._v(" "),a("el-table-column",{attrs:{prop:"status",label:"状态"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text ",size:"mini"},nativeOn:{click:function(a){return t.add(e)}}},[t._v("编辑")])]}}],null,!1,3390267874)})],1):t._e(),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":t.tableQuery.page,"page-sizes":[10,50,100,500,1e3],"page-size":t.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)])],1)},staticRenderFns:[]};var l=a("VU/8")(o,i,!1,function(t){a("x444")},null,null);e.default=l.exports},mwgJ:function(t,e){},x444:function(t,e){}});