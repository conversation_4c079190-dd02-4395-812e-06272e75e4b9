package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.BaseInvoiceTypeMapper;
import com.koron.zys.baseConfig.vo.BaseInvoiceTypeListVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

public class BaseInvoiceTypeList implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
		
		BaseInvoiceTypeMapper mapper = factory.getMapper(BaseInvoiceTypeMapper.class);

		List<BaseInvoiceTypeListVO> list = mapper.selectList();
		for (BaseInvoiceTypeListVO vo : list) {
			if("0".equals(vo.getStatus())) {
				vo.setStatusName("启用");
			}else if("1".equals(vo.getStatus())){
				vo.setStatusName("停用");
			}
		}
		info.setData(new PageInfo<>(list));
		return info;
	}

}
