package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MatrBean;
import com.koron.zys.baseConfig.mapper.MatrMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;

/**
 * 材料价格-添加
 *
 * <AUTHOR>
 */
public class MatrAdd implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MatrAdd.class);

    @Override
    @ValidationKey(clazz = MatrBean.class,method = "insert")
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            MatrMapper mapper = factory.getMapper(MatrMapper.class);
            MatrBean bean = JsonUtils.objectToPojo(req.getData(), MatrBean.class);
            // 校验字段重复
            if (mapper.check("matr_no", bean.getMatrNo()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "材料编号为"+bean.getMatrNo()+"的材料已设置价格。", void.class);
            }
            bean.setCreateInfo(userInfo);
            mapper.insertMatr(bean);
        } catch (Exception e) {
            logger.error("材料价格添加失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "材料价格添加失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}