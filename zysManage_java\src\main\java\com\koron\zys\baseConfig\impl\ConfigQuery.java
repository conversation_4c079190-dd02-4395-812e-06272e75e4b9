package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ConfigBean;
import com.koron.zys.baseConfig.mapper.ConfigMapper;
import com.koron.zys.baseConfig.queryBean.ConfigQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 全局参数-编辑初始化
 * <AUTHOR>
 */
public class ConfigQuery implements ServerInterface {
	
private static Logger logger = LoggerFactory.getLogger(ConfigQuery.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<ConfigBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", ConfigBean.class);

		try {
			ConfigQueryBean bean = JsonUtils.objectToPojo(req.getData(), ConfigQueryBean.class);
			ConfigMapper mapper = factory.getMapper(ConfigMapper.class);
			ConfigBean Configbean = mapper.selectConfigById(bean.getConfigId());
			info.setData( Configbean);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}
