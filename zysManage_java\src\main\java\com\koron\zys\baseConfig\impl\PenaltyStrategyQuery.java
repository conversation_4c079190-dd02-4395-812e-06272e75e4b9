package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CostNameBean;
import com.koron.zys.baseConfig.bean.PenaltyBean;
import com.koron.zys.baseConfig.bean.PenaltyStrategyBean;
import com.koron.zys.baseConfig.mapper.CostMapper;
import com.koron.zys.baseConfig.mapper.PenaltyStrategyMapper;
import com.koron.zys.baseConfig.queryBean.PenaltyQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 违约金策略-编辑初始化
 * <AUTHOR>
 *
 */
public class PenaltyStrategyQuery implements ServerInterface {
	
private static Logger logger = LoggerFactory.getLogger(PenaltyStrategyQuery.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<HashMap> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", HashMap.class);

		try { 
			PenaltyQueryBean bean = JsonUtils.objectToPojo(req.getData(), PenaltyQueryBean.class);
			PenaltyStrategyMapper mapper = factory.getMapper(PenaltyStrategyMapper.class);
			PenaltyBean penaltyBean = mapper.selectPenaltyById(bean.getPenaltyId());
			if (penaltyBean != null) {
				List<PenaltyStrategyBean> list = mapper.selectPenaltyStrategyListByPenaltyId(bean.getPenaltyId());
				List<PenaltyStrategyBean> list2 = new ArrayList<>();
				boolean t = true;
				CostMapper cmapper = factory.getMapper(CostMapper.class);
				List<CostNameBean>  costList = cmapper.selectCostNameList();
				for(CostNameBean costNameBean : costList) {
					t = true;
					for(PenaltyStrategyBean penaltyStrategyBean : list) {
						if (costNameBean.getId().equals(penaltyStrategyBean.getCostId())) {
							t = false;
							break;
						}
					}
					if (t) {
						PenaltyStrategyBean p = new PenaltyStrategyBean();
						p.setCostId(costNameBean.getId());
						list2.add(p);
					}
				}
				penaltyBean.setList(list);
				penaltyBean.getList().addAll(list2);

				HashMap<String,Object> map = new HashMap<>();
				map.put("penaltyBean", penaltyBean);
				map.put("costlist", costList);
				info.setData(map);
			}
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}
