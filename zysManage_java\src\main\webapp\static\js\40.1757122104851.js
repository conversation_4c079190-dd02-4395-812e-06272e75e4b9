webpackJsonp([40],{"T+/8":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r("m1D0"),o={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"login",on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.submitForm("ruleForm")}}},[r("el-container",[r("el-header",{staticClass:"head",staticStyle:{height:"initial","margin-left":"50px","margin-top":"30px"}},[r("img",{staticClass:"logo",attrs:{src:"static/images/logo2.png",alt:""}})]),e._v(" "),r("el-main",[r("div",{staticClass:"mainBackground",style:e.tu}),e._v(" "),r("div",{staticClass:"register"},[r("div",{staticClass:"content"},[r("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",attrs:{model:e.ruleForm,"label-width":"50px"}},[r("span",{staticStyle:{"font-size":"16px"}},[r("b",[e._v("账号登录")])]),e._v(" "),r("el-form-item",{staticClass:"lbl",attrs:{label:"账号",prop:"loginName"}},[r("el-input",{staticClass:"ipt",model:{value:e.ruleForm.loginName,callback:function(t){e.$set(e.ruleForm,"loginName",t)},expression:"ruleForm.loginName"}})],1),e._v(" "),r("el-form-item",{staticClass:"lbl",attrs:{label:"密码",prop:"password"}},[r("el-input",{staticClass:"ipt",attrs:{type:"password","auto-complete":"off"},model:{value:e.ruleForm.password,callback:function(t){e.$set(e.ruleForm,"password",t)},expression:"ruleForm.password"}})],1),e._v(" "),r("el-form-item",{attrs:{prop:"verifyCode"}},[r("el-input",{staticClass:"ipt",staticStyle:{width:"120px","margin-left":"-90px"},attrs:{type:"text",id:"verifyCode",placeholder:"请输入验证码"},model:{value:e.ruleForm.verifyCode,callback:function(t){e.$set(e.ruleForm,"verifyCode",t)},expression:"ruleForm.verifyCode"}}),e._v(" "),r("div",{staticClass:"divIdentifyingCode"},[r("img",{ref:"verifyCodeRef",attrs:{src:e.verifyCodeSrc,id:"verifyCodeImg"},on:{click:function(t){return e.getVCode("ruleForm")}}})])],1),e._v(" "),r("el-form-item",{staticClass:"btu"},[r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("ruleForm")}}},[e._v("提交")]),e._v(" "),r("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")])],1)],1)],1)])])],1)],1)},staticRenderFns:[]};var a=function(e){r("yJjH")},s=r("VU/8")(i.a,o,!1,a,"data-v-671b7e96",null);t.default=s.exports},m1D0:function(e,t,r){"use strict";(function(e){t.a={name:"login",data:function(){return{ruleForm:{loginName:"",password:"",verifyCode:""},rules2:{loginName:[{required:!0,message:"请输入账号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],verifyCode:[{required:!0,message:"请输入验证码",trigger:"blur"}]},description:"",tu:{background:"url(static/images/banner3.jpg) 0px -111px no-repeat"},tu1:{background:"url(static/images/banner.jpg) no-repeat 0px 0px",backgroundSize:"101% 100%"},tu2:{background:"url(static/images/banner2.jpg) no-repeat 0px 0px",backgroundSize:"100% 100%"},errorClass:"",isEnter:!0,verifyCodeSrc:""}},mounted:function(){this.loginName=sessionStorage.getItem("loginName"),this.getVCode("ruleForm")},methods:{changePhoto:function(e){this.isEnter=-1==e},submitForm:function(t){var r=this,i=this;this.$refs[t].validate(function(t){if(!t)return!1;var o=i.ruleForm;r.$api.fetch({apiUrl:"/loginMaintain.api",params:o}).then(function(t){console.log(t),localStorage.setItem("token",t),sessionStorage.setItem("loginName",i.ruleForm.loginName),i.$router.push("/home"),e("#app #firstNav #nav .navList").children("li").eq(0).addClass("active").siblings().removeClass("active")}).catch(function(e){console.log(e.data),r.getVCode("ruleForm")})})},getVCode:function(e){var t=this;(new Date).getTime();this.$refs[e].validate(function(e){if(!e)return!1;t.$api.fetch({apiUrl:"/verifyCode.htm",params:{},responseType:"blob"}).then(function(e){console.log(e)}).catch(function(e){var r=[];r.push(e);var i=window.URL.createObjectURL(new Blob(r));t.verifyCodeSrc=i,console.log(t.verifyCodeSrc)})})},resetForm:function(e){this.$refs[e].resetFields()}}}}).call(t,r("7t+N"))},yJjH:function(e,t){}});