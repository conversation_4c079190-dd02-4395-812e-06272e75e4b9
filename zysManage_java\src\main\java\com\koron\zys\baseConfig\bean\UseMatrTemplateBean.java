package com.koron.zys.baseConfig.bean;

import java.util.List;

import com.koron.util.Check;

/**
 * 用料模板实体类
 *
 * <AUTHOR>
 */
public class UseMatrTemplateBean extends BaseBean {
    /**
     * 模板名称
     */
	@Check(name = "模板名称", notNull = true)
    private String templateName;
    /**
     * 材料数量
     */
	@Check(name = "材料数量", notNull = true)
    private Integer matrNum;
	@Check(name = "材料数量", max = 150)
    private String comments;
	@Check(name = "状态", notNull = true)
    private Integer status;
	@Check(name = "排序号", notNull = true)
    private Integer sortNo;

    /**
     * 用料模板明细集合
     */
	@Check(name = "用料模板明细集合", notEmpty = true, notNull = true)
    private List<UseMatrTemplateListBean> useMatrTemplateList;

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Integer getMatrNum() {
        return matrNum;
    }

    public void setMatrNum(Integer matrNum) {
        this.matrNum = matrNum;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public List<UseMatrTemplateListBean> getUseMatrTemplateList() {
        return useMatrTemplateList;
    }

    public void setUseMatrTemplateList(List<UseMatrTemplateListBean> useMatrTemplateList) {
        this.useMatrTemplateList = useMatrTemplateList;
    }
}
