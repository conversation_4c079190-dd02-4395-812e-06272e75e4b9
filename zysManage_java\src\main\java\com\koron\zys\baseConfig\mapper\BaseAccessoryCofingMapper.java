package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean;
import com.koron.zys.baseConfig.queryBean.BaseAccessoryConfigQueryBean;

public interface BaseAccessoryCofingMapper {
	
	List<BaseAccessoryConfigBean> selectList(BaseAccessoryConfigQueryBean query);
	
	BaseAccessoryConfigBean selectById(String id);
	
	List<BaseAccessoryConfigBean> selectByReceiptType(String receiptType);
	
	List<BaseAccessoryConfigBean> selectByReceiptType2(String receiptType);
	
	List<BaseAccessoryConfigBean> selectByAccessoryNo(String accessoryNo);
	
	int insert(BaseAccessoryConfigBean bean);
	
	int update(BaseAccessoryConfigBean bean);
	
	int insertBaseConfig(List<BaseAccessoryConfigBean> list);
	
	List<BaseAccessoryConfigBean> selectList1(BaseAccessoryConfigQueryBean query);
	
	void delete();
}
