webpackJsonp([48],{"R+Md":function(e,t){},zHSS:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("Gu7T"),o=a.n(n),i={name:"CompanyEmpower",data:function(){return{checkAll:!1,indeterminate:!1,allBtnList:[],companyList:[],defaultProps:{children:"children",label:"opName"},firstOpCode:"",empowerShow:!1,listAddShow:!1,treeData:[],companyData:[],checkTreeData:[],checkedNodeData:[],tableShow:!1,maxHeight:0,btnRoleName:"",getOperationKey:[]}},mounted:function(){var e=this;this.getTreeData(),this.$nextTick(function(){e.common.changeTable(e,".CompanyEmpower",[".CompanyEmpower .toolbar",".CompanyEmpower .block"])})},methods:{handleCheckAllChange:function(e){var t=this;e?this.companyData.forEach(function(e){t.companyList.indexOf(e.companyNo)<0&&t.companyList.push(e.companyNo)}):this.companyList=[]},getTreeData:function(e){var t=this,a=this;this.$api.fetch({params:{busicode:"OperationListTree",data:{page:1,pageCount:1e4}}}).then(function(e){a.treeData=e,a.firstOpCode=a.treeData[0].opCode,console.log(a.treeData[0].children),console.log(a.treeData),a.checkTreeData=[],t.initChecked(e)})},changeCheck:function(e){this.companyData.length==e.length?this.checkAll=!0:this.checkAll=!1},getCompanyData:function(e){var t=this;this.getOperationKey=[],this.getOperationKey.push(e.key);var a=this;if(0==e.childNodes.length){var n={busicode:"CompanyCheckList",data:{operationCode:e.key}};this.$api.fetch({params:n}).then(function(e){t.companyList=[],a.companyData=e,a.companyData1=a.companyData;for(var n=0;n<a.companyData.length;n++)"1"==a.companyData[n].checked&&t.companyList.push(a.companyData[n].companyNo);a.companyData.length==t.companyList.length?t.checkAll=!0:t.checkAll=!1}),a.empowerShow=!0}},initChecked:function(e){var t=this;e.forEach(function(e){e.checked&&t.checkTreeData.push(e.opCode),""!==e.btnList&&e.btnList.forEach(function(e){e.checked&&t.allBtnList.push(e.opCode)}),e.children&&e.children.length>0&&t.initChecked(e.children)})},save:function(){var e=this,t=[].concat(o()(this.allBtnList));if(this.$refs.tree.getCheckedNodes().forEach(function(e){t.push(e.opCode)}),t.length<=0)this.$message({message:"菜单不能为空！",type:"warning"});else{var a={busicode:"AddRoleOperation",data:{roleCode:this.clickUserData.roleCode,operation:t}};this.$api.fetch({params:a}).then(function(t){e.$message({message:"保存成功！",type:"success"})})}},submit:function(){var e=this,t=[].concat(o()(this.companyList)),a=[].concat(o()(this.getOperationKey));if(this.$refs.tree.getCheckedNodes().forEach(function(e){a.push(e.opCode)}),this.$refs.tree.getCheckedNodes().forEach(function(e){t.push(e.companyNo)}),t.length<=0)this.$message({message:"菜单不能为空！",type:"warning"});else{var n={busicode:"BatchAddOrgOperation",data:{orgCodeList:t,operationCodeList:a}};this.$api.fetch({params:n}).then(function(t){e.$message({message:"保存成功！",type:"success"}),e.empowerShow=!1})}},checkNodeClick:function(e,t,a){var n=this;e.children&&0!==e.children.length&&e.children.forEach(function(o){o.opCode!==t.opCode&&e.opCode!==t.opCode?n.checkNodeClick(o,t,a):n.toggleChildrenClick(o,a)})},toggleChildrenClick:function(e,t){var a=this;e.btnList&&e.btnList.length>0&&this.toggleBtnClick(e,t),this.$refs.tree.setChecked(e.opCode,t),e.children&&0!==e.children.length&&e.children.forEach(function(e){a.$refs.tree.setChecked(e.opCode,t),a.toggleChildrenClick(e,t)})},toggleBtnClick:function(e,t){var a=this;t?(e.btnList.forEach(function(e){a.allBtnList.push(e.opCode)}),e.companyData.forEach(function(e){a.companyList.push(e.companyNo)})):(this.allBtnList=this.allBtnList.filter(function(t){return e.btnList.every(function(e){return e.opCode!==t})}),this.companyList=this.companyList.filter(function(t){return e.companyData.every(function(e){return e.companyNo!==t})}))}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},c={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"CompanyEmpower"},[a("div",{staticClass:"role-right"},[a("div",{staticClass:"role-title"},[e._v("\n      菜单功能权限\n      "),a("div",{staticClass:"save-btn"},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.save}},[e._v("保存")])],1)]),e._v(" "),a("div",{staticClass:"role-tree"},[a("el-tree",{ref:"tree",staticClass:"filter-tree department",attrs:{"check-strictly":!0,data:e.treeData,props:e.defaultProps,"node-key":"opCode","default-checked-keys":e.checkTreeData,"default-expanded-keys":[e.firstOpCode],accordion:""},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.node,o=t.data;return a("span",{staticClass:"custom-tree-node"},[a("span",{staticStyle:{display:"inline-block"}},[e._v(e._s(n.label))]),e._v(" "),a("span",{staticStyle:{display:"inline-block","margin-left":"30px"}},[a("el-checkbox-group",{model:{value:e.allBtnList,callback:function(t){e.allBtnList=t},expression:"allBtnList"}},e._l(o.btnList,function(t){return a("el-checkbox",{key:t.opCode,attrs:{label:t.opCode}},[e._v(e._s(t.opName))])}),1),e._v(" "),a("div",{staticClass:"empower-btn"},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.getCompanyData(n)}}},[e._v("授权")])],1)],1)])}}])})],1)]),e._v(" "),a("el-dialog",{attrs:{title:"授权",visible:e.empowerShow,width:"50%"},on:{"update:visible":function(t){e.empowerShow=t}}},[a("el-checkbox",{staticStyle:{"margin-left":"15px"},attrs:{indeterminate:e.indeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选")]),e._v(" "),a("el-checkbox-group",{staticStyle:{"margin-left":"15px"},on:{change:e.changeCheck},model:{value:e.companyList,callback:function(t){e.companyList=t},expression:"companyList"}},e._l(e.companyData,function(t){return a("el-checkbox",{key:t.companyNo,staticStyle:{"margin-top":"10px"},attrs:{label:t.companyNo}},[e._v(e._s(t.shortName))])}),1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("确 定")]),e._v(" "),a("el-button",{on:{click:function(t){e.empowerShow=!1}}},[e._v("取 消")])],1)],1)],1)},staticRenderFns:[]};var s=a("VU/8")(i,c,!1,function(e){a("R+Md")},null,null);t.default=s.exports}});