package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BaseAccessoryMetadataBean;
import com.koron.zys.baseConfig.bean.BaseReceiptAccessoryBean;
import com.koron.zys.baseConfig.bean.PubAccessoryTypeBean;
import com.koron.zys.baseConfig.mapper.BaseAccessoryMetadataMapper;
import com.koron.zys.baseConfig.mapper.BaseReceiptAccessoryMapper;
import com.koron.zys.baseConfig.mapper.PubAccessoryTypeMapper;
import com.koron.zys.baseConfig.queryBean.BaseReceiptAccessoryQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

/**
 * 获取附件列表
 * <AUTHOR>
 *
 */
public class ReceiptAccessoryList implements ServerInterface{
	
	private static Logger log = LoggerFactory.getLogger(ReceiptAccessoryList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			BaseReceiptAccessoryQueryBean query = JsonUtils.objectToPojo(req.getData(), BaseReceiptAccessoryQueryBean.class);
			if(StringUtils.isBlank(query.getReceiptId())) {
				return MessageBean.create(Constant.MESSAGE_DBFAIL, "单据ID 不能为空", Void.class);
			}
			if(StringUtils.isBlank(query.getReceiptType())) {
				return MessageBean.create(Constant.MESSAGE_DBFAIL, "单据类型 不能为空", Void.class);
			}
			BaseReceiptAccessoryMapper mapper = factory.getMapper(BaseReceiptAccessoryMapper.class);
			BaseAccessoryMetadataMapper accessoryMetadataMapper = factory.getMapper(BaseAccessoryMetadataMapper.class);
			PubAccessoryTypeMapper accessoryTypeMapper = factory.getMapper(PubAccessoryTypeMapper.class);
			List<BaseReceiptAccessoryBean> receiptAccessorys = mapper.selectList(query);
			for(BaseReceiptAccessoryBean receiptAccessory : receiptAccessorys) {
				BaseAccessoryMetadataBean accessoryMetadata = accessoryMetadataMapper.selectById(receiptAccessory.getMetadataId());
				String fileName = accessoryMetadata.getAccessoryName();
				if (StringUtils.isBlank(fileName)) {
					fileName = accessoryMetadata.getAccessoryPath().substring(accessoryMetadata.getAccessoryPath().lastIndexOf("/")+1);
				}
			//	accessoryMetadata.setAccessoryPath(ApplicationConfig.getAccessoryDownloadUrl() + "fileDownload?path=" + accessoryMetadata.getAccessoryPath() + "&fileName=" + fileName);
				PubAccessoryTypeBean accessoryType = accessoryTypeMapper.selectByAccessNo(receiptAccessory.getAccessoryNo());
				receiptAccessory.setAccessoryMetadata(accessoryMetadata);
				if (accessoryType!=null && accessoryType.getAccessoryName()!=null) {
					receiptAccessory.setAccessoryType(accessoryType.getAccessoryName());
				}
			}
			info.setData(receiptAccessorys);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "获取附件列表失败", Void.class);
		}
		return info;
	}

}
