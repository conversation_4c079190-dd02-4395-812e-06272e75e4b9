package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.MeterStatusMapper;
import com.koron.zys.baseConfig.queryBean.MeterStatusQueryBean;
import com.koron.zys.baseConfig.vo.MeterStatusVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 抄表状态-初始化列表
 * <AUTHOR>
 *
 */
public class MeterStatusList implements ServerInterface  {
	
	private static Logger logger = LoggerFactory.getLogger(MeterStatusList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
		try {
			MeterStatusQueryBean bean = JsonUtils.objectToPojo(req.getData(), MeterStatusQueryBean.class);
			MeterStatusMapper mapper = factory.getMapper(MeterStatusMapper.class);	
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			List<MeterStatusVO> list = mapper.selectMeterStatusList(bean);			
			info.setData(new PageInfo<>(list));
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}