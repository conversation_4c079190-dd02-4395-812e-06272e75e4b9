webpackJsonp([33],{AiVP:function(e,t){},bT8g:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"appVersionAdd"},[a("el-form",{ref:"ruleFormappVersionAdd",staticClass:"formBill-One",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"150px",inline:!0}},[a("el-form-item",{attrs:{label:"版本编号：",prop:"versionCode"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.versionCode,callback:function(t){e.$set(e.ruleForm,"versionCode",t)},expression:"ruleForm.versionCode"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"版本名称：",prop:"versionName"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.versionName,callback:function(t){e.$set(e.ruleForm,"versionName",t)},expression:"ruleForm.versionName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"域名：",prop:"domain"}},[a("el-input",{attrs:{maxlength:"100",clearable:""},model:{value:e.ruleForm.domain,callback:function(t){e.$set(e.ruleForm,"domain",t)},expression:"ruleForm.domain"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态：",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),a("el-option",{attrs:{label:"禁用",value:0}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"描述:",clearable:""}},[a("el-input",{attrs:{type:"textarea",maxlength:"500","show-word-limit":""},model:{value:e.ruleForm.comments,callback:function(t){e.$set(e.ruleForm,"comments",t)},expression:"ruleForm.comments"}})],1)],1)],1)},staticRenderFns:[]};var i={name:"appVersion",components:{appVersionAdd:a("VU/8")({name:"appVersionAdd",data:function(){return{databaseData:[],ruleForm:{versionNo:"",versionName:"",comments:"",domain:"",status:1},rules:{versionNo:[{required:!0,message:"版本编号",trigger:"blur"}],versionName:[{required:!0,message:"版本名称",trigger:"blur"}],domain:[{required:!0,message:"域名",trigger:"blur"}],status:[{message:"请输入状态",trigger:"blur",required:!0}]}}},mounted:function(){this.getData()},methods:{getData:function(){},submitForm:function(e,t){var a=this,s=this,i={};this.$refs[e].validate(function(e){if(!e)return!1;i="添加"===t?{busicode:"appVersionAdd",data:a.ruleForm}:{busicode:"appVersionUpdate",data:a.ruleForm},a.$api.fetch({params:i}).then(function(e){s.$message({showClose:!0,message:"保存成功",type:"success"}),s.$parent.selectTSubSystem(),s.$parent.closeDialog()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","appVersionAdd",this.$parent.closeDialog)},editData:function(e){this.ruleForm=e}}},s,!1,function(e){a("jsyM")},null,null).exports},data:function(){return{tableShow:!0,tableQuery:{page:1,pageCount:50},maxHeight:0,appVersionData:[],formData:{versionCode:"",versionName:"",comments:"",domain:"",status:1},crumbsData:{titleList:[{title:"系统管理",path:"/ChangeTables"},{title:"应用程序版本",method:function(){window.histroy.back()}}]},appVersionShow:!0,appVersionAddVisible:!1}},mounted:function(){this.selectTSubSystem()},methods:{appAdd:function(e){var t=this;if("add"===e)this.$set(this.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),this.$refs.appVersionAdd.editData(this.formData),this.common.chargeObjectEqual(this,this.formData,"set","appVersionAdd");else{this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var a={busicode:"AppVersionList",data:{versionId:e.row.versionId}};this.$api.fetch({params:a}).then(function(e){t.$refs.appVersionAdd.editData(e[0]),t.common.chargeObjectEqual(t,e[0],"set","appVersionAdd")})}this.appVersionShow=!1,this.appVersionAddVisible=!0},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},formatStatus:function(e){return 1===e.status?"启用":"禁用"},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(e){this.tableQuery.page=e,this.selectTSubSystem()},selectTSubSystem:function(){var e=this;this.$api.fetch({params:{busicode:"appVersionList",data:{}}}).then(function(t){e.appVersionData=t,e.common.changeTable(e,".appVersion .kl-table",[])})},closeDialog:function(){this.appVersionShow=!0,this.appVersionAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.appVersionAdd.handleClose()},submit:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.appVersionAdd.submitForm(e,t)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},r={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"appVersion"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.appVersionShow,expression:"appVersionShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.appVersionAddVisible,expression:"appVersionAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submit("ruleFormappVersionAdd")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.appVersionShow,expression:"appVersionShow"}],staticClass:"kl-table"},[e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.appVersionData,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"versionCode",label:"版本编号","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"versionName",label:"版本名称","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"domain",label:"域名","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"statusName","min-width":"80",label:"状态",formatter:e.formatStatus}}),e._v(" "),a("el-table-column",{attrs:{prop:"comments",label:"描述","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.appAdd(t)}}},[e._v("编辑")])]}}],null,!1,1191321586)})],1):e._e()],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.appVersionAddVisible,expression:"appVersionAddVisible"}]},[a("appVersionAdd",{ref:"appVersionAdd"})],1)])])},staticRenderFns:[]};var o=a("VU/8")(i,r,!1,function(e){a("AiVP")},null,null);t.default=o.exports},jsyM:function(e,t){}});