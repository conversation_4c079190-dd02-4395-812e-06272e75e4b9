package com.koron.zys.baseConfig.bean;

public class PubCodeRuleBean {
	
	/**
	 * 主键
	 */
	private String id;
	
	/**
	 * 模块标志
	 */
	private String ruleCode;
	
	/**
	 * 模块名称
	 */
	private String ruleName;
	
	/**
	 * 编号规则
	 */
	private String ruleExp;
	
	/**
	 * 开始流水号
	 */
	private Integer startSerialNumber;
	
	/**
	 * 流水号自增
	 */
	private Integer serialNumberIncrement;
	
	/**
	 * 重置周期 (1 年  2 月  3 日,0无)
	 */
	private Integer resetCycle;
	
	


	public String getId() {
		return id;
	}


	public void setId(String id) {
		this.id = id;
	}


	public String getRuleCode() {
		return ruleCode;
	}


	public void setRuleCode(String ruleCode) {
		this.ruleCode = ruleCode;
	}


	public String getRuleName() {
		return ruleName;
	}


	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}


	public String getRuleExp() {
		return ruleExp;
	}


	public void setRuleExp(String ruleExp) {
		this.ruleExp = ruleExp;
	}


	public Integer getStartSerialNumber() {
		return startSerialNumber;
	}


	public void setStartSerialNumber(Integer startSerialNumber) {
		this.startSerialNumber = startSerialNumber;
	}


	public Integer getSerialNumberIncrement() {
		return serialNumberIncrement;
	}


	public void setSerialNumberIncrement(Integer serialNumberIncrement) {
		this.serialNumberIncrement = serialNumberIncrement;
	}


	public Integer getResetCycle() {
		return resetCycle;
	}


	public void setResetCycle(Integer resetCycle) {
		this.resetCycle = resetCycle;
	}


	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((ruleCode == null) ? 0 : ruleCode.hashCode());
		return result;
	}


	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PubCodeRuleBean other = (PubCodeRuleBean) obj;
		if (ruleCode == null) {
			if (other.ruleCode != null)
				return false;
		} else if (!ruleCode.equals(other.ruleCode))
			return false;
		return true;
	}
	
}
