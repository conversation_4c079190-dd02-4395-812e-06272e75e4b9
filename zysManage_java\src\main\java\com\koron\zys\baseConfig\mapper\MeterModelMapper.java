package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.baseConfig.queryBean.MsInfoQueryBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.koron.ebs.mybatis.EnvSource;

import com.koron.zys.baseConfig.bean.MeterModelBean;
import com.koron.zys.baseConfig.queryBean.MeterModelQueryBean;
import com.koron.zys.baseConfig.vo.MeterModelVO;

@EnvSource("_default")
public interface MeterModelMapper {
	
	
	/**
	 * 查询列表
	 * 
	 * @return
	 */
	List<MeterModelVO> selectMeterModelList(MeterModelQueryBean meterModelQueryBean);
	
	/**
	 * 根据条件获取型号信息
	 * 
	 * @return
	 */
	List<MeterModelBean> queryMeterModelList(MsInfoQueryBean msInfoQueryBean);
	
	/**
	 * 根据id查询
	 * @param id
	 * @return
	 */
	@Select("select * from pub_meter_model where id =#{id}")
	MeterModelBean selectMeterModelById(@Param("id") String id);
	/**
	 * 根据id查询
	 * @param id
	 * @return
	 */
	@Select("select * from pub_meter_model where DEVICE_TYPE =1 and METER_TYPE =2")
	List<MeterModelBean> getAll();

	List<MeterModelBean> selectMeterModelByIds(@Param("ids") List<String> ids);
	
	/**
	 * 根据水表编号查询厂家
	 */
	@Select("select factory_name from pub_meter_factory where id =#{meterFactory}")
	String selectFactoryById(@Param("meterFactory") String meterFactory);
	
	/**
	 * 根据name查询
	 * @param name
	 * @return
	 */
	@Select("select * from pub_meter_model where model_name = #{name} limit 1")
	MeterModelBean selectMeterModelByName(@Param("name") String name);
	
	/**
	 * 添加
	 * 
	 * @param meterModelBean
	 * @return
	 */
	void insertMeterModel(MeterModelBean meterModelBean);
	
	/**
	 * 校验字段内容重复
	 */
	@Select("select count(*) from pub_meter_model where ${key} = #{val}")
	Integer check(@Param("key") String key, @Param("val") String val);
	
	/**
	 * 校验字段内容重复-排除当前记录
	 */
	@Select("select count(*) from pub_meter_model where ${key} = #{val} and id <> #{id}")
	Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);
	
	/**
	 * 修改
	 * 
	 * @param meterModelBean
	 * @return
	 */
	Integer updateMeterModel(MeterModelBean meterModelBean);
	
	
}
