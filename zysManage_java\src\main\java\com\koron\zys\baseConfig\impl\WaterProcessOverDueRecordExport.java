package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;

import com.koron.common.bean.query.BaseQueryBean;
import com.koron.zys.baseConfig.bean.ProcessRecordBean;
import com.koron.zys.baseConfig.mapper.ProcessTimeMapper;
import com.koron.zys.common.dto.AttachmentDto;
import com.koron.zys.common.service.AbstractExportExcel;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.zys.serviceManage.utils.JsonUtils;


public class WaterProcessOverDueRecordExport extends AbstractExportExcel {

	@Override
	public AttachmentDto getAttachment(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		BaseQueryBean bean = JsonUtils.objectToPojo(req.getData(), BaseQueryBean.class);
		ProcessTimeMapper mapper = factory.getMapper(ProcessTimeMapper.class);
		List<ProcessRecordBean> list = mapper.selectRecordList(bean);	
		for(ProcessRecordBean record: list ) {
			if(record.getProcessState() == null || !"END".equals(record.getProcessState())) {
				record.setProcessState("未结束");
			}else {
				record.setProcessState("已结束");
			}
		}
		// 返回结果 FnInvoiceExport
		AttachmentDto attachment = new AttachmentDto();
		attachment.setBtlTemplate("WaterProcessOverDueRecordExport.btl");
		attachment.setFileTemplate("WaterProcessOverDueRecordExport.xlsx");
		attachment.setData(list);
		attachment.setFileType("excel");
		attachment.setFilename("流程超时记录报表" + CommonUtils.getCurrentDate());
		return attachment;
	}

}
