<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BaseAccessoryCofingMapper">

		
 	<select id="selectList" parameterType="com.koron.zys.baseConfig.queryBean.BaseAccessoryConfigQueryBean" resultType="com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean" >
 		SELECT
			t.accessory_no,
			t.create_account,
			t.create_name,
			t.create_time,
			t.id,
			t.process_node,
			t.receipt_type,
			t.required_flag,
			t.tenant_id
		FROM
			base_accessory_config t
		<where>
			<if test="receiptType != null and receiptType != ''">
				and receipt_type = #{receiptType, jdbcType=VARCHAR}
			</if>
			<if test="processNode != null and processNode != ''">
				and process_node = #{processNode, jdbcType=VARCHAR}
			</if>
			<if test="accessoryCode != null and accessoryCode != ''">
				and accessory_no = #{accessoryNo, jdbcType=VARCHAR}
			</if>
		</where>
	</select>
	
	<select id="selectById" parameterType="java.lang.String" resultType="com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean">
		SELECT
			t.accessory_no,
			t.create_account,
			t.create_name,
			t.create_time,
			t.id,
			t.process_node,
			t.receipt_type,
			t.required_flag,
			t.tenant_id
		FROM
			base_accessory_config t
		WHERE t.id = #{id, jdbcType=VARCHAR}
	</select>
	
	<select id="selectByReceiptType" parameterType="java.lang.String" resultType="com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean">
		SELECT
			t.accessory_no,
			t.create_account,
			t.create_name,
			t.create_time,
			t.id,
			t.process_node,
			t.receipt_type,
			t.required_flag,
			t.tenant_id
		FROM
			base_accessory_config t
		WHERE t.receipt_type = #{receiptType, jdbcType=VARCHAR}
	</select>
	
	<select id="selectByReceiptType2" parameterType="java.lang.String" resultType="com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean">
		SELECT
			t.accessory_no,
			t.receipt_type
		FROM
			base_accessory_config t

		WHERE t.receipt_type = #{receiptType, jdbcType=VARCHAR} group by t.accessory_no,t.receipt_Type
	</select>
	
	<select id="selectByAccessoryNo" parameterType="java.lang.String" resultType="com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean">
		SELECT
			t.accessory_no,
			t.create_account,
			t.create_name,
			t.create_time,
			t.id,
			t.process_node,
			t.receipt_type,
			t.required_flag,
			t.tenant_id
		FROM
			base_accessory_config t
		WHERE t.accessory_no = #{accessoryNo, jdbcType=VARCHAR}
	</select>
	
	<insert id="insert" parameterType="com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean">
		insert into base_accessory_config (
			id,
			accessory_no,
			create_account,
			create_name,
			create_time,
			process_node,
			receipt_type,
			required_flag,
			tenant_id
		)values(
			#{id,jdbcType=VARCHAR},
			#{accessoryNo,jdbcType=VARCHAR},
			#{createAccount,jdbcType=VARCHAR},
			#{createName,jdbcType=VARCHAR},
			now(),
			#{processNode,jdbcType=VARCHAR},
			#{receiptType,jdbcType=VARCHAR},
			#{requiredFlag,jdbcType=INTEGER},
			#{tenantId,jdbcType=VARCHAR}
		)
	</insert>
	
	<update id="update" parameterType="com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean">
		update base_accessory_config 
		<set>
			<if test="receiptType != null and receiptType != ''">
				receipt_type = #{receiptType, jdbcType=VARCHAR},
			</if>
			<if test="processNode != null and processNode != ''">
				process_node = #{processNode, jdbcType=VARCHAR},
			</if>
			<if test="accessoryNo != null and accessoryNo != ''">
				accessory_no = #{accessoryNo, jdbcType=VARCHAR},
			</if>
			<if test="requiredFlag != null">
				required_flag = #{requiredFlag, jdbcType=INTEGER},
			</if>
			<if test="updateName != null and updateName != ''">
				update_name = #{updateName, jdbcType=VARCHAR},
			</if>
			<if test="updateAccount != null and updateAccount != ''">
				update_account = #{updateAccount, jdbcType=VARCHAR},
			</if>
			update_time = now(),
		</set>
		where id = #{id, jdbcType=VARCHAR}
	</update>
	<insert id="insertBaseConfig" parameterType="com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean">
		insert into base_accessory_config (
			id,
			receipt_type,
			accessory_no,
			required_flag,
			create_account,
			create_name,
			create_time,
			tenant_id
		)values
		<foreach item="item" index="index" collection="list" separator=",">
			(
			#{item.id,jdbcType=VARCHAR},
			#{item.receiptType,jdbcType=VARCHAR},
			#{item.accessoryNo,jdbcType=VARCHAR},
			#{item.requiredFlag,jdbcType=VARCHAR},
			#{item.createAccount,jdbcType=VARCHAR},
			#{item.createName,jdbcType=VARCHAR},
			now(),
			#{item.tenantId}
			)
		</foreach>
		
	</insert>
	<select id="selectList1" parameterType="com.koron.zys.baseConfig.queryBean.BaseAccessoryConfigQueryBean" resultType="com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean" >
 		SELECT
			t.accessory_no,
			t.create_account,
			t.create_name,
			t.create_time,
			t.id,
			t.process_node,
			t.receipt_type,
			t.required_flag,
			t.tenant_id
		FROM
			base_accessory_config t
	</select>
	<delete id="delete">
		delete from base_accessory_config
	</delete>
</mapper>