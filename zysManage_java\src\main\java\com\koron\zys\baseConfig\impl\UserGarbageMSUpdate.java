package com.koron.zys.baseConfig.impl;

import java.util.List;


import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.GarbageUniversalBean;
import com.koron.zys.baseConfig.mapper.GarbageMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;


public class UserGarbageMSUpdate implements ServerInterface{

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			List<GarbageUniversalBean> list = JsonUtils.jsonToList(JsonUtils.objectToJson(req.getData()),GarbageUniversalBean.class);
			GarbageMapper mapper = factory.getMapper(GarbageMapper.class);
			for(GarbageUniversalBean bean : list) {
				if(bean.getId() != null && bean.getId() != "") {
					
		            bean.setUpdateName(userInfo.getUserInfo().getName());
					bean.setUpdateTime(CommonUtils.getCurrentTime());
					bean.setUid(new ObjectId().toHexString());
					mapper.updateMS(bean);
				}else {
					bean.setId(new ObjectId().toHexString());
		            bean.setCreateName(userInfo.getUserInfo().getName());
		    		bean.setCreateTime(CommonUtils.getCurrentTime());
		            mapper.insertMS(bean);
				}				
			}
						
		}catch(Exception e){
			logger.error("操作失败", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
