package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.ToolAccessoryBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.ToolAccessoryMapper;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;


/**
 * 工具下载页附件修改
 *
 * @author: lrk
 * @date: 2022-04-21 11:27
 * @description:
 */
public class ToolAccessoryUpdate implements ServerInterface {

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            ToolAccessoryBean bean = JsonUtils.objectToPojo(req.getData(), ToolAccessoryBean.class);
            //上传时间
            bean.setCreateTime(CommonUtils.getCurrentTime());
            ToolAccessoryMapper mapper = factory.getMapper(ToolAccessoryMapper.class,"_default");
            mapper.updateToolAccessory(bean);
            return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "修改成功", null);
        } catch (Exception e) {
            logger.error("工具页附件修改失败", e.getMessage(), e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, e.getMessage(), null);
        }
    }
}
