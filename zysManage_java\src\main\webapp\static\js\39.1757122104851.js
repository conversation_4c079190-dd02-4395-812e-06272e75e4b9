webpackJsonp([39],{FBFU:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=a("I6AS"),s={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"RoleManage"},[a("div",{staticClass:"role-left"},[a("div",{staticClass:"role-title"},[e._v("\n      角色\n      "),a("el-button",{staticClass:"el-icon-plus add-role",attrs:{type:"primary",size:"small"},on:{click:e.openAddDialog}},[e._v("新增角色")])],1),e._v(" "),a("div",{staticClass:"role-user"},e._l(e.userData,function(t){return a("dir",{key:t.roleCode,staticClass:"role-item"},[a("div",{staticClass:"role-item-name",attrs:{id:t.roleCode},on:{click:function(a){return e.getTreeData(t)}}},[e._v(e._s(t.roleName))]),e._v(" "),a("div",{staticClass:"role-item-delete el-icon-delete",attrs:{title:"删除"},on:{click:function(a){return e.userDetele(t)}}})])}),1)]),e._v(" "),a("div",{staticClass:"role-right"},[a("div",{staticClass:"role-title"},[e._v("\n      菜单功能权限\n      "),a("div",{staticClass:"save-btn"},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.save}},[e._v("保存")])],1)]),e._v(" "),a("div",{staticClass:"role-tree"},[a("el-tree",{ref:"tree",staticClass:"filter-tree department",attrs:{"check-strictly":!0,data:e.treeData,props:e.defaultProps,"node-key":"opCode","default-checked-keys":e.checkTreeData,"default-expanded-keys":[e.firstOpCode],accordion:"","show-checkbox":""},on:{check:e.treeNodeClick},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.node,s=t.data;return a("span",{staticClass:"custom-tree-node"},[a("span",{staticStyle:{display:"inline-block"}},[e._v(e._s(o.label))]),e._v(" "),a("span",{staticStyle:{display:"inline-block","margin-left":"30px"}},[a("el-checkbox-group",{model:{value:e.allBtnList,callback:function(t){e.allBtnList=t},expression:"allBtnList"}},e._l(s.btnList,function(t){return a("el-checkbox",{key:t.opCode,attrs:{label:t.opCode}},[e._v(e._s(t.opName))])}),1)],1)])}}])})],1)]),e._v(" "),a("el-dialog",{attrs:{title:"新增角色",visible:e.userAddShow,width:"30%"},on:{"update:visible":function(t){e.userAddShow=t}}},[a("el-form",{ref:"addForm",staticClass:"demo-form",attrs:{model:e.formData,rules:e.rules}},[a("el-form-item",{attrs:{label:"角色名称",prop:"roleName"}},[a("el-input",{attrs:{placeholder:"角色名称"},model:{value:e.formData.roleName,callback:function(t){e.$set(e.formData,"roleName",t)},expression:"formData.roleName"}})],1),e._v(" "),a("el-form-item")],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.userAdd}},[e._v("确 定")]),e._v(" "),a("el-button",{on:{click:function(t){e.userAddShow=!1}}},[e._v("取 消")])],1)],1)],1)},staticRenderFns:[]};var i=function(e){a("dl57")},n=a("VU/8")(o.a,s,!1,i,null,null);t.default=n.exports},I6AS:function(e,t,a){"use strict";(function(e){var o=a("Gu7T"),s=a.n(o);t.a={name:"RoleManage",data:function(){return{allBtnList:[],companyList:[],defaultProps:{children:"children",label:"opName"},firstOpCode:"",userData:[],clickUserData:{},userAddShow:!1,empowerShow:!1,listAddShow:!1,formData:{roleName:""},treeData:[],companyData:[],checkTreeData:[],rules:{roleName:[{required:!0,message:"请输入角色名称",trigger:"blur"}]},tableShow:!1,maxHeight:0,btnRoleName:""}},mounted:function(){var e=this;this.init(),this.$nextTick(function(){e.common.changeTable(e,".RoleManage",[".RoleManage .toolbar",".RoleManage .block"])})},methods:{init:function(){var e=this;this.$api.fetch({params:{busicode:"PubRoleList",data:{}}}).then(function(t){e.userData=t,e.getTreeData(e.userData[0])})},getTreeData:function(t){var a=this,o=this;setTimeout(function(){e(".role-item-name").css("color","black"),e("#"+t.roleCode).css("color","#3193f5")},0),this.clickUserData=t;var s={busicode:"RoleOperationList",data:{roleCode:t.roleCode}};this.$api.fetch({params:s}).then(function(e){o.treeData=e,o.firstOpCode=o.treeData[0].opCode,o.checkTreeData=[],a.initChecked(e)})},getCompanyData:function(e){var t=this;t.empowerShow=!0;this.$api.fetch({params:{busicode:"CompanyList",data:{}}}).then(function(e){t.companyData=e.list,console.log(t.companyData)})},initChecked:function(e){var t=this;e.forEach(function(e){e.checked&&t.checkTreeData.push(e.opCode),""!==e.btnList&&e.btnList.forEach(function(e){e.checked&&t.allBtnList.push(e.opCode)}),e.children&&e.children.length>0&&t.initChecked(e.children)})},save:function(){var e=this,t=[].concat(s()(this.allBtnList));if(this.$refs.tree.getCheckedNodes().forEach(function(e){t.push(e.opCode)}),t.length<=0)this.$message({message:"菜单不能为空！",type:"warning"});else{var a={busicode:"AddRoleOperation",data:{roleCode:this.clickUserData.roleCode,operation:t}};this.$api.fetch({params:a}).then(function(t){e.$message({message:"保存成功！",type:"success"})})}},submit:function(){var e=this,t=[].concat(s()(this.companyList));console.log(t);var a=[].concat(s()(this.allBtnList));if(console.log(a),this.$refs.tree.getCheckedNodes().forEach(function(e){a.push(e.opCode)}),this.$refs.tree.getCheckedNodes().forEach(function(e){t.push(e.companyNo)}),t.length<=0)this.$message({message:"菜单不能为空！",type:"warning"});else{var o={busicode:"BatchAddOrgOperation",data:{orgCodeList:t,operationCodeList:a}};this.$api.fetch({params:o}).then(function(t){e.$message({message:"保存成功！",type:"success"}),e.empowerShow=!1})}},openAddDialog:function(){this.userAddShow=!0},userAdd:function(){var e=this,t=this;this.$refs.addForm.validate(function(a){if(!a)return!1;var o={busicode:"AddRole",data:{roleName:e.formData.roleName}};e.$api.fetch({params:o}).then(function(a){t.userAddShow=!1,e.$message({message:"新增角色："+t.formData.roleName+"成功！",type:"success"}),t.init()})})},userDetele:function(e){var t=this,a=this;this.$confirm("是否确定删除角色："+e.roleName+"？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var o={busicode:"DeleteRole",data:{roleCode:e.roleCode}};t.$api.fetch({params:o}).then(function(e){a.$message({type:"success",message:"删除成功!"}),a.init()})}).catch(function(){a.$message({type:"info",message:"已取消删除"})})},treeNodeClick:function(e,t){var a=this,o=t.checkedNodes,s=(t.checkedKeys,this.$refs.tree.getNode(e.opCode)),i=this.$refs.tree.getCheckedKeys();if(s.checked)for(var n=s.level;n>1;n--)s.parent.checked||(s=s.parent,i.push(s.data.opCode));this.$refs.tree.setCheckedKeys(i),this.treeData.forEach(function(t){a.checkNodeClick(t,e,o.some(function(t){return t.opCode===e.opCode}))})},checkNodeClick:function(e,t,a){var o=this;e.children&&0!==e.children.length&&e.children.forEach(function(s){s.opCode!==t.opCode&&e.opCode!==t.opCode?o.checkNodeClick(s,t,a):o.toggleChildrenClick(s,a)})},toggleChildrenClick:function(e,t){var a=this;e.btnList&&e.btnList.length>0&&this.toggleBtnClick(e,t),this.$refs.tree.setChecked(e.opCode,t),e.children&&0!==e.children.length&&e.children.forEach(function(e){a.$refs.tree.setChecked(e.opCode,t),a.toggleChildrenClick(e,t)})},toggleBtnClick:function(e,t){var a=this;t?(e.btnList.forEach(function(e){a.allBtnList.push(e.opCode)}),e.companyData.forEach(function(e){a.companyList.push(e.companyNo)})):(this.allBtnList=this.allBtnList.filter(function(t){return e.btnList.every(function(e){return e.opCode!==t})}),this.companyList=this.companyList.filter(function(t){return e.companyData.every(function(e){return e.companyNo!==t})}))}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}}}).call(t,a("7t+N"))},dl57:function(e,t){}});