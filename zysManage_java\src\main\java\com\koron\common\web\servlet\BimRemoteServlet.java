package com.koron.common.web.servlet;

import com.banboocloud.Codec.BamboocloudFacade;
import com.koron.common.web.service.BimRemoteService;
import com.koron.common.web.util.BamboocloudUtils;
import com.koron.zys.ApplicationConfig;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.ADOConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.swan.bean.MessageBean;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 竹云统一用户平台对接
 */
@Controller
public class BimRemoteServlet {

    private static Logger logger = LoggerFactory.getLogger(BimRemoteServlet.class);

    private static final String type = "SM4";

    private BimRemoteService bimRemoteService;

    @Autowired
    public void setBimRemoteService(BimRemoteService bimRemoteService) {
        this.bimRemoteService = bimRemoteService;
    }

    /**
     * 元数据创建
     * @param request
     * @return
     */
    @RequestMapping(value = "/SchemaService", method = RequestMethod.POST)
    @ResponseBody
    public String schemaService(HttpServletRequest request){
        try {
            String body = BamboocloudUtils.getRequestBody(request);
            body = BamboocloudUtils.getPlaintext(body, ApplicationConfig.getIamKey(), type);
            Map<String, String> dataMap = JsonUtils.stringToMap(body);
            Map<String, Object> schemaMap = ADOConnection.runTask(factory -> bimRemoteService.schemaService(dataMap), Map.class);
            String mapJson = JsonUtils.objectToJson(schemaMap);
            return BamboocloudFacade.encrypt(mapJson, ApplicationConfig.getIamKey(), type);
        }catch (Exception exception){
            logger.error("【SchemaService】接口出现异常，原因：{}", exception);
        }
        return null;
    }

    /**
     * 组织部门数据创建
     * @param request
     * @return
     */
    @RequestMapping(value = "/OrgCreateService", method = RequestMethod.POST)
    @ResponseBody
    public String orgCreateService(HttpServletRequest request){
        String body = BamboocloudUtils.getRequestBody(request);
        body = BamboocloudUtils.getPlaintext(body, ApplicationConfig.getIamKey(), type);
        Map<String, String> dataMap = JsonUtils.stringToMap(body);
        String bimRequestId = dataMap.get("bimRequestId");
        try {
            MessageBean<String> message = ADOConnection.runTask(factory -> bimRemoteService.orgCreateService(factory, dataMap), MessageBean.class);
            if (message.getCode() != Constant.MESSAGE_INT_SUCCESS){
                throw new RuntimeException(message.getDescription());
            }
            return ResponseEntity.success(bimRequestId, message.getData());
        }catch (Exception exception){
            logger.error("【OrgCreateService】接口出现异常，原因：{}", exception);
        }
        return ResponseEntity.fail(bimRequestId, "组织部门创建失败");
    }

    /**
     * 组织部门数据修改
     * @param request
     * @return
     */
    @RequestMapping(value = "/OrgUpdateService", method = RequestMethod.POST)
    @ResponseBody
    public String orgUpdateService(HttpServletRequest request){
        String body = BamboocloudUtils.getRequestBody(request);
        body = BamboocloudUtils.getPlaintext(body, ApplicationConfig.getIamKey(), type);
        Map<String, String> dataMap = JsonUtils.stringToMap(body);
        String bimRequestId = dataMap.get("bimRequestId");
        try {
            MessageBean message = ADOConnection.runTask(factory -> bimRemoteService.orgUpdateService(factory, dataMap), MessageBean.class);
            if (message.getCode() != Constant.MESSAGE_INT_SUCCESS){
                throw new RuntimeException(message.getDescription());
            }
            return ResponseEntity.success(bimRequestId);
        }catch (Exception exception){
            logger.error("【OrgUpdateService】接口出现异常，原因：{}", exception);
        }
        return ResponseEntity.fail(bimRequestId, "组织部门修改失败");
    }

    /**
     * 组织部门数据修改
     * @param request
     * @return
     */
    @RequestMapping(value = "/OrgDeleteService", method = RequestMethod.POST)
    @ResponseBody
    public String orgDeleteService(HttpServletRequest request){
        String body = BamboocloudUtils.getRequestBody(request);
        body = BamboocloudUtils.getPlaintext(body, ApplicationConfig.getIamKey(), type);
        Map<String, String> dataMap = JsonUtils.stringToMap(body);
        String bimRequestId = dataMap.get("bimRequestId");
        try {
            MessageBean message = ADOConnection.runTask(factory -> bimRemoteService.orgDeleteService(factory, dataMap), MessageBean.class);
            if (message.getCode() != Constant.MESSAGE_INT_SUCCESS){
                throw new RuntimeException(message.getDescription());
            }
            return ResponseEntity.success(bimRequestId);
        }catch (Exception exception){
            logger.error("【OrgDeleteService】接口出现异常，原因：{}", exception);
        }
        return ResponseEntity.fail(bimRequestId, "组织部门删除失败");
    }

    /**
     * 用户数据创建
     * @param request
     * @return
     */
    @RequestMapping(value = "/UserCreateService", method = RequestMethod.POST)
    @ResponseBody
    public String userCreateService(HttpServletRequest request){
        String body = BamboocloudUtils.getRequestBody(request);
        body = BamboocloudUtils.getPlaintext(body, ApplicationConfig.getIamKey(), type);
        Map<String, String> dataMap = JsonUtils.stringToMap(body);
        String bimRequestId = dataMap.get("bimRequestId");
        try {
            MessageBean<String> message = ADOConnection.runTask(factory -> bimRemoteService.userCreateService(factory, dataMap), MessageBean.class);
            if (message.getCode() != Constant.MESSAGE_INT_SUCCESS){
                throw new RuntimeException(message.getDescription());
            }
            return ResponseEntity.success(bimRequestId, message.getData());
        }catch (Exception exception){
            logger.error("【UserCreateService】接口出现异常，原因：{}", exception);
        }
        return ResponseEntity.fail(bimRequestId, "用户数据新增失败");
    }

    /**
     * 用户数据修改
     * @param request
     * @return
     */
    @RequestMapping(value = "/UserUpdateService", method = RequestMethod.POST)
    @ResponseBody
    public String userUpdateService(HttpServletRequest request){
        String body = BamboocloudUtils.getRequestBody(request);
        body = BamboocloudUtils.getPlaintext(body, ApplicationConfig.getIamKey(), type);
        Map<String, String> dataMap = JsonUtils.stringToMap(body);
        String bimRequestId = dataMap.get("bimRequestId");
        try {
            MessageBean message = ADOConnection.runTask(factory -> bimRemoteService.userUpdateService(factory, dataMap), MessageBean.class);
            if (message.getCode() != Constant.MESSAGE_INT_SUCCESS){
                throw new RuntimeException(message.getDescription());
            }
            return ResponseEntity.success(bimRequestId);
        }catch (Exception exception){
            logger.error("【UserUpdateService】接口出现异常，原因：{}", exception);
        }
        return ResponseEntity.fail(bimRequestId, "用户数据修改失败");
    }

    /**
     * 用户数据删除
     * @param request
     * @return
     */
    @RequestMapping(value = "/UserDeleteService", method = RequestMethod.POST)
    @ResponseBody
    public String userDeleteService(HttpServletRequest request){
        String body = BamboocloudUtils.getRequestBody(request);
        body = BamboocloudUtils.getPlaintext(body, ApplicationConfig.getIamKey(), type);
        Map<String, String> dataMap = JsonUtils.stringToMap(body);
        String bimRequestId = dataMap.get("bimRequestId");
        try {
            MessageBean message = ADOConnection.runTask(factory -> bimRemoteService.userDeleteService(factory, dataMap), MessageBean.class);
            if (message.getCode() != Constant.MESSAGE_INT_SUCCESS){
                throw new RuntimeException(message.getDescription());
            }
            return ResponseEntity.success(bimRequestId);
        }catch (Exception exception){
            logger.error("【UserDeleteService】接口出现异常，原因：{}", exception);
        }
        return ResponseEntity.fail(bimRequestId, "用户数据删除失败");
    }

    public static class ResponseEntity{

        private String bimRequestId;

        private String resultCode;

        private String message;

        private String uid;

        public ResponseEntity(String bimRequestId, String resultCode, String message){
            this.bimRequestId = bimRequestId;
            this.resultCode = resultCode;
            this.message = message;
        }

        public static String success(String bimRequestId){
            ResponseEntity responseEntity = new ResponseEntity(bimRequestId, "0", "success");
            String mapJson = JsonUtils.objectToJson(responseEntity);
            return BamboocloudFacade.encrypt(mapJson, ApplicationConfig.getIamKey(), type);
        }

        public static String success(String bimRequestId, String uid){
            ResponseEntity responseEntity = new ResponseEntity(bimRequestId, "0", "success");
            responseEntity.setUid(uid);
            String mapJson = JsonUtils.objectToJson(responseEntity);
            return BamboocloudFacade.encrypt(mapJson, ApplicationConfig.getIamKey(), type);
        }

        public static String fail(String bimRequestId, String message){
            ResponseEntity responseEntity = new ResponseEntity(bimRequestId, "500", message);
            String mapJson = JsonUtils.objectToJson(responseEntity);
            return BamboocloudFacade.encrypt(mapJson, ApplicationConfig.getIamKey(), type);
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getBimRequestId() {
            return bimRequestId;
        }

        public void setBimRequestId(String bimRequestId) {
            this.bimRequestId = bimRequestId;
        }

        public String getResultCode() {
            return resultCode;
        }

        public void setResultCode(String resultCode) {
            this.resultCode = resultCode;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

}
