package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.baseConfig.bean.QuickChangeConfigBean;
import org.apache.ibatis.annotations.Param;

public interface QuickChangeConfigMapper {
	void update(QuickChangeConfigBean bean);
	List<QuickChangeConfigBean> select();
	List<QuickChangeConfigBean> selectCodeNameByType(String type);
	List<QuickChangeConfigBean> selectCodeNameByUser();
	void delete(@Param("code") String code ,@Param("type") String type);
	void add(QuickChangeConfigBean quickChangeConfigBean);

	/**
	 * 验证快速变更是否勾选如下字段：【银行账号】、【电子邮箱】、【托号】、【开户银行】、【开户名称】
	 * @return
	 */
	List<QuickChangeConfigBean> selectBillInfoData();
}
