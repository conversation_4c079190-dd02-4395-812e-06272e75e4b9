package com.koron.common.web.servlet;

import com.koron.common.bean.query.StaffQueryBean;
import com.koron.common.web.service.DepartmentService;
import com.koron.common.web.service.SyncDepartmentNewTask;
import oracle.jdbc.proxy.annotation.Post;
import org.koron.ebs.mybatis.ADOConnection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

//@RestController
public class DepartmentServlet {

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private SyncDepartmentNewTask syncDepartmentNewTask;

    @RequestMapping("/dep/json.htm")
    public String departmentJson(@RequestBody Map<String,String> params) {

        String id = null;
        String type = null;
        if (params != null){
            id = params.get("id");
            type = params.get("type");
        }
        return ADOConnection.runTask(departmentService,"list",String.class,id,type);
    }

    @PostMapping("/syncUser.htm")
    public String syncUser(){
        syncDepartmentNewTask.syncStaffTask();

        return "成功";
    }

    @PostMapping("/syncDept.htm")
    public String syncDept(){
        syncDepartmentNewTask.syncDepartmentTask();
        return "成功";
    }

    @PostMapping("/dep/staff.htm")
    public String listStaff(@RequestBody Map<String,String> params) {

        String departmentCode = null;
        String name = null;
        if (params != null){
            departmentCode = params.get("departmentCode");
            name = params.get("name");
        }
        return ADOConnection.runTask(departmentService,"listStaff",String.class,departmentCode,name);
    }

    @RequestMapping("/staffquery.htm")
    public String query(StaffQueryBean query){
        return ADOConnection.runTask(departmentService,"query",String.class,query);
    }

//    @RequestMapping("/departmentquery")
//    public String departmentQuery(@RequestParam("q") String name)
//    {
//        MessageBean<List<DepartmentTreeBean>> ret = new MessageBean<>();
//        ret.setCode(0);
//        ret.setDescription("获取成功");
//        List<DepartmentTreeBean> list = new ArrayList<>();
//        ret.setData(list);
//        int count = 0;
//        for (Map.Entry<String,DepartmentTreeBean> staff : Constant.DEPARTMENT.entrySet()) {
//            if(staff.getKey().indexOf(name) != -1)
//            {
//                list.add(staff.getValue());
//                count++;
//            }
//            if(count == 200)
//                break;
//        }
//        return ret.toJson();
//    }

}
