package com.koron.common.web.service;

import com.koron.common.web.bean.DepartmentBean;
import com.koron.common.web.bean.StaffBean;
import com.koron.common.web.bean.StaffDepartmentRelationBean;
import com.koron.common.web.mapper.DepartmentMapper;
import com.koron.common.web.mapper.StaffDepartmentRelationMapper;
import com.koron.common.web.mapper.StaffMapper;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.apache.commons.io.IOUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.springframework.stereotype.Service;
import org.swan.bean.MessageBean;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 竹云统一用户平台接口实现
 */
@Service
public class BimRemoteService {

    /**
     * 获取第三方目标系统中“账号”，“组织机构”等对象全部属性信息，
     * 包括属性名称、类型、是否必填字段、是否多值。
     * 用以建立起BIM组织机构、自然人字段和第三方目标系统组织机构及账号字段的映射关系
     * @return
     */
    public Map<String, Object> schemaService(Map<String, String> paramMap){
        Map<String, Object> schemaMap = new HashMap<String,Object>();
        schemaMap.put("bimRequestId", paramMap.get("bimRequestId"));
        try (InputStream inputStream = BimRemoteService.class.getClassLoader().
                getResourceAsStream("SchemaAttribute.json");){
            String data = IOUtils.toString(inputStream, "UTF-8");
            schemaMap.putAll(JsonUtils.stringToMap(data));
        }catch (Exception e){
            throw new RuntimeException("获取字段属性配置文件失败");
        }
        return schemaMap;
    }

    /**
     * 应用系统的组织机构创建
     * @return
     */
    public MessageBean<String> orgCreateService(SessionFactory factory, Map<String, String> paramMap){
        DepartmentBean department = new DepartmentBean();
        department.setCode(Objects.toString(paramMap.get("code")));
        department.setName(Objects.toString(paramMap.get("name")));
        department.setState(Objects.equals("true", paramMap.get("state")) ? 1 : 0);
        if (paramMap.containsKey("parentcode")){
            department.setParentcode(Objects.toString(paramMap.get("parentcode")));
        }
        if (paramMap.containsKey("sn")){
            department.setSn(Integer.parseInt(paramMap.get("sn").toString()));
        }
        if (paramMap.containsKey("shortname")){
            department.setShortname(Objects.toString(paramMap.get("shortname")));
        }
        if (paramMap.containsKey("description")){
            department.setDescription(Objects.toString(paramMap.get("description")));
        }
        DepartmentMapper departmentMapper = factory.getMapper(DepartmentMapper.class);
        departmentMapper.insertDepartment(department); // 插入部门信息
        MessageBean<String> message = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", String.class);
        message.setData(department.getId().toString());
        return message;
    }

    /**
     * 应用系统的组织机构修改
     * @return
     */
    public MessageBean<?> orgUpdateService(SessionFactory factory, Map<String, String> paramMap){
        DepartmentMapper departmentMapper = factory.getMapper(DepartmentMapper.class);
        String deptId = Objects.toString(paramMap.get("bimOrgId"));
        DepartmentBean department = departmentMapper.getDepartmentInfoById(Integer.parseInt(deptId));
        if (department == null){
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "未获取到【" + deptId + "】部门信息", String.class);
        }
        if (paramMap.containsKey("code")){
            department.setCode(Objects.toString(paramMap.get("code")));
        }
        if (paramMap.containsKey("name")){
            department.setName(Objects.toString(paramMap.get("name")));
        }
        if (paramMap.containsKey("state")){
            department.setState(Objects.equals("true", paramMap.get("state")) ? 1 : 0);
        }
        if (paramMap.containsKey("__ENABLE__")){
            department.setState(Objects.equals("true", paramMap.get("__ENABLE__")) ? 0 : 1);
        }
        if (paramMap.containsKey("parentcode")){
            department.setParentcode(Objects.toString(paramMap.get("parentcode")));
        }
        if (paramMap.containsKey("sn")){
            department.setSn(Integer.parseInt(paramMap.get("sn")));
        }
        if (paramMap.containsKey("shortname")){
            department.setShortname(Objects.toString(paramMap.get("shortname")));
        }
        if (paramMap.containsKey("description")){
            department.setDescription(Objects.toString(paramMap.get("description")));
        }
        departmentMapper.updateDepartment(department); // 修改部门信息
        MessageBean<String> message = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", String.class);
        message.setData(deptId);
        return message;
    }

    /**
     * 应用系统的组织机构删除
     * @return
     */
    public MessageBean<String> orgDeleteService(SessionFactory factory, Map<String, String> paramMap){
        DepartmentMapper departmentMapper = factory.getMapper(DepartmentMapper.class);
        String deptId = Objects.toString(paramMap.get("bimOrgId"));
        departmentMapper.deleteById(Integer.parseInt(deptId)); // 删除部门信息
        MessageBean<String> message = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", String.class);
        message.setData(deptId);
        return message;
    }

    /**
     * 应用系统的账号创建
     * @return
     */
    public MessageBean<String> userCreateService(SessionFactory factory, Map<String, String> paramMap){
        StaffBean staff = new StaffBean();
        staff.setCode(Objects.toString(paramMap.get("loginname")));
        staff.setName(Objects.toString(paramMap.get("name")));
        staff.setStatus(Objects.equals("false", paramMap.get("status")) ? 0 : 1);
        staff.setDepartmentCode(Objects.toString(paramMap.get("departmentCode")));
        staff.setLoginid(Objects.toString(paramMap.get("loginname")));
        if (paramMap.containsKey("phone")){
            staff.setPhone(paramMap.get("phone"));
        }
        if (paramMap.containsKey("mobile")){
            staff.setMobile(Objects.toString(paramMap.get("mobile")));
        }
        if (paramMap.containsKey("email")){
            staff.setEmail(Objects.toString(paramMap.get("email")));
        }
        DepartmentMapper departmentMapper = factory.getMapper(DepartmentMapper.class);
        DepartmentBean department = departmentMapper.getDepartmentInfoById(Integer.parseInt(staff.getDepartmentCode()));
        if (department == null){
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "部门信息为空", String.class);
        }
        staff.setDepartmentName(department.getName());
        staff.setDepartmentCode(department.getCode());
        StaffMapper staffMapper = factory.getMapper(StaffMapper.class);
        StaffBean oldStaff = staffMapper.getStaff(staff.getLoginid());
        if (oldStaff == null){
            staffMapper.insertStaff(staff); // 插入人员信息
        }else {
            staff.setId(oldStaff.getId());
        }
        StaffDepartmentRelationBean relation = new StaffDepartmentRelationBean(staff.getCode(), staff.getDepartmentCode());
        StaffDepartmentRelationMapper relationMapper = factory.getMapper(StaffDepartmentRelationMapper.class);
        relationMapper.deleteByUserAndDept(relation.getUserCode(), relation.getDepartmentCode());
        relationMapper.insertStaffDepartment(relation); // 插入部门人员关系
        MessageBean<String> message = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", String.class);
        message.setData(staff.getId().toString());
        return message;
    }

    /**
     * 应用系统的账号修改
     * @return
     */
    public MessageBean<String> userUpdateService(SessionFactory factory, Map<String, String> paramMap){
        StaffMapper staffMapper = factory.getMapper(StaffMapper.class);
        StaffDepartmentRelationMapper relationMapper = factory.getMapper(StaffDepartmentRelationMapper.class);
        String staffId = Objects.toString(paramMap.get("bimUid"));
        StaffBean staff = staffMapper.getStaffById(Integer.parseInt(staffId));
        if (staff == null){
            throw new RuntimeException("未获取到【" + staffId + "】人员信息");
        }
        if (paramMap.containsKey("code")){
            staff.setCode(Objects.toString(paramMap.get("code")));
        }
        if (paramMap.containsKey("name")){
            staff.setName(Objects.toString(paramMap.get("name")));
        }//__ENABLE__
        if (paramMap.containsKey("status")){
            staff.setStatus(Objects.equals("true", paramMap.get("status")) ? 0 : 1);
        }
        if (paramMap.containsKey("__ENABLE__")){
            staff.setStatus(Objects.equals("true", paramMap.get("__ENABLE__")) ? 0 : 1);
        }
        if (paramMap.containsKey("departmentCode")){
            staff.setDepartmentCode(Objects.toString(paramMap.get("departmentCode")));
        }
        if (paramMap.containsKey("loginname")){
            staff.setLoginname(Objects.toString(paramMap.get("loginname")));
        }
        if (paramMap.containsKey("phone")){
            staff.setPhone(paramMap.get("phone"));
        }
        if (paramMap.containsKey("mobile")){
            staff.setMobile(Objects.toString(paramMap.get("mobile")));
        }
        if (paramMap.containsKey("email")){
            staff.setEmail(Objects.toString(paramMap.get("email")));
        }
        staffMapper.updateStaff(staff); // 修改人员信息

        StaffDepartmentRelationBean relation = new StaffDepartmentRelationBean(staff.getCode(), staff.getDepartmentCode());
        relationMapper.deleteByUserAndDept(staff.getCode(), staff.getDepartmentCode()); // 先删除旧的
        relationMapper.insertStaffDepartment(relation); // 插入新的
        MessageBean<String> message = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", String.class);
        message.setData(staffId);
        return message;
    }

    /**
     * 应用系统的账号删除
     * @return
     */
    public MessageBean<String> userDeleteService(SessionFactory factory, Map<String, String> paramMap){
        StaffMapper staffMapper = factory.getMapper(StaffMapper.class);
        String uid = Objects.toString(paramMap.get("bimUid"));
        StaffBean staff = staffMapper.getStaffById(Integer.parseInt(uid));
        staffMapper.deleteById(Integer.parseInt(uid));     // 直接删除

        StaffDepartmentRelationMapper relationMapper = factory.getMapper(StaffDepartmentRelationMapper.class);
        relationMapper.deleteByUser(staff.getCode());

        MessageBean<String> message = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", String.class);
        message.setData(uid);
        return message;
    }
}
