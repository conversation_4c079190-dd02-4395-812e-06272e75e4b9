package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MfactoryListBean;
import com.koron.zys.baseConfig.mapper.MfactoryListMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import java.util.ArrayList;
import java.util.List;

/**
* 水表厂家下拉框
* <AUTHOR>
* @version 创建时间：2018年9月17日 上午11:36:30
*/
public class SelectMfactoryList implements ServerInterface {
	private static final Logger logger = LoggerFactory.getLogger(SelectMfactoryList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			List<MfactoryListBean> beans = new ArrayList<MfactoryListBean>();
			MfactoryListMapper mapper = factory.getMapper(MfactoryListMapper.class);
			beans = mapper.findMfactoryList(userInfo.getCurWaterCode());
			info.setCode(Constant.MESSAGE_INT_SUCCESS);
			info.setDescription("success");
			info.setData(beans);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_DBFAIL);
			info.setDescription("数据库异常");
			logger.error("数据库异常",e);
			factory.close(false);
		}
		return info;
	}

}
