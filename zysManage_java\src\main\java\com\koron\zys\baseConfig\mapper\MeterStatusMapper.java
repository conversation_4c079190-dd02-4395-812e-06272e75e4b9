package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.koron.ebs.mybatis.EnvSource;

import com.koron.zys.baseConfig.bean.MeterStatusBean;
import com.koron.zys.baseConfig.queryBean.MeterStatusQueryBean;
import com.koron.zys.baseConfig.vo.MeterStatusVO;
import com.koron.zys.baseConfig.vo.SelectVO;

@EnvSource("_default")
public interface MeterStatusMapper {
	
	/**
	 * 查询列表
	 * 
	 * @return
	 */
	List<MeterStatusVO> selectMeterStatusList(MeterStatusQueryBean meterStatusQueryBean);
	
	/**
	 * 根据id查询
	 * @param factoryId
	 * @return
	 */
	MeterStatusBean selectMeterStatusById(@Param("statusId") String statusId);
	
	/**
	 * 查询异常状态下拉框
	 * 
	 * @return
	 */
	List<SelectVO> selectComboBox(@Param("abnormalFlag") int abnormalFlag);

	/**
	 * 添加
	 * 
	 * @param MeterStatusBean
	 * @return
	 */
	void insertMeterStatus(MeterStatusBean meterStatusBean);
	/**
	 * 校验字段内容重复
	 */
	@Select("select count(*) from BASE_METER_STATUS where ${key} = #{val}")
	Integer check(@Param("key") String key, @Param("val") String val);
	
	/**
	 * 校验字段内容重复-排除当前记录
	 */
	@Select("select count(*) from BASE_METER_STATUS where ${key} = #{val} and status_id <> #{id}")
	Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);
	
	/**
	 * 修改
	 * 
	 * @param MeterStatusBean
	 * @return
	 */
	Integer updateMeterStatus(MeterStatusBean meterStatusBean);

}
