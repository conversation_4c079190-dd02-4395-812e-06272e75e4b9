webpackJsonp([47],{Phj5:function(e,t){},kxRM:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"billTypeManAdd"},[a("el-form",{ref:"billTypeManAddRuleForm",staticClass:"formBill-One",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px",inline:!0}},[a("el-form-item",{attrs:{label:"单据编号：",prop:"receiptNo"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.receiptNo,callback:function(t){e.$set(e.ruleForm,"receiptNo",t)},expression:"ruleForm.receiptNo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"单据名称：",prop:"receiptName"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.receiptName,callback:function(t){e.$set(e.ruleForm,"receiptName",t)},expression:"ruleForm.receiptName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"流程编号：",prop:"processCode"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.processCode,callback:function(t){e.$set(e.ruleForm,"processCode",t)},expression:"ruleForm.processCode"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"流程接口：",prop:"workflowInterface"}},[a("el-input",{attrs:{maxlength:"100",clearable:""},model:{value:e.ruleForm.workflowInterface,callback:function(t){e.$set(e.ruleForm,"workflowInterface",t)},expression:"ruleForm.workflowInterface"}}),e._v(" "),a("font",{staticStyle:{"text-align":"left",width:"100%",display:"block",color:"rgb(134, 140, 134)"}},[e._v("\n          如单据启用审批流，这里可配置审批流接口，以执行特殊操作")])],1),e._v(" "),a("el-form-item",{attrs:{label:"URL地址：",prop:"url"}},[a("el-input",{attrs:{maxlength:"100",clearable:""},model:{value:e.ruleForm.url,callback:function(t){e.$set(e.ruleForm,"url",t)},expression:"ruleForm.url"}}),e._v(" "),a("font",{staticStyle:{"text-align":"left",width:"100%",display:"block",color:"rgb(134, 140, 134)"}},[e._v("\n          VUE开发的项目为路由地址")])],1),e._v(" "),a("el-form-item",{attrs:{label:"启用附件：",prop:"startAccess"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.startAccess,callback:function(t){e.$set(e.ruleForm,"startAccess",t)},expression:"ruleForm.startAccess"}},[a("el-option",{key:1,attrs:{label:"是",value:"1"}}),e._v(" "),a("el-option",{key:2,attrs:{label:"否",value:"2"}})],1)],1)],1)],1)},staticRenderFns:[]},i={name:"billTypeMan",components:{billTypeManAdd:a("VU/8")({name:"billTypeManAdd",data:function(){return{ruleForm:{receiptNo:"",receiptName:"",workflowInterface:"",processCode:"",url:"",startAccess:""},rules:{receiptNo:[{required:!0,message:"请输入单据编号",trigger:"blur"}],processCode:[{required:!0,message:"请输入流程编号",trigger:"blur"}],receiptName:[{required:!0,message:"请输入单据名称",trigger:"blur"}]}}},mounted:function(){},methods:{resetForm:function(){this.$refs.billTypeManAddRuleForm.resetFields()},submitForm:function(e,t){var a=this,l=this,i={};this.$refs[e].validate(function(e){if(!e)return!1;i="添加"===t?{busicode:"ReceiptAdd",data:a.ruleForm}:{busicode:"ReceiptUpdate",data:a.ruleForm},a.$api.fetch({params:i}).then(function(e){l.$message({showClose:!0,message:"保存成功",type:"success"}),l.$parent.selectTSubSystem(),l.$parent.closeDialog(),a.resetForm()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","billTypeManAdd",this.$parent.closeDialog)},editData:function(e){this.ruleForm=e}}},l,!1,null,null,null).exports},data:function(){return{tableShow:!1,tableQuery:{page:1,pageCount:50},maxHeight:0,appServerData:[],crumbsData:{titleList:[{title:"基础配置",path:"/ChangeTables"},{title:"单据类型管理",method:function(){window.histroy.back()}}]},billTypeManShow:!0,billTypeManAddVisible:!1,formData:{receiptNo:"",receiptName:"",workflowInterface:"",url:""}}},mounted:function(){this.selectTSubSystem()},methods:{formatStatus:function(e){return 1===e.status?"启用":"禁用"},appAdd:function(e){var t=this;if(this.billTypeManShow=!1,this.billTypeManAddVisible=!0,"add"===e)this.$refs.billTypeManAdd.editData({receiptNo:"",receiptName:"",workflowInterface:"",url:""}),this.$set(this.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),this.common.chargeObjectEqual(this,this.formData,"set","billTypeManAdd");else{this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var a={busicode:"ReceiptQuery",data:{receiptId:e.row.receiptId}};this.$api.fetch({params:a}).then(function(e){t.$refs.billTypeManAdd.editData(e),t.common.chargeObjectEqual(t,e,"set","billTypeManAdd")})}},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(e){this.tableQuery.page=e,this.selectTSubSystem()},selectTSubSystem:function(){var e=this,t={busicode:"ReceiptList",data:this.tableQuery};this.$api.fetch({params:t}).then(function(t){e.appServerData=t,e.common.changeTable(e,".billTypeMan .kl-table",[".billTypeMan .block"])})},closeDialog:function(){this.billTypeManShow=!0,this.billTypeManAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.billTypeManAdd.handleClose()},submitForm:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.billTypeManAdd.submitForm(e,t)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},r={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"billTypeMan"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.billTypeManShow,expression:"billTypeManShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.billTypeManAddVisible,expression:"billTypeManAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("billTypeManAddRuleForm")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.billTypeManShow,expression:"billTypeManShow"}],staticClass:"kl-table"},[e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.appServerData.list,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"receiptNo",label:"单据编号","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"receiptName",label:"单据名称","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"url",label:"URL地址","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.appAdd(t)}}},[e._v("编辑")])]}}],null,!1,1191321586)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.appServerData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.billTypeManAddVisible,expression:"billTypeManAddVisible"}]},[a("billTypeManAdd",{ref:"billTypeManAdd"})],1)])])},staticRenderFns:[]};var s=a("VU/8")(i,r,!1,function(e){a("Phj5")},null,null);t.default=s.exports}});