package com.koron.util.secret;

import java.io.DataInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.util.Map;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HttpUtils {
	private static final String NEWLINE = "\r\n"; // 换行，或者说是回车

	private static final String BOUNDARY = "---------------------------7e222e13115bc"; // 固定的前缀

	private static final String PREFIX = "--";
	private static Logger logger = LoggerFactory.getLogger(HttpUtils.class);

	public static String sendPost(String url, String param, String orderType) throws Exception {
		PrintWriter out = null;
		String result = "";
		DataInputStream dis = null;
		// logger.debug("url=" + url + ";orderType=" + orderType + ";param=" + param);
		try {
			URL realUrl = new URL(url);
			// 打开和URL之间的连接
			URLConnection conn = realUrl.openConnection();
			conn.addRequestProperty("orderType", orderType);
			// 设置通用的请求属性
			conn.setRequestProperty("accept", "*/*");
			conn.setRequestProperty("connection", "Keep-Alive");
			conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
			conn.setRequestProperty("Content-Type", "application/Json; charset=UTF-8");
			conn.setConnectTimeout(6000);
			conn.setReadTimeout(50000);
			// conn.setRequestProperty("contentType", "UTF-8");
			// 发送POST请求必须设置如下两行
			conn.setDoOutput(true);
			conn.setDoInput(true);
			// 获取URLConnection对象对应的输出流
			// out = new PrintWriter(conn.getOutputStream());
			// // 发送请求参数
			// out.print(param);
			// // flush输出流的缓冲
			// out.flush();
			OutputStream os = conn.getOutputStream();
			os.write(param.getBytes("UTF-8"));
			os.flush();
			// 定义BufferedReader输入流来读取URL的响应
			byte[] bs = new byte[0];
			byte[] btemp = new byte[1024];
			int count = 0;
			dis = new DataInputStream(conn.getInputStream());
			while ((count = dis.read(btemp)) > -1) {
				byte[] temp = bs;
				bs = new byte[temp.length + count];
				System.arraycopy(temp, 0, bs, 0, temp.length);
				System.arraycopy(btemp, 0, bs, temp.length, count);
			}
			result = new String(bs, "UTF-8");
		}
		// 使用finally块来关闭输出流、输入流
		finally {
			try {
				if (out != null) {
					out.close();
				}
				if (dis != null) {
					dis.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}
		return result;
	}
	
	public static String sendGet(String url, Map<String, Object> param) throws Exception {
		// 创建Httpclient对象
		CloseableHttpClient httpclient = HttpClients.createDefault();
		String resultString = "";
		CloseableHttpResponse response = null;
		try {
			// 创建uri
			URIBuilder builder = new URIBuilder(url);
			if (param != null) {
				for (String key : param.keySet()) {
					builder.addParameter(key, param.get(key).toString());
				}
			}
			URI uri = builder.build();
			// 创建http GET请求
			HttpGet httpGet = new HttpGet(uri);
			// 执行请求
			response = httpclient.execute(httpGet);
			// 判断返回状态是否为200
			if (response.getStatusLine().getStatusCode() == 200) {
				resultString = EntityUtils.toString(response.getEntity(), "utf-8");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
				throw e;
			}
		}
		return resultString;
	}

	public static String sendGet(String url) throws Exception {
		return sendGet(url, null);
	}

	public static String getEncoding(String str) {
		String encode = "GB2312";
		try {
			if (str.equals(new String(str.getBytes(encode), encode))) { //判断是不是GB2312
				String s = encode;
				return s; //是的话，返回“GB2312“，以下代码同理
			}
		} catch (Exception exception) {
		}
		encode = "ISO-8859-1";
		try {
			if (str.equals(new String(str.getBytes(encode), encode))) { //判断是不是ISO-8859-1
				String s1 = encode;
				return s1;
			}
		} catch (Exception exception1) {
		}
		encode = "UTF-8";
		try {
			if (str.equals(new String(str.getBytes(encode), encode))) { //判断是不是UTF-8
				String s2 = encode;
				return s2;
			}
		} catch (Exception exception2) {
		}
		encode = "GBK";
		try {
			if (str.equals(new String(str.getBytes(encode), encode))) { //判断是不是GBK
				String s3 = encode;
				return s3;
			}
		} catch (Exception exception3) {
		}
		return "";
	}
}
