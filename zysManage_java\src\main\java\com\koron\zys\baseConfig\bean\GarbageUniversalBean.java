package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;

public class GarbageUniversalBean extends BaseBean{
	
	private String userNo;
	private String ctmName;
	private String ctmAddr;
	private String useWaterType;
	private String useWaterTypeName;
	
	private String garbateType;
	
	private String garbateTypeName;
	
	@Check(name = "水量占比", notNull = true)
	private double waterRatio;
	
	@Check(name = "垃圾费免收", notNull = true)
	private int garbateFree;
	private String freeBegin;
	private String freeEnd;
	
	private double monthFixedGarbage;			//每月定额垃圾费
	private double industrialFixedGarbage;		//工业定额垃圾费
	private double businessFixedGarbage;		//商业定额垃圾费
	private double monthTransportGarbage;		//每月垃圾清运费
	private double otherFixedGarbage;			//其它定额垃圾费
	
	private double fixedAmount;
	
	private String comments;			//垃圾费定价 备注
	
	private String mianShouComments;	//垃圾费免收 备注
	
	private String uid;
	
	private String changeItem;		//变更事项
	
	private String status;		//用户状态
	public double getFixedAmount() {
		return fixedAmount;
	}

	public void setFixedAmount(double fixedAmount) {
		this.fixedAmount = fixedAmount;
	}

	public String getMianShouComments() {
		return mianShouComments;
	}

	public void setMianShouComments(String mianShouComments) {
		this.mianShouComments = mianShouComments;
	}

	public String getChangeItem() {
		return changeItem;
	}

	public void setChangeItem(String changeItem) {
		this.changeItem = changeItem;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public double getMonthFixedGarbage() {
		return monthFixedGarbage;
	}

	public void setMonthFixedGarbage(double monthFixedGarbage) {
		this.monthFixedGarbage = monthFixedGarbage;
	}

	public double getIndustrialFixedGarbage() {
		return industrialFixedGarbage;
	}

	public void setIndustrialFixedGarbage(double industrialFixedGarbage) {
		this.industrialFixedGarbage = industrialFixedGarbage;
	}

	public double getBusinessFixedGarbage() {
		return businessFixedGarbage;
	}

	public void setBusinessFixedGarbage(double businessFixedGarbage) {
		this.businessFixedGarbage = businessFixedGarbage;
	}

	public double getMonthTransportGarbage() {
		return monthTransportGarbage;
	}

	public void setMonthTransportGarbage(double monthTransportGarbage) {
		this.monthTransportGarbage = monthTransportGarbage;
	}

	public double getOtherFixedGarbage() {
		return otherFixedGarbage;
	}

	public void setOtherFixedGarbage(double otherFixedGarbage) {
		this.otherFixedGarbage = otherFixedGarbage;
	}

	private String setupMeterAddr;
	
	
	
	public String getSetupMeterAddr() {
		return setupMeterAddr;
	}

	public void setSetupMeterAddr(String setupMeterAddr) {
		this.setupMeterAddr = setupMeterAddr;
	}

	public String getUserNo() {
		return userNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}

	public String getCtmName() {
		return ctmName;
	}

	public void setCtmName(String ctmName) {
		this.ctmName = ctmName;
	}

	public String getCtmAddr() {
		return ctmAddr;
	}

	public void setCtmAddr(String ctmAddr) {
		this.ctmAddr = ctmAddr;
	}

	public String getUseWaterType() {
		return useWaterType;
	}

	public void setUseWaterType(String useWaterType) {
		this.useWaterType = useWaterType;
	}

	public String getUseWaterTypeName() {
		return useWaterTypeName;
	}

	public void setUseWaterTypeName(String useWaterTypeName) {
		this.useWaterTypeName = useWaterTypeName;
	}

	public String getGarbateType() {
		return garbateType;
	}

	public void setGarbateType(String garbateType) {
		this.garbateType = garbateType;
	}

	public String getGarbateTypeName() {
		return garbateTypeName;
	}

	public void setGarbateTypeName(String garbateTypeName) {
		this.garbateTypeName = garbateTypeName;
	}

	public double getWaterRatio() {
		return waterRatio;
	}

	public void setWaterRatio(double waterRatio) {
		this.waterRatio = waterRatio;
	}

	public int getGarbateFree() {
		return garbateFree;
	}

	public void setGarbateFree(int garbateFree) {
		this.garbateFree = garbateFree;
	}



	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	public String getFreeBegin() {
		return freeBegin;
	}

	public void setFreeBegin(String freeBegin) {
		this.freeBegin = freeBegin;
	}

	public String getFreeEnd() {
		return freeEnd;
	}

	public void setFreeEnd(String freeEnd) {
		this.freeEnd = freeEnd;
	}
	
}
