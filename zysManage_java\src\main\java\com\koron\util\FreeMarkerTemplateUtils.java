package com.koron.util;

import java.util.Map;


import com.koron.util.TemplateConfig.BaseConfig;

public class FreeMarkerTemplateUtils {
	
	private FreeMarkerTemplateUtils(){
		
	}
	
   // private static final Configuration CONFIGURATION = new Configuration(Configuration.VERSION_2_3_22);

    static{
       /* CONFIGURATION.setTemplateLoader(new ClassTemplateLoader(FreeMarkerTemplateUtils.class, "/template/ftl/"));
        CONFIGURATION.setDefaultEncoding("UTF-8");
        CONFIGURATION.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
        CONFIGURATION.setCacheStorage(NullCacheStorage.INSTANCE);*/
    }
    
    public static void generate(BaseConfig config, Map<String, Object> data, String templateName) {
    	/*FileOutputStream fos = null;
		Writer out = null;
    	try {
	    	Template template = FreeMarkerTemplateUtils.getTemplate(templateName);
			String path = System.getProperty("user.dir") + File.separator + "src"  + File.separator + "main" + File.separator + "java" + File.separator + "com" + File.separator + "koron" + File.separator + "css2";
			String moduleName = config.getModuleName();
			String[] arr = moduleName.split("\\.", -1);
			for(String a : arr) {
				path = path + File.separator + a;
				File file = new File(path);
				if(!file.exists()) {
					file.mkdir();
				}
			}
			
			path = path + File.separator + config.getClassName();
			if(StringUtils.isNoneEmpty(config.getModuleType())) {
				path = path + getInitialsUpper(config.getModuleType());
			}
			path = path + "." + config.getFileSuffix();
			File file = new File(path);
			if(!file.exists()) {
				file.createNewFile();
			}
			data.put("datetime", DateUtils.parseDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			fos = new FileOutputStream(file);
			out = new BufferedWriter(new OutputStreamWriter(fos, "utf-8"),10240);
	        template.process(data, out);
		}catch(Exception ex) {
			ex.printStackTrace();
		}finally {
			if(fos != null) {
				try {
					fos.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if(out != null) {
				try {
					out.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}*/
    }

   /* public static Template getTemplate(String templateName) throws IOException {
        try {
            return CONFIGURATION.getTemplate(templateName);
        } catch (IOException e) {
            throw e;
        }
    }
    
    private static String getInitialsUpper(String str) {
		StringBuilder sb = new StringBuilder(str);
		sb.replace(0, 1, str.substring(0, 1).toUpperCase());
		return sb.toString();
	}

    public static void clearCache() {
        CONFIGURATION.clearTemplateCache();
    }*/

}
