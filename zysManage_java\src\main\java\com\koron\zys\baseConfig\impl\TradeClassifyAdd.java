package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.TradeClassifyBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.baseConfig.mapper.TradeClassifyMapper;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
/**
 * 行业分类-添加
 * 
 * <AUTHOR>
 *
 */
public class TradeClassifyAdd implements ServerInterface{

	private static Logger logger = LoggerFactory.getLogger(TradeClassifyAdd.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		TradeClassifyMapper mapper = factory.getMapper(TradeClassifyMapper.class,"_default");
		TradeClassifyBean bean = new TradeClassifyBean();
		try {
			bean = JsonUtils.objectToPojo(req.getData(), TradeClassifyBean.class);
		} catch (Exception e) {
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		// 校验字段重复
		if (mapper.check("trade_name", bean.getTradeName()) > 0) {
			return MessageBean.create(Constant.ILLEGAL_PARAMETER,"行业名称：" + bean.getTradeName() + "的信息已存在。", void.class);
		}
		bean.setCreateInfo(userInfo);
	
		mapper.insertTradeClassify(bean);
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
	
}
