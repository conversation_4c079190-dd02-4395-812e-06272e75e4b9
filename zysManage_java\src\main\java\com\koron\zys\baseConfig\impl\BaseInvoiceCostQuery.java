package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.BaseInvoiceCostMapper;
import com.koron.zys.baseConfig.vo.BaseInvoiceCostVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

public class BaseInvoiceCostQuery implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub
		BaseInvoiceCostMapper mapper = factory.getMapper(BaseInvoiceCostMapper.class);
		List<BaseInvoiceCostVO> list = mapper.select();
		
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		info.setData(list);
		return info;
	}

}
