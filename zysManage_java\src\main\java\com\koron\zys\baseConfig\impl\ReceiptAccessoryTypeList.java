package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean;
import com.koron.zys.baseConfig.bean.PubAccessoryTypeBean;
import com.koron.zys.baseConfig.mapper.BaseAccessoryCofingMapper;
import com.koron.zys.baseConfig.mapper.PubAccessoryTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

/**
 * 获取允许上传的单据附件类型
 * <AUTHOR>
 *
 */
public class ReceiptAccessoryTypeList implements ServerInterface{
	
	private static Logger log = LoggerFactory.getLogger(ReceiptAccessoryTypeList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			String receiptType = JsonUtils.objectToPojo(req.getData(), String.class);
			BaseAccessoryCofingMapper mapper = factory.getMapper(BaseAccessoryCofingMapper.class);
			PubAccessoryTypeMapper pubAccessoryTypeMapper = factory.getMapper(PubAccessoryTypeMapper.class);
			List<BaseAccessoryConfigBean> accessoryConfigs = mapper.selectByReceiptType(receiptType);
			for(BaseAccessoryConfigBean accessoryConfig : accessoryConfigs) {
				PubAccessoryTypeBean accessoryType = pubAccessoryTypeMapper.selectByAccessNo(accessoryConfig.getAccessoryNo());
				accessoryConfig.setAccessoryType(accessoryType.getAccessoryName());
			}
			info.setData(accessoryConfigs);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			log.error(e.getMessage(), e);
		}
		return info;
	}

}
