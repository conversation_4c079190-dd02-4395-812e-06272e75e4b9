package com.koron.common.mapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface PrintMapper {
	
	/**
	 * 根据模板id查业务id
	 * @param id
	 * @return
	 */
	public String getBusinessId(@Param("id") String id);

	/**
	 * 根据模板id查业务id
	 * @param id
	 * @return
	 */
	public String getBusinuessIdByCode(@Param("code") String code);
	

	/**
	 * 根据模板id查业务id
	 * @param id
	 * @return
	 */
	public ArrayList<HashMap<String,String>> getTemplateDefault(@Param("id") String id);

	/**
	 * 根据业务id查字段
	 * @param id
	 * @return
	 */
	public String getFieldList(@Param("id") String id);
	
	/**
	 * 根据模板id查模板样式文件
	 * @param id
	 * @return
	 */
	public String getTemplate(@Param("id") String id);
	
	/**
	 * 
	 * @param id
	 * @param template
	 * @return
	 */
	public int updateTemplate(@Param("id") String id,@Param("template") String template);
	
}