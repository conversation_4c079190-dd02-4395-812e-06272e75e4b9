package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.WaterChargeElfBean;
import com.koron.zys.baseConfig.mapper.WaterChargeElfMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

/**
 * 水费账单精灵保存
 */
public class WaterChargeElfSave implements ServerInterface {
    @Override
    @ValidationKey(clazz = WaterChargeElfBean.class,method="insert")
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            WaterChargeElfMapper mapper = factory.getMapper(WaterChargeElfMapper.class);
            //获取短信模板信息
            WaterChargeElfBean waterChargeElfBean=JsonUtils.objectToPojo(req.getData(), WaterChargeElfBean.class);
            if (null == waterChargeElfBean) {
                return MessageBean.create(Constant.NOT_NULL, "参数为空", void.class);
            }
            //先清空历史记录
            mapper.deleteAllWaterChargeElf();
            // 再添加新纪录
            waterChargeElfBean.setCreateInfo(userInfo);
            Integer integer = mapper.insertWaterChargeElf(waterChargeElfBean);
        } catch (Exception e) {
            factory.close(false);
            logger.error("水费账单精灵保存失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "水费账单精灵保存失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}
