package com.koron.business.bean;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 待办接口用的BEAN
 * 
 * <AUTHOR>
 *
 */
public class DealBean {

	private String result = "success";
	private Deal deal = new Deal();

	public void setResult(String result) {
		this.result = result;
	}

	public String getResult() {
		return result;
	}

	public void setDeal(Deal deal) {
		this.deal = deal;
	}

	public Deal getDeal() {
		return deal;
	}

	public static class Deal {

		private Pager pager = new Pager();
		private List<Data> data = new ArrayList<Data>();

		public void setPager(Pager pager) {
			this.pager = pager;
		}

		public Pager getPager() {
			return pager;
		}

		public void setData(List<Data> data) {
			this.data = data;
		}

		public List<Data> getData() {
			return data;
		}

	}

	public static class Data {
		/**
		 * 消息ID
		 */
		private String msgid;
		/**
		 * 消息标题
		 */
		private String msgtitle;
		/**
		 * 消息链接
		 */
		private String msglink;
		/**
		 * 业务系统原ID
		 */
		private String sysid;
		/**
		 * 用户账号
		 */
		private String useraccount;
		/**
		 * 消息时间戳
		 */
		private String msgtimestamp;
		/**
		 * 消息状态
		 */
		private int msgstate;

		public void setMsgid(String msgid) {
			this.msgid = msgid;
		}

		public String getMsgid() {
			return msgid;
		}

		public void setMsgtitle(String msgtitle) {
			this.msgtitle = msgtitle;
		}

		public String getMsgtitle() {
			return msgtitle;
		}

		public void setMsglink(String msglink) {
			this.msglink = msglink;
		}

		public String getMsglink() {
			return msglink;
		}

		public void setSysid(String sysid) {
			this.sysid = sysid;
		}

		public String getSysid() {
			return sysid;
		}

		public void setUseraccount(String useraccount) {
			this.useraccount = useraccount;
		}

		public String getUseraccount() {
			return useraccount;
		}

		public void setMsgtimestamp(String msgtimestamp) {
			this.msgtimestamp = msgtimestamp;
		}

		public String getMsgtimestamp() {
			return msgtimestamp;
		}

		public void setMsgstate(int msgstate) {
			this.msgstate = msgstate;
		}

		public int getMsgstate() {
			return msgstate;
		}

	}

	public static class Pager {

		@JsonProperty("recordCount")
		/**
		 * 总数
		 */
		private int recordcount;
		@JsonProperty("pageCount")
		/**
		 * 页数
		 */
		private int pagecount;
		@JsonProperty("curPage")
		/**
		 * 当前页
		 */
		private int curpage;
		@JsonProperty("pageSize")
		/**
		 * 每页数据条数
		 */
		private int pagesize;

		public Pager setRecordcount(int recordcount) {
			this.recordcount = recordcount;
			return this;
		}

		public int getRecordcount() {
			return recordcount;
		}

		public Pager setPagecount(int pagecount) {
			this.pagecount = pagecount;
			return this;
		}

		public int getPagecount() {
			return pagecount;
		}

		public Pager setCurpage(int curpage) {
			this.curpage = curpage;
			return this;
		}

		public int getCurpage() {
			return curpage;
		}

		public Pager setPagesize(int pagesize) {
			this.pagesize = pagesize;
			return this;
		}

		public int getPagesize() {
			return pagesize;
		}
	}
}
