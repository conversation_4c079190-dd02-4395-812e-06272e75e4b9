package com.koron.business.web;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.koron.business.bean.DealBean;
import com.koron.business.bean.DealBean.Data;
import com.koron.business.bean.DealBean.Deal;

@Controller
public class DealAction {
	/**
	 * 待办消息接口
	 * @param userAccount 账号
	 * @param curPage 当前页面
	 * @param pageSize 每页条数
	 * @param msgState 状态
	 * @return
	 */
	@RequestMapping("/deal.htm")
	@ResponseBody
	public String json(String userAccount, int curPage, int pageSize, int msgState) {
		DealBean bean = new DealBean();
		Deal deal = bean.getDeal();
		try(SessionFactory factory = new SessionFactory();){
			DealMapper mapper = factory.getMapper(DealMapper.class);
			List<Data> list = mapper.list(userAccount, (curPage-1)*pageSize, pageSize, msgState);
			int size = mapper.listCount(userAccount, (curPage-1)*pageSize, pageSize, msgState);
			//Deal deal = bean.getDeal();
			deal.getPager().setCurpage(curPage).setRecordcount(size).setPagecount((size-1)/pageSize+1).setPagesize(pageSize);
			deal.setData(list);
		}
		try {
			return new ObjectMapper().writeValueAsString(deal);
		} catch (JsonProcessingException ex) {
			ex.printStackTrace();
		}
		return "";
	}
}
