package com.koron.util;

import java.util.List;

public class TableInfo {
	
	private String name;
	
	private String remark;
	
	private List<FieldInfo> field;
	
	@Override
	public String toString() {
		return "TableInfo [name=" + name + ", remark=" + remark + ", field=" + field + "]";
	}

	public List<FieldInfo> getField() {
		return field;
	}

	public void setField(List<FieldInfo> field) {
		this.field = field;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
}
