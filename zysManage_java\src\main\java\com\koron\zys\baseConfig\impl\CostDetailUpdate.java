package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CostDetailBean;
import com.koron.zys.baseConfig.bean.CostDetailLadderBean;
import com.koron.zys.baseConfig.mapper.CostDetailMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;

/**
 * 费用明细-编辑
 * <AUTHOR>
 */
public class CostDetailUpdate implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(CostDetailUpdate.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			CostDetailMapper mapper = factory.getMapper(CostDetailMapper.class);
			CostDetailBean bean = JsonUtils.objectToPojo(req.getData(), CostDetailBean.class);
			// 校验字段重复
			if (mapper.check2("detail_name", bean.getDetailName(), bean.getCostDetailId()) > 0) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "明细名称：" + bean.getDetailName() + "的信息已存在。",
						void.class);
			}
			if (StringUtils.isNullOrEmpty(bean.getCostDetailId())) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
			}
			bean.setUpdateName(userInfo.getUserInfo().getName());
			bean.setUpdateTime(CommonUtils.getCurrentTime());
			// 修改之后的数据
			List<CostDetailLadderBean> ladderlist = bean.getLadderlist();
			// 查询编辑初始化的数据
			List<CostDetailBean> ladderlist1 = mapper.selectCostDetailById(bean.getCostDetailId());
			
			if (ladderlist.size() == 0 || ladderlist==null) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "所选阶梯内容不能为空！", void.class);
			}
			
			for (CostDetailBean ladderlist3 : ladderlist1) {
				List<CostDetailLadderBean> ladderlist4 = ladderlist3.getLadderlist();
				for (CostDetailLadderBean costOut : ladderlist4) {// 编辑初始化的数据集ladderlist4
					Boolean bo = true;
					for (CostDetailLadderBean costIn : ladderlist) { // 获取前端传过来的修改数据集ladderlist
						if(costIn.getBeginWater()==null || costIn.getCoefficient()==null || costIn.getEndWater()==null || costIn.getLadderBeginMoney()==null ||costIn.getPrice()==null ) {
							return MessageBean.create(Constant.ILLEGAL_PARAMETER, "价格阶梯填写内容不能为空！", void.class);
						}
						if (costOut.getCostDetailLadderId().equals(costIn.getCostDetailLadderId())) { // id值相等，做修改
							mapper.updateLadder(costIn);
							bo = false;
							break;
						}
					}
					if (bo) {
						mapper.delLadder(costOut.getCostDetailLadderId()); // 删除
					}
				}

				for (CostDetailLadderBean costOut : ladderlist) {// 获取前端传过来的修改数据集ladderlist
					Boolean bo = true;
					for (CostDetailLadderBean costIn : ladderlist4) { // 编辑初始化的数据集ladderlist4
						if (costIn.getCostDetailLadderId().equals(costOut.getCostDetailLadderId())) {
							bo = false;
							break;
						}
					}
					if (bo) {
						costOut.setCostDetailLadderId(new ObjectId().toHexString());
						costOut.setCostDetailId(bean.getCostDetailId());
						mapper.insertLadder(costOut); // id值没有相同的，添加
					}
				}
			}
			
			
			mapper.updateCostDetail(bean);
		} catch (Exception e) {
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
}