package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.baseConfig.bean.UseMatrTemplateBean;
import com.koron.zys.baseConfig.bean.UseMatrTemplateListBean;
import com.koron.zys.baseConfig.queryBean.UseMatrTemplateQueryBean;
import com.koron.zys.baseConfig.vo.UseMatrTemplateVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;



public interface UseMatrTemplateMapper {

    /**
     * 校验字段内容重复
     */
    @Select("select count(1) from BASE_USE_MATR_TEMP where ${key} = #{val}")
    Integer check(@Param("key") String key, @Param("val") String val);

    /**
     * 校验字段内容重复-排除当前记录
     */
    @Select("select count(1) from BASE_USE_MATR_TEMP where ${key} = #{val} and id <> #{id}")
    Integer check3(@Param("key") String key, @Param("val") String val, @Param("id") String id);

    /**
     * 根据用料模板id查询用料模板信息
     *
     * @param templateId
     * @return
     */
    @Select("select * from BASE_USE_MATR_TEMP where id =#{templateId}")
    UseMatrTemplateBean getMatrTemplate(@Param("templateId") String templateId);

    /**
     * 根据用料模板id查询明细信息
     * @param templateId
     * @return
     */
    @Select("Select * from BASE_USE_MATR_TEMP_LIST where template_id = #{templateId}")
    List<UseMatrTemplateListBean> getMatrTemplateListByTemplateId(@Param("templateId") String templateId);

    /**
     * 新增用料模板
     * @param bean
     * @return
     */
    Integer insertUseMatrTemplate(UseMatrTemplateBean bean);

    /**
     * 批量添加用料模板明细
     * @param matrTemplateList
     * @return
     */
    Integer insertUseMatrTemplateList(@Param("matrTemplateList") List<UseMatrTemplateListBean> matrTemplateList);

    /**
     * 查询用料模板列表
     * @param bean
     * @return
     */
    List<UseMatrTemplateVO> selectUseMatrTemplate(UseMatrTemplateQueryBean bean);


    /**
     * 根据模板id删除模板明细信息
     * @param templateId
     * @return
     */
    @Delete("delete from BASE_USE_MATR_TEMP_LIST where template_id = #{templateId}")
    Integer deleteMatrTemplateList(@Param("templateId") String templateId);

    /**
     * 更新用料模板信息
     * @param bean
     * @return
     */
    Integer updateUseMatrTemplate(UseMatrTemplateBean bean);

}
