package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.IncomeBankBean;
import com.koron.zys.baseConfig.mapper.IncomeBankMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import java.text.SimpleDateFormat;
import java.util.Date;

public class IncomeBankUpdate implements ServerInterface {

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            IncomeBankMapper mapper = factory.getMapper(IncomeBankMapper.class);
            IncomeBankBean bean = JsonUtils.objectToPojo(req.getData(), IncomeBankBean.class);
            String userName = userInfo.getUserInfo().getName();
            String account = userInfo.getUserInfo().getAcount();
            String nowTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            bean.setModifyAccount(account);
            bean.setModifyName(userName);
            bean.setModifyTime(nowTime);
            mapper.update(bean);
            MessageBean<?> msg = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", Void.class);
            return msg;
        } catch (Exception e) {
            logger.error("修改进账银行信息失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "修改进账银行信息失败", void.class);
        }
    }
}
