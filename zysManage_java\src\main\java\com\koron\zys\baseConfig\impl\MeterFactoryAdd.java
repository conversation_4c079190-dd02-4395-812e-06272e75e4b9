package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterFactoryBean;
import com.koron.zys.baseConfig.mapper.MeterFactoryMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

/**
 * 水表供应商-添加
 *
 * <AUTHOR>
 */
public class MeterFactoryAdd implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MeterFactoryAdd.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            MeterFactoryMapper mapper = factory.getMapper(MeterFactoryMapper.class);
            MeterFactoryBean bean = JsonUtils.objectToPojo(req.getData(), MeterFactoryBean.class);
            // 校验字段重复
            if (mapper.check("factory_name", bean.getFactoryName()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "厂家名称：" + bean.getFactoryName() + "的信息已存在。", void.class);
            }
            if (mapper.check("factory_full_name", bean.getFactoryFullName()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "厂家全称：" + bean.getFactoryFullName() + "的信息已存在。", void.class);
            }
            bean.setCreateInfo(userInfo);
            mapper.insertMeterFactory(bean);
        } catch (Exception e) {
            logger.error("水表供应商添加失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "水表供应商添加失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}