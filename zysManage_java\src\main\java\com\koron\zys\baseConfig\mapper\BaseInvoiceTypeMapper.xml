<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BaseInvoiceTypeMapper">
	<select id="selectList"
		resultType="com.koron.zys.baseConfig.vo.BaseInvoiceTypeListVO">
		select 
			id,
			invoice_no,
			invoice_name,
			comments,
			status,
			type,
			isPro,
			auto_print
		from BASE_INVOICE_TYPE
	</select>
	<select id="selectOnList"
		parameterType="com.koron.zys.baseConfig.bean.BaseInvoiceTypeBean"
		resultType="com.koron.zys.baseConfig.vo.BaseInvoiceTypeListVO">
		select 
			id,
			invoice_no,
			invoice_name,
			comments,
			status,
			type,
			isPro,
			auto_print
		from BASE_INVOICE_TYPE
		where 1=1 
		<if test="status!=null and status !=''">
			and status=#{status}
		</if>
		order by id desc
	</select>
	<insert id="insert"
		parameterType="com.koron.zys.baseConfig.bean.BaseInvoiceTypeBean">
		insert into BASE_INVOICE_TYPE(id,invoice_no,invoice_name,comments,status,create_time,create_account,
			create_name,type,isPro,auto_print)
		values(#{id},#{invoiceNo},#{invoiceName},#{comments},#{status},#{createTime},#{createAccount},
			#{createName},#{type},#{isPro},#{autoPrint})
	</insert>
	<update id="update"
		parameterType="com.koron.zys.baseConfig.bean.BaseInvoiceTypeBean">
		update BASE_INVOICE_TYPE set invoice_no=#{invoiceNo},invoice_name=#{invoiceName},
			comments=#{comments},status=#{status},update_time=#{updateTime},update_name=#{updateName},
			update_account=#{updateAccount},type=#{type},isPro=#{isPro} ,auto_print =#{autoPrint} where id=#{id}
	</update>
</mapper>