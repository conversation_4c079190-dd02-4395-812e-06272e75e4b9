webpackJsonp([24],{Ek2N:function(t,e){},WoKI:function(t,e){},ijcP:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"WaterMeterFactoryEdit"},[a("el-form",{ref:"WaterMeterFactoryEditForm",staticClass:"formBill-One",attrs:{rules:t.rules,inline:!0,size:"mini",model:t.formData,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"供应商名称：",prop:"factoryName"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入供应商名称"},model:{value:t.formData.factoryName,callback:function(e){t.$set(t.formData,"factoryName",e)},expression:"formData.factoryName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"供应商全称：",prop:"factoryFullName"}},[a("el-input",{attrs:{maxlength:"200",clearable:"",placeholder:"请输入供应商全称"},model:{value:t.formData.factoryFullName,callback:function(e){t.$set(t.formData,"factoryFullName",e)},expression:"formData.factoryFullName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"供应商地址：",prop:"factoryAddr"}},[a("el-input",{attrs:{maxlength:"200",clearable:"",placeholder:"请输入供应商地址"},model:{value:t.formData.factoryAddr,callback:function(e){t.$set(t.formData,"factoryAddr",e)},expression:"formData.factoryAddr"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"联系人：",prop:"linkMan"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入联系人"},model:{value:t.formData.linkMan,callback:function(e){t.$set(t.formData,"linkMan",e)},expression:"formData.linkMan"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"联系电话：",prop:"linkTel"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入联系电话"},model:{value:t.formData.linkTel,callback:function(e){t.$set(t.formData,"linkTel",e)},expression:"formData.linkTel"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"排序号：",prop:"sortNo"}},[a("el-input",{attrs:{maxlength:"6",clearable:"",placeholder:"请输入排序号"},model:{value:t.formData.sortNo,callback:function(e){t.$set(t.formData,"sortNo",t._n(e))},expression:"formData.sortNo"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"状态：",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:t.formData.status,callback:function(e){t.$set(t.formData,"status",e)},expression:"formData.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),t._v(" "),a("el-option",{attrs:{label:"停用",value:0}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"备注："}},[a("el-input",{attrs:{"show-word-limit":"",maxlength:"150",clearable:"",placeholder:"请输入备注",type:"textarea"},model:{value:t.formData.comments,callback:function(e){t.$set(t.formData,"comments",e)},expression:"formData.comments"}})],1)],1)],1)},staticRenderFns:[]};var l={components:{WaterMeterFactoryEdit:a("VU/8")({name:"WaterMeterFactoryEdit",data:function(){return{formData:{id:"",factoryName:"",factoryFullName:"",factoryAddr:"",linkMan:"",linkTel:"",status:"",tenantId:"",sortNo:"",comments:""},rules:{factoryName:[{required:!0,message:"请输入供应商名称",trigger:"blur"}],factoryFullName:[{required:!0,message:"请输入供应商全称",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"blur"}],sortNo:[{required:!0,message:"请输入排序号",trigger:"blur"},{type:"number",message:"排序号必须为数字值"}]}}},methods:{resetForm:function(){this.$refs.WaterMeterFactoryEditForm.resetFields()},submitForm:function(t){var e=this,a={};a="MeterFactoryAdd"===t?{busicode:"MeterFactoryAdd",data:this.formData}:{busicode:"MeterFactoryUpdate",data:this.formData},this.$api.fetch({params:a}).then(function(t){e.$message({showClose:!0,message:"保存成功",type:"success"}),e.$parent.init(),e.$parent.closeDialog()})},editData:function(t){this.formData=t}}},r,!1,function(t){a("WoKI")},"data-v-f4ec2e6e",null).exports},name:"WaterMeterFactory",data:function(){return{EditVisible:!1,formData:{id:"",factoryName:"",factoryFullName:"",factoryAddr:"",linkMan:"",linkTel:"",status:"",sortNo:"",comments:""},crumbsData:{titleList:[{title:"基础配置",path:"/baseInfo"},{title:"水表供应商",method:function(){window.histroy.back()}}]},maxHeight:0,tableShow:!1,tableData:[],tableQuery:{page:1,pageCount:10,factoryName:""},formType:""}},mounted:function(){var t=this;this.init(),this.$nextTick(function(){t.common.changeTable(t,".WaterMeterFactory .WaterMeterFactoryIndex",[".WaterMeterFactory .block",".WaterMeterFactory .toolbar"])})},methods:{init:function(){var t=this,e={busicode:"MeterFactoryList",data:this.tableQuery};this.$api.fetch({params:e}).then(function(e){t.tableData=e})},search:function(){this.tableQuery.page=1,this.init()},add:function(t){var e=this;if(this.EditVisible=!0,"add"===t)this.formType="MeterFactoryAdd";else{this.formType="MeterFactoryUpdate";var a={busicode:"MeterFactoryQuery",data:{id:t.id}};this.$api.fetch({params:a}).then(function(t){e.$refs.WaterMeterFactoryEdit.editData(t)})}},indexMethod:function(t){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(t+1)},handleSizeChange:function(t){this.tableQuery.pageCount=t,this.tableQuery.page=1,this.init()},handleCurrentChange:function(t){this.tableQuery.page=t,this.init()},closeDialog:function(){this.EditVisible=!1},submitForm:function(){this.$refs.WaterMeterFactoryEdit.submitForm(this.formType)}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"WaterMeterFactory"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:t.crumbsData}}),t._v(" "),t.EditVisible?a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.submitForm()}}},[t._v("保存")]),t._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:t.closeDialog}},[t._v("返回")])],1):a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-plus",type:"primary"},on:{click:function(e){return t.add("add")}}},[t._v("添加")])],1)],1),t._v(" "),t.EditVisible?a("WaterMeterFactoryEdit",{ref:"WaterMeterFactoryEdit"}):a("div",{staticClass:"WaterMeterFactoryIndex"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,size:"mini"}},[a("el-form-item",{staticClass:"form-left"},[a("el-form-item",{attrs:{label:"供应商名称："}},[a("el-input",{attrs:{clearable:"",placeholder:"供应商名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search.apply(null,arguments)}},model:{value:t.tableQuery.factoryName,callback:function(e){t.$set(t.tableQuery,"factoryName",e)},expression:"tableQuery.factoryName"}})],1),t._v(" "),a("el-form-item",[a("el-button",{staticClass:"searchBtn",attrs:{icon:"el-icon-search"},on:{click:t.search}})],1)],1)],1)],1),t._v(" "),a("div",{staticClass:"kl-table"},[t.tableShow?a("el-table",{staticClass:"wuserInfo-table",attrs:{"max-height":t.maxHeight,stripe:"",border:"",data:t.tableData.list}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:t.indexMethod}}),t._v(" "),a("el-table-column",{attrs:{prop:"factoryName","min-width":"100",label:"供应商名称","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"factoryFullName","min-width":"100",label:"供应商全称","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"factoryAddr","min-width":"100",label:"供应商地址","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"linkMan","min-width":"100",label:"联系人","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"linkTel","min-width":"100",label:"联系电话","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"sortNo","min-width":"100",label:"排序号","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"status","min-width":"80",label:"状态"}}),t._v(" "),a("el-table-column",{attrs:{prop:"comments","min-width":"100",label:"备注","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticStyle:{"font-size":"12px"},attrs:{type:"text"},nativeOn:{click:function(a){return t.add(e.row)}}},[t._v("编辑")])]}}],null,!1,754757891)})],1):t._e(),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":t.tableQuery.page,"page-sizes":[10,50,100,500,1e3],"page-size":t.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)])],1)},staticRenderFns:[]};var i=a("VU/8")(l,o,!1,function(t){a("Ek2N")},null,null);e.default=i.exports}});