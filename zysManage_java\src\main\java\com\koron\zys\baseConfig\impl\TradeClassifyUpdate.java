package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.TradeClassifyBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.baseConfig.mapper.TradeClassifyMapper;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;

/**
 * 行业分类-编辑
 * 
 * <AUTHOR>
 *
 */
public class TradeClassifyUpdate implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(TradeClassifyUpdate.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			TradeClassifyMapper mapper = factory.getMapper(TradeClassifyMapper.class,"_default");
			TradeClassifyBean bean = JsonUtils.objectToPojo(req.getData(), TradeClassifyBean.class);
			// 校验字段重复
			if (mapper.check2("trade_name", bean.getTradeName(), bean.getId()) > 0) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "行业名称：" + bean.getTradeName() + "的信息已存在。",
						void.class);
			}
			if (StringUtils.isNullOrEmpty(bean.getId())) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
			}
			bean.setUpdateName(userInfo.getUserInfo().getName());
			bean.setUpdateTime(CommonUtils.getCurrentTime());
			mapper.updateTradeClassify(bean);
		} catch (Exception e) {
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
}
