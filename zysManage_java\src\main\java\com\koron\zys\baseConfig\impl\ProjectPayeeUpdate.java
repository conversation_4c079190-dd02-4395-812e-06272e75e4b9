package com.koron.zys.baseConfig.impl;

import org.apache.log4j.Logger;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ProjectPayeeBean;
import com.koron.zys.baseConfig.mapper.ProjectPayeeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 工程收款主体 修改
 * <AUTHOR>
 *
 */
public class ProjectPayeeUpdate implements ServerInterface{
	private static Logger log = Logger.getLogger(ProjectPayeeSave.class);
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			ProjectPayeeBean bean = JsonUtils.objectToPojo(req.getData(), ProjectPayeeBean.class);
			ProjectPayeeMapper mapper = factory.getMapper(ProjectPayeeMapper.class);
			bean.setUpdateInfo(userInfo);
			mapper.update(bean);
			return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
		}catch(Exception e) {
			log.error("修改失败",e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "修改失败", void.class);
		}
	}

}
