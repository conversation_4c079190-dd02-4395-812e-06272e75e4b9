package com.koron.zys.baseConfig.impl;

import java.util.List;
import java.util.Map;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ApplicationConfig;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BaseAccessoryMetadataBean;
import com.koron.zys.baseConfig.bean.BaseReceiptAccessoryBean;
import com.koron.zys.baseConfig.bean.ContractTemplateBean;
import com.koron.zys.baseConfig.mapper.BaseAccessoryMetadataMapper;
import com.koron.zys.baseConfig.mapper.BaseReceiptAccessoryMapper;
import com.koron.zys.baseConfig.mapper.ContractTemplateMapper;
import com.koron.zys.baseConfig.queryBean.BaseReceiptAccessoryQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 合同模板列表
 * <AUTHOR>
 *
 */
public class ContractTemplateList implements ServerInterface{

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		 @SuppressWarnings("rawtypes")
	        MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
	        try {
	        	String type="";
	        	Map bean =  JsonUtils.objectToPojo(req.getData(),Map.class);
	        	ContractTemplateMapper mapper = factory.getMapper(ContractTemplateMapper.class);
	        	PageHelper.startPage(Integer.parseInt(bean.get("page").toString()) , Integer.parseInt(bean.get("pageCount").toString()));
	        	if(bean.get("type") != null) {
	        		type = bean.get("type").toString();
	        	}
	        	List<ContractTemplateBean> cdList = mapper.selectList(type);
	        	BaseReceiptAccessoryMapper accessoryMapper = factory.getMapper(BaseReceiptAccessoryMapper.class);
	        	BaseAccessoryMetadataMapper accessoryMetadataMapper = factory.getMapper(BaseAccessoryMetadataMapper.class);
	        	
	        	if(cdList!=null && cdList.size()>0) {
	        		for(ContractTemplateBean templateBean : cdList) {
	        			BaseReceiptAccessoryQueryBean query = new BaseReceiptAccessoryQueryBean();
		        		query.setReceiptId(templateBean.getId());
		        		List<BaseReceiptAccessoryBean> receiptAccessorys = accessoryMapper.selectList(query);
		        		if(receiptAccessorys !=null && receiptAccessorys.size()>0) {
		        			BaseAccessoryMetadataBean accessoryMetadata = accessoryMetadataMapper.selectById(receiptAccessorys.get(0).getMetadataId());
		        			templateBean.setFjName(ApplicationConfig.getAccessoryUploadUrl() + "fileDownload?path=" + accessoryMetadata.getAccessoryPath());
		        		}
	        		}
	        		
	        	}
	            info.setCode(Constant.MESSAGE_INT_SUCCESS);
	            info.setDescription("success");
	            info.setData(new PageInfo<>(cdList));
	        } catch (Exception e) {
	            info.setCode(Constant.MESSAGE_INT_FAIL);
	            info.setDescription("操作失败");
	            logger.error("操作失败", e);
	        }
	        return info;
	}
	
}
