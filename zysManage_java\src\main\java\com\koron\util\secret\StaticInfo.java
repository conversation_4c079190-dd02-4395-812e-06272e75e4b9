package com.koron.util.secret;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

public class StaticInfo {


	public static String templateSendUrl;

	@Value("${middleGround.message.template.url}")
	public void setTemplateSendUrl(String templateSendUrl) {
		StaticInfo.templateSendUrl = templateSendUrl + "/message/MsgSend";
	}
	
	public static String smsSendUrl;

	@Value("${middleGround.message.template.url}")
	public void setSmsSendUrl(String smsSendUrl) {
		StaticInfo.smsSendUrl = smsSendUrl + "/message/smsSend";
	}
	
	public static String dataPushUrl;

	@Value("${wx.ap.url}")
	public void setDataPushUrl(String dataPushUrl) {
		StaticInfo.dataPushUrl = dataPushUrl + "/opAndAp/dataPush";
	}
	
	public static String mgPayment;

	@Value("${middleGround.payment.url}")
	public void setMgPayment(String mgPayment) {
		StaticInfo.mgPayment = mgPayment;
	}
	public static String openInvoiceUrl;
	
	@Value("${middleGround.openInvoice.url}")
	public void setOpenInvoiceUrl(String openInvoiceUrl) {
		StaticInfo.openInvoiceUrl = openInvoiceUrl+"/invoice/open";
	}
	
	public static String openCZInvoiceUrl;
	
	@Value("${middleGround.openInvoice.url}")
	public void setOpenCZInvoiceUrl(String openCZInvoiceUrl) {
		StaticInfo.openCZInvoiceUrl = openCZInvoiceUrl+"/electronic/bill/open";
	}
	
	public static String mgMobile;
	
	@Value("${middleGround.mobile.url}")
	public void setMgMobile(String mgMobile) {
		StaticInfo.mgMobile = mgMobile;
	}

	public static String workOrderUrl;
	@Value("${workOrder.url}")
	public void setWorkOrderUrl(String workOrderUrl) {
		StaticInfo.workOrderUrl = workOrderUrl;
	}
	
	public static String billNoticeTemplateId;
	@Value("${middleGround.message.billNoticeTemplateId}")
	public void setBillNoticeTemplateId(String billNoticeTemplateId) {
		StaticInfo.billNoticeTemplateId = billNoticeTemplateId;
	}
	
	public static String payNoticeTemplateId;
	@Value("${middleGround.message.payNoticeTemplateId}")
	public void setPayNoticeTemplateId(String payNoticeTemplateId) {
		StaticInfo.payNoticeTemplateId = payNoticeTemplateId;
	}
	
	public static String arrearsNoticeTemplateId;
	@Value("${middleGround.message.arrearsNoticeTemplateId}")
	public void setArrearsNoticeTemplateId(String arrearsNoticeTemplateId) {
		StaticInfo.arrearsNoticeTemplateId = arrearsNoticeTemplateId;
	}

	public static String systemId;
	@Value("${middleGround.message.systemId}")
	public void setSystemId(String systemId) {
		StaticInfo.systemId = systemId;
	}
	
	public static String wxFirstMsgUrl;
	@Value("${middleGround.message.template.url}")
	public void setWxFirstMsgUrl(String templateMsgUrl) {
		StaticInfo.wxFirstMsgUrl = templateMsgUrl + "/message/priorWxMsgSend";
	}

}
