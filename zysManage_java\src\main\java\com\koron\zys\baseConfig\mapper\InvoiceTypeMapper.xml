<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.koron.zys.baseConfig.mapper.InvoiceTypeMapper">
<select id="select" resultType="com.koron.zys.baseConfig.bean.InvoiceTypeBean">
	select id,invoice_no,invoice_name,comments,case when status = 1 then '启用' else '停用' end status from base_invoice_type order by invoice_no
</select>
<select id="query" parameterType="String" resultType="com.koron.zys.baseConfig.bean.InvoiceTypeBean">
	select * from base_invoice_type
</select>
<insert id="insert" parameterType="com.koron.zys.baseConfig.bean.InvoiceTypeBean">
	insert into base_invoice_type(id,invoice_no,invoice_name,comments,status,tenant_id,create_time,create_account,create_name)
	values(#{id},#{invoiceNo},#{invoiceName},#{comments},#{status},#{tenantId},#{createTime},#{createAccount},#{createName})
</insert>
<update id="update" parameterType="com.koron.zys.baseConfig.bean.InvoiceTypeBean">
	update base_invoice_type set
		invoice_no = #{invoiceNo},
		invoice_name = #{invoiceName},
		comments = #{comments},
		status = #{status},
		tenant_id = #{tenantId},
		update_time = #{updateTime},
		update_account = #{updateAccount},
		update_name = #{updateName}
	where id = #{id}
</update>
</mapper>