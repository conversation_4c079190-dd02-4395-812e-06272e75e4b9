<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BusinessAreaMapper">

	<sql id="Base_Column_List">
		`ID`
		, `parentid`, `BUSINESS_ABODE_ID`, `AREA_NO`, `AREA_NAME`, `AREA_COMMENTS`, `STATUS`, `SORT_NO`, `GROUP_CODE`, `TENANT_ID`, `CREATE_TIME`, `CREATE_ACCOUNT`, `CREATE_NAME`, `UPDATE_TIME`, `UPDATE_ACCOUNT`, `UPDATE_NAME`, `book_no`
	</sql>

	<select id="select" resultType="com.koron.zys.baseConfig.vo.SelectVO">
		SELECT ID,AREA_NAME NAME
		FROM PUB_BUSINESS_AREA
		WHERE STATUS = 1
		AND GROUP_CODE = #{groupCode}
		<if test="abodeId != null and abodeId != ''">
			AND BUSINESS_ABODE_ID = #{abodeId}
		</if>
		order by ID
	</select>
	<select id="selectList" resultType="com.koron.zys.serviceManage.vo.BusinessAreaVo">
		SELECT *
		FROM PUB_BUSINESS_AREA
		WHERE GROUP_CODE = #{groupCode}
		order by ID
	</select>
	<select id="findBusinessAreaById" resultType="String">
		SELECT area_name
		FROM PUB_BUSINESS_AREA
		WHERE STATUS = 1
		  AND GROUP_CODE = #{groupCode}
		  AND id = #{id}
	</select>
	<select id="findBusinessAreaByParentId" resultType="com.koron.zys.serviceManage.bean.SelectBean">
		SELECT ID,AREA_NAME NAME,area_no code
		FROM PUB_BUSINESS_AREA
		where GROUP_CODE = #{groupCode}
		<choose>
			<when test="parentId == null or parentId == '' or parentId == '0'.toString()">
				and (parentId is null or parentId = '0' or parentId = '' )
			</when>
			<otherwise>
				and parentid=#{parentId}
			</otherwise>
		</choose>
	</select>
	<select id="findBusinessAreaByName" resultType="String">
		SELECT id
		FROM PUB_BUSINESS_AREA
		WHERE STATUS = 1
		  AND GROUP_CODE = #{groupCode}
		  AND area_name = #{areaName}
	</select>
	<select id="findAllByParentId" resultType="String">
		SELECT ID
		FROM PUB_BUSINESS_AREA
		where GROUP_CODE = #{groupCode}
		<if test="id != null and id != ''">
			and area_no like CONCAT((SELECT area_no
			FROM PUB_BUSINESS_AREA
			where GROUP_CODE = #{groupCode}
			and id=#{id}),'%')
		</if>
	</select>
	<select id="selectByCode" resultType="com.koron.zys.serviceManage.vo.BusinessAreaVo">
		select b.id,
			   b.business_abode_id,
			   b.area_no,
			   b.area_name,
			   b.area_comments,
			   b.sort_no,
			   b.group_code,
			   b.tenant_id,
			   b.parentid,
			   case when b.status = 1 then '启用' else '停用' end status
		from pub_business_area b
		where area_no = #{code}
	</select>
	<select id="selectById" resultType="com.koron.zys.serviceManage.vo.BusinessAreaVo">
		select b.id,
			   b.business_abode_id,
			   b.area_no,
			   b.area_name,
			   b.area_comments,
			   b.sort_no,
			   b.group_code,
			   b.tenant_id,
			   b.parentid,
			   case when b.status = 1 then '启用' else '停用' end status
		from pub_business_area b
		where id = #{id}
	</select>

	<select id="findAllByParentNo" resultType="String">
		SELECT ID
		FROM PUB_BUSINESS_AREA
		where GROUP_CODE = #{groupCode}
		  and area_no like concat(#{areaNo}, '%')
	</select>

	<select id="getIdByAreaName" resultType="java.lang.String">
		select id
		from PUB_BUSINESS_AREA
		where area_name = #{areaName}
	</select>

	<!--查询单个-->
	<select id="getSingle" resultType="com.koron.zys.serviceManage.bean.BusinessAreaBean">
		select
		<include refid="Base_Column_List"/>
		from pub_business_area
		<where>
			<if test="id != null and id != ''">
				and ID = #{id}
			</if>
			<if test="parentId != null and parentId != ''">
				and parentid = #{parentId}
			</if>
			<if test="businessAbodeId != null and businessAbodeId != ''">
				and BUSINESS_ABODE_ID = #{businessAbodeId}
			</if>
			<if test="areaName != null and areaName != ''">
				and AREA_NAME = #{areaName}
			</if>
		</where>
	</select>


</mapper>