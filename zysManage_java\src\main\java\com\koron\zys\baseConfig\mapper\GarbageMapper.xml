<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.koron.zys.baseConfig.mapper.GarbageMapper">

	<!--根据id查询 -->
	<select id="selectById"
		resultType="com.koron.zys.baseConfig.bean.GarbageUniversalBean">
		select b.id,
		b.month_fixed_garbage,b.industrial_fixed_garbage,
		b.business_fixed_garbage,b.month_transport_garbage,b.other_fixed_garbage from
		BILL_GARBAGE_UNIVERSAL b where id = #{_parameter}

	</select>
	<!--查询垃圾费定价列表 -->
	<select id="selectList"
		resultType="com.koron.zys.baseConfig.bean.GarbageUniversalBean"
		parameterType="com.koron.zys.baseConfig.queryBean.UserGarbageQueryBean">
		select
		a.status,b.id,a.user_no,a.ctm_name,a.ctm_addr,a.setup_meter_addr,a.use_water_type,b.garbate_type,b.water_ratio,
		b.month_fixed_garbage,b.industrial_fixed_garbage,
		b.business_fixed_garbage,b.month_transport_garbage,b.other_fixed_garbage,
		ifnull(b.fixed_amount,0) fixed_amount,b.comments from
		(
			select a.user_no,a.ctm_no,a.setup_meter_addr,a.use_water_type,a.status,a.ctm_name,a.ctm_addr from user_info a where 1=1
			<if test="status!='' and status != null">
				and a.status = #{status}
			</if>
		) a

		left join BILL_GARBAGE_UNIVERSAL b on a.user_no = b.user_no where 1=1
		<if test="fuzzyQuery!='' and fuzzyQuery != null">
			and (a.user_no LIKE CONCAT('%',#{fuzzyQuery},'%') or a.ctm_name LIKE
			CONCAT('%',#{fuzzyQuery},'%') )
		</if>
	</select>
	<!--查询垃圾费免收列表 -->
	<select id="selectMSList"
		resultType="com.koron.zys.baseConfig.bean.GarbageUniversalBean"
		parameterType="com.koron.zys.baseConfig.queryBean.UserGarbageQueryBean">
		select
		a.status,b.id,a.user_no,a.ctm_name,a.ctm_addr,a.setup_meter_addr,a.use_water_type,
		b.GARBATE_FREE,b.free_begin,b.free_end,b.mian_shou_comments from
		(
			select a.user_no,a.ctm_no,a.setup_meter_addr,a.use_water_type,a.status,a.ctm_name,a.ctm_addr from user_info a where 1=1
			<if test="status!='' and status != null">
				and a.status = #{status}
			</if>
		) a
		left join BILL_GARBAGE_UNIVERSAL b on a.user_no = b.user_no where 1=1
		<if test="fuzzyQuery!='' and fuzzyQuery != null">
			and (a.user_no LIKE CONCAT('%',#{fuzzyQuery},'%') or a.ctm_name LIKE
			CONCAT('%',#{fuzzyQuery},'%') )
		</if>
	</select>

	<select id="selectGarbageUniversalBean" parameterType="String" resultType="com.koron.zys.baseConfig.bean.GarbageUniversalBean">
		SELECT * FROM BILL_GARBAGE_UNIVERSAL WHERE USER_NO = #{userNo}
	</select>

	<!-- 查询垃圾费定价历史列表 -->
	<select id="selectHisList"
		resultType="com.koron.zys.baseConfig.bean.GarbageUniversalBean"
		parameterType="com.koron.zys.baseConfig.queryBean.GarbageHisQueryBean">
		select
		a.status,b.id,a.user_no,a.ctm_name,a.ctm_addr,a.use_water_type,b.garbate_type,b.water_ratio,b.month_fixed_garbage,b.industrial_fixed_garbage,
		b.business_fixed_garbage,b.month_transport_garbage,b.other_fixed_garbage,
		b.fixed_amount,b.comments,b.change_item,b.create_time,b.create_name
		from
		(
			select a.user_no,a.ctm_no,a.setup_meter_addr,a.use_water_type,a.status,a.ctm_name,a.ctm_addr from user_info a where 1=1
			<if test="status!='' and status != null">
				and a.status = #{status}
			</if>
		) a
		join (
			select * from BILL_GARBAGE_UNIVERSAL_HIS  b where 1=1
			<if test="startDate != '' and startDate != null">
			and left(b.create_time,10) &gt;= #{startDate}
			</if>
			<if test="endDate != '' and endDate != null">
				and left(b.create_time,10) &lt;= #{endDate}
			</if>
		) b on a.user_no = b.user_no where 1=1
		<if test="fuzzyQuery!='' and fuzzyQuery != null">
			and (a.ctm_addr like CONCAT('%',#{fuzzyQuery},'%') or a.user_no LIKE
			CONCAT('%',#{fuzzyQuery},'%')
			or b.create_name LIKE CONCAT('%',#{fuzzyQuery},'%') )
		</if>
		order by b.create_time desc
	</select>

	<insert id="insert"
		parameterType="com.koron.zys.baseConfig.bean.GarbageUniversalBean">
		insert into
		BILL_GARBAGE_UNIVERSAL(id,user_no,garbate_type,water_ratio,month_fixed_garbage,industrial_fixed_garbage,business_fixed_garbage,
		month_transport_garbage,other_fixed_garbage,
		fixed_amount,comments,create_name,create_time)
		values
		(#{id},#{userNo},#{garbateType},#{waterRatio},#{monthFixedGarbage},#{industrialFixedGarbage},#{businessFixedGarbage},
		#{monthTransportGarbage},#{otherFixedGarbage},
		#{fixedAmount},#{comments},#{createName},#{createTime})
	</insert>

	<insert id="insertMS"
		parameterType="com.koron.zys.baseConfig.bean.GarbageUniversalBean">
		insert into
		BILL_GARBAGE_UNIVERSAL(id,user_no,GARBATE_FREE,free_begin,free_end,
		mian_shou_comments,create_name,create_time)
		values
		(#{id},#{userNo},#{garbateFree},#{freeBegin},#{freeEnd},
		#{mianShouComments},#{createName},#{createTime})
	</insert>

	<insert id="insertOld"
		parameterType="com.koron.zys.baseConfig.bean.GarbageUniversalBean">
		insert into
		BILL_GARBAGE_UNIVERSAL_HIS(id,user_no,garbate_type,water_ratio,month_fixed_garbage,industrial_fixed_garbage,business_fixed_garbage,
		month_transport_garbage,other_fixed_garbage,fixed_amount,comments,change_item,create_name,create_time)
		select #{uid},user_no,garbate_type,water_ratio,month_fixed_garbage,industrial_fixed_garbage,business_fixed_garbage,
		month_transport_garbage,other_fixed_garbage,fixed_amount,comments,#{changeItem},#{updateName},#{updateTime} from
		BILL_GARBAGE_UNIVERSAL where id = #{id}
	</insert>

	<update id="update"
		parameterType="com.koron.zys.baseConfig.bean.GarbageUniversalBean">
		update BILL_GARBAGE_UNIVERSAL
		set garbate_type = #{garbateType},
			water_ratio=#{waterRatio},
			month_fixed_garbage=#{monthFixedGarbage},
			industrial_fixed_garbage=#{industrialFixedGarbage},
			business_fixed_garbage=#{businessFixedGarbage},
			month_transport_garbage=#{monthTransportGarbage},
			other_fixed_garbage=#{otherFixedGarbage},
			fixed_amount=#{fixedAmount},

			comments=#{comments},
			update_name=#{updateName},
			update_time=#{updateTime}
		where id=#{id}
	</update>

	<update id="updateMS"
		parameterType="com.koron.zys.baseConfig.bean.GarbageUniversalBean">
		update BILL_GARBAGE_UNIVERSAL
		set garbate_type = #{garbateType},
			garbate_free = #{garbateFree},
			free_begin = #{freeBegin},
			free_end=#{freeEnd},
			mian_shou_comments=#{mianShouComments},
			update_name=#{updateName},
			update_time=#{updateTime}
		where id=#{id}
	</update>
</mapper>
