<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BaseBusinessHallMapper">
    <select id="selectList" resultType="com.koron.zys.baseConfig.vo.BaseBusinessHallVO">
            select * from base_business_hall where status = 1
    </select>

    <select id="selectChargeStaffs" resultType="String">
            select b.charge_staff_account from base_business_hall a  join base_hall_staff_relation b
            on a.id = b.business_hall_id where a.id=#{businessHallId}
    </select>
</mapper>