package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ConfigBean;
import com.koron.zys.baseConfig.mapper.ConfigMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;
import com.mysql.cj.util.StringUtils;

/**
 * 全局参数-编辑
 * <AUTHOR>
 */
public class ConfigUpdate implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(ConfigUpdate.class);

	@Override
	@ValidationKey(clazz = ConfigBean.class,method = "update")
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			ConfigMapper mapper = factory.getMapper(ConfigMapper.class);
			ConfigBean bean = JsonUtils.objectToPojo(req.getData(), ConfigBean.class);
			// 校验字段重复
			if (mapper.check2("config_name", bean.getConfigName(), bean.getConfigId()) > 0) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "参数名称：" + bean.getConfigName() + "的信息已存在。",
						void.class);
			}
			if (StringUtils.isNullOrEmpty(bean.getConfigId())) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
			}
			bean.setUpdateName(userInfo.getUserInfo().getName());
			bean.setUpdateTime(CommonUtils.getCurrentTime());
			mapper.updateConfig(bean);
		} catch (Exception e) {
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
}