package com.koron.zys.baseConfig.mapper;

import com.koron.zys.baseConfig.bean.IncomeBankBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * 进账银行数据库操作
 */
public interface IncomeBankMapper {

    List<IncomeBankBean> select(IncomeBankBean bean);

    IncomeBankBean selectIsDefaultOne();

    int insert(IncomeBankBean bean);

    int update(IncomeBankBean bean);

    int delete(@Param("ids")List<String> ids);
}
