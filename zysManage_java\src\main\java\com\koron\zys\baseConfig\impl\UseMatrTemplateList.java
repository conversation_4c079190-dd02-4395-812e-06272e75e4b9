package com.koron.zys.baseConfig.impl;

import java.util.List;

import com.koron.zys.baseConfig.mapper.UseMatrTemplateMapper;
import com.koron.zys.baseConfig.queryBean.UseMatrTemplateQueryBean;
import com.koron.zys.baseConfig.vo.UseMatrTemplateVO;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 用料模板-列表初始化
 * 
 * <AUTHOR>
 *
 */
public class UseMatrTemplateList implements ServerInterface  {
	
	private static Logger logger = LoggerFactory.getLogger(UseMatrTemplateList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
		try {
			UseMatrTemplateQueryBean bean = JsonUtils.objectToPojo(req.getData(), UseMatrTemplateQueryBean.class);
			UseMatrTemplateMapper mapper = factory.getMapper(UseMatrTemplateMapper.class);
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			List<UseMatrTemplateVO> list = mapper.selectUseMatrTemplate(bean);
			info.setData(new PageInfo<>(list));
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}