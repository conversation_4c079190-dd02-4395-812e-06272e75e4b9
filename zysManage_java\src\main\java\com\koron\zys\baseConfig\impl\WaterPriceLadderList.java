package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.LadderListBean;
import com.koron.zys.baseConfig.bean.WaterPriceDetailBean;
import com.koron.zys.baseConfig.bean.WaterPriceLadderBean;
import com.koron.zys.baseConfig.mapper.WaterPriceMapper;
import com.koron.zys.baseConfig.queryBean.WaterPriceQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
import org.apache.bsf.BSFManager;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WaterPriceLadderList implements ServerInterface {
    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        // TODO Auto-generated method stub
        @SuppressWarnings("rawtypes")
        MessageBean<Map> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", Map.class);

        try {
            WaterPriceQueryBean bean = JsonUtils.objectToPojo(req.getData(), WaterPriceQueryBean.class);
            WaterPriceMapper mapper = factory.getMapper(WaterPriceMapper.class);
            //查询用水价格明细
            List<WaterPriceDetailBean> waterPricDetailList = mapper.selectWaterPriceBetailByIdForRedRush(bean.getId());
            List<LadderListBean> ladderList = new ArrayList();
            int ladderSize = 0;
            for (WaterPriceDetailBean waterPriceDetailBean : waterPricDetailList) {
                List<WaterPriceLadderBean> ladder = mapper.selectWaterPriceLadderById(waterPriceDetailBean.getId());
                if("1".equals(waterPriceDetailBean.getLadderType()) || ladder == null || ladder.size()<=0) {    //没有阶梯明细，默认阶梯1
                    ladder = new ArrayList<WaterPriceLadderBean>();
                    WaterPriceLadderBean ladderBean = new WaterPriceLadderBean();
                    ladderBean.setBeginWater("0");
                    ladderBean.setEndWater("0");
                    ladderBean.setPrice(waterPriceDetailBean.getFixedPrice());
                    ladder.add(ladderBean);
                }else {
                    if ("6".equals(waterPriceDetailBean.getLadderType())) {//判断是否定额阶梯,若是则修改起止水量
                        for (WaterPriceLadderBean waterPrice : ladder) {
                            BSFManager bsfManager = new BSFManager();
                            if (waterPrice.getBeginWater().indexOf("[定额水量]") > -1) {
                                String beginWater = waterPrice.getBeginWater().replaceAll("\\[定额水量\\]", bean.getQuotaWater().toString());
                                Object begin = bsfManager.eval("javascript", "XX", 0, 0, beginWater);        //执行公式
                                if ("NaN".equals(begin.toString())) {
                                    begin = "0.0";
                                }
                                java.text.DecimalFormat df = new java.text.DecimalFormat("#.########");
                                String average = df.format(BigDecimal.valueOf(Double.parseDouble(begin.toString())));
                                waterPrice.setBeginWater(average);
                            }

                            if (waterPrice.getEndWater().indexOf("[定额水量]") > -1) {
                                String endWater = waterPrice.getEndWater().replaceAll("\\[定额水量\\]", bean.getQuotaWater().toString());
                                Object end = bsfManager.eval("javascript", "XX", 0, 0, endWater);        //执行公式
                                if ("NaN".equals(end.toString())) {
                                    end = "0.0";
                                }
                                java.text.DecimalFormat df = new java.text.DecimalFormat("#.########");
                                String average = df.format(BigDecimal.valueOf(Double.parseDouble(end.toString())));
                                waterPrice.setEndWater(average);
                            }

                        }
                    }
                }
                if(ladder.size() > ladderSize)
                    ladderSize = ladder.size();
                waterPriceDetailBean.setLadders(ladder);
            }
            for(int i=0; i<ladderSize; i++) {   //动态显示阶梯列表
                LadderListBean ladderBean = new LadderListBean();
                ladderBean.setPriceEn("priceJ"+(i+1));
                ladderBean.setPriceName("阶梯"+(i+1)+"单价");
                ladderBean.setWaterEn("waterJ"+(i+1));
                ladderBean.setWaterName("阶梯"+(i+1)+"水量");
                ladderList.add(ladderBean);
            }
            Map map = new HashMap();
            map.put("waterPricDetailList",waterPricDetailList);
            map.put("ladderList",ladderList);
            info.setData(map);
        }catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            // TODO: handle exception
        }
        return info;
    }
}
