package com.koron.zys.baseConfig.impl;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ContractTemplateBean;
import com.koron.zys.baseConfig.mapper.ContractTemplateMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

public class ContractTemplateQuery implements ServerInterface{

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
        MessageBean<ContractTemplateBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", ContractTemplateBean.class);

        try {
            ContractTemplateMapper mapper = factory.getMapper(ContractTemplateMapper.class);
            Map map = JsonUtils.objectToPojo(req.getData(), Map.class);
            if (StringUtils.isEmpty(map.get("id").toString())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id不能为空", void.class);
            }
            ContractTemplateBean contractTemplateBean = mapper.selectById(map.get("id").toString());
            info.setData(contractTemplateBean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
	}

}
