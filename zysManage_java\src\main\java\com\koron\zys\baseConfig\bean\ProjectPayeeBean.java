package com.koron.zys.baseConfig.bean;

/**
 * 工程收款主体
 * <AUTHOR>
 *
 */
public class ProjectPayeeBean extends BaseBean{
	private String subjectName; 
	
	private String status;
	
	private String comments;
	
	private int page;
	
	private int pageCount; 

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public int getPage() {
		return page;
	}

	public void setPage(int page) {
		this.page = page;
	}

	public int getPageCount() {
		return pageCount;
	}

	public void setPageCount(int pageCount) {
		this.pageCount = pageCount;
	}

	public String getSubjectName() {
		return subjectName;
	}

	public void setSubjectName(String subjectName) {
		this.subjectName = subjectName;
	}
}
