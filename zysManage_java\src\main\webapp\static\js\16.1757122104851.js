webpackJsonp([16],{"7QMZ":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("yJVD"),n={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"departmentAdd"},[a("el-form",{ref:"departmentAddRuleForm",staticClass:"formBill-One",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px",inline:!0}},[a("el-form-item",{attrs:{label:"组织编号：",prop:"matrNo"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.matrNo,callback:function(t){e.$set(e.ruleForm,"matrNo",t)},expression:"ruleForm.matrNo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"组织名称：",prop:"matrName"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.matrName,callback:function(t){e.$set(e.ruleForm,"matrName",t)},expression:"ruleForm.matrName"}})],1)],1)],1)},staticRenderFns:[]};var i={name:"department",components:{departmentAdd:a("VU/8")({name:"departmentAdd",data:function(){return{databaseData:[],ruleForm:{matrNo:"",matrName:"",matrMode:"",matrNum:"",matrUnit:"",matrPrice:""},rules:{matrNo:[{required:!0,message:"请输入材料编号",trigger:"blur"}],matrName:[{required:!0,message:"请输入材料名称",trigger:"blur"}],matrMode:[{required:!0,message:"请输入材料规格",trigger:"blur"}],matrNum:[{required:!0,message:"请输入材料数量"},{type:"number",message:"材料数量必须为数字值"}],matrUnit:[{message:"请输入材料单位",trigger:"blur",required:!0}],matrPrice:[{message:"请输入材料单价",trigger:"blur",required:!0},{type:"number",message:"材料单价必须为数字值"}]}}},mounted:function(){},methods:{resetForm:function(){this.$refs.departmentAddRuleForm.resetFields()},submitForm:function(e,t){var a=this,r=this,n={};this.$refs[e].validate(function(e){if(!e)return!1;n="添加"===t?{busicode:"MatrTemplateAdd",data:a.ruleForm}:{busicode:"MatrTemplateUpdate",data:a.ruleForm},a.$api.fetch({params:n}).then(function(e){r.$message({showClose:!0,message:"保存成功",type:"success"}),r.$parent.selectTSubSystem(),r.$parent.closeDialog(),a.resetForm()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","departmentAdd",this.$parent.closeDialog)},editData:function(e){this.ruleForm=e}}},n,!1,function(e){a("iiet")},null,null).exports,autoTree:r.a},data:function(){return{tableShow:!0,tableQuery:{page:1,pageCount:20},maxHeight:0,appServerData:[],formData:{matrNo:"",matrName:"",matrMode:"",matrNum:"",matrUnit:"",matrPrice:""},crumbsData:{titleList:[{title:"系统设置",path:"/ChangeTables"},{title:"组织架构",method:function(){window.histroy.back()}}]},treeDatas:{tree:[{name:"根目录",id:"2",children:[]}],defaultProps:{label:"name",children:"children"},inputProp:{showSearch:!0,Inp_placeholder:"请输入节点"},rootName:"根目录",defaultOpen:{nodeKey:"id"}},departmentShow:!0,departmentAddVisible:!1}},mounted:function(){this.selectTSubSystemTree()},methods:{selectTSubSystemTree:function(){var e=this;this.$axios({url:"/dep/json.htm?id=-1",method:"post"}).then(function(t){e.treeDatas.tree[0].children=t.data[0].children,e.appServerData=t.data,e.common.changeTable(e,".department .kl-table",[".department .toolbar",".department .block"])})},formatStatus:function(e){return 1===e.status?"启用":"禁用"},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1},handleCurrentChange:function(e){this.tableQuery.page=e},closeDialog:function(){this.departmentShow=!0,this.departmentAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.departmentAdd.handleClose()},submitForm:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.departmentAdd.submitForm(e,t)},backTreeData:function(e){console.log(e),this.appServerData=e.children,this.common.changeTable(this,".department .kl-table",[".department .toolbar",".department .block"])}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},s={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"department"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.departmentAddVisible,expression:"departmentAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("departmentAddRuleForm")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.departmentShow,expression:"departmentShow"}],staticClass:"company-content"},[a("div",{staticClass:"company-left"},[a("auto-tree",{attrs:{treeData:e.treeDatas},on:{sendTreeData:e.backTreeData}})],1),e._v(" "),a("div",{staticClass:"company-right kl-table"},[e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.appServerData,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"code",label:"组织编号","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"组织名称","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"description",label:"组织描述","min-width":"100"}})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[20,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.appServerData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)]),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.departmentAddVisible,expression:"departmentAddVisible"}]},[a("departmentAdd",{ref:"departmentAdd"})],1)])])},staticRenderFns:[]};var l=a("VU/8")(i,s,!1,function(e){a("nJ5C")},null,null);t.default=l.exports},iiet:function(e,t){},nJ5C:function(e,t){}});