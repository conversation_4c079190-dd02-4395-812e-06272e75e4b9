package com.koron.zys.baseConfig.impl;

import org.apache.log4j.Logger;
import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ProcessTimeBean;
import com.koron.zys.baseConfig.mapper.ProcessTimeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;

public class ProcessTimeAdd implements ServerInterface{

	private Logger log = Logger.getLogger(ProcessTimeAdd.class);	
	@Override
	@ValidationKey(clazz = ProcessTimeBean.class,method = "insert")
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
            ProcessTimeMapper mapper = factory.getMapper(ProcessTimeMapper.class);
            ProcessTimeBean bean = JsonUtils.objectToPojo(req.getData(), ProcessTimeBean.class);
            bean.setId(new ObjectId().toHexString());
            bean.setCreateName(userInfo.getUserInfo().getName());
    		bean.setCreateTime(CommonUtils.getCurrentTime());
            mapper.insert(bean);
        } catch (Exception e) {
            logger.error("操作失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
