package com.koron.zys.baseConfig.bean;


/**
 * 管理区域实体类
 * <AUTHOR>
 *
 */
public class ManageAreaBean {
	
	/**
	 * 主键ID
	 */
	private String manageAreaId;
	
	private String parentId;
	/**
	 * 行政区划代码（国标）code
	 */
	private String areaNo;
	/**
	 * 名称name
	 */
	private String areaName;

	/**
	 * 是否叶子节点 1是0否
	 */
	private Integer isLeaf;
	
	/**
	 * 状态启用状态 [ 0 停用 1 启用 ]
	 */
	private Integer status;
	
	/**
	 * 排序号
	 */
	private Integer sortNo;
	
	/**
	 * 区域描述
	 */
	private String areaComments;
	/**
	 * 创建人
	 */
	private String createName;
	/**
	 * 创建时间
	 */
	private String createTime;
	/**
	 * 修改人
	 */
	private String updateName;
	/**
	 * 修改时间
	 */
	private String updateTime;
	public String getManageAreaId() {
		return manageAreaId;
	}
	public void setManageAreaId(String manageAreaId) {
		this.manageAreaId = manageAreaId;
	}
	public String getParentId() {
		return parentId;
	}
	public void setParentId(String parentId) {
		this.parentId = parentId;
	}
	public String getAreaNo() {
		return areaNo;
	}
	public void setAreaNo(String areaNo) {
		this.areaNo = areaNo;
	}
	public String getAreaName() {
		return areaName;
	}
	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}
	public Integer getIsLeaf() {
		return isLeaf;
	}
	public void setIsLeaf(Integer isLeaf) {
		this.isLeaf = isLeaf;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Integer getSortNo() {
		return sortNo;
	}
	public void setSortNo(Integer sortNo) {
		this.sortNo = sortNo;
	}
	public String getAreaComments() {
		return areaComments;
	}
	public void setAreaComments(String areaComments) {
		this.areaComments = areaComments;
	}
	public String getCreateName() {
		return createName;
	}
	public void setCreateName(String createName) {
		this.createName = createName;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getUpdateName() {
		return updateName;
	}
	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
}
