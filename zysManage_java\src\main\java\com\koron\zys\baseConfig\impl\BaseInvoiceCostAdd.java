package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BaseInvoiceCostBean;
import com.koron.zys.baseConfig.mapper.BaseInvoiceCostMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

public class BaseInvoiceCostAdd implements ServerInterface {

	@Override
	//@ValidationKey(clazz = BaseInvoiceCostBean.class,method = "insert")
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub

		List<BaseInvoiceCostBean> list = JsonUtils.jsonToList(JsonUtils.objectToJson(req.getData()), BaseInvoiceCostBean.class);
		BaseInvoiceCostMapper mapper = factory.getMapper(BaseInvoiceCostMapper.class);
		for (BaseInvoiceCostBean bean : list) {
			//if(bean.getId()!=null && !"".equals(bean.getId())) {
			bean.setCreateInfo(userInfo);
			//}
			if("".equals(bean.getTaxRate())) {
				bean.setTaxRate("0");
			}
			bean.setStatus("1");
//			if(StringUtils.isBlank(bean.getCommodityName())){
//				return MessageBean.create(Constant.MESSAGE_INT_FAIL, "商品名称不能为空。", void.class);
//			}
//			if(StringUtils.isBlank(bean.getInvoiceNo())){
//				return MessageBean.create(Constant.MESSAGE_INT_FAIL, bean.getCommodityName()+ "的票据类型不能为空。", void.class);
//			}
//			if(StringUtils.isBlank(bean.getCommodityNo())){
//				return MessageBean.create(Constant.MESSAGE_INT_FAIL,bean.getCommodityName()+ "的商品编号不能为空。", void.class);
//			}
//			if(StringUtils.isBlank(bean.getTaxRate())){
//				return MessageBean.create(Constant.MESSAGE_INT_FAIL, bean.getCommodityName()+ "的税率不能为空。", void.class);
//			}
		}
		mapper.delete();
		mapper.insert(list);
		
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
