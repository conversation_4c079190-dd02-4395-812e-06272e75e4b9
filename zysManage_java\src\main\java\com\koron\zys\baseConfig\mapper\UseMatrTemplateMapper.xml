<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.UseMatrTemplateMapper">

    <!--列表初始化  -->
    <select id="selectUseMatrTemplate" parameterType="com.koron.zys.baseConfig.queryBean.UseMatrTemplateQueryBean"
            resultType="com.koron.zys.baseConfig.vo.UseMatrTemplateVO">
        select id,template_name,matr_num,
        comments,case when status=1 then '启用' else '停用' end status ,sort_no
        from BASE_USE_MATR_TEMP
        where 1=1
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="searchContent !=null and searchContent !=''">
            AND (template_name like CONCAT('%',#{searchContent},'%')
            or matr_num like CONCAT('%',#{searchContent},'%'))
        </if>
        order by sort_no asc
    </select>
    <!--添加用料模板  -->
    <insert id="insertUseMatrTemplate" parameterType="com.koron.zys.baseConfig.bean.UseMatrTemplateBean">
		insert into BASE_USE_MATR_TEMP(
		id,template_name,matr_num,comments,status,sort_no,tenant_id,
		create_time, create_account,create_name)
		values
		(
		#{id},
		#{templateName},
		#{matrNum},
		#{comments},
		#{status},
		#{sortNo},
		#{tenantId},
		#{createTime},
		#{createAccount},
		#{createName}
		)
	</insert>
    <!--修改费用  -->
    <update id="updateUseMatrTemplate" parameterType="com.koron.zys.baseConfig.bean.UseMatrTemplateBean">
		update BASE_USE_MATR_TEMP
		set template_name = #{templateName},
		matr_num = #{matrNum},
		comments = #{comments},
		status = #{status},
		sort_no = #{sortNo},
		update_time=#{updateTime},
		update_account=#{updateAccount},
		update_name = #{updateName}
		where id = #{id}
	</update>

    <!--批量添加List集合明细对象  -->
    <insert id="insertUseMatrTemplateList">
        insert into BASE_USE_MATR_TEMP_LIST
        (id,template_id,matr_no,matr_num,tenant_id,create_time,create_account,create_name)
        values
        <foreach collection="matrTemplateList" item="bean" separator=",">
            (#{bean.id},#{bean.templateId},#{bean.matrNo}, #{bean.matrNum},#{bean.tenantId},#{bean.createTime}, #{bean.createAccount},#{bean.createName})
        </foreach>
    </insert>

</mapper>