package com.koron.common.stub;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonAppend;

import java.io.IOException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 配置中心.
 * 获取杰为配置中心属性.
 * 需要预选配置 config_center_url参数，作为配置中心的URL地址。目前开发环境为：http://10.13.1.248:14200.
 * 主要方法：
 * {@link #getProperties(String, String)}
 */
public class ConfigCenter {
    /**
     * 获取配置属性.
     *
     * @param tarant 租户信息，即配置中心第一级
     * @param app    应用信息，即配置中心第二级
     * @return 相对应的属性集
     * @throws IOException 连接服务异常
     */
    public static Properties getProperties(String tarant, String app) throws IOException {
        Properties p = new Properties();
        if (System.getenv("config_center_url") != null) {
            URL u = new URL(System.getenv("config_center_url") + "/configCenter/findConfig?side=server&serviceName=" + tarant + "&env=" + app + "&configType=properties");
            ObjectMapper mapper = new ObjectMapper();
            HashMap<String, Object> map = mapper.readValue(u, mapper.getTypeFactory().constructParametricType(HashMap.class, String.class, Object.class));
            if ("0".equals(String.valueOf(map.get("code")))) {
                p.putAll((Map) map.get("result"));
            }
        }
        return p;
    }

    /**
     * 合并多个节点的配置并一起返回
     *
     * @param layer 多个节点的数组，每个节点均为第二级，用符号[.]进行分隔
     * @return 多个节点的属性值
     * @throws Exception 当调用的
     */
    public static Properties getProperties(String... layer) throws Exception {
        Properties p = new Properties();
        for (String key : layer) {
            if (key.indexOf('.') == -1)
                throw new Exception(key + " isn't contain char(.)");
            Properties tmp = getProperties(key.substring(0, key.indexOf('.')), key.substring(key.indexOf('.') + 1));
            p.putAll(tmp);
        }
        return p;
    }
}
