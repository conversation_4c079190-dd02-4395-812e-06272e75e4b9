webpackJsonp([43],{"RO+r":function(e,t){},W1s5:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"EnclosureManAdd"},[a("el-form",{ref:"EnclosureManAddRuleForm",staticClass:"formBill-One",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px",inline:!0}},[a("el-form-item",{attrs:{label:"附件编号：",prop:"accessoryNo"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.accessoryNo,callback:function(t){e.$set(e.ruleForm,"accessoryNo",t)},expression:"ruleForm.accessoryNo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"附件对象：",prop:"accessoryObj"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.accessoryObj,callback:function(t){e.$set(e.ruleForm,"accessoryObj",t)},expression:"ruleForm.accessoryObj"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"附件名称：",prop:"accessoryName"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.accessoryName,callback:function(t){e.$set(e.ruleForm,"accessoryName",t)},expression:"ruleForm.accessoryName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"拍照类型：",prop:"pjType"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.pjType,callback:function(t){e.$set(e.ruleForm,"pjType",t)},expression:"ruleForm.pjType"}},[a("el-option",{key:"1",attrs:{label:"无",value:"1"}}),e._v(" "),a("el-option",{key:"2",attrs:{label:"主摄像头",value:"2"}}),e._v(" "),a("el-option",{key:"3",attrs:{label:"副摄像头",value:"3"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"排序号：",prop:"sortNo"}},[a("el-input",{attrs:{placeholder:"排序号",oninput:"value=value.replace(/[\\D\\n]/g,'')",maxlength:"6",clearable:""},model:{value:e.ruleForm.sortNo,callback:function(t){e.$set(e.ruleForm,"sortNo",e._n(t))},expression:"ruleForm.sortNo"}})],1)],1)],1)},staticRenderFns:[]},r={name:"EnclosureMan",components:{EnclosureManAdd:a("VU/8")({name:"EnclosureManAdd",data:function(){return{ruleForm:{accessoryNo:"",accessoryName:"",accessoryObj:"",pjType:"",sortNo:""},rules:{accessoryNo:[{required:!0,message:"请输入附件编号",trigger:"blur"}],accessoryName:[{required:!0,message:"请输入附件名称",trigger:"blur"}]}}},mounted:function(){},methods:{resetForm:function(){this.$refs.EnclosureManAddRuleForm.resetFields()},submitForm:function(e,t){var a=this,s=this,r={};this.$refs[e].validate(function(e){if(!e)return!1;r="添加"===t?{busicode:"AccessoryTypeAdd",data:a.ruleForm}:{busicode:"AccessoryTypeUpdate",data:a.ruleForm},a.$api.fetch({params:r}).then(function(e){s.$message({showClose:!0,message:"保存成功",type:"success"}),s.$parent.selectTSubSystem(),s.$parent.closeDialog(),a.resetForm()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","EnclosureManAdd",this.$parent.closeDialog)},editData:function(e){this.ruleForm=e,""==this.ruleForm.accessoryNo&&(this.ruleForm.sortNo=this.$parent.appServerData.list.length+1)}}},s,!1,null,null,null).exports},data:function(){return{tableShow:!0,tableQuery:{page:1,pageCount:50},maxHeight:0,appServerData:[],crumbsData:{titleList:[{title:"基础配置",path:"/ChangeTables"},{title:"附件管理",method:function(){window.histroy.back()}}]},EnclosureManShow:!0,EnclosureManAddVisible:!1,formData:{accessoryNo:"",accessoryName:"",accessoryObj:""}}},mounted:function(){this.selectTSubSystem()},methods:{formatStatus:function(e){return 1===e.status?"启用":"禁用"},stateFormat:function(e,t){return"1"===e.pjType?"无":"2"===e.pjType?"主摄像头":"3"===e.pjType?"副摄像头":void 0},appAdd:function(e){var t=this;if(this.EnclosureManShow=!1,this.EnclosureManAddVisible=!0,"add"===e)this.$set(this.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),this.$refs.EnclosureManAdd.editData({accessoryNo:"",accessoryName:"",accessoryObj:"",sortNo:""}),this.common.chargeObjectEqual(this,this.formData,"set","EnclosureManAdd");else{this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var a={busicode:"AccessoryTypeQuery",data:{accessoryTypeId:e.row.accessoryTypeId}};this.$api.fetch({params:a}).then(function(e){t.$refs.EnclosureManAdd.editData(e),t.common.chargeObjectEqual(t,e,"set","EnclosureManAdd")})}},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(e){this.tableQuery.page=e,this.selectTSubSystem()},selectTSubSystem:function(){var e=this,t={busicode:"AccessoryTypeList",data:this.tableQuery};this.$api.fetch({params:t}).then(function(t){e.appServerData=t,e.common.changeTable(e,".EnclosureMan .kl-table",[".EnclosureMan .block"])})},closeDialog:function(){this.EnclosureManShow=!0,this.EnclosureManAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.EnclosureManAdd.handleClose()},submitForm:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.EnclosureManAdd.submitForm(e,t)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"EnclosureMan"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.EnclosureManShow,expression:"EnclosureManShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.EnclosureManAddVisible,expression:"EnclosureManAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("EnclosureManAddRuleForm")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.EnclosureManShow,expression:"EnclosureManShow"}],staticClass:"kl-table"},[e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.appServerData.list,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"accessoryNo",label:"附件编号","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"accessoryObj",label:"附件对象","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"accessoryName",label:"附件名称","min-width":"100","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"pjType",label:"拍照类型","min-width":"100",formatter:e.stateFormat}}),e._v(" "),a("el-table-column",{attrs:{prop:"sortNo",label:"排序号","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.appAdd(t)}}},[e._v("编辑")])]}}],null,!1,1191321586)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.appServerData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.EnclosureManAddVisible,expression:"EnclosureManAddVisible"}]},[a("EnclosureManAdd",{ref:"EnclosureManAdd"})],1)])])},staticRenderFns:[]};var l=a("VU/8")(r,o,!1,function(e){a("RO+r")},null,null);t.default=l.exports}});