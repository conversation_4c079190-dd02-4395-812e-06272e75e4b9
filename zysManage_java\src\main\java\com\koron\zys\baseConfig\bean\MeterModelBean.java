package com.koron.zys.baseConfig.bean;

import java.math.BigDecimal;

/**
 * 水表型号
 *
 * <AUTHOR>
 */
public class MeterModelBean extends BaseBean {
	/**
	 * 水表型号编号
	 */
	private String modelNo;
    /**
     * 型号名称
     */
    private String modelName;
    /**
     * 厂商
     */
    private String factoryId;
    /**
     * 厂商
     */
    private String factoryName;
    /**
     * 设备类型
     */
    private String deviceType;
    /**
     * 设备类型
     */
    private String deviceTypeName;
    /**
     * 水表类式
     */
    private String meterType;
    /**
     * 水表类式
     */
    private String meterTypeName;
    /**
     * 是否是阀控表
     */
    private int valveControl;
    /**
     * 是否是阀控表
     */
    private String valveControlName;
    /**
     * 传输方式
     */
    private String transWay;
    /**
     * 传输方式
     */
    private String transWayName;
    /**
     * 下行规约
     */
    private String protocol;
    /**
     * 下行规约
     */
    private String protocolName;
    /**
     * 水表型式
     */
    private String meterForm;
    /**
     * 水表型式
     */
    private String meterFormName;
    /**
     * 精度
     */
    private String accuracy;
    /**
     * 精度
     */
    private String accuracyName;
    /**
     * R值
     */
    private double r;
    /**
     * Q值
     */
    private double q;
    /**
     * 流量范围
     */
    private String qRange;
    /**
     * 水表传感器
     */
    private String meterSensor;
    /**
     * 水表传感器
     */
    private String meterSensorName;
    /**
     * 口径
     */
    private String meterBore;
    /**
     * 口径
     */
    private String meterBoreName;
    /**
     * 满码值
     */
    private BigDecimal maxValue;
    /**
     * 使用周期
     */
    private String shelfLife;
    /**
     * 预留字段1
     */
    private String reserve1;
    /**
     * 预留字段2
     */
    private String reserve2;
    /**
     * 备注
     */
    private String comments;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 排序号
     */
    private Integer sortNo;
    /**
     * 首测校验周期
     */
    private Integer firstCheckLife;
    /**
     * 校验周期
     */
    private Integer checkLife;
    
	public Integer getFirstCheckLife() {
		return firstCheckLife;
	}
	public void setFirstCheckLife(Integer firstCheckLife) {
		this.firstCheckLife = firstCheckLife;
	}
	public Integer getCheckLife() {
		return checkLife;
	}
	public void setCheckLife(Integer checkLife) {
		this.checkLife = checkLife;
	}
	public String getModelNo() {
		return modelNo;
	}
	public void setModelNo(String modelNo) {
		this.modelNo = modelNo;
	}
	public String getModelName() {
		return modelName;
	}
	public void setModelName(String modelName) {
		this.modelName = modelName;
	}
	public String getFactoryId() {
		return factoryId;
	}
	public void setFactoryId(String factoryId) {
		this.factoryId = factoryId;
	}
	
	public int getValveControl() {
		return valveControl;
	}
	public void setValveControl(int valveControl) {
		this.valveControl = valveControl;
	}
	
	public double getR() {
		return r;
	}
	public void setR(double r) {
		this.r = r;
	}
	public double getQ() {
		return q;
	}
	public void setQ(double q) {
		this.q = q;
	}
	public String getqRange() {
		return qRange;
	}
	public void setqRange(String qRange) {
		this.qRange = qRange;
	}
	public String getMeterBore() {
		return meterBore;
	}
	public void setMeterBore(String meterBore) {
		this.meterBore = meterBore;
	}
	public BigDecimal getMaxValue() {
		return maxValue;
	}
	public void setMaxValue(BigDecimal maxValue) {
		this.maxValue = maxValue;
	}
	public String getReserve1() {
		return reserve1;
	}
	public void setReserve1(String reserve1) {
		this.reserve1 = reserve1;
	}
	public String getReserve2() {
		return reserve2;
	}
	public void setReserve2(String reserve2) {
		this.reserve2 = reserve2;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Integer getSortNo() {
		return sortNo;
	}
	public void setSortNo(Integer sortNo) {
		this.sortNo = sortNo;
	}
	public String getDeviceType() {
		return deviceType;
	}
	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}
	public String getMeterType() {
		return meterType;
	}
	public void setMeterType(String meterType) {
		this.meterType = meterType;
	}
	public String getTransWay() {
		return transWay;
	}
	public void setTransWay(String transWay) {
		this.transWay = transWay;
	}
	public String getProtocol() {
		return protocol;
	}
	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}
	public String getMeterForm() {
		return meterForm;
	}
	public void setMeterForm(String meterForm) {
		this.meterForm = meterForm;
	}
	public String getAccuracy() {
		return accuracy;
	}
	public void setAccuracy(String accuracy) {
		this.accuracy = accuracy;
	}
	public String getMeterSensor() {
		return meterSensor;
	}
	public void setMeterSensor(String meterSensor) {
		this.meterSensor = meterSensor;
	}
	public String getShelfLife() {
		return shelfLife;
	}
	public void setShelfLife(String shelfLife) {
		this.shelfLife = shelfLife;
	}
	public String getFactoryName() {
		return factoryName;
	}
	public void setFactoryName(String factoryName) {
		this.factoryName = factoryName;
	}
	public String getDeviceTypeName() {
		return deviceTypeName;
	}
	public void setDeviceTypeName(String deviceTypeName) {
		this.deviceTypeName = deviceTypeName;
	}
	
	public String getMeterTypeName() {
		return meterTypeName;
	}
	public void setMeterTypeName(String meterTypeName) {
		this.meterTypeName = meterTypeName;
	}
	
	public String getValveControlName() {
		return valveControlName;
	}
	public void setValveControlName(String valveControlName) {
		this.valveControlName = valveControlName;
	}
	public String getTransWayName() {
		return transWayName;
	}
	public void setTransWayName(String transWayName) {
		this.transWayName = transWayName;
	}
	public String getProtocolName() {
		return protocolName;
	}
	public void setProtocolName(String protocolName) {
		this.protocolName = protocolName;
	}
	public String getMeterFormName() {
		return meterFormName;
	}
	public void setMeterFormName(String meterFormName) {
		this.meterFormName = meterFormName;
	}
	public String getAccuracyName() {
		return accuracyName;
	}
	public void setAccuracyName(String accuracyName) {
		this.accuracyName = accuracyName;
	}
	public String getMeterSensorName() {
		return meterSensorName;
	}
	public void setMeterSensorName(String meterSensorName) {
		this.meterSensorName = meterSensorName;
	}
	public String getMeterBoreName() {
		return meterBoreName;
	}
	public void setMeterBoreName(String meterBoreName) {
		this.meterBoreName = meterBoreName;
	}
	
    
}
