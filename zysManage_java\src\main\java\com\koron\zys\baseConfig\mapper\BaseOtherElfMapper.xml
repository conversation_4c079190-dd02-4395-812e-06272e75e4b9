<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BaseOtherElfMapper">
	<select id="selectList"
		resultType="com.koron.zys.baseConfig.bean.BaseOtherElfBean">
		
		select * from base_other_elf
	</select>
	<insert id="addBaseOtherElf" 
		parameterType="com.koron.zys.baseConfig.bean.BaseOtherElfBean">
		insert into base_other_elf(id,notice_type,notice_staff,create_time,create_name)
		values
		<foreach collection="list" separator="," item="i">
          (#{i.id},#{i.noticeType},#{i.noticeStaff},#{i.createTime},#{i.createName})
        </foreach>
	</insert>
	<delete id="delBaseOtherElf">
		delete from base_other_elf
	</delete>
</mapper>


