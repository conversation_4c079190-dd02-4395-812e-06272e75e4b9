package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BookBean;
import com.koron.zys.baseConfig.mapper.BookMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
/**
 * 册本信息-添加
 * <AUTHOR>
 */
public class BookAdd implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(BookAdd.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			BookMapper mapper = factory.getMapper(BookMapper.class);
			BookBean bean = JsonUtils.objectToPojo(req.getData(), BookBean.class);
			if(StringUtils.isBlank(bean.getBookNo())){
				return MessageBean.create(Constant.MESSAGE_INT_FAIL, "册本号不能为空。", void.class);
			}
			if(StringUtils.isBlank(bean.getBookName())){
				return MessageBean.create(Constant.MESSAGE_INT_FAIL, "册本名称不能为空。", void.class);
			}
			bean.setId(new ObjectId().toHexString());
			bean.setStatus("1");
			bean.setCreateName(userInfo.getUserInfo().getName());
			bean.setCreateAccount(userInfo.getUserInfo().getAcount());
			bean.setCreateTime(CommonUtils.getCurrentTime());
			mapper.insert(bean);
		} catch (Exception e) {
			logger.error("操作失败", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
}