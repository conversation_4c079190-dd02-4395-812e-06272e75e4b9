<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.common.web.mapper.PubDepartmentNewMapper">

	<resultMap id="BaseResultMap" type="com.koron.common.bean.PubDepartmentBean">
		<!--@mbg.generated-->
		<!--@Table pub_department-->
		<id column="id" property="id" />
		<result column="name" property="name" />
		<result column="shortName" property="shortName" />
		<result column="parentId" property="parentId" />
		<result column="seq" property="seq" />
		<result column="groupCode" property="groupCode" />
		<result column="code" property="code" />
	</resultMap>

	<select id="selectById" parameterType="java.lang.String" resultType="com.koron.common.bean.PubDepartmentBean">
      SELECT * FROM pub_department WHERE id = #{id}
    </select>

	<update id="updateByPrimaryKeySelective" parameterType="com.koron.common.bean.PubDepartmentBean">
		<!--@mbg.generated-->
		update pub_department
		<set>
			<if test="name != null">
				`name` = #{name},
			</if>
			<if test="shortName != null">
				shortName = #{shortName},
			</if>
			<if test="parentId != null">
				parentId = #{parentId},
			</if>
			<if test="seq != null">
				seq = #{seq},
			</if>
			<if test="groupCode != null">
				groupCode = #{groupCode},
			</if>
			<if test="code != null">
				code = #{code},
			</if>
		</set>
		where id = #{id}
	</update>

	<insert id="insert" parameterType="com.koron.common.bean.PubDepartmentBean">
		<!--@mbg.generated-->
		insert into pub_department (id, `name`, shortName, parentId, seq, groupCode, code)
		values (#{id}, #{name}, #{shortName}, #{parentId}, #{seq}, #{groupCode}, #{code})
	</insert>


	<select id="selectSupId" resultMap="BaseResultMap" parameterType="java.lang.String">
    SELECT * FROM pub_department WHERE code = #{code}
  </select>

	<update id="updateParentId" parameterType="java.lang.String">
    UPDATE pub_department SET parentId = #{parentId} WHERE id = #{id}
  </update>

	<select id="getDeptByCode" resultType="java.util.Map">
    SELECT id,name FROM pub_department WHERE code = #{code}
  </select>

	<insert id="insertStaffByMap" parameterType="java.util.Map">
      insert into tblstaff (id,`name`, code, departmentCode, `position`, phone, mobile, email, sex, idcard, status, loginid, weighting, photourl, openid, userid,departmentName)
    values(#{id},#{name},#{code},#{departmentCode},#{position},#{phone},#{mobile},#{email},#{sex},#{idcard},#{status},#{loginid},#{weight},#{photo},#{openid},#{userid},#{departmentName})
  </insert>

	<select id="list" parameterType="java.lang.String" resultType="com.koron.common.bean.PubDepartmentBean">
		SELECT *
		FROM pub_department
		<where>
			<choose>
				<when test="id != null and id != ''">
					<choose>
						<when test="type != null and type == 'one'">
							id = #{id}
						</when>
						<otherwise>
							parentId = #{id}
						</otherwise>
					</choose>
				</when>
				<otherwise>
					parentId is null
				</otherwise>
			</choose>
		</where>
    </select>

	<select id="listStaff" parameterType="java.lang.String" resultType="java.util.Map">
		SELECT *
		FROM tblstaff
		<where>
			<if test="departmentCode != null and departmentCode != ''">
				AND departmentCode = #{departmentCode}
			</if>
			<if test="name != null and name != ''">
				AND name LIKE CONCAT('%',#{name},'%')
			</if>
		</where>
	</select>

	<select id = "selectByCode" resultType="com.koron.common.bean.StaffBean">
		SELECT
			id,
			name,
			code,
			departmentCode,
			departmentName,
			position,
			phone,
			mobile,
			email,
			sex,
			idcard,
			status,
			loginid,
			weighting,
			photourl,
			openid,
			userid
		FROM
		tblstaff
		where code =#{code}
	</select>

	<select id="selectList" resultType="com.koron.common.bean.StaffBean">
		SELECT
			id,
			name,
			code,
			departmentCode,
			departmentName,
			position,
			phone,
			mobile,
			email,
			sex,
			idcard,
			status,
			loginid,
			weighting,
			photourl,
			openid,
			userid
		FROM
		tblstaff
		<where>
			<if test="code != null and code != ''">
				AND code = #{code}
			</if>
			<if test="name != null and name != ''">
				AND name like CONCAT('%',#{name},'%')
			</if>
			<if test="departmentCode != null and departmentCode != ''">
				AND departmentCode = #{departmentCode}
			</if>
		</where>
    </select>
</mapper>