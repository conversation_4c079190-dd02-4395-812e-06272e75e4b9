package com.koron.zys.baseConfig.impl;

import java.io.File;
import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.common.utils.ExcelUtils;
import com.koron.zys.serviceManage.bean.KpiBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.KpiMapper;
import com.koron.util.Constant;
import com.koron.util.Tools;

public class KpiFillData implements ServerInterface{
	
	private static Logger logger = LoggerFactory.getLogger(KpiFillData.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			KpiMapper mapper = factory.getMapper(KpiMapper.class);
			File file = new File("E:\\workspace_git\\customer_service_ysxt\\文档\\客服\\客服优化系统功能需求说明书V1.7.6.xlsx");
			List<String[]> data = ExcelUtils.readExcelData(file, false);
			for(String[] array : data) {
				KpiBean bean = new KpiBean();
				bean.setId(Tools.getObjectId());
				bean.setThemeName(array[0]);
				bean.setThemeUnique(array[1]);
				bean.setSubThemeName(array[2]);
				bean.setSubThemeUnique(array[3]);
				bean.setKpiUnique(array[4]);
				bean.setKpiName(array[5]);
				bean.setKpiComments(array[6]);
				bean.setUnit(array[9]);
				bean.setKpiLevel(1);
				int iCount = mapper.insertKpi(bean);
				if(iCount > 0) {
					logger.info("添加成功：{}", array[3]);
				}
			}
		} catch (Exception e) {
			logger.error("操作失败", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
	
}
