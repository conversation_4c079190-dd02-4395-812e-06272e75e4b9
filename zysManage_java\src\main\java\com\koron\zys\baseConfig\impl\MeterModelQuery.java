package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterFactoryBean;
import com.koron.zys.baseConfig.bean.MeterModelBean;
import com.koron.zys.baseConfig.mapper.MeterBoreMapper;
import com.koron.zys.baseConfig.mapper.MeterFactoryMapper;
import com.koron.zys.baseConfig.mapper.MeterModelMapper;
import com.koron.zys.baseConfig.queryBean.MeterModelQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
import com.koron.util.Tools;

/**
 * 水表型号-编辑初始化
 *
 * <AUTHOR>
 */
public class MeterModelQuery implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MeterModelQuery.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        MessageBean<MeterModelBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", MeterModelBean.class);
        try {
            MeterModelQueryBean bean = JsonUtils.objectToPojo(req.getData(), MeterModelQueryBean.class);
            MeterModelMapper mapper = factory.getMapper(MeterModelMapper.class);
            if (StringUtils.isEmpty(bean.getId())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "水表型号id为空", void.class);
            }
            MeterModelBean query = mapper.selectMeterModelById(bean.getId());
            if(query==null)
            	return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", MeterModelBean.class);
            //水表厂商
            MeterFactoryMapper meterFactoryMapper=factory.getMapper(MeterFactoryMapper.class);
            MeterFactoryBean factoryBean =  meterFactoryMapper.selectMeterFactoryById(query.getFactoryId());     
            query.setFactoryName(factoryBean==null?query.getFactoryId():factoryBean.getFactoryName());
            //设备类型
            query.setDeviceTypeName(Tools.getDicNameByCodeAndValue(factory,"SBLX",query.getDeviceType()));
            //水表类型T
            query.setMeterTypeName(Tools.getDicNameByCodeAndValue(factory,"MMT",query.getMeterType()));
            if(query.getValveControl()==0) {
            	query.setValveControlName("否");
            }
            if(query.getValveControl()==1) {
            	query.setValveControlName("是");
            }
            //传输方式
            query.setTransWayName(Tools.getDicNameByCodeAndValue(factory,"TPW",query.getTransWay()));
            //下行传输规约
            query.setProtocolName(Tools.getDicNameByCodeAndValue(factory,"XXGY",query.getProtocol()));
            //下行传输规约
            query.setProtocolName(Tools.getDicNameByCodeAndValue(factory,"XXGY",query.getProtocol()));
            //水表型式
            query.setMeterFormName(Tools.getDicNameByCodeAndValue(factory,"MMP",query.getMeterForm()));
            //精度
            query.setAccuracyName(Tools.getDicNameByCodeAndValue(factory,"JD",query.getAccuracy()));
            //水表传感器
            query.setMeterSensorName(Tools.getDicNameByCodeAndValue(factory,"MSS",query.getMeterSensor()));
            //口径
            MeterBoreMapper meterBoreMapper = factory.getMapper(MeterBoreMapper.class);
            query.setMeterBoreName(meterBoreMapper.selectMeterBoreById(query.getMeterBore()).getBoreName());
            info.setData(query);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }
}
