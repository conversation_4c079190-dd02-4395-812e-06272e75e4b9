package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.koron.zys.baseConfig.bean.CostDetailBean;
import com.koron.zys.baseConfig.bean.CostDetailLadderBean;
import com.koron.zys.baseConfig.queryBean.CostDetailQueryBean;
import com.koron.zys.baseConfig.vo.CostDetailVO;

public interface CostDetailMapper {
	
	/**
	 * 查询列表
	 * 
	 * @return
	 */
	List<CostDetailVO> selectCostDetailList(CostDetailQueryBean costDetailQueryBean);
	
	/**
	 * 根据id查询 编辑初始化
	 * @param factoryId
	 * @return
	 */
	List<CostDetailBean> selectCostDetailById(@Param("costDetailId") String costDetailId);

	/**
	 * 添加费用
	 * 
	 * @param CostDetailBean
	 * @return
	 */
	void insertCostDetail(CostDetailBean costDetailBean);
	
	/**
	 * 校验字段内容重复
	 */
	@Select("select count(*) from BASE_COST_DETAIL where ${key} = #{val}")
	Integer check(@Param("key") String key, @Param("val") String val);
	
	
	/**
	 * 校验字段内容重复-排除当前记录
	 */
	@Select("select count(*) from BASE_COST_DETAIL where ${key} = #{val} and cost_detail_id <> #{id}")
	Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);
	
	/**
	 * 修改费用
	 * 
	 * @param CostDetailBean
	 * @return
	 */
	Integer updateCostDetail(CostDetailBean costDetailBean);
	
	/**
	 * 批量添加阶梯阶梯信息
	 * @param ladderlist
	 */
	 void insertLadderList(@Param("ladderlist")List<CostDetailLadderBean> ladderlist);
	 
	 /**
	  * 批量删除阶梯信息
	  * @param costDetailLadderIds
	  */
	 void DeleteLadderList(@Param("costDetailLadderIds") List<String> costDetailLadderIds);
	 
	 /**
	  * 批量更新阶梯信息
	  * @param list
	  * @return
	  * @throws Exception
	  */
	 Integer updateLadderList(@Param(value = "ladderlist") List<CostDetailLadderBean> ladderlist);
	 
	 /**
	  * 添加阶梯类型信息
	  * @param costDetailLadderBean
	  */
	void insertLadder(CostDetailLadderBean costDetailLadderBean);
	 
	 /**
	  * 修改阶梯类型信息
	  * @param costDetailBean
	  * @return
	  */
	 Integer updateLadder(CostDetailLadderBean costDetailLadderBean);
	 
	 /**
	  * 删除阶梯信息
	  * @param costDetailLadderId
	  */
	 void delLadder(@Param("costDetailLadderId") String costDetailLadderId);
	 
	

}
