package com.koron.common.bean;

import com.koron.common.bean.LongTreeBean;

/**
 * 部门树形实体
 */
public class DepartmentTreeBean extends LongTreeBean{
	/**
	 * 部门名称
	 */
	private String name;
	/**
	 * 部门code
	 */
	private String departmentCode;
	/**
	 * 组织架构code
	 */
	private String orgCode;
	/**
	 * 部门类型
	 */
	private int deptType;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDepartmentCode() {
		return departmentCode;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}
	
	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public int getDeptType() {
		return deptType;
	}

	public void setDeptType(int deptType) {
		this.deptType = deptType;
	}

	@Override
	public String toString() {
		return "DepartmentTreeBean [name=" + name + ", departmentCode=" + departmentCode + ", orgCode=" + orgCode
				+ ", deptType=" + deptType + "]";
	}
	
	

}
