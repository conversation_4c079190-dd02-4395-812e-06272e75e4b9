package com.koron.zys.baseConfig.bean;

/**
 * 票据类型
 * <AUTHOR>
 * 2020年1月16日
 */
public class InvoiceTypeBean extends BaseBean {

	/*
	 * 主键
	 */
	private String id;
	/*
	 * 票据编号
	 */
	private String invoiceNo;
	/*
	 * 票据名称
	 */
	private String invoiceName;
	/*
	 * 备注
	 */
	private String comments;
	/*
	 * 状态
	 */
	private String status;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getInvoiceNo() {
		return invoiceNo;
	}
	public void setInvoiceNo(String invoiceNo) {
		this.invoiceNo = invoiceNo;
	}
	public String getInvoiceName() {
		return invoiceName;
	}
	public void setInvoiceName(String invoiceName) {
		this.invoiceName = invoiceName;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
}
