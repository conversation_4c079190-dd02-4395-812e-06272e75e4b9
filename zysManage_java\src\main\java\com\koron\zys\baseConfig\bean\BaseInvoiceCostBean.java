package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;

public class BaseInvoiceCostBean extends BaseBean{
	private String costId;
	
	@Check(name = "票据类型", notEmpty = true)
	private String invoiceNo;
	
	@Check(name = "商品编号", notEmpty = true)
	private String commodityNo;
	
	@Check(name = "商品名称", notEmpty = true)
	private String commodityName;

	@Check(name = "是否为主营收入", notEmpty = true)
	private Integer isMain;

	//是否可开专票
	private Integer isSpecialTax;

	private String commodityUnit;
	
	@Check(name = "税率", notEmpty = true, min=1)
	private String taxRate;
	private String comments;
	private String status;
	public String getCostId() {
		return costId;
	}
	public void setCostId(String costId) {
		this.costId = costId;
	}
	public String getInvoiceNo() {
		return invoiceNo;
	}
	public void setInvoiceNo(String invoiceNo) {
		this.invoiceNo = invoiceNo;
	}
	public String getCommodityNo() {
		return commodityNo;
	}
	public void setCommodityNo(String commodityNo) {
		this.commodityNo = commodityNo;
	}
	public String getCommodityName() {
		return commodityName;
	}
	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}
	public String getCommodityUnit() {
		return commodityUnit;
	}
	public void setCommodityUnit(String commodityUnit) {
		this.commodityUnit = commodityUnit;
	}
	public String getTaxRate() {
		return taxRate;
	}
	public void setTaxRate(String taxRate) {
		this.taxRate = taxRate;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getIsMain() {
		return isMain;
	}

	public void setIsMain(Integer isMain) {
		this.isMain = isMain;
	}

	public Integer getIsSpecialTax() {
		return isSpecialTax;
	}

	public void setIsSpecialTax(Integer isSpecialTax) {
		this.isSpecialTax = isSpecialTax;
	}
}
