package com.koron.zys.baseConfig.impl;

import java.util.List;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.TradeClassifyMapper;
import com.koron.zys.baseConfig.vo.SelectVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

/**
 * 行业分类下拉框
 * 
 * <AUTHOR>
 *
 */
public class TradeClassifySelect implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			TradeClassifyMapper mapper = factory.getMapper(TradeClassifyMapper.class,"_default");
			// 获取下拉框
			List<SelectVO> list = mapper.tradeClassifySelect();
			info.setData(list);
			return info;
		} catch (Exception e) {
			logger.error("行业分类查询失败" + e.getMessage(), e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "行业分类查询失败", null);
		}

	}
}
