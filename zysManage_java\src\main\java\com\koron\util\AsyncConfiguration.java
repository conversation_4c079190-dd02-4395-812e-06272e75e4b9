package com.koron.util;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * 并发使用
 * 
 * <AUTHOR> 2020年11月11日
 */
@Configuration
@EnableScheduling
public class AsyncConfiguration {

	//线程池数量
	private static final int POOL_SIZE = 100;

	public static int getPoolSize() {
		return POOL_SIZE;
	}

	/**
	 * 声明一个线程池
	 * @return ThreadPoolTaskScheduler
	 */
	@Bean
	public ThreadPoolTaskScheduler scheduledExecutorService() {
		ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
		//设置线程池容量
        scheduler.setPoolSize(POOL_SIZE);
        //线程名前缀
        scheduler.setThreadNamePrefix("ThreadPoolTaskScheduler-");
        //等待时常
        scheduler.setAwaitTerminationSeconds(60);
        //当调度器shutdown被调用时等待当前被调度的任务完成
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        //当任务被取消的同时从当前调度器移除的策略
        scheduler.setRemoveOnCancelPolicy(true);
		return scheduler;
	}
}
