package com.koron.util;

public enum TypeEnum {
	
	CHAR("String", "CHAR"),
	
	TIME("Date", "TIME"),
	
	VARCHAR("String", "VARCHAR"),
	
	INT("Integer", "INTEGER"),
	
	DECIMAL("BigDecimal", "DECIMAL"),
	
	DOUBLE("Double", "DOUBLE"),
	
	DATETIME("Date", "DATE"),
	
	DATE("Date", "DATE"),
	
	TEXT("String", "VARCHAR"),
	
	BIGINT("Long", "BIGINT"),
	
	LONGTEXT("String", "LONGVARCHAR"),

	BINARY("Byte", "LONGVARBINARY"),

	BLOB("byte[]", "BLOB");

	private String javaType;
	
	private String jdbcType;
	
	public String getJdbcType() {
		return jdbcType;
	}

	public void setJdbcType(String jdbcType) {
		this.jdbcType = jdbcType;
	}

	public String getJavaType() {
		return javaType;
	}

	TypeEnum(String javaType, String jdbcType) {
		this.javaType = javaType;
		this.jdbcType = jdbcType;
	}

}
