package com.koron.common.bean;

import java.io.Serializable;

/**
 * 应用角色实体bean
 */
public class AppRoleBean implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 操作的主键id
	 */
	private Integer id;
	/**
	 * 操作名称
	 */
	private String name;
	/**
	 * 角色的code编码
	 */
	private String code;
	/**
	 * 参数
	 */
	private String attr;
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getAttr() {
		return attr;
	}
	public void setAttr(String attr) {
		this.attr = attr;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	@Override
	public String toString() {
		return "AppRoleBean [id=" + id + ", name=" + name + ", code=" + code + ", attr=" + attr + "]";
	}
	

}
