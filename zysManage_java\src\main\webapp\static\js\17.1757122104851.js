webpackJsonp([17],{"7Wah":function(e,t){},FAI2:function(e,t){},sV20:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dataImportEdit"},[a("el-form",{ref:"dataImportEditForm",staticClass:"formBill-Two",attrs:{inline:!0,size:"mini",rules:e.rules,model:e.formData,"label-width":"130px"}},[a("el-form-item",{attrs:{label:"导入表名：",prop:"importFileName"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入导入表名"},model:{value:e.formData.importFileName,callback:function(t){e.$set(e.formData,"importFileName",t)},expression:"formData.importFileName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"导入文件描述：",prop:"importFileContent"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入导入文件描述"},model:{value:e.formData.importFileContent,callback:function(t){e.$set(e.formData,"importFileContent",t)},expression:"formData.importFileContent"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"数据源：",prop:"dbName"}},[a("el-select",{attrs:{placeholder:"请选择数据源",clearable:""},model:{value:e.formData.dbName,callback:function(t){e.$set(e.formData,"dbName",t)},expression:"formData.dbName"}},e._l(e.serverDbNameArr,function(e,t){return a("el-option",{key:t,attrs:{value:e.value,label:e.label}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"文件上传: ",prop:"fileImport"}},[a("el-upload",{staticClass:"upload-file",staticStyle:{display:"inline-block","margin-right":"10px"},attrs:{action:"/","http-request":e.uploadAttachment,"on-remove":e.handleRemove,accept:".xlsx"}},[a("el-button",{attrs:{size:"mini",type:"primary",disabled:e.importDisabled}},[e._v("上传附件")])],1)],1)],1)],1)},staticRenderFns:[]};var r={name:"dataImport",components:{dataImportEdit:a("VU/8")({name:"dataImportEdit",data:function(){return{formData:{id:"",createTime:"",createName:"",importFileName:"",importFileContent:"",dbName:"",fileImport:""},rules:{importFileName:[{required:!0,message:"请输入导入表名",trigger:"blur"}],importFileContent:[{required:!0,message:"请输入导入文件描述",trigger:"blur"}],dbName:[{required:!0,message:"请输入状态数据源",trigger:"blur"}],fileImport:[{required:!0,message:"请上传文件",trigger:["blur","change"]}]},maxHeight:500,NotDisabled:!1,serverDbNameArr:[],importDisabled:!1}},mounted:function(){this.dbSourceDicData()},methods:{submitForm:function(e){var t=this,a=this,i=!1;if(a.$refs.dataImportEditForm.validate(function(e){e||(i=!0)}),i)a.$message({message:"信息未填写完整",type:"warning"});else if(""!=this.formData.fileImport){var r={busicode:"DataOperationImport",type:"import",data:{importFileName:"temp_"+this.formData.importFileName,importFileContent:this.formData.importFileContent,dbName:this.formData.dbName,file:this.formData.fileImport}};this.$api.fetch({params:r}).then(function(e){a.$message({message:"保存成功！",type:"success"}),t.$parent.init(),t.$parent.closeDialog()})}else a.$message({message:"文件未上传",type:"warning"})},uploadAttachment:function(e){this.formData.fileImport=e.file,this.importDisabled=!0},dbSourceDicData:function(){var e=this;this.$api.fetch({params:{busicode:"DbSourceDic",data:{}}}).then(function(t){t.forEach(function(t,a){var i={};i.label=t.groupCodeName+"("+t.groupCode+")",i.value=""+t.groupCode,e.serverDbNameArr.push(i)})})},handleRemove:function(){this.formData.fileImport="",this.importDisabled=!1}}},i,!1,function(e){a("FAI2")},"data-v-530c1b40",null).exports},data:function(){return{tableData:[],tableQuery:{page:1,pageCount:20,importFileName:"",importFileContent:"",dbName:"",openCreateTime:"",endCreateTime:""},EditVisible:!1,formData:{postNo:"",postName:"",tenantId:"",sortNo:"",status:"",comments:""},crumbsData:{titleList:[{title:"租户管理",path:"/tenant"},{title:"数据导入",method:function(){window.histroy.back()}}]},tableShow:!1,maxHeight:0,formType:"",NotDisabled:!1,serverDbNameArr:[]}},mounted:function(){var e=this;eventBus.$emit("asideMenuShow","basicsMenuShow1"),this.init(),this.dbSourceDicData(),this.$nextTick(function(){e.common.changeTable(e,".dataImport .dataImportIndex","block")})},methods:{init:function(){var e=this,t=this,a={busicode:"DataOperationImportList",data:this.tableQuery};this.$api.fetch({params:a}).then(function(a){t.tableData=a,e.$nextTick(function(){t.common.changeTable(e,".dataImportIndex",[".dataImportIndex .toolbar",".dataImportIndex .block"])})})},add:function(e){this.EditVisible=!0},search:function(){this.tableQuery.page=1,this.init()},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.init()},handleCurrentChange:function(e){this.tableQuery.page=e,this.init()},closeDialog:function(){this.EditVisible=!1,this.init()},submitForm:function(){this.$refs.dataImportEdit.submitForm(this.formType)},dbSourceDicData:function(){var e=this;this.$api.fetch({params:{busicode:"DbSourceDic",data:{}}}).then(function(t){t.forEach(function(t,a){var i={};i.label=t.groupCodeName+"("+t.groupCode+")",i.value=""+t.groupCode,e.serverDbNameArr.push(i)})})}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dataImport"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),e.EditVisible?a("div",{staticClass:"bread-contain-right"},[e.NotDisabled?e._e():a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm()}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.closeDialog}},[e._v("返回")])],1):a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-plus",type:"primary"},on:{click:function(t){return e.add("add")}}},[e._v("添加")])],1)],1),e._v(" "),e.EditVisible?a("dataImportEdit",{ref:"dataImportEdit"}):e._e(),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.EditVisible,expression:"!EditVisible"}],staticClass:"dataImportIndex"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,size:"mini",model:e.formData}},[a("el-form-item",{staticClass:"form-left"},[a("el-form-item",{attrs:{label:"创建时间: "}},[a("el-date-picker",{attrs:{type:"date",clearable:"",format:"yyyy-MM-dd",placeholder:"开始时间","value-format":"yyyy-MM-dd"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.openCreateTime,callback:function(t){e.$set(e.tableQuery,"openCreateTime",t)},expression:"tableQuery.openCreateTime"}}),e._v(" "),a("span",[e._v("-")]),e._v(" "),a("el-form-item",{attrs:{label:""}},[a("el-date-picker",{attrs:{type:"date",clearable:"",format:"yyyy-MM-dd",placeholder:"结束时间","value-format":"yyyy-MM-dd"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.endCreateTime,callback:function(t){e.$set(e.tableQuery,"endCreateTime",t)},expression:"tableQuery.endCreateTime"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"结束时间: "}}),e._v(" "),a("el-form-item",{attrs:{label:"导入表名："}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入导入表名"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.importFileName,callback:function(t){e.$set(e.tableQuery,"importFileName",t)},expression:"tableQuery.importFileName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"导入文件描述："}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入导入文件描述"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.importFileContent,callback:function(t){e.$set(e.tableQuery,"importFileContent",t)},expression:"tableQuery.importFileContent"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"数据源："}},[a("el-select",{attrs:{placeholder:"请选择数据源",clearable:""},model:{value:e.tableQuery.dbName,callback:function(t){e.$set(e.tableQuery,"dbName",t)},expression:"tableQuery.dbName"}},e._l(e.serverDbNameArr,function(e,t){return a("el-option",{key:t,attrs:{value:e.value,label:e.label}})}),1)],1),e._v(" "),a("el-form-item",[a("el-button",{staticClass:"searchBtn",attrs:{icon:"el-icon-search"},on:{click:e.search}})],1)],1)],1)],1),e._v(" "),a("div",{staticClass:"kl-table"},[e.tableShow?a("el-table",{staticClass:"wuserInfo-table",attrs:{"max-height":e.maxHeight,stripe:"",border:"",data:e.tableData.list}},[a("el-table-column",{attrs:{type:"index",label:"NO.",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"importFileName",label:"导入表名"}}),e._v(" "),a("el-table-column",{attrs:{prop:"importFileContent",label:"导入文件描述"}}),e._v(" "),a("el-table-column",{attrs:{prop:"dbName",label:"数据源"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createName",label:"建立人"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"建立时间"}})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[e._m(0),e._v(" "),a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[20,50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)])],1)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"tipInfo"},[t("p",[this._v("使用说明：")]),this._v(" "),t("p",[this._v("1、根据所填【导入表名】作为【数据库表名】、导入excel第一个行数据作为【数据库字段名】，默认字段类型text，生成数据库表结构")]),this._v(" "),t("p",[this._v("2、从导入excel文件第二行数据开始作为【数据库表记录】")]),this._v(" "),t("p",[this._v("3、生成的数据库表名会默认增加前缀【temp_】，此功能仅为了将数据导入临时表，具体和正式表的业务操作，需自行写sql操作")])])}]};var o=a("VU/8")(r,l,!1,function(e){a("7Wah")},null,null);t.default=o.exports}});