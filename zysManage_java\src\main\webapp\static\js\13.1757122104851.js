webpackJsonp([13],{fDj9:function(e,t){},lVnA:function(e,t){},vVzJ:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("BO1k"),r=a.n(i),l=a("mvHQ"),n=a.n(l),o=a("woOf"),s=a.n(o),c={name:"syplatformBuildAdd",data:function(){return{databaseData:[],chargingBidList:[],operationList:[],ruleForm:{id:"",bid:"",bankName:"",ifCheck:"",passwd:"",reconciliationUrl:"",signCheckUrl:"",signUrl:"",unsignUrl:"",withholdUrl:"",ipWhite:"",companyId:"",payChannel:""},props:{value:"id",label:"name"},bankTreeData:[],companyNo:"",formData:{id:"",bid:"",bankName:"",ifCheck:"",passwd:"",reconciliationUrl:"",signCheckUrl:"",signUrl:"",unsignUrl:"",withholdUrl:"",ipWhite:"",companyId:"",payChannel:""},bankDriverClassList:[],BankData:[],rules:{bid:[{required:!0,message:"请输入机构编号",trigger:"blur"}],bankName:[{required:!0,message:"请输入机构名称",trigger:"blur"}],payChannel:[{required:!0,message:"请输入缴费渠道",trigger:"blur"}]},gridData:[{fieldName:"",fieldValue:""}],otherConfiguration:""}},mounted:function(){},methods:{add:function(){this.gridData.push({fieldName:"",fieldValue:""})},deleteRow:function(e,t){var a=this;this.$confirm("是否确认删除该行?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){a.gridData.splice(e,1),a.$message({type:"success",message:"删除成功！"})}).catch(function(){a.$message({type:"info",message:"已取消删除"})})},reset:function(){this.gridData=[]},uploadAttachment:function(e){var t=this,a={busicode:"add"==e?"WaterBankConfigAdd":"WaterBankConfigUpdate",data:{bid:this.ruleForm.bid,tenantId:this.ruleForm.tenantId,createTime:this.ruleForm.createTime,createName:this.ruleForm.createName,createAccount:this.ruleForm.createAccount,updateTime:this.ruleForm.updateTime,updateName:this.ruleForm.updateName,updateAccount:this.ruleForm.updateAccount,bankName:this.ruleForm.bankName,ifCheck:this.ruleForm.ifCheck,passwd:this.ruleForm.passwd,reconciliationUrl:this.ruleForm.reconciliationUrl,signCheckUrl:this.ruleForm.signCheckUrl,signUrl:this.ruleForm.signUrl,unsignUrl:this.ruleForm.unsignUrl,withholdUrl:this.ruleForm.withholdUrl,ipWhite:this.ruleForm.ipWhite,companyId:this.ruleForm.companyId,payChannel:this.ruleForm.payChannel,otherConfiguration:this.otherConfiguration},sysType:"001"};"update"==e&&(a.data=s()({},a.data,{id:this.ruleForm.id})),this.$api.fetch({params:a}).then(function(e){t.$message({message:"添加成功！",type:"success"}),t.resetForm(),t.$parent.getInstitutionList(),t.$parent.closeDialog()})},resetForm:function(){this.$refs.syplatformBuildAddRuleForm.resetFields()},getArr:function(e){return function e(t){t.map(function(t){!1===t.isParent?delete t.children:e(t.children)})}(e.children),e},chargingTypeChange:function(e){this.formData.ifCheck=e},getBankInstitutionList:function(){var e=this,t={busicode:"BaseBankConfigList",data:{companyNo:e.companyNo}};this.$api.fetch({params:t}).then(function(t){e.chargingBidList=t})},BankDataOption:function(e){var t=this,a={busicode:"BankSelect",data:{companyNo:e}};this.$api.fetch({params:a}).then(function(e){t.BankData=e})},submitForm:function(e,t,a){var i=this,r=this,l={};this.ruleForm.companyNo=a,this.ruleForm=this.common.handleData(this.ruleForm,this.formData),this.$refs[e].validate(function(e){if(e){var a=i.gridData,o="";if(a.length>0)for(var s=0;s<a.length;s++)if(!a[s].fieldName||""==a[s].fieldName)return a[s].fieldName&&""!=a[s].fieldName||(o="第"+(s+1)+"行字段名不能为空"),void i.$message({message:o,type:"warning"});for(var c=0;c<a.length;c++)l[a[c].fieldName]=a[c].fieldValue;i.otherConfiguration=n()(l),"添加"===t?r.uploadAttachment("add"):r.uploadAttachment("update")}})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","syplatformBuildAdd",this.$parent.closeDialog)},editData:function(e,t){this.ruleForm=e,this.companyNo=t;var a=[];if(e.otherConfiguration){var i=JSON.parse(e.otherConfiguration);for(var r in i)a.push({fieldName:r,fieldValue:i[r]})}this.gridData=a,this.getBankInstitutionList()},getBankNameBybid:function(e){var t=!0,a=!1,i=void 0;try{for(var l,n=r()(this.chargingBidList);!(t=(l=n.next()).done);t=!0){var o=l.value;if(o.bid===e)return o.outBankName}}catch(e){a=!0,i=e}finally{try{!t&&n.return&&n.return()}finally{if(a)throw i}}return""},chargingBid:function(e){console.log(e),console.log(this.chargingBidList),this.ruleForm.bankName=this.getBankNameBybid(e)}}},u={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"syplatformBuildAdd"},[a("el-form",{ref:"syplatformBuildAddRuleForm",staticClass:"formBill",attrs:{model:e.ruleForm,size:"mini",rules:e.rules,"label-width":"160px",inline:!0}},[a("legend",{staticClass:"legendColumn"},[e._v("基本信息")]),e._v(" "),a("el-form-item",{attrs:{label:"机构编号：",prop:"bid"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",size:"mini"},on:{change:e.chargingBid},model:{value:e.ruleForm.bid,callback:function(t){e.$set(e.ruleForm,"bid",t)},expression:"ruleForm.bid"}},e._l(e.chargingBidList,function(e,t){return a("el-option",{key:""+t+e.bid,attrs:{label:e.bid,value:e.bid}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"机构名称：",prop:"bankName"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.bankName,callback:function(t){e.$set(e.ruleForm,"bankName",t)},expression:"ruleForm.bankName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"签约短信验证：",prop:"ifCheck"}},[a("el-select",{attrs:{placeholder:"请选择",clearable:"",size:"mini"},on:{change:e.chargingTypeChange},model:{value:e.ruleForm.ifCheck,callback:function(t){e.$set(e.ruleForm,"ifCheck",e._n(t))},expression:"ruleForm.ifCheck"}},[a("el-option",{attrs:{label:"需要",value:1}}),e._v(" "),a("el-option",{attrs:{label:"不需要",value:0}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"机构密码：",prop:"passwd"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.passwd,callback:function(t){e.$set(e.ruleForm,"passwd",t)},expression:"ruleForm.passwd"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"对账接口地址：",prop:"reconciliationUrl"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.reconciliationUrl,callback:function(t){e.$set(e.ruleForm,"reconciliationUrl",t)},expression:"ruleForm.reconciliationUrl"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"签约解约验证接口地址：",prop:"signCheckUrl"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.signCheckUrl,callback:function(t){e.$set(e.ruleForm,"signCheckUrl",t)},expression:"ruleForm.signCheckUrl"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"签约接口地址：",prop:"signUrl"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.signUrl,callback:function(t){e.$set(e.ruleForm,"signUrl",t)},expression:"ruleForm.signUrl"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"解约接口地址：",prop:"unsignUrl"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.unsignUrl,callback:function(t){e.$set(e.ruleForm,"unsignUrl",t)},expression:"ruleForm.unsignUrl"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"代扣出盘通知接口地址：",prop:"withholdUrl"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.withholdUrl,callback:function(t){e.$set(e.ruleForm,"withholdUrl",t)},expression:"ruleForm.withholdUrl"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"IP白名单：",prop:"ipWhite"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.ipWhite,callback:function(t){e.$set(e.ruleForm,"ipWhite",t)},expression:"ruleForm.ipWhite"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"水司编号：",prop:"companyId"}},[a("el-input",{attrs:{disabled:""},model:{value:e.ruleForm.companyId,callback:function(t){e.$set(e.ruleForm,"companyId",t)},expression:"ruleForm.companyId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"缴费渠道：",prop:"payChannel"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.payChannel,callback:function(t){e.$set(e.ruleForm,"payChannel",t)},expression:"ruleForm.payChannel"}})],1)],1),e._v(" "),a("div",{staticStyle:{width:"100%","line-height":"40px"}},[a("div",{staticStyle:{width:"40%",float:"left","margin-left":"10px"}},[e._v("其他配置：")]),e._v(" "),a("div",{staticStyle:{width:"50%",float:"right","text-align":"right","margin-right":"10px","margin-bottom":"10px"}},[a("el-button",{staticStyle:{height:"35px"},attrs:{type:"warning",icon:"el-icon-refresh-left",size:"mini"},on:{click:e.reset}},[e._v("重置")]),e._v(" "),a("el-button",{staticStyle:{"margin-left":"0",height:"35px"},attrs:{type:"success",icon:"el-icon-plus",size:"mini"},on:{click:e.add}},[e._v("添加")])],1)]),e._v(" "),a("div",{staticClass:"kl-table",staticStyle:{height:"280px"}},[a("el-table",{attrs:{data:e.gridData,border:"","max-height":280}},[a("el-table-column",{attrs:{type:"index",label:"NO.","min-width":"50"}}),e._v(" "),a("el-table-column",{attrs:{label:"字段名","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{attrs:{size:"mini",oninput:"value=value.replace(/\\ +/g,'').replace(/[\\r\\n]/g,'')",placeholder:"请输入字段名",clearable:""},model:{value:t.row.fieldName,callback:function(a){e.$set(t.row,"fieldName",a)},expression:"scope.row.fieldName"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"字段值","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{attrs:{size:"mini",oninput:"value=value.replace(/\\ +/g,'').replace(/[\\r\\n]/g,'')",placeholder:"请输入字段值",clearable:""},model:{value:t.row.fieldValue,callback:function(a){e.$set(t.row,"fieldValue",a)},expression:"scope.row.fieldValue"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.deleteRow(t.$index,t.row)}}},[e._v("删除")])]}}])})],1)],1)],1)},staticRenderFns:[]};var d={name:"syplatformBuild",components:{syplatformBuildAdd:a("VU/8")(c,u,!1,function(e){a("fDj9")},"data-v-7712a093",null).exports,autoTree:a("yJVD").a},data:function(){return{tableShow:!1,maxHeight:0,appServerData:[],formData:{bid:"",bankName:"",ifCheck:"",passwd:"",reconciliationUrl:"",signCheckUrl:"",signUrl:"",unsignUrl:"",withholdUrl:"",ipWhite:"",companyId:"",payChannel:""},crumbsData:{titleList:[{title:"水司配置",path:"/waterSet"},{title:"水银平台配置",method:function(){window.histroy.back()}}]},syplatformBuildShow:!0,syplatformBuildAddVisible:!1,treeDatas:{tree:[{shortName:"根目录",id:"2",children:[]}],defaultProps:{label:"shortName",children:"children"},inputProp:{showSearch:!1,Inp_placeholder:"请输入节点"},sendTreeProp:["code","shortName","districtArr","children","companyNo","group","isLeaf","isParent","parent","sonData"],rootName:"根目录"},treeParantId:"",companyNo:"C000001"}},mounted:function(){var e=this;this.getTreeDatas(),this.$nextTick(function(){e.common.changeTable(e,".syplatformBuild .kl-table",[])})},methods:{formatCheck:function(e){return 1==e.ifCheck?"需要":"不需要"},getInstitutionList:function(){var e=this,t={busicode:"WaterBankConfigList",data:{companyNo:this.companyNo}};this.$api.fetch({params:t}).then(function(t){e.appServerData=t})},appAdd:function(e){this.syplatformBuildShow=!1,this.syplatformBuildAddVisible=!0;var t=this;this.$nextTick(function(){if("add"===e)t.formData={bid:"",bankName:"",ifCheck:"",passwd:"",reconciliationUrl:"",signCheckUrl:"",signUrl:"",unsignUrl:"",withholdUrl:"",ipWhite:"",companyId:this.companyNo,payChannel:""},t.crumbsData.titleList.push({title:"添加",method:function(){window.histroy.back()}}),t.common.chargeObjectEqual(t,t.formData,"set","syplatformBuildAdd"),t.$refs.syplatformBuildAdd.editData(t.formData,t.companyNo);else{t.crumbsData.titleList.push({title:"编辑",method:function(){window.histroy.back()}});var a={busicode:"WaterBankConfigQuery",data:{id:e.row.id}};t.$api.fetch({params:a}).then(function(e){t.$refs.syplatformBuildAdd.editData(e,t.companyNo),t.common.chargeObjectEqual(t,e,"set","syplatformBuildAdd")})}})},indexMethod:function(e){return e+1},getTreeDatas:function(){var e=this,t=this;this.$api.fetch({params:{busicode:"CompanyNameList",data:{}}}).then(function(a){t.treeDatas.tree[0].children=a,t.treeDatas.tree[0].companyNo=a[0].companyNo,t.companyNo=a[0].companyNo,e.$set(e.crumbsData.titleList,"2",{title:"根目录",method:function(){window.histroy.back()}})})},closeDialog:function(){this.syplatformBuildShow=!0,this.syplatformBuildAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.syplatformBuildAdd.handleClose()},submitForm:function(e){var t=this.crumbsData.titleList[3].title;this.$refs.syplatformBuildAdd.submitForm(e,t,this.companyNo)},backTreeData:function(e){var t=this,a=this;this.companyNo=e.companyNo,this.$set(this.crumbsData.titleList,"2",{title:e.shortName,method:function(){window.histroy.back()}});var i={busicode:"WaterBankConfigList",data:{companyNo:this.companyNo}};this.$api.fetch({params:i}).then(function(e){console.log(e,"列表"),a.appServerData=e,t.common.changeTable(t,".syplatformBuild .kl-table",[])})}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},m={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"syplatformBuild"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.syplatformBuildShow,expression:"syplatformBuildShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.syplatformBuildAddVisible,expression:"syplatformBuildAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("syplatformBuildAddRuleForm")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.syplatformBuildShow,expression:"syplatformBuildShow"}],staticClass:"company-content"},[a("div",{staticClass:"company-left"},[a("auto-tree",{attrs:{treeData:e.treeDatas},on:{sendTreeData:e.backTreeData}})],1),e._v(" "),a("div",{staticClass:"kl-table company-right"},[e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.appServerData,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"bid",label:"机构编号","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"bankName",label:"机构名称","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"ifCheck","min-width":"100",label:"签约短信验证",formatter:e.formatCheck}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.appAdd(t)}}},[e._v("编辑")])]}}],null,!1,**********)})],1):e._e()],1)]),e._v(" "),e.syplatformBuildAddVisible?a("syplatformBuildAdd",{ref:"syplatformBuildAdd",attrs:{formData:e.formData}}):e._e()],1)])},staticRenderFns:[]};var h=a("VU/8")(d,m,!1,function(e){a("lVnA")},null,null);t.default=h.exports}});