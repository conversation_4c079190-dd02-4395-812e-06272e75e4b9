package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.AlipayConfigBean;
import com.koron.zys.baseConfig.mapper.AlipayConfigMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

/**
 * 支付便编辑-新增
 *
 * <AUTHOR>
 */
public class AlipayConfigAdd implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(AlipayConfigAdd.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            AlipayConfigMapper mapper = factory.getMapper(AlipayConfigMapper.class);
            AlipayConfigBean bean = JsonUtils.objectToPojo(req.getData(), AlipayConfigBean.class);
            bean.setCreateInfo(userInfo);
            mapper.insertAlipayConfig(bean);
        } catch (Exception e) {
            logger.error("添加支付宝配置失败", e);
            return MessageBean.create(Constant.ILLEGAL_PARAMETER, "添加支付宝配置失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}