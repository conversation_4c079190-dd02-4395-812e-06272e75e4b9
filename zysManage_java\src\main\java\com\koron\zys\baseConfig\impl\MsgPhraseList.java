package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MsgPhraseBean;
import com.koron.zys.baseConfig.mapper.MsgPhraseMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

/**
 * 短信模板-列表
 *
 * <AUTHOR>
 */
public class MsgPhraseList implements ServerInterface {
    private static Logger logger = LoggerFactory.getLogger(MsgPhraseList.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        @SuppressWarnings("rawtypes")
        MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
        try {
            MsgPhraseMapper mapper = factory.getMapper(MsgPhraseMapper.class);
            List<MsgPhraseBean> beans = mapper.selectMsgPhrase();
            info.setCode(Constant.MESSAGE_INT_SUCCESS);
            info.setDescription("success");
            info.setData(beans);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription("操作失败");
            logger.error("操作失败", e);
        }
        return info;
    }
}
