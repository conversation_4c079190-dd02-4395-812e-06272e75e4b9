package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.InvoiceTypeBean;
import com.koron.zys.baseConfig.mapper.InvoiceTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
/**
 * 票据信息-修改
 * <AUTHOR>
 */
public class InvoiceTypeUpdate implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(InvoiceTypeUpdate.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			InvoiceTypeMapper mapper = factory.getMapper(InvoiceTypeMapper.class);
			InvoiceTypeBean bean = JsonUtils.objectToPojo(req.getData(), InvoiceTypeBean.class);
			if(StringUtils.isBlank(bean.getId())){
				return MessageBean.create(Constant.MESSAGE_INT_FAIL, "主键不能为空。", void.class);
			}
			if(StringUtils.isBlank(bean.getInvoiceNo())){
				return MessageBean.create(Constant.MESSAGE_INT_FAIL, "票据编号号不能为空。", void.class);
			}
			if(StringUtils.isBlank(bean.getInvoiceName())){
				return MessageBean.create(Constant.MESSAGE_INT_FAIL, "票据名称不能为空。", void.class);
			}
			bean.setStatus("1");
			bean.setUpdateName(userInfo.getUserInfo().getName());
			bean.setUpdateAccount(userInfo.getUserInfo().getAcount());
			bean.setUpdateTime(CommonUtils.getCurrentTime());
			mapper.update(bean);
		} catch (Exception e) {
			logger.error("操作失败", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
}