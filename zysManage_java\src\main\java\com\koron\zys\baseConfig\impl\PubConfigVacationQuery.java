package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ConfigVacationBean;
import com.koron.zys.baseConfig.mapper.ConfigVacationMapper;
import com.koron.zys.baseConfig.queryBean.ConfigVacationQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 假期设置-编辑初始化
 *
 * <AUTHOR>
 */
public class PubConfigVacationQuery implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(PubConfigVacationQuery.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        @SuppressWarnings("rawtypes")
        MessageBean<ConfigVacationBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", ConfigVacationBean.class);

        try {
            ConfigVacationQueryBean bean = JsonUtils.objectToPojo(req.getData(), ConfigVacationQueryBean.class);
            ConfigVacationMapper mapper = factory.getMapper(ConfigVacationMapper.class,"_default");
            if (StringUtils.isEmpty(bean.getId())){
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
            }
            ConfigVacationBean ConfigVacationbean = mapper.selectConfigVacationById(bean.getId());
            info.setData(ConfigVacationbean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }
}
