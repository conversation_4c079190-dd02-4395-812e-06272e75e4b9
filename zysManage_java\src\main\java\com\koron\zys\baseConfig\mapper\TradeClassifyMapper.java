package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.koron.ebs.mybatis.EnvSource;

import com.koron.zys.baseConfig.bean.TradeClassifyBean;
import com.koron.zys.baseConfig.queryBean.TradeClassifyQueryBean;
import com.koron.zys.baseConfig.vo.SelectVO;
import com.koron.zys.baseConfig.vo.TradeClassifyVO;
@EnvSource("_default")
public interface TradeClassifyMapper {
	
	/**
	 * 查询列表
	 * 
	 * @return
	 */
	List<TradeClassifyVO> selectTradeClassifyList(TradeClassifyQueryBean tradeClassifyQueryBean);
	/**
	 * 下拉框
	 * 
	 * @return
	 */
	List<SelectVO> tradeClassifySelect();
	
	/**
	 * 
	 * @param tradeClassifyId
	 * @return
	 */
	TradeClassifyBean selectTradeClassifyById(@Param("id") String id);
	/**
	 * 根据名称查询
	 * @param tradeName
	 * @return
	 */
	@Select("select * from PUB_TRADE_CLASSIFY where trade_name = #{tradeName}")
	TradeClassifyBean selectTradeClassifyByName(@Param("tradeName") String tradeName);
	/**
	 * 添加
	 * 
	 * @param tradeClassifyBean
	 * @return
	 */
	void insertTradeClassify(TradeClassifyBean tradeClassifyBean);
	
	/**
	 * 校验字段内容重复
	 */
	@Select("select count(*) from PUB_TRADE_CLASSIFY where ${key} = #{val}")
	Integer check(@Param("key") String key, @Param("val") String val);
	
	/**
	 * 校验字段内容重复-排除当前记录
	 */
	@Select("select count(*) from PUB_TRADE_CLASSIFY where ${key} = #{val} and ID <> #{id}")
	Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);
	
	
	/**
	 * 修改
	 * 
	 * @param tradeClassifyBean
	 * @return
	 */
	Integer updateTradeClassify(TradeClassifyBean tradeClassifyBean);


}
