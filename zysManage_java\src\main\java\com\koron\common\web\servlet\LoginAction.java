package com.koron.common.web.servlet;

import java.io.UnsupportedEncodingException;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.koron.util.*;
import com.koron.util.verifyCode.SimpleCharVerifyCodeGen;
import com.koron.util.verifyCode.VerifyCode;
import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.ADOConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.swan.bean.MessageBean;

import com.koron.zys.ApplicationConfig;
import com.koron.zys.serviceManage.bean.ServicePtUserBean;
import com.koron.zys.serviceManage.bean.ServiceUserBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.service.ServiceUserService;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.zys.serviceManage.utils.RedisUtils;
import com.koron.zys.systemManage.dto.UserAuthorityDto;
import com.koron.zys.systemManage.service.UserService;

@Controller
public class LoginAction {

	@Autowired
	private ServiceUserService serviceUserService;
	
	@Autowired
	private UserService userService;

	private static Logger logger = LoggerFactory.getLogger(LoginAction.class);

	/**
	 * 运维平台的登陆
	 * 
	 * @param userBean
	 * @return
	 */
	@RequestMapping(value = "/loginMaintain.api", method = RequestMethod.POST)
	@ResponseBody
	public String loginMaintain(@RequestBody ServiceUserBean userBean,
								HttpServletRequest request, HttpServletResponse response) {
		MessageBean<String> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "登录成功", String.class);
		try {
			if (StringUtils.isAnyBlank(userBean.getLoginName(), userBean.getPassword())) {
				info.setDescription("请输入账号、密码！");
				info.setCode(Constant.ILLEGAL_PARAMETER);
				return info.toJson();
			}
			String verifyCode = RedisUtils.get("VerifyCode");
			if (StringUtils.isBlank(verifyCode)){
				info.setDescription("验证码已过期！");
				info.setCode(Constant.MESSAGE_INT_FAIL);
				return info.toJson();
			}else {
				String relCode = verifyCode.toString();
				if(!relCode.equalsIgnoreCase(userBean.getVerifyCode())){
					info.setDescription("验证码错误！");
					info.setCode(Constant.MESSAGE_INT_FAIL);
					return info.toJson();
				}
			}
			// 根据登陆名，读取运维账户信息
			ServiceUserBean serviceUserBean = ADOConnection.runTask("_default", serviceUserService, "selectByLoginName", ServiceUserBean.class, userBean);
			if (serviceUserBean == null) {
				info.setDescription("无权限");
				info.setCode(Constant.NO_AUTH);
			} else if (!serviceUserBean.getPassword().equals(SM3.encrypt(userBean.getPassword()))) {
				info.setCode(Constant.MESSAGE_INT_PWDERROR);
				info.setDescription("账号或密码不正确");
			} else {
				String token = UUID.randomUUID().toString().replace("-", "");
				info.setData(token);// 返回token至前端
				UserInfoBean userInfo = new UserInfoBean();
				userInfo.setCurService(Constant.APP_DEVOPS);
				ServicePtUserBean servicePtUserBean = new ServicePtUserBean();// 本方法用于运维登陆会话信息
				servicePtUserBean.setAcount(serviceUserBean.getLoginName());
				servicePtUserBean.setName(serviceUserBean.getUserName());
				userInfo.setUserInfo(servicePtUserBean);
				RedisUtils.setEx("uma_" + token, ApplicationConfig.getExpireTime(), userBean.getToken());
				RedisUtils.setEx(token, ApplicationConfig.getExpireTime(), JsonUtils.objectToJson(userInfo));
				loginRecord(token, userInfo, "营收运维平台");
			}
		} catch (Exception e) {
			logger.error("数据库服务器异常", e);
			info.setCode(Constant.MESSAGE_DBFAIL);
			info.setDescription("数据库服务器异常");
		}
		return info.toJson();
	}

	/**
	 * 记录登录信息
	 * 
	 * @param token
	 * @param userInfo
	 * @return
	 */
	private void loginRecord(String token, UserInfoBean userInfo, String moduleName) {
		ADOConnection.runTask("_default", serviceUserService, "loginLog", userInfo.getUserInfo().getAcount(), 1, "用户登录", moduleName);
	}
	
	private void cacheUserAuthority(UserInfoBean userInfo, String appId, String token) {
		RedisUtils.del("user_auth_" + token + "_" + userInfo.getUserInfo().getAcount());
		String dbEv = DBSourceUtils.getDbEnv(userInfo.getCurWaterCode());
		ADOConnection.runTask(dbEv, factory -> userService.getUserAuthority(factory, userInfo, appId, token), UserAuthorityDto.class);
	}

	@RequestMapping(value="/verifyCode.htm")
	@ResponseBody
	public void verifyCode( HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException {
		try {
			//设置长宽
			VerifyCode verifyCode = SimpleCharVerifyCodeGen.generate(80, 35);
			String code = verifyCode.getCode();
			RedisUtils.setEx("VerifyCode", 30, code);
			////将VerifyCode绑定session
			//request.getSession().setAttribute("VerifyCode", code);
			//request.getSession().setMaxInactiveInterval(30);
			//设置响应头
			response.setHeader("Pragma", "no-cache");
			//设置响应头
			response.setHeader("Cache-Control", "no-cache");
			//在代理服务器端防止缓冲
			response.setDateHeader("Expires", 0);
			//设置响应内容类型
			response.setContentType("image/jpeg");
			response.getOutputStream().write(verifyCode.getImgBytes());
			response.getOutputStream().flush();
		} catch (Exception e) {
			logger.error("获取登录验证码失败", e);
		}
	}
}