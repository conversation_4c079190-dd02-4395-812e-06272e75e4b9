package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BaseArrearageElfBean;
import com.koron.zys.baseConfig.mapper.ArrearageElfMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

/**
 * 欠费催缴精灵保存
 */
public class ArrearageElfSave implements ServerInterface {
    @Override
    @ValidationKey(clazz = BaseArrearageElfBean.class,method="insert")
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            ArrearageElfMapper mapper = factory.getMapper(ArrearageElfMapper.class);
            //获取短信模板信息
            BaseArrearageElfBean baseArrearageElfBean=JsonUtils.objectToPojo(req.getData(), BaseArrearageElfBean.class);
            if (null == baseArrearageElfBean) {
                return MessageBean.create(Constant.NOT_NULL, "参数为空", void.class);
            }
            //先清空历史记录
            mapper.deleteAllArrearageElf();
            // 再添加新纪录
            baseArrearageElfBean.setCreateInfo(userInfo);
            Integer integer = mapper.insertArrearageElf(baseArrearageElfBean);
        } catch (Exception e) {
            factory.close(false);
            logger.error("欠费催缴精灵保存失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "欠费催缴精灵保存失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}
