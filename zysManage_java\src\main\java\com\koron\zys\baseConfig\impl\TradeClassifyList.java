package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.baseConfig.mapper.TradeClassifyMapper;
import com.koron.zys.baseConfig.vo.TradeClassifyVO;
import com.koron.zys.baseConfig.queryBean.TradeClassifyQueryBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 行业分类-列表初始化
 * 
 * <AUTHOR>
 *
 */
public class TradeClassifyList implements ServerInterface  {

	private static Logger logger = LoggerFactory.getLogger(TradeClassifyList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
		try {
			TradeClassifyQueryBean bean = JsonUtils.objectToPojo(req.getData(), TradeClassifyQueryBean.class);
			TradeClassifyMapper mapper = factory.getMapper(TradeClassifyMapper.class,"_default");			
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			List<TradeClassifyVO> list = mapper.selectTradeClassifyList(bean);			
			info.setData(new PageInfo<>(list));
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}
