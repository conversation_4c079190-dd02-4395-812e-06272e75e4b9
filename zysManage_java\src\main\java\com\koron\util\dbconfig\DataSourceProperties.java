package com.koron.util.dbconfig;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DataSourceProperties {
	
	@Value("${spring.datasource.hikari.readOnly}")
	private Boolean readOnly;

	@Value("${spring.datasource.hikari.connectionTimeout}")
	private Long connectionTimeout;

	@Value("${spring.datasource.hikari.idleTimeout}")
	private Long idleTimeout;

	@Value("${spring.datasource.hikari.validationTimeout}")
	private Long validationTimeout;

	@Value("${spring.datasource.hikari.maxLifetime}")
	private Long maxLifetime;

	@Value("${spring.datasource.hikari.loginTimeout}")
	private Integer loginTimeout;

	@Value("${spring.datasource.hikari.maximumPoolSize}")
	private Integer maximumPoolSize;

	@Value("${spring.datasource.hikari.minimumIdle}")
	private Integer minimumIdle;

	@Value("${spring.datasource.hikari.allow-pool-suspension}")
	private Boolean allowPoolSuspension;

	public Boolean getReadOnly() {
		return readOnly;
	}

	public Long getConnectionTimeout() {
		return connectionTimeout;
	}

	public Long getIdleTimeout() {
		return idleTimeout;
	}

	public Long getValidationTimeout() {
		return validationTimeout;
	}

	public Long getMaxLifetime() {
		return maxLifetime;
	}

	public Integer getLoginTimeout() {
		return loginTimeout;
	}

	public Integer getMaximumPoolSize() {
		return maximumPoolSize;
	}

	public Integer getMinimumIdle() {
		return minimumIdle;
	}

	public Boolean getAllowPoolSuspension() {
		return allowPoolSuspension;
	}

	public void setReadOnly(Boolean readOnly) {
		this.readOnly = readOnly;
	}

	public void setConnectionTimeout(Long connectionTimeout) {
		this.connectionTimeout = connectionTimeout;
	}

	public void setIdleTimeout(Long idleTimeout) {
		this.idleTimeout = idleTimeout;
	}

	public void setValidationTimeout(Long validationTimeout) {
		this.validationTimeout = validationTimeout;
	}

	public void setMaxLifetime(Long maxLifetime) {
		this.maxLifetime = maxLifetime;
	}

	public void setLoginTimeout(Integer loginTimeout) {
		this.loginTimeout = loginTimeout;
	}

	public void setMaximumPoolSize(Integer maximumPoolSize) {
		this.maximumPoolSize = maximumPoolSize;
	}

	public void setMinimumIdle(Integer minimumIdle) {
		this.minimumIdle = minimumIdle;
	}

	public void setAllowPoolSuspension(Boolean allowPoolSuspension) {
		this.allowPoolSuspension = allowPoolSuspension;
	}

}
