<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.koron.zys.baseConfig.mapper.CostInvoiceMapper">
<select id="select" parameterType="String" resultType="com.koron.zys.baseConfig.bean.CostInvoiceBean">
	select * from base_invoice_cost where cost_id = #{costId} and invoice_type = #{invoiceType}
</select> 
<insert id="insert" parameterType="com.koron.zys.baseConfig.bean.CostInvoiceBean">
	insert into base_invoice_cost(id,cost_id,invoice_type,commodity_no,commodity_name,commodity_unit,tax_rate,comments,status,tenant_id,create_time,create_name,create_account)
	values(#{id},#{costId},#{invoiceType},#{commodityNo},#{commodityName},#{commodityUnit},#{taxRate},#{comments},#{status},#{tenantId},#{createTime},#{createName},#{createAccount})
</insert>
<delete id="deleteAll">
	delete from base_invoice_cost
</delete>
</mapper>