package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;

/**
 * 欠费催缴精灵规格实体类
 */
public class BaseArrearageElfBean extends BaseBean {

    /**
     * 规格方式 1开账后，2下个月
     */
    private Integer ruleWay;

    /**
     * 规格值
     */
    private Integer ruleValue;

    /**
     * 违约前
     */
    @Check(name = "违约前", notNull = true)
    private Integer penaltyBefore;
    /**
     * 违约后
     */
    @Check(name = "违约后", notNull = true)
    private Integer penaltyLater;
    /**
     * 通知方式
     */
    @Check(name = "通知方式", notNull = true, min = 1)
    private Integer noticeWay;
    /**
     * 通知起始时间
     */
    private String beginTime;
    /**
     * 通知结束时间
     */
    private String endTime;

    public Integer getRuleWay() {
        return ruleWay;
    }

    public void setRuleWay(Integer ruleWay) {
        this.ruleWay = ruleWay;
    }

    public Integer getRuleValue() {
        return ruleValue;
    }

    public void setRuleValue(Integer ruleValue) {
        this.ruleValue = ruleValue;
    }

    public Integer getPenaltyBefore() {
        return penaltyBefore;
    }

    public void setPenaltyBefore(Integer penaltyBefore) {
        this.penaltyBefore = penaltyBefore;
    }

    public Integer getPenaltyLater() {
        return penaltyLater;
    }

    public void setPenaltyLater(Integer penaltyLater) {
        this.penaltyLater = penaltyLater;
    }

    public Integer getNoticeWay() {
        return noticeWay;
    }

    public void setNoticeWay(Integer noticeWay) {
        this.noticeWay = noticeWay;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
