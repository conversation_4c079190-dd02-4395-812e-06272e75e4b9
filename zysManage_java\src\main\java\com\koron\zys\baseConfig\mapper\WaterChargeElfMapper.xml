<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.WaterChargeElfMapper">
    <!--添加水费精灵  -->
    <insert id="insertWaterChargeElf" parameterType="com.koron.zys.baseConfig.bean.WaterChargeElfBean">
		insert into base_water_charge_elf(id,rule_way,rule_value, notice_way,begin_time,end_time,tenant_id,create_time, create_name,create_account)
		values
		(
		#{id},
		#{ruleWay},
		#{ruleValue},
		#{noticeWay},
		#{beginTime},
		#{endTime},
		#{tenantId},
		#{createTime},
		#{createName},
		#{createAccount}
		)
	</insert>
</mapper>