<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.common.mapper.PrintMapper">
	<select id="getBusinessId" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT BUSINESS_ID as businessId from BASE_PRINT_TEMPLATE WHERE ID=#{id}
	</select>
	<select id="getFieldList" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT FIELDS as fields from PUB_PRINT_BUSINESS WHERE ID=#{id}
	</select>
	<select id="getBusinuessIdByCode" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT ID as code from PUB_PRINT_BUSINESS WHERE CODE=#{code}
	</select>
	<select id="getTemplateDefault" resultType="java.util.HashMap" parameterType="java.lang.String">
		SELECT ID,ifnull(scope,'') SCOPE,NAME from BASE_PRINT_TEMPLATE WHERE BUSINESS_ID=#{id}  ORDER BY DEFAULT_FLAG DESC
	</select>
	<select id="getTemplate" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT TEMPLATE as fields from BASE_PRINT_TEMPLATE WHERE ID=#{id}
	</select>
	<update id="updateTemplate" parameterType="java.lang.String">
		UPDATE BASE_PRINT_TEMPLATE SET  TEMPLATE = #{template},HAS_DESIGN=1  WHERE ID=#{id}
	</update>
</mapper>
