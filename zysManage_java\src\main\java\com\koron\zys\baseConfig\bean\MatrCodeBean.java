package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;

/**
 * 材料信息表
 *
 * <AUTHOR>

public class MatrCodeBean extends BaseBean {
    /**
     * 材料定价主键id
     */
    private String matrPriceId;
    /**
     * 材料编号
     */
    private String matrNo;
    /**
     * 材料名称
     */
    private String matrName;
    /**
     * \
     * 材料规格
     */
    private String matrMode;
    /**
     * 材料数量
     */
    @Check(name = "材料数量", min = 0)
    private Integer matrNum;
    /**
     * 材料单位
     */
    private String matrUnit;
    /**
     * 材料单价（标准定价）
     */
    @Check(name = "标准定价", min = 0)
    private Double matrPrice;
    /**
     * 材料单价（水司定价）
     */
    @Check(name = "水司定价", min = 0)
    private Double price;

    /**
     * 层级编号，五位一级
     */
    private String classCode;
    /**
     * 父节点id
     */
    private String parentId;
    /**
     * 是否为叶子节点0否1是
     */
    private Integer isLeaf;
    /**
     * 排序号
     */
    private Integer sortNo;

    public String getMatrPriceId() {
        return matrPriceId;
    }

    public void setMatrPriceId(String matrPriceId) {
        this.matrPriceId = matrPriceId;
    }

    public String getMatrNo() {
        return matrNo;
    }

    public void setMatrNo(String matrNo) {
        this.matrNo = matrNo;
    }

    public String getMatrName() {
        return matrName;
    }

    public void setMatrName(String matrName) {
        this.matrName = matrName;
    }

    public String getMatrMode() {
        return matrMode;
    }

    public void setMatrMode(String matrMode) {
        this.matrMode = matrMode;
    }

    public Integer getMatrNum() {
        return matrNum;
    }

    public void setMatrNum(Integer matrNum) {
        this.matrNum = matrNum;
    }

    public String getMatrUnit() {
        return matrUnit;
    }

    public void setMatrUnit(String matrUnit) {
        this.matrUnit = matrUnit;
    }

    public Double getMatrPrice() {
        return matrPrice;
    }

    public void setMatrPrice(Double matrPrice) {
        this.matrPrice = matrPrice;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Integer getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }
}
