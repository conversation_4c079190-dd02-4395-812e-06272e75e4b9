package com.koron.zys.baseConfig.bean;

import com.koron.common.bean.query.BaseQueryBean;

public class SubCostQueryBean extends BaseQueryBean {
    private String costNo;  //费用类型
    private String subCostNo; //子费用类型编号
    private String subCostName; //子费用类型名称
    private String isQuota; //是否定额
    private String managers; //定额管理员

    public String getCostNo() {
        return costNo;
    }

    public void setCostNo(String costNo) {
        this.costNo = costNo;
    }

    public String getSubCostNo() {
        return subCostNo;
    }

    public void setSubCostNo(String subCostNo) {
        this.subCostNo = subCostNo;
    }

    public String getSubCostName() {
        return subCostName;
    }

    public void setSubCostName(String subCostName) {
        this.subCostName = subCostName;
    }

    public String getIsQuota() {
        return isQuota;
    }

    public void setIsQuota(String isQuota) {
        this.isQuota = isQuota;
    }

    public String getManagers() {
        return managers;
    }

    public void setManagers(String managers) {
        this.managers = managers;
    }
}
