<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.AlipayConfigMapper">

    <resultMap id="baseResultMap" type="com.koron.zys.baseConfig.bean.AlipayConfigBean">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="company_no" jdbcType="VARCHAR" property="companyNo"/>
        <result column="org_no" jdbcType="VARCHAR" property="orgNo"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="org_en_short_name" jdbcType="VARCHAR" property="orgEnShortName"/>
        <result column="free_pay" jdbcType="VARCHAR" property="freePay"/>
        <result column="sftp_ip" jdbcType="VARCHAR" property="sftpIp"/>
        <result column="sftp_port" jdbcType="INTEGER" property="sftpPort"/>
        <result column="sftp_user_name" jdbcType="INTEGER" property="sftpUserName"/>
        <result column="sftp_user_psw" jdbcType="TIMESTAMP" property="sftpUserPsw"/>
        <result column="last_date" jdbcType="VARCHAR" property="lastDate"/>
        <result column="rule_type" jdbcType="TIMESTAMP" property="ruleType"/>
        <result column="rule_value" jdbcType="VARCHAR" property="ruleValue"/>
		<result column="create_time" jdbcType="VARCHAR" property="createTime"/>
		<result column="create_account" jdbcType="VARCHAR" property="createAccount"/>
		<result column="create_name" jdbcType="VARCHAR" property="createName"/>
		<result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
		<result column="update_account" jdbcType="VARCHAR" property="updateAccount"/>
		<result column="update_name" jdbcType="VARCHAR" property="updateName"/>
    </resultMap>
    <select id="selectAlipayConfigList" parameterType="com.koron.zys.baseConfig.queryBean.AlipayConfigQueryBean"
            resultType="com.koron.zys.baseConfig.bean.AlipayConfigBean">
        select id,company_no,org_no,org_name,org_en_short_name,free_pay,sftp_ip,sftp_port,sftp_user_name,sftp_user_psw,
        last_date,rule_type,rule_value
        from pub_alipay_config
        where 1=1
        <if test="companyNo != null and companyNo != ''">
			and company_no like concat('%',#{companyNo},'%')
        </if>
		<if test="orgName != null and orgName != ''">
			and org_name like concat('%',#{orgName},'%')
		</if>
    </select>

    <insert id="insertAlipayConfig" parameterType="com.koron.zys.baseConfig.bean.AlipayConfigBean">
		insert into pub_alipay_config (id,company_no,org_no, org_name, org_en_short_name, free_pay,
		sftp_ip,sftp_port,sftp_user_name,sftp_user_psw,rule_type,rule_value,create_time,create_account,
		create_name)
		values
		(
		#{id},
		#{companyNo},
		#{orgNo},
		#{orgName},
		#{orgEnShortName},
		#{freePay},
		#{sftpIp},
		#{sftpPort},
		#{sftpUserName},
		#{sftpUserPsw},
		#{ruleType},
		#{ruleValue},
		sysdate(),
		#{createAccount},
		#{createName}
		)
	</insert>

    <update id="updateAlipayConfig" parameterType="com.koron.zys.baseConfig.bean.AlipayConfigBean">
		update pub_alipay_config
		set company_no = #{companyNo},
		    org_no = #{orgNo},
			org_name = #{orgName},
			org_en_short_name= #{orgEnShortName},
			free_pay = #{freePay},
			sftp_ip = #{sftpIp},
			sftp_port = #{sftpPort},
			sftp_user_name = #{sftpUserName},
			sftp_user_psw= #{sftpUserPsw},
			rule_type = #{ruleType},
			rule_value = #{ruleValue},
			update_time = sysdate(),
			update_account=#{updateAccount},
			update_name=#{updateName}
		    where id = #{id}
	</update>

</mapper>