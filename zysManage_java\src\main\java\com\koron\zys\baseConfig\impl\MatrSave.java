package com.koron.zys.baseConfig.impl;

import com.koron.zys.baseConfig.bean.MatrCodeBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MatrBean;
import com.koron.zys.baseConfig.mapper.MatrMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.ValidationKey;
import com.mysql.cj.util.StringUtils;

import java.util.List;

/**
 * 材料价格-保存
 *
 * <AUTHOR>
 */
public class MatrSave implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MatrSave.class);

    @Override
    @ValidationKey(clazz = MatrCodeBean.class,method = "update")
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            MatrMapper mapper = factory.getMapper(MatrMapper.class);
            List<MatrCodeBean> list = JsonUtils.objectToList(req.getData(), MatrCodeBean.class);
            if (null != list && list.size() > 0) {
                for (MatrCodeBean matrCodeBean : list) {
                    if (null != matrCodeBean.getPrice()) {
                        if (StringUtils.isNullOrEmpty(matrCodeBean.getMatrPriceId())) {
                            MatrBean bean = new MatrBean();
                            bean.setMatrNo(matrCodeBean.getMatrNo());
                            bean.setMatrPrice(matrCodeBean.getPrice());
                            bean.setCreateInfo(userInfo);
                            mapper.insertMatr(bean);
                        } else {
                            MatrBean bean = new MatrBean();
                            bean.setId(matrCodeBean.getMatrPriceId());
                            bean.setMatrNo(matrCodeBean.getMatrNo());
                            bean.setMatrPrice(matrCodeBean.getPrice());
                            bean.setUpdateInfo(userInfo);
                            mapper.updateMatr(bean);
                        }
                    }else{
                        MatrBean matrBean = mapper.selectMatrByMatrNo(matrCodeBean.getMatrNo());
                        if (null != matrBean){
                            mapper.delete(matrBean.getId());
                        }
                    }
                }
            } else {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "数据异常", void.class);
            }
        } catch (Exception e) {
            factory.close(false);
            logger.error("材料价格保存失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "材料价格保存失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}