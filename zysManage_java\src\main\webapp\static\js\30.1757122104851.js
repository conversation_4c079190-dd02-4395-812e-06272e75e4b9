webpackJsonp([30],{Vmsu:function(e,t){},ez0b:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"WatermeterModelEdit"},[a("el-form",{ref:"WatermeterModelEditForm",staticClass:"formBill-Two",attrs:{inline:!0,size:"mini",model:e.formData,rules:e.formRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"水表型号：",prop:"modelNo"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入水表型号"},model:{value:e.formData.modelNo,callback:function(t){e.$set(e.formData,"modelNo",t)},expression:"formData.modelNo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"型号名称：",prop:"modelName"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入型号名称"},model:{value:e.formData.modelName,callback:function(t){e.$set(e.formData,"modelName",t)},expression:"formData.modelName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"生产厂商：",prop:"factoryId"}},[a("el-select",{attrs:{clearable:"",placeholder:"生产厂商："},model:{value:e.formData.factoryId,callback:function(t){e.$set(e.formData,"factoryId",t)},expression:"formData.factoryId"}},e._l(e.meterFactoryData.list,function(e){return a("el-option",{key:e.id,attrs:{label:e.factoryName,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"设备类型：",prop:"deviceType"}},[a("el-select",{attrs:{placeholder:"请选择设备类型"},model:{value:e.formData.deviceType,callback:function(t){e.$set(e.formData,"deviceType",t)},expression:"formData.deviceType"}},e._l(e.dictionaryData.SBLX,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"水表类型：",prop:"meterType"}},[a("el-select",{attrs:{placeholder:"请选择水表类型"},model:{value:e.formData.meterType,callback:function(t){e.$set(e.formData,"meterType",t)},expression:"formData.meterType"}},e._l(e.dictionaryData.MMT,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否阀控：",prop:"valveControl"}},[a("el-select",{attrs:{placeholder:"请选择是否阀控"},model:{value:e.formData.valveControl,callback:function(t){e.$set(e.formData,"valveControl",t)},expression:"formData.valveControl"}},[a("el-option",{attrs:{label:"是",value:1}}),e._v(" "),a("el-option",{attrs:{label:"否",value:0}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"通讯类型：",prop:"transWay"}},[a("el-select",{attrs:{placeholder:"请选择通讯类型"},model:{value:e.formData.transWay,callback:function(t){e.$set(e.formData,"transWay",t)},expression:"formData.transWay"}},e._l(e.dictionaryData.TPW,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"下行规约：",prop:"protocol"}},[a("el-select",{attrs:{placeholder:"请选择下行规约"},model:{value:e.formData.protocol,callback:function(t){e.$set(e.formData,"protocol",t)},expression:"formData.protocol"}},e._l(e.dictionaryData.XXGY,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"结构类型：",prop:"meterForm"}},[a("el-select",{attrs:{placeholder:"请选择结构类型"},model:{value:e.formData.meterForm,callback:function(t){e.$set(e.formData,"meterForm",t)},expression:"formData.meterForm"}},e._l(e.dictionaryData.MMP,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"精度：",prop:"accuracy"}},[a("el-select",{attrs:{placeholder:"请选择精度"},model:{value:e.formData.accuracy,callback:function(t){e.$set(e.formData,"accuracy",t)},expression:"formData.accuracy"}},e._l(e.dictionaryData.JD,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"R值：",prop:"r"}},[a("el-input",{attrs:{clearable:"",oninput:"value=value.replace(/^(-)(\\d+).(\\d\\d).$/,'$1$2.$3').replace(/[^\\d.]/g,'')",placeholder:"请输入R值"},model:{value:e.formData.r,callback:function(t){e.$set(e.formData,"r",t)},expression:"formData.r"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"Q值：",prop:"q"}},[a("el-input",{attrs:{clearable:"",oninput:"value=value.replace(/^(-)(\\d+).(\\d\\d).$/,'$1$2.$3').replace(/[^\\d.]/g,'')",placeholder:"请输入Q值"},model:{value:e.formData.q,callback:function(t){e.$set(e.formData,"q",t)},expression:"formData.q"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"流量范围：",prop:"qRange"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入流量范围"},model:{value:e.formData.qRange,callback:function(t){e.$set(e.formData,"qRange",t)},expression:"formData.qRange"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"采样类型：",prop:"meterSensor"}},[a("el-select",{attrs:{placeholder:"请选择采样类型"},model:{value:e.formData.meterSensor,callback:function(t){e.$set(e.formData,"meterSensor",t)},expression:"formData.meterSensor"}},e._l(e.dictionaryData.MSS,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"口径：",prop:"meterBore"}},[a("el-select",{attrs:{clearable:"",placeholder:"水表口径"},model:{value:e.formData.meterBore,callback:function(t){e.$set(e.formData,"meterBore",t)},expression:"formData.meterBore"}},e._l(e.meterBoreData,function(e){return a("el-option",{key:e.id,attrs:{label:e.boreName,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"满码值：",prop:"maxValue"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入满码值"},model:{value:e.formData.maxValue,callback:function(t){e.$set(e.formData,"maxValue",t)},expression:"formData.maxValue"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"使用期限：",prop:"shelfLife"}},[a("el-input",{attrs:{oninput:"value=value.replace(/[^\\d\\.-]/g,'')",maxlength:"6",clearable:"",placeholder:"请输入使用期限"},model:{value:e.formData.shelfLife,callback:function(t){e.$set(e.formData,"shelfLife",e._n(t))},expression:"formData.shelfLife"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"首次校验周期：",prop:"firstCheckLife","label-width":"110px"}},[a("el-input",{attrs:{maxlength:"6",clearable:"",placeholder:"请输入首次校验周期"},model:{value:e.formData.firstCheckLife,callback:function(t){e.$set(e.formData,"firstCheckLife",e._n(t))},expression:"formData.firstCheckLife"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"校验周期",prop:"checkLife"}},[a("el-input",{attrs:{maxlength:"6",clearable:"",placeholder:"请输入校验周期"},model:{value:e.formData.checkLife,callback:function(t){e.$set(e.formData,"checkLife",e._n(t))},expression:"formData.checkLife"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"排序号：",prop:"sortNo"}},[a("el-input",{attrs:{maxlength:"6",clearable:"",placeholder:"请输入排序号"},model:{value:e.formData.sortNo,callback:function(t){e.$set(e.formData,"sortNo",e._n(t))},expression:"formData.sortNo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态：",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.formData.status,callback:function(t){e.$set(e.formData,"status",t)},expression:"formData.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),a("el-option",{attrs:{label:"停用",value:0}})],1)],1),e._v(" "),a("el-form-item",{staticClass:"f4",attrs:{label:"备注："}},[a("el-input",{attrs:{"show-word-limit":"",maxlength:"150",clearable:"",placeholder:"请输入备注",type:"textarea"},model:{value:e.formData.comments,callback:function(t){e.$set(e.formData,"comments",t)},expression:"formData.comments"}})],1)],1)],1)},staticRenderFns:[]};var l={components:{WatermeterModelEdit:a("VU/8")({name:"WatermeterModelEdit",data:function(){return{formData:{id:"",firstCheckLife:"",checkLife:"",modelNo:"",modelName:"",factoryId:"",deviceType:"",valveControl:0,meterType:"",transWay:"",protocol:"",meterForm:"",accuracy:"",r:"",q:"",qRange:"",meterSensor:"",meterBore:"",maxValue:"",comments:"",status:"",sortNo:"",shelfLife:"",tenantId:""},dictionaryData:[],meterFactoryData:{},meterBoreData:[],formRules:{modelNo:{required:!0,trigger:"blur",message:"请输入水表型号"},modelName:{required:!0,trigger:"blur",message:"请输入型号名称"},factoryId:{required:!0,trigger:"change",message:"请选择生产厂商"},deviceType:{required:!0,trigger:"change",message:"请选择设备类型"},meterType:{required:!0,trigger:"change",message:"请选择水表类型"},valveControl:{required:!0,trigger:"change",message:"请选择是否阀控"},transWay:{required:!0,trigger:"change",message:"请选择通讯类型"},meterBore:{required:!0,trigger:"change",message:"请选择口径"},shelfLife:{required:!0,trigger:"blur",message:"请输入使用期限"},status:{required:!0,trigger:"change",message:"请选择状态"}}}},mounted:function(){this.getData(),this.getMeterFactory(),this.getMeterBore()},methods:{getData:function(){var e=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"SBLX,MMT,TPW,XXGY,MMP,JD,MSS,TPW"}}).then(function(t){e.$set(e,"dictionaryData",t)})},resetForm:function(){this.$refs.WatermeterModelEditForm.resetFields()},getMeterBore:function(){var e=this;this.$api.fetch({params:{busicode:"MeterBoreList",data:{}}}).then(function(t){e.meterBoreData=t.list})},getMeterFactory:function(){var e=this;this.$api.fetch({params:{busicode:"MeterFactoryList",data:{page:1,pageCount:20}}}).then(function(t){e.meterFactoryData=t})},submitForm:function(e){var t=this,a=this;a.$refs.WatermeterModelEditForm.validate(function(r){if(r){var l={};l="MeterModelAdd"===e?{busicode:"MeterModelAdd",data:t.formData}:{busicode:"MeterModelUpdate",data:t.formData},t.$api.fetch({params:l}).then(function(e){a.$message({showClose:!0,message:"保存成功",type:"success"}),a.$parent.init(),a.$parent.closeDialog()})}else a.$message({showClose:!0,message:"必填项未填写",type:"error"})})},editData:function(e){this.formData=e}}},r,!1,function(e){a("Vmsu")},"data-v-1dbb4c5b",null).exports},name:"WatermeterModel",data:function(){return{EditVisible:!1,formData:{id:"",modelName:"",meterType:"",transWay:"",meterSensor:"",comments:"",status:"",sortNo:"",tenantId:"",factoryId:"",valveControl:"",meterBore:""},crumbsData:{titleList:[{title:"基础配置",path:"/baseInfo"},{title:"水表型号",method:function(){window.histroy.back()}}]},maxHeight:0,tableShow:!1,tableData:[],tableQuery:{page:1,pageCount:10,modelName:"",factoryId:"",valveControl:"",tramsWay:"",meterType:""},formType:"",dictionaryData:{},SelectMfactoryOptions:[]}},mounted:function(){var e=this;this.getData(),this.getFactorySelect(),this.init(),this.$nextTick(function(){e.common.changeTable(e,".WatermeterModel .wWatermeterModelIndex",[".WatermeterModel .block",".WatermeterModel .toolbar"])})},methods:{init:function(){var e=this,t={busicode:"MeterModelList",data:this.tableQuery};this.$api.fetch({params:t}).then(function(t){e.tableData=t})},search:function(){this.tableQuery.page=1,this.init()},getData:function(){var e=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"MMT,TPW"}}).then(function(t){console.log(t),e.dictionaryData=t,console.log("dictionaryData",e.dictionaryData)})},getFactorySelect:function(){var e=this;this.$api.fetch({params:{busicode:"SelectMfactoryList",data:{}}}).then(function(t){e.SelectMfactoryOptions=t})},add:function(e){var t=this;if(this.EditVisible=!0,"add"===e)this.formType="MeterModelAdd";else{this.formType="MeterModelUpdate";var a={busicode:"MeterModelQuery",data:{id:e.id}};this.$api.fetch({params:a}).then(function(e){t.$refs.WatermeterModelEdit.editData(e)})}},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.init()},handleCurrentChange:function(e){this.tableQuery.page=e,this.init()},closeDialog:function(){this.EditVisible=!1},submitForm:function(){this.$refs.WatermeterModelEdit.submitForm(this.formType)},queryMfactoryChange:function(e){this.tableQuery.factoryId=e},queryValveControlChange:function(e){this.tableQuery.valveControl=e},queryTramsWayChange:function(e){this.tableQuery.tramsWay=e},queryMeterTypeChange:function(e){this.tableQuery.meterType=e}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"WatermeterModel"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),e.EditVisible?a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm()}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.closeDialog}},[e._v("返回")])],1):a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-plus",type:"primary"},on:{click:function(t){return e.add("add")}}},[e._v("添加")])],1)],1),e._v(" "),e.EditVisible?a("WatermeterModelEdit",{ref:"WatermeterModelEdit"}):a("div",{staticClass:"WatermeterModelIndex"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,size:"mini",model:e.formData}},[a("el-form-item",{staticClass:"form-left"},[a("el-form-item",{attrs:{label:"型号名称："}},[a("el-input",{attrs:{placeholder:"请输入型号名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.modelName,callback:function(t){e.$set(e.tableQuery,"modelName",t)},expression:"tableQuery.modelName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"厂商："}},[a("el-select",{attrs:{placeholder:"请选择厂商",clearable:""},on:{change:e.queryMfactoryChange},model:{value:e.tableQuery.factoryId,callback:function(t){e.$set(e.tableQuery,"factoryId",t)},expression:"tableQuery.factoryId"}},e._l(e.SelectMfactoryOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.code}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否阀控：",prop:"valveControl"}},[a("el-select",{attrs:{placeholder:"请选择是否阀控",clearable:""},on:{change:e.queryValveControlChange},model:{value:e.tableQuery.valveControl,callback:function(t){e.$set(e.tableQuery,"valveControl",t)},expression:"tableQuery.valveControl"}},[a("el-option",{attrs:{label:"是",value:1}}),e._v(" "),a("el-option",{attrs:{label:"否",value:0}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"通讯类型：",prop:"tramsWay"}},[a("el-select",{attrs:{placeholder:"请选择通讯类型",clearable:""},on:{change:e.queryTramsWayChange},model:{value:e.tableQuery.tramsWay,callback:function(t){e.$set(e.tableQuery,"tramsWay",t)},expression:"tableQuery.tramsWay"}},e._l(e.dictionaryData.TPW,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"水表类型：",prop:"meterType"}},[a("el-select",{attrs:{placeholder:"请选择水表类型",clearable:""},on:{change:e.queryMeterTypeChange},model:{value:e.tableQuery.meterType,callback:function(t){e.$set(e.tableQuery,"meterType",t)},expression:"tableQuery.meterType"}},e._l(e.dictionaryData.MMT,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",[a("el-button",{staticClass:"searchBtn",attrs:{icon:"el-icon-search"},on:{click:e.search}})],1)],1)],1)],1),e._v(" "),a("div",{staticClass:"kl-table"},[e.tableShow?a("el-table",{staticClass:"wuserInfo-table",attrs:{"max-height":e.maxHeight,stripe:"",border:"",data:e.tableData.list}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"modelNo","min-width":"100",label:"水表型号","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"modelName","min-width":"100",label:"型号名称","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"factoryName","min-width":"100",label:"生产厂商","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"valveControl","min-width":"100",label:"是否阀控","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"transWay","min-width":"100",label:"通讯类型","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"meterBore","min-width":"100",label:"口径","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"status","min-width":"80",label:"状态"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"70",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"mini"},nativeOn:{click:function(a){return e.add(t.row)}}},[e._v("编辑")])]}}],null,!1,669204806)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[10,50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)])],1)},staticRenderFns:[]};var i=a("VU/8")(l,o,!1,function(e){a("l9+v")},null,null);t.default=i.exports},"l9+v":function(e,t){}});