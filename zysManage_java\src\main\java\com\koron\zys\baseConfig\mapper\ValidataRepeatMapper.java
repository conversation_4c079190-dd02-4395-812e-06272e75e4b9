package com.koron.zys.baseConfig.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface ValidataRepeatMapper {
	
	@Select("select count(1) from ${tableName} where ${columnName} = '${value}'")
	int validataRepeat(@Param("tableName") String tableName, @Param("columnName") String columnName, @Param("value") String value);
	
	@Select("select data_type from information_schema.COLUMNS where TABLE_SCHEMA = (select database()) and table_name = '${tableName}' AND column_name = '${columnName}'")
	String getDataType(@Param("tableName") String tableName, @Param("columnName")  String columnName);

}
