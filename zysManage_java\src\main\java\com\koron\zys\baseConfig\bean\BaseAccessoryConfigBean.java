package com.koron.zys.baseConfig.bean;

/**
 * 单据附件配置表
 * <AUTHOR>
 *
 */
public class BaseAccessoryConfigBean extends BaseBean{
	
	/**
	 * 主键
	 */
	private String id;
	
	/**
	 * 单据类型 
	 */
	private String receiptType;
	
	/**
	 * 流程节点
	 */
	private String processNode;
	
	/**
	 * 附件编码   UFI
	 */
	private String accessoryNo;
	
	/**
	 * 附件类型
	 */
	private String accessoryType;
	
	/**
	 * 是否必须 0否，1是
	 */
	private Integer requiredFlag;
	
	public String getAccessoryType() {
		return accessoryType;
	}

	public void setAccessoryType(String accessoryType) {
		this.accessoryType = accessoryType;
	}

	public String getId() {
		return id;
	}

	public String getReceiptType() {
		return receiptType;
	}

	public String getProcessNode() {
		return processNode;
	}

	public Integer getRequiredFlag() {
		return requiredFlag;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setReceiptType(String receiptType) {
		this.receiptType = receiptType;
	}

	public void setProcessNode(String processNode) {
		this.processNode = processNode;
	}

	public void setRequiredFlag(Integer requiredFlag) {
		this.requiredFlag = requiredFlag;
	}

	public String getAccessoryNo() {
		return accessoryNo;
	}

	public void setAccessoryNo(String accessoryNo) {
		this.accessoryNo = accessoryNo;
	}
	
}
