package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.ConfigVacationMapper;
import com.koron.zys.baseConfig.queryBean.ConfigVacationQueryBean;
import com.koron.zys.baseConfig.vo.ConfigVacationVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
public class PubMessageRemind implements ServerInterface {
    private static Logger logger = LoggerFactory.getLogger(PubMessageRemind.class);

    /**
     * 一月
     */
    public static final Integer January = 1;
    /**
     * 12月
     */
    public static final Integer December = 12;

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
        // 创建 Calendar 对象并设置为当前时间
        Calendar calendar = Calendar.getInstance();
        // 获取当前年份
        int year = calendar.get(Calendar.YEAR);
        // 获取当前月份（注意需要加上1）
        int month = calendar.get(Calendar.MONTH) + 1;
        List<String> notice = new ArrayList<>();
        ConfigVacationMapper mapper = factory.getMapper(ConfigVacationMapper.class, "_default");
        //每年12月起，判断设置中是否包含次年日期设置
        if (month >= December) {
            ConfigVacationQueryBean configVacationQueryBean = new ConfigVacationQueryBean();
            configVacationQueryBean.setVacationBeginDate((year + 1) + "-01-01");
            configVacationQueryBean.setVacationEndDate((year + 1) + "-12-31");
            List<ConfigVacationVO> penalList = mapper.selectConfigVacationList(configVacationQueryBean);
            if (penalList == null || penalList.size() == 0) {
                notice.add("未设置" + (year + 1) + "年假期，请注意尽快设置！");
            }
            //每年1月起，判断设置中是否包含当前年度日期设置
        } else if (month >= January) {
            ConfigVacationQueryBean configVacationQueryBean = new ConfigVacationQueryBean();
            configVacationQueryBean.setVacationBeginDate(year + "-01-01");
            configVacationQueryBean.setVacationEndDate(year + "-12-31");
            List<ConfigVacationVO> penalList = mapper.selectConfigVacationList(configVacationQueryBean);
            if (penalList == null || penalList.size() == 0) {
                notice.add("未设置" + year + "年假期，请注意尽快设置！");
            }
        }
        info.setData(notice);
        return info;
    }
}