<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.MfactoryListMapper">
	<select id="findMfactoryList" resultType="com.koron.zys.baseConfig.bean.MfactoryListBean" parameterType="STRING">
		select cm.id code,cm.FACTORY_NAME name
		from pub_meter_factory  cm
		where cm.status=1  
	</select>
</mapper>