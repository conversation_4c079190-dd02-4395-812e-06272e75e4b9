package com.koron.zys.baseConfig.bean;

/**
 * 用水价格阶梯
 * <AUTHOR>
 * 2020年5月12日
 */
public class WaterPriceLadderBean extends BaseBean {
	
	/**
	 * 主键
	 */
	private String id;

	/**
	 * 用水价格明细ID
	 */
	private String waterPriceDetailId;
	/**
	 * 开始水量
	 */
	private String beginWater;
	/**
	 * 结束水量
	 */
	private String endWater;
	/**
	 * 单价
	 */
	private double price;
	/**
	 * 阶梯起始金额
	 */
	private double ladderBeginMoney;
	
	private double tenantId;
	
	
	
	

	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getWaterPriceDetailId() {
		return waterPriceDetailId;
	}
	public void setWaterPriceDetailId(String waterPriceDetailId) {
		this.waterPriceDetailId = waterPriceDetailId;
	}
	public String getBeginWater() {
		return beginWater;
	}
	public void setBeginWater(String beginWater) {
		this.beginWater = beginWater;
	}
	public String getEndWater() {
		return endWater;
	}
	public void setEndWater(String endWater) {
		this.endWater = endWater;
	}
	public double getPrice() {
		return price;
	}
	public void setPrice(double price) {
		this.price = price;
	}
	public double getLadderBeginMoney() {
		return ladderBeginMoney;
	}
	public void setLadderBeginMoney(double ladderBeginMoney) {
		this.ladderBeginMoney = ladderBeginMoney;
	}
	
}
