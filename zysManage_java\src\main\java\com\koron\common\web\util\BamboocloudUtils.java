package com.koron.common.web.util;

import com.banboocloud.Codec.BamboocloudFacade;
import org.apache.commons.io.IOUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

public class BamboocloudUtils {

    public static boolean checkUsernamePassword(String userName, String password){
        return true;
    }

    public static String getRequestBody(HttpServletRequest request){
        try (InputStream inputStream = request.getInputStream();){
            return IOUtils.toString(inputStream, "UTF-8");
        }catch (Exception exception){
            throw new RuntimeException("获取 HttpServletRequest 数据失败");
        }
    }

    public static String getPlaintext(String ciphertext, String key, String type) {
        return BamboocloudFacade.decrypt(ciphertext, key, type);
    }

    public static boolean verify(Map<String, String> reqmap, String type) {
        Map<String, String> verifymap = new TreeMap<String, String>();
        StringBuffer sb = new StringBuffer();
        Iterator<String> it = reqmap.keySet().iterator();
        while (it.hasNext()) {
            String key = (String) it.next();
            verifymap.put(key, reqmap.get(key));
        }
        Iterator<String> ittree = verifymap.keySet().iterator();
        while (ittree.hasNext()) {
            String key = (String) ittree.next();
            if (!"signature".equals(key)) {
                sb.append(key).append("=").append(verifymap.get(key)).append("&");
            }
        }
        sb.deleteCharAt(sb.length() - 1);
        return BamboocloudFacade.verify(reqmap.get("signature").toString(), sb.toString(), type);
    }
}