package com.koron.common.web.bean;

import com.koron.common.bean.query.BaseQueryBean;

/**
 * 用户满意度查询实体
 *
 * @author: zhongxj
 * @date: 2020-07-23 15:58
 **/

public class UserSatisfiedQueryBean extends BaseQueryBean {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 单据类型
     */
    private String receiptType;

    /**
     * 满意结果 1满意 0不满意
     */
    private String satisfied;

    /**
     * 建立人账号
     */
    private String createAccount;

    /**
     * 建立人
     */
    private String createName;

    /**
     * 建立时间
     */
    private String createTime;

    /**
     * 水司编号
     */
    private String companyNo;

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(String receiptType) {
        this.receiptType = receiptType;
    }

    public String getSatisfied() {
        return satisfied;
    }

    public void setSatisfied(String satisfied) {
        this.satisfied = satisfied;
    }

    public String getCreateAccount() {
        return createAccount;
    }

    public void setCreateAccount(String createAccount) {
        this.createAccount = createAccount;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}
