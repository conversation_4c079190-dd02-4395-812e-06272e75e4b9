<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.common.web.mapper.TreeMapper">
	<insert id="batchInsertTree" parameterType="com.koron.common.web.mapper.LongTreeBean">
    	INSERT INTO tbltree	(
			seq,
			parentmask,
			mask,
			childmask,
			type,
			foreignkey
		) VALUES 
		<foreach collection="list" item="tree" separator=",">
			(
			#{tree.seq,jdbcType=BIGINT},
			#{tree.parentMask,jdbcType=INTEGER},
			#{tree.mask,jdbcType=INTEGER},
			#{tree.childMask,jdbcType=INTEGER},
			#{tree.type,jdbcType=INTEGER},
			#{tree.foreignkey,jdbcType=VARCHAR}
			)
		</foreach>
    </insert>
</mapper>