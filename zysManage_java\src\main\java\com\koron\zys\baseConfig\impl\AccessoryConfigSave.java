package com.koron.zys.baseConfig.impl;

import java.util.ArrayList;
import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.AccessoryConfigBean;
import com.koron.zys.baseConfig.bean.AccessoryConfigInBean;
import com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean;
import com.koron.zys.baseConfig.mapper.BaseAccessoryCofingMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
import com.koron.util.Tools;

public class AccessoryConfigSave implements ServerInterface {
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub
		try {
			AccessoryConfigInBean abean = JsonUtils.objectToPojo(req.getData(),AccessoryConfigInBean.class);
			//插入数据库的数据预处理
			List<BaseAccessoryConfigBean> config = new ArrayList<BaseAccessoryConfigBean>();
			for (List<AccessoryConfigBean> accssory : abean.getList()) {
				for (AccessoryConfigBean bean : accssory) {
					BaseAccessoryConfigBean cbean = new BaseAccessoryConfigBean();
					cbean.setReceiptType(bean.getReceiptNo());
					cbean.setAccessoryNo(bean.getAccessoryNo());
					cbean.setId(Tools.getObjectId());
					cbean.setCreateInfo(userInfo);
					if("true".equals(bean.getIsChecked())) {
						cbean.setRequiredFlag(1);
					}else {
						cbean.setRequiredFlag(0);
					}
					if("true".equals(bean.getIsUse())) {
						config.add(cbean);
					}
				}
			}
			BaseAccessoryCofingMapper mapper = factory.getMapper(BaseAccessoryCofingMapper.class);
			mapper.delete();
			mapper.insertBaseConfig(config);
			return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.error("保存附件配置信息失败", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "保存附件配置信息失败", void.class);
		}
		
		
	}

}
