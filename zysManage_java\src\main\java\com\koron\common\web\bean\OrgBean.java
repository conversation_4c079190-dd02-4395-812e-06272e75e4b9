package com.koron.common.web.bean;

import java.util.ArrayList;
import java.util.List;

public class OrgBean {
	
	private Integer id;
	/**
	 * 组织名称
	 */
	private String name;
	
	/**
	 * 路径
	 */
	private String namePath;
	/**
	 * 短名
	 */
	private String shortName;
	/**
	 * 组织编码
	 */
	private String code;
	/**
	 * 组织描述
	 */
	private String description;
	/**
	 * 排序值
	 */
	private Integer sort;
	/**
	 * 组织电话
	 */
	private String phone;
	/**
	 * 组织状态
	 */
	private Integer status;
	/**
	 * 组织状态1部门,2水司4水厂8自来水16污水
	 */
	private Integer flag;
	/**
	 * 父级部门CODE
	 */
	private String parentCode;
	
	/**
	 * 分类级别CODE
	 */
	private String classCode;
	
	/**
	 * 字部门
	 */
	private List<OrgBean> children = new ArrayList<OrgBean>();

	public Integer getId() {
		return id;
	}

	public String getName() {
		return name;
	}

	public String getNamePath() {
		return namePath;
	}

	public String getShortName() {
		return shortName;
	}

	public String getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public Integer getSort() {
		return sort;
	}

	public String getPhone() {
		return phone;
	}

	public Integer getStatus() {
		return status;
	}

	public Integer getFlag() {
		return flag;
	}

	public String getParentCode() {
		return parentCode;
	}

	public String getClassCode() {
		return classCode;
	}

	public List<OrgBean> getChildren() {
		return children;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setNamePath(String namePath) {
		this.namePath = namePath;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public void setFlag(Integer flag) {
		this.flag = flag;
	}

	public void setParentCode(String parentCode) {
		this.parentCode = parentCode;
	}

	public void setClassCode(String classCode) {
		this.classCode = classCode;
	}

	public void setChildren(List<OrgBean> children) {
		this.children = children;
	}

}
