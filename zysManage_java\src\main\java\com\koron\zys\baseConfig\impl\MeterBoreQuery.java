package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterBoreBean;
import com.koron.zys.baseConfig.mapper.MeterBoreMapper;
import com.koron.zys.baseConfig.queryBean.MeterBoreQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 水表口径-编辑初始化
 * <AUTHOR>
 *
 */
public class MeterBoreQuery implements ServerInterface {
	
private static Logger logger = LoggerFactory.getLogger(MeterBoreQuery.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<MeterBoreBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", MeterBoreBean.class);

		try {
			MeterBoreQueryBean bean = JsonUtils.objectToPojo(req.getData(), MeterBoreQueryBean.class);
			MeterBoreMapper mapper = factory.getMapper(MeterBoreMapper.class);
			MeterBoreBean MeterBorebean = mapper.selectMeterBoreById(bean.getBoreId());
			info.setData( MeterBorebean);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}
