package com.koron.zys.baseConfig.bean;

/**
 * 抄表状态实体类
 * 
 * <AUTHOR>
 *
 */
public class ReadingMeterStatusBean {
	/**
	 * 主键ID
	 */
	private String id;
	/**
	 * 状态名称
	 */
	private String statusName;
	/**
	 * 状态值
	 */
	private int statusValue;
	/**
	 * 异常标记
	 */
	private int abnormalFlag;
	/**
	 * 备注
	 */
	private String comments;
	/**
	 * 状态
	 */
	private int status;
	/**
	 * 排序号
	 */
	private int sortNo;
	/**
	 * 租户编号
	 */
	private String tenantId;
	/**
	 * // 建立时间
	 */
	private String createTime;

	/**
	 * 建立人ID
	 */
	private String createAccount;

	/**
	 * 建立人
	 */
	private String createName;

	/**
	 * 最后修改时间
	 */
	private String updateTime;

	/**
	 * 最后修改人Id
	 */
	private String updateAccount;

	/**
	 * 最后修改人
	 */
	private String updateName;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getStatusName() {
		return statusName;
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}

	public int getStatusValue() {
		return statusValue;
	}

	public void setStatusValue(int statusValue) {
		this.statusValue = statusValue;
	}

	public int getAbnormalFlag() {
		return abnormalFlag;
	}

	public void setAbnormalFlag(int abnormalFlag) {
		this.abnormalFlag = abnormalFlag;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public int getSortNo() {
		return sortNo;
	}

	public void setSortNo(int sortNo) {
		this.sortNo = sortNo;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getCreateAccount() {
		return createAccount;
	}

	public void setCreateAccount(String createAccount) {
		this.createAccount = createAccount;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	public String getUpdateAccount() {
		return updateAccount;
	}

	public void setUpdateAccount(String updateAccount) {
		this.updateAccount = updateAccount;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

}
