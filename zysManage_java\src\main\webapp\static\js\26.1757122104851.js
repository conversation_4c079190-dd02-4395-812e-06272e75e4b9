webpackJsonp([26],{Wx52:function(e,t){},fwZe:function(e,t){},sZcC:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=l("woOf"),o=l.n(a),r={name:"saleSetAdd",data:function(){return{databaseData:[],chargingTypeList:[],operationList:[],ruleForm:{id:"",bid:"",companyNo:"",outBankName:"",outType:2,comments:"",outExpSql:"",outExpFileType:"",outAmount:"",backAccountPeriodColumn:"",backUserNoColumn:"",backMoneyColumn:"",backResultColumn:"",backDescColumn:"",backSuccessFlag:"",status:"",signExpSql:"",signExpFileType:"",signUserNoColumn:"",signEntrustContractColumn:"",signResultCodeColumn:"",signResultDescColumn:"",signBankAccountColumn:"",signBankOpenAccountNameColumn:"",signSuccessFlag:"",backFileColumn:"",pluginId:"",outExcelTemplateFile:{},bankSignTemplateFile:{},chargingType:1,chargeStaffAccount:"",chargeStaffName:""},props:{value:"id",label:"name"},bankTreeData:[],companyNo:"",formData:{id:"",pluginId:"",bid:[],companyNo:"",descIdx:"",outBankName:"",outType:"",comments:"",bankDriverClass:"",outExpSql:"",outExpFileType:"",outAmount:"",backAccountPeriodColumn:"",backUserNoColumn:"",backMoneyColumn:"",backResultColumn:"",backDescColumn:"",backSuccessFlag:"",status:"",signExpSql:"",signExpFileType:"",signUserNoColumn:"",bankSignTemplateFile:{},signEntrustContractColumn:"",signResultCodeColumn:"",signResultDescColumn:"",signBankAccountColumn:"",signBankOpenAccountNameColumn:"",signSuccessFlag:"",backFileColumn:"",outExcelTemplateFile:{},chargingType:"",chargeStaffAccount:"",chargeStaffName:""},bankDriverClassList:[],BankData:[],rules:{bid:[{required:!0,message:"请输入机构编号",trigger:"blur"}],outExpSql:[{required:!0,message:"请输入出盘导出sql",trigger:"blur"}],expFileType:[{required:!0,message:"请输入导出文件类型",trigger:"blur"}],backAccountPeriodColumn:[{required:!0,message:"请输入回盘账期列",trigger:"blur"},{type:"number",message:"回盘账期列必须为数字值",trigger:"blur"}],backUserNoColumn:[{required:!0,message:"请输入回盘编号列",trigger:"blur"},{type:"number",message:"回盘编号列必须为数字值",trigger:"blur"}],backMoneyColumn:[{required:!0,message:"请输入回盘金额列",trigger:"blur"},{type:"number",message:"回盘金额列必须为数字值",trigger:"blur"}],backResultColumn:[{required:!0,message:"请输入回盘结果列",trigger:"blur"},{type:"number",message:"回盘结果列必须为数字值",trigger:"blur"}],backDescColumn:[{required:!0,message:"请输入回盘结果说明列",trigger:"blur"},{type:"number",message:"回盘结果说明列必须为数字值",trigger:"blur"}],backSuccessFlag:[{required:!0,message:"请输入扣款成功标记",trigger:"blur"}],signBankAccountColumn:[{required:!0,message:"请输入签约银行账号列",trigger:"blur"}],signBankOpenAccountNameColumn:[{required:!0,message:"请输入银行开户名列",trigger:"blur"}],outAmount:[{required:!0,message:"请输入分割数量",trigger:"blur"},{type:"number",message:"分割数量必须为数字值",trigger:"blur"}]}}},mounted:function(){this.getBankInstitutionList()},methods:{getFile:function(e){console.log(e.target.files[0]),this.ruleForm.bankDriverClass=e.target.files[0]},getFile1:function(e){console.log(e.target.files[0]),this.ruleForm.outExcelTemplateFile=e.target.files[0]},getFile2:function(e){console.log(e.target.files[0]),this.ruleForm.bankSignTemplateFile=e.target.files[0]},uploadAttachment:function(e){var t=this,l={busicode:"add"==e?"BankConfigAdd":"BankConfigUpdate",type:"signUpload",data:{bid:this.ruleForm.bid,pluginId:this.ruleForm.pluginId,companyNo:this.ruleForm.companyNo,outBankName:this.ruleForm.outBankName,outType:this.ruleForm.outType,chargingType:this.ruleForm.chargingType,chargeStaffAccount:this.ruleForm.chargeStaffAccount,chargeStaffName:this.ruleForm.chargeStaffName,status:this.ruleForm.status,comments:this.ruleForm.comments,outExpSql:this.ruleForm.outExpSql,outExpFileType:this.ruleForm.outExpFileType,outAmount:this.ruleForm.outAmount,backAccountPeriodColumn:this.ruleForm.backAccountPeriodColumn,backUserNoColumn:this.ruleForm.backUserNoColumn,backMoneyColumn:this.ruleForm.backMoneyColumn,backResultColumn:this.ruleForm.backResultColumn,backDescColumn:this.ruleForm.backDescColumn,backSuccessFlag:this.ruleForm.backSuccessFlag,signExpSql:this.ruleForm.signExpSql,signExpFileType:this.ruleForm.signExpFileType,signUserNoColumn:this.ruleForm.signUserNoColumn,signEntrustContractColumn:this.ruleForm.signEntrustContractColumn,signResultCodeColumn:this.ruleForm.signResultCodeColumn,signResultDescColumn:this.ruleForm.signResultDescColumn,signSuccessFlag:this.ruleForm.signSuccessFlag,signBankAccountColumn:this.ruleForm.signBankAccountColumn,signBankOpenAccountNameColumn:this.ruleForm.signBankOpenAccountNameColumn,backFileColumn:this.ruleForm.backFileColumn,outExcelTemplateFile:this.ruleForm.outExcelTemplateFile,bankSignTemplateFile:this.ruleForm.bankSignTemplateFile},token:"krrjdev123",sysType:"002"};"update"==e&&(l.data=o()({},l.data,{id:this.ruleForm.id})),this.$api.fetch({params:l}).then(function(e){t.$message({message:"添加成功！",type:"success"}),t.resetForm(),t.$parent.selectTSubSystem(),t.$parent.closeDialog()})},uploadAttachment1:function(){var e=this,t={busicode:"BankConfigUpdate",type:"signUpload",data:{id:this.ruleForm.id,bid:this.ruleForm.bid,companyNo:this.ruleForm.companyNo,outBankName:this.ruleForm.outBankName,outType:this.ruleForm.outType,status:this.ruleForm.status,comments:this.ruleForm.comments,outExpSql:this.ruleForm.outExpSql,outExpFileType:this.ruleForm.outExpFileType,outAmount:this.ruleForm.outAmount,backAccountPeriodColumn:this.ruleForm.backAccountPeriodColumn,backUserNoColumn:this.ruleForm.backUserNoColumn,backMoneyColumn:this.ruleForm.backMoneyColumn,backResultColumn:this.ruleForm.backResultColumn,backDescColumn:this.ruleForm.backDescColumn,backSuccessFlag:this.ruleForm.backSuccessFlag,signExpSql:this.ruleForm.signExpSql,signExpFileType:this.ruleForm.signExpFileType,signUserNoColumn:this.ruleForm.signUserNoColumn,signEntrustContractColumn:this.ruleForm.signEntrustContractColumn,signResultCodeColumn:this.ruleForm.signResultCodeColumn,signResultDescColumn:this.ruleForm.signResultDescColumn,signSuccessFlag:this.ruleForm.signSuccessFlag,signBankAccountColumn:this.ruleForm.signBankAccountColumn,signBankOpenAccountNameColumn:this.ruleForm.signBankOpenAccountNameColumn,backFileColumn:this.ruleForm.backFileColumn,pluginId:this.ruleForm.pluginId,outExcelTemplateFile:this.ruleForm.outExcelTemplateFile,bankSignTemplateFile:this.ruleForm.bankSignTemplateFile},token:"krrjdev123",sysType:"002"};this.$api.fetch({params:t}).then(function(t){e.$message({message:"修改成功！",type:"success"}),e.resetForm(),e.$parent.selectTSubSystem(),e.$parent.closeDialog()})},resetForm:function(){this.$refs.saleSetAddRuleForm.resetFields()},getArr:function(e){return function e(t){t.map(function(t){!1===t.isParent?delete t.children:e(t.children)})}(e.children),e},chargingTypeChange:function(e){this.ruleForm.outType="",1==e?this.getBwpDictionary():2==e&&this.getBcpDictionary()},getBwpDictionary:function(){var e=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"BWP"}}).then(function(t){e.operationList=t.BWP.map(function(e){return e.value=Number(e.value),e})})},getBipDictionary:function(){var e=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"BIP"}}).then(function(t){e.chargingTypeList=t.BIP.map(function(e){return e.value=Number(e.value),e})})},getBcpDictionary:function(){var e=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"BCP"}}).then(function(t){e.operationList=t.BCP})},getBankInstitutionList:function(){var e=this;this.$api.fetch({params:{busicode:"BankPluginList",data:{}}}).then(function(t){e.bankDriverClassList=t})},BankDataOption:function(e){var t=this,l={busicode:"BankSelect",data:{companyNo:e}};this.$api.fetch({params:l}).then(function(e){t.BankData=e})},submitForm:function(e,t,l){var a=this;this.ruleForm.companyNo=l,this.ruleForm=this.common.handleData(this.ruleForm,this.formData),this.$refs[e].validate(function(e){e&&("添加"===t?a.uploadAttachment("add"):a.uploadAttachment("update"))})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","saleSetAdd",this.$parent.closeDialog)},editData:function(e,t){this.ruleForm=e,console.log(this.ruleForm),console.log(t),this.companyNo=t,this.BankDataOption(t),this.getBipDictionary(),this.getBwpDictionary()}}},n={render:function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"saleSetAdd"},[l("el-form",{ref:"saleSetAddRuleForm",staticClass:"formBill",attrs:{model:e.ruleForm,size:"mini",rules:e.rules,"label-width":"130px",inline:!0}},[l("legend",{staticClass:"legendColumn"},[e._v("基本信息")]),e._v(" "),l("el-form-item",{attrs:{label:"机构编号：",prop:"bid"}},[l("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.bid,callback:function(t){e.$set(e.ruleForm,"bid",t)},expression:"ruleForm.bid"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"机构名称：",prop:"outBankName"}},[l("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.outBankName,callback:function(t){e.$set(e.ruleForm,"outBankName",t)},expression:"ruleForm.outBankName"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"收费类型：",prop:"chargingType"}},[l("el-select",{attrs:{placeholder:"请选择",clearable:"",size:"mini"},on:{change:e.chargingTypeChange},model:{value:e.ruleForm.chargingType,callback:function(t){e.$set(e.ruleForm,"chargingType",e._n(t))},expression:"ruleForm.chargingType"}},e._l(e.chargingTypeList,function(e,t){return l("el-option",{key:""+t+e.id,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),1==e.ruleForm.chargingType?l("el-form-item",{attrs:{label:"代扣方式：",prop:"outType"}},[l("el-select",{attrs:{placeholder:"请选择",clearable:"",size:"mini"},model:{value:e.ruleForm.outType,callback:function(t){e.$set(e.ruleForm,"outType",t)},expression:"ruleForm.outType"}},e._l(e.operationList,function(e,t){return l("el-option",{key:""+t+e.id,attrs:{label:e.name,value:e.value}})}),1)],1):2==e.ruleForm.chargingType?l("el-form-item",{attrs:{label:"托收方式：",prop:"outType"}},[l("el-select",{attrs:{placeholder:"请选择",clearable:"",size:"mini"},model:{value:e.ruleForm.outType,callback:function(t){e.$set(e.ruleForm,"outType",t)},expression:"ruleForm.outType"}},e._l(e.operationList,function(e,t){return l("el-option",{key:""+t+e.id,attrs:{label:e.name,value:e.value}})}),1)],1):e._e(),e._v(" "),l("el-form-item",{attrs:{label:"状态：",prop:"status"}},[l("el-select",{attrs:{placeholder:"请选择",clearable:""},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[l("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),l("el-option",{attrs:{label:"停用",value:0}})],1)],1),e._v(" "),l("el-form-item",{attrs:{label:"分割数量：",prop:"outAmount"}},[l("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.outAmount,callback:function(t){e.$set(e.ruleForm,"outAmount",e._n(t))},expression:"ruleForm.outAmount"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"收费人账号：",prop:"chargeStaffAccount"}},[l("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.chargeStaffAccount,callback:function(t){e.$set(e.ruleForm,"chargeStaffAccount",t)},expression:"ruleForm.chargeStaffAccount"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"收费人名称：",prop:"chargeStaffName"}},[l("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.chargeStaffName,callback:function(t){e.$set(e.ruleForm,"chargeStaffName",t)},expression:"ruleForm.chargeStaffName"}})],1),e._v(" "),l("el-form-item",{staticClass:"remark f3",attrs:{label:"备注："}},[l("el-input",{attrs:{type:"textarea","show-word-limit":"",maxlength:"150",clearable:""},model:{value:e.ruleForm.comments,callback:function(t){e.$set(e.ruleForm,"comments",t)},expression:"ruleForm.comments"}})],1),e._v(" "),2==e.ruleForm.outType?[l("legend",{staticClass:"legendColumn"},[e._v("直连代扣配置")]),e._v(" "),l("el-form-item",{attrs:{label:"驱动文件："}},[l("el-select",{attrs:{clearable:"",placeholder:""},model:{value:e.ruleForm.pluginId,callback:function(t){e.$set(e.ruleForm,"pluginId",t)},expression:"ruleForm.pluginId"}},e._l(e.bankDriverClassList,function(e,t){return l("el-option",{key:""+t+e.id,attrs:{label:e.name,value:e.id}})}),1)],1)]:e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("legend",{staticClass:"legendColumn"},[e._v("人工出回盘配置")]):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{staticClass:"f3",attrs:{label:"出盘导出sql：",prop:"outExpSql"}},[l("el-input",{staticClass:"expSql",attrs:{type:"textarea","show-word-limit":"",maxlength:"2000",clearable:""},model:{value:e.ruleForm.outExpSql,callback:function(t){e.$set(e.ruleForm,"outExpSql",t)},expression:"ruleForm.outExpSql"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"导出文件类型：",prop:"outExpFileType"}},[l("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.outExpFileType,callback:function(t){e.$set(e.ruleForm,"outExpFileType",t)},expression:"ruleForm.outExpFileType"}},[l("el-option",{attrs:{label:"txt",value:1}}),e._v(" "),l("el-option",{attrs:{label:"excel",value:2}})],1)],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"文件模板："}},[l("input",{staticStyle:{"font-size":"12px"},attrs:{type:"file"},on:{change:function(t){return e.getFile1(t)}}})]):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"回盘账期列：",prop:"backAccountPeriodColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.backAccountPeriodColumn,callback:function(t){e.$set(e.ruleForm,"backAccountPeriodColumn",e._n(t))},expression:"ruleForm.backAccountPeriodColumn"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"回盘编号列：",prop:"backUserNoColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.backUserNoColumn,callback:function(t){e.$set(e.ruleForm,"backUserNoColumn",e._n(t))},expression:"ruleForm.backUserNoColumn "}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"回盘金额列：",prop:"backMoneyColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.backMoneyColumn,callback:function(t){e.$set(e.ruleForm,"backMoneyColumn",e._n(t))},expression:"ruleForm.backMoneyColumn"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"回盘结果列：",prop:"backResultColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.backResultColumn,callback:function(t){e.$set(e.ruleForm,"backResultColumn",e._n(t))},expression:"ruleForm.backResultColumn"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"结果说明列：",prop:"backDescColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.backDescColumn,callback:function(t){e.$set(e.ruleForm,"backDescColumn",e._n(t))},expression:"ruleForm.backDescColumn"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"回盘文件名列：",prop:"backFileColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.backFileColumn,callback:function(t){e.$set(e.ruleForm,"backFileColumn",e._n(t))},expression:"ruleForm.backFileColumn"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"成功标记：",prop:"backSuccessFlag"}},[l("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.backSuccessFlag,callback:function(t){e.$set(e.ruleForm,"backSuccessFlag",t)},expression:"ruleForm.backSuccessFlag"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("legend",{staticClass:"legendColumn"},[e._v("人工签约配置")]):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{staticClass:"f3",attrs:{label:"签约导出sql：",prop:"signExpSql"}},[l("el-input",{staticClass:"expSql",attrs:{type:"textarea","show-word-limit":"",maxlength:"2000",clearable:""},model:{value:e.ruleForm.signExpSql,callback:function(t){e.$set(e.ruleForm,"signExpSql",t)},expression:"ruleForm.signExpSql"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"导出文件类型：",prop:"signExpFileType"}},[l("el-select",{attrs:{placeholder:"",clearable:"",size:"mini"},model:{value:e.ruleForm.signExpFileType,callback:function(t){e.$set(e.ruleForm,"signExpFileType",t)},expression:"ruleForm.signExpFileType"}},[l("el-option",{attrs:{label:"txt",value:1}}),e._v(" "),l("el-option",{attrs:{label:"excel",value:2}})],1)],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"文件模板："}},[l("input",{staticStyle:{"font-size":"12px"},attrs:{type:"file"},on:{change:function(t){return e.getFile2(t)}}})]):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"用户编号列：",prop:"signUserNoColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.signUserNoColumn,callback:function(t){e.$set(e.ruleForm,"signUserNoColumn",e._n(t))},expression:"ruleForm.signUserNoColumn"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"协议号列：",prop:"signEntrustContractColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.signEntrustContractColumn,callback:function(t){e.$set(e.ruleForm,"signEntrustContractColumn",e._n(t))},expression:"ruleForm.signEntrustContractColumn"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"结果状态列：",prop:"signResultCodeColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.signResultCodeColumn,callback:function(t){e.$set(e.ruleForm,"signResultCodeColumn",e._n(t))},expression:"ruleForm.signResultCodeColumn"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"结果说明列：",prop:"signResultDescColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.signResultDescColumn,callback:function(t){e.$set(e.ruleForm,"signResultDescColumn",e._n(t))},expression:"ruleForm.signResultDescColumn"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"银行账号列：",prop:"signBankAccountColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.signBankAccountColumn,callback:function(t){e.$set(e.ruleForm,"signBankAccountColumn",e._n(t))},expression:"ruleForm.signBankAccountColumn"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"开户名列：",prop:"signBankOpenAccountNameColumn"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.signBankOpenAccountNameColumn,callback:function(t){e.$set(e.ruleForm,"signBankOpenAccountNameColumn",e._n(t))},expression:"ruleForm.signBankOpenAccountNameColumn"}})],1):e._e(),e._v(" "),1===Number(e.ruleForm.outType)?l("el-form-item",{attrs:{label:"成功标记：",prop:"signSuccessFlag"}},[l("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.signSuccessFlag,callback:function(t){e.$set(e.ruleForm,"signSuccessFlag",e._n(t))},expression:"ruleForm.signSuccessFlag"}})],1):e._e()],2)],1)},staticRenderFns:[]};var s={name:"saleSet",components:{saleSetAdd:l("VU/8")(r,n,!1,function(e){l("Wx52")},"data-v-259a146c",null).exports,autoTree:l("yJVD").a},data:function(){return{tableShow:!1,maxHeight:0,appServerData:[],formData:{businessBranch:"",chargingType:1,outType:"",outBank:"",expSql:"",expFileType:"",monthidIdx:"",returnUserNoColumn:"",returnMoneyColumn:"",returnResultColumn:"",descIdx:"",successFlag:"",status:"",comments:"",chargeStaffAccount:"",chargeStaffName:""},crumbsData:{titleList:[{title:"水司配置",path:"/waterSet"},{title:"代扣托收机构",method:function(){window.histroy.back()}}]},saleSetShow:!0,saleSetAddVisible:!1,treeDatas:{tree:[{shortName:"根目录",id:"2",children:[]}],defaultProps:{label:"shortName",children:"children"},inputProp:{showSearch:!1,Inp_placeholder:"请输入节点"},sendTreeProp:["code","shortName","districtArr","children","companyNo","group","isLeaf","isParent","parent","sonData"],rootName:"根目录",defaultOpen:{nodeKey:"id"}},treeParantId:"",companyNo:"C000001"}},mounted:function(){var e=this;this.getTreeDatas(),this.$nextTick(function(){e.common.changeTable(e,".saleSet .kl-table",[])})},methods:{appAdd:function(e){this.saleSetShow=!1,this.saleSetAddVisible=!0;var t=this;this.$nextTick(function(){if("add"===e)t.formData={businessBranch:"",chargingType:1,outType:2,outBank:"",expSql:"",expFileType:"",monthidIdx:"",returnUserNoColumn:"",returnMoneyColumn:"",returnResultColumn:"",descIdx:"",successFlag:"",status:"",comments:""},t.crumbsData.titleList.push({title:"添加",method:function(){window.histroy.back()}}),t.common.chargeObjectEqual(t,t.formData,"set","saleSetAdd"),t.$refs.saleSetAdd.editData(t.formData,t.companyNo);else{t.crumbsData.titleList.push({title:"编辑",method:function(){window.histroy.back()}});var l={busicode:"BankConfigQuery",data:{id:e.row.id,companyNo:t.companyNo}};t.$api.fetch({params:l}).then(function(e){t.$refs.saleSetAdd.editData(e,t.companyNo),t.common.chargeObjectEqual(t,e,"set","saleSetAdd")})}})},indexMethod:function(e){return e+1},selectTSubSystem:function(){var e=this,t={busicode:"BankConfigList",data:{companyNo:this.companyNo}};this.$api.fetch({params:t}).then(function(t){e.appServerData=t})},getTreeDatas:function(){var e=this,t=this;this.$api.fetch({params:{busicode:"CompanyNameList",data:{}}}).then(function(l){t.treeDatas.tree[0].children=l,t.treeDatas.tree[0].companyNo=l[0].companyNo,t.companyNo=l[0].companyNo,e.$set(e.crumbsData.titleList,"2",{title:"根目录",method:function(){window.histroy.back()}})})},closeDialog:function(){this.saleSetShow=!0,this.saleSetAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.saleSetAdd.handleClose()},submitForm:function(e){var t=this.crumbsData.titleList[3].title;this.$refs.saleSetAdd.submitForm(e,t,this.companyNo)},backTreeData:function(e){var t=this,l=this;this.companyNo=e.companyNo,this.$set(this.crumbsData.titleList,"2",{title:e.shortName,method:function(){window.histroy.back()}});var a={busicode:"BankConfigList",data:{companyNo:this.companyNo}};this.$api.fetch({params:a}).then(function(e){l.appServerData=e,t.common.changeTable(t,".saleSet .kl-table",[])})}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},u={render:function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"saleSet"},[l("div",{staticClass:"main-content"},[l("div",{staticClass:"bread-contain"},[l("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:e.saleSetShow,expression:"saleSetShow"}],staticClass:"bread-contain-right"},[l("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:e.saleSetAddVisible,expression:"saleSetAddVisible"}],staticClass:"bread-contain-right"},[l("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("saleSetAddRuleForm")}}},[e._v("保存")]),e._v(" "),l("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:e.saleSetShow,expression:"saleSetShow"}],staticClass:"company-content"},[l("div",{staticClass:"company-left"},[l("auto-tree",{attrs:{treeData:e.treeDatas},on:{sendTreeData:e.backTreeData}})],1),e._v(" "),l("div",{staticClass:"kl-table company-right"},[e.tableShow?l("el-table",{attrs:{stripe:"",border:"",data:e.appServerData,"max-height":e.maxHeight}},[l("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),l("el-table-column",{attrs:{prop:"chargingType",label:"收费类型","min-width":"100"}}),e._v(" "),l("el-table-column",{attrs:{prop:"outType",label:"代扣托收方式","min-width":"100"}}),e._v(" "),l("el-table-column",{attrs:{prop:"outBankName","min-width":"100",label:"代扣托收机构"}}),e._v(" "),l("el-table-column",{attrs:{prop:"status","min-width":"80",label:"状态"}}),e._v(" "),l("el-table-column",{attrs:{prop:"comments","min-width":"120",label:"备注"}}),e._v(" "),l("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{attrs:{type:"text"},on:{click:function(l){return e.appAdd(t)}}},[e._v("编辑")])]}}],null,!1,**********)})],1):e._e()],1)]),e._v(" "),e.saleSetAddVisible?l("saleSetAdd",{ref:"saleSetAdd",attrs:{formData:e.formData}}):e._e()],1)])},staticRenderFns:[]};var i=l("VU/8")(s,u,!1,function(e){l("fwZe")},null,null);t.default=i.exports}});