webpackJsonp([4],{"8Je2":function(e,t,a){"use strict";var i={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"MenuManageEdit"},[e._m(0),e._v(" "),a("el-form",{ref:"MenuManageEditEditForm",staticClass:"formBill-Two",attrs:{inline:!0,size:"mini",rules:e.rules,model:e.formData,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"菜单名称：",prop:"opName"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入菜单名称"},model:{value:e.formData.opName,callback:function(t){e.$set(e.formData,"opName",t)},expression:"formData.opName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"类型：",prop:"opFlag"}},[a("el-select",{attrs:{placeholder:"请选择菜单类型",clearable:""},model:{value:e.formData.opFlag,callback:function(t){e.$set(e.formData,"opFlag",t)},expression:"formData.opFlag"}},[a("el-option",{attrs:{label:"菜单",value:1}}),e._v(" "),a("el-option",{attrs:{label:"按钮",value:2}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"菜单路径：",prop:"url"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入菜单路径"},model:{value:e.formData.url,callback:function(t){e.$set(e.formData,"url",e._n(t))},expression:"formData.url"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"菜单图标："}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入菜单图标"},model:{value:e.formData.icon,callback:function(t){e.$set(e.formData,"icon",t)},expression:"formData.icon"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"菜单标志："}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入菜单标志"},model:{value:e.formData.symbol,callback:function(t){e.$set(e.formData,"symbol",e._n(t))},expression:"formData.symbol"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"排序："}},[a("el-input",{attrs:{clearable:""},on:{input:e.handleInput},model:{value:e.formData.opWeight,callback:function(t){e.$set(e.formData,"opWeight",e._n(t))},expression:"formData.opWeight"}})],1)],1)],1)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"toolbar"},[t("div",{staticClass:"info"},[t("div",{staticClass:"toolbar-line"}),this._v("添加菜单\n    ")])])}]};var n=a("VU/8")({name:"MenuManageEdit",props:["editNeedData"],data:function(){return{formData:{opName:"",opFlag:"",url:"",symbol:"",icon:"",opWeight:""},rules:{opName:[{required:!0,message:"请输入菜单名称",trigger:"blur"}],opFlag:[{required:!0,message:"请输入类型",trigger:"change"}]}}},mounted:function(){},methods:{save:function(e){var t=this,a=this,i="UpdateOperation";this.$refs.MenuManageEditEditForm.validate(function(n){if(n){var o="编辑成功！";if("add"===e){i="AddOperation",t.formData.foreignkey=t.editNeedData.opCode,o="添加成功！";var l={type:1,list:t.editNeedData}}else l={type:2,list:t.formData};var s={busicode:i,data:t.formData};t.$api.fetch({params:s}).then(function(e){a.$message({title:"成功",message:o,type:"success"}),a.$parent.closeDialog(),a.$parent.initMenuData(l)})}})},editData:function(e){this.formData=e},handleInput:function(e){this.formData.opWeight=e.replace(/^0/,"").replace(/[^0-9]/g,"")}}},i,!1,function(e){a("Clct")},null,null);t.a=n.exports},"Asn+":function(e,t,a){"use strict";(function(e){var i=a("bOdI"),n=a.n(i),o=a("8Je2"),l=a("rfgc");t.a={name:"MenuManage",components:{MenuManageEdit:o.a,waterMenuTree:l.a},data:function(){return{indexShow:!0,crumbsData:{titleList:[{title:"基础配置"},{title:"菜单管理"}]},tableQuery:{page:1,pageCount:20},tableData:[],tableShow:!1,maxHeight:0,editNeedData:{},optionsType:""}},mounted:function(){this.initMenuData()},methods:{setTableData:function(e){this.$set(this,"tableData",e);for(var t=0;t<this.tableData.length;t++)1==this.tableData[t].opFlag?this.tableData[t].opFlagName="菜单":2==this.tableData[t].opFlag&&(this.tableData[t].opFlagName="按钮"),1==this.tableData[t].opStatus?this.tableData[t].opStatusName="启用":0==this.tableData[t].opStatus&&(this.tableData[t].opStatusName="禁用")},menuAdd:function(e){this.optionsType="add",this.editNeedData=e,this.indexShow=!1;this.$refs.MenuManageEdit.editData({opFlag:"",opName:"",url:"",icon:"",symbol:"",opWeight:""})},edit:function(e){this.optionsType="edit",this.indexShow=!1,this.editNeedData=e;var t=n()({opCode:e.opCode,opWeight:e.opWeight,opFlag:e.opFlag,opName:e.opName,url:e.url,icon:e.icon,symbol:e.symbol},"opWeight",e.opWeight);this.$refs.MenuManageEdit.editData(t)},save:function(){this.$refs.MenuManageEdit.save(this.optionsType)},remove:function(e){var t=this;console.log(e);var a=this;this.$confirm("此操作将删除此菜单（按钮）？, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var i={busicode:"DeleteOperation",data:e.opCode};t.$api.fetch({params:i}).then(function(e){a.$message({type:"success",message:"删除成功！"}),t.initMenuData()})}).catch(function(){t.$message({type:"info",message:"已取消删除"})})},closeDialog:function(){this.indexShow=!0},initMenuData:function(t){var a=this;this.$nextTick(function(){a.$refs.waterOperation.getMenuData(t),a.common.changeTable(a,".MenuManage",[".MenuManage #crumbs"]),e(".left-box").css("height",a.maxHeight)})},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.init()},handleCurrentChange:function(e){this.tableQuery.page=e,this.init()},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}}}).call(t,a("7t+N"))},Clct:function(e,t){},hSyg:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("Asn+"),n={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"MenuManage"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{staticClass:"bread-contain-right"}),e._v(" "),e.indexShow?e._e():a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.save}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.closeDialog}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.indexShow,expression:"indexShow"}]},[a("el-col",{staticClass:"left-box",attrs:{span:4}},[a("waterMenuTree",{ref:"waterOperation",on:{setTableData:e.setTableData}})],1),e._v(" "),a("el-col",{staticStyle:{"overflow-y":"auto"},attrs:{span:20}},[a("div",{staticClass:"kl-table"},[e.tableShow?a("el-table",{staticClass:"change-tables-table",attrs:{"max-height":e.maxHeight,stripe:"",border:"",data:e.tableData}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"opName","min-width":"100",label:"菜单名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"opFlagName","min-width":"100",label:"类型"}}),e._v(" "),a("el-table-column",{attrs:{prop:"opStatusName","min-width":"100",label:"菜单状态"}}),e._v(" "),a("el-table-column",{attrs:{prop:"opWeight","min-width":"100",label:"排序号",sortable:""}}),e._v(" "),a("el-table-column",{attrs:{prop:"symbol","min-width":"100",label:"菜单标志"}}),e._v(" "),a("el-table-column",{staticClass:"cell",attrs:{label:"操作",fixed:"right",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.menuAdd(t.row)}}},[e._v("添加")]),e._v(" "),a("span",[e._v("|")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.remove(t.row)}}},[e._v("删除")]),e._v(" "),a("span",[e._v("|")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.edit(t.row)}}},[e._v("编辑")])]}}],null,!1,1292313217)})],1):e._e()],1)])],1),e._v(" "),a("MenuManageEdit",{directives:[{name:"show",rawName:"v-show",value:!e.indexShow,expression:"!indexShow"}],ref:"MenuManageEdit",attrs:{editNeedData:e.editNeedData}})],1)},staticRenderFns:[]};var o=function(e){a("uB7a")},l=a("VU/8")(i.a,n,!1,o,null,null);t.default=l.exports},rfgc:function(e,t,a){"use strict";var i=a("Dd8w"),n=a.n(i),o=a("fZjL"),l=a.n(o),s={name:"waterMenuTree",components:{},data:function(){return{waterOperationForm:{},rules:{},treeArr:{},treeData:[],defaultProps:{children:"children",label:"opName"},firstOpCode:"",checkTreeData:[]}},methods:{getMenuData:function(e){var t=this,a=this;this.$api.fetch({params:{busicode:"OperationListTree",data:{page:1,pageCount:9999}}}).then(function(i){if(a.treeData=i,0==l()(a.treeArr).length)a.firstOpCode=a.treeData[0].opCode,t.$emit("setTableData",a.treeData[0].children||[]);else if(void 0!==e)if(0==e.type)a.firstOpCode=a.treeArr.opCode;else if(1==e.type){a.firstOpCode=e.list.opCode;var n=t.$refs.tree.getNode(e.list);n.loaded=!1,n.expand()}else a.firstOpCode=e.list.opCode})},handleNodeClick:function(e){this.treeArr=e;var t=n()({},e);""==t.children&&(t.children=[]),""==t.btnList&&(t.btnList=[]),t.children=t.children.concat(t.btnList),this.$emit("setTableData",t.children||[])}}},r={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"waterOperation"},[a("div",{staticClass:"backfillSubmitForm"},[a("el-form",{ref:"ruleForm",staticClass:" ",attrs:{inline:!0,size:"mini",model:e.waterOperationForm,"label-width":"150px",rules:e.rules}},[a("el-tree",{ref:"tree",staticClass:"filter-tree department",attrs:{data:e.treeData,props:e.defaultProps,"node-key":"opCode","default-checked-keys":e.checkTreeData,"expand-on-click-node":!1,"default-expanded-keys":[e.firstOpCode],accordion:""},on:{"node-click":e.handleNodeClick},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.node;return a("span",{staticClass:"custom-tree-node"},[a("span",{staticStyle:{display:"inline-block"}},[e._v(e._s(i.label))])])}}])})],1)],1)])},staticRenderFns:[]};var c=a("VU/8")(s,r,!1,function(e){a("s6Cs")},null,null);t.a=c.exports},s6Cs:function(e,t){},uB7a:function(e,t){}});