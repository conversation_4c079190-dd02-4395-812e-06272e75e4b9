package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.WaterChargeElfBean;
import com.koron.zys.baseConfig.mapper.WaterChargeElfMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;


/**
 * 水费账单精灵-列表
 *
 * <AUTHOR>
 */
public class WaterChargeElfList implements ServerInterface {
    private static Logger logger = LoggerFactory.getLogger(WaterChargeElfList.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        @SuppressWarnings("rawtypes")
        MessageBean<WaterChargeElfBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", WaterChargeElfBean.class);
        try {
            WaterChargeElfMapper mapper = factory.getMapper(WaterChargeElfMapper.class);
            WaterChargeElfBean waterChargeElfBean = mapper.selectWaterChargeElf();
            info.setCode(Constant.MESSAGE_INT_SUCCESS);
            info.setDescription("success");
            info.setData(waterChargeElfBean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription("操作失败");
            logger.error("操作失败", e);
        }
        return info;
    }
}
