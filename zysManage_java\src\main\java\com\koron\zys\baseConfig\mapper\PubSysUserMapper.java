package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.koron.ebs.mybatis.EnvSource;

import com.koron.zys.baseConfig.bean.PubSysUserBean;
import com.koron.zys.baseConfig.queryBean.PubSysUserQueryBean;

@EnvSource("_default")
public interface PubSysUserMapper {
	
	/**
	 * 通过登录账号获取系统用户
	 * @param loginName
	 * @return
	 */
	PubSysUserBean getUserByLoginName(String loginName);
	
	/**
	 * 通过用户名查询
	 * @param userName
	 * @return
	 */
	PubSysUserBean getUserByUserName(String userName);
	
	/**
	 * 查询系统人员
	 * @param query
	 * @return
	 */
	List<PubSysUserBean> selectPubSysUserList(PubSysUserQueryBean query);
	
	/**
	 * 通过岗位查询人员
	 * @param post
	 * @return
	 */
	List<PubSysUserBean> selectPubSysUserByPost(String post);
	
	/**
	 * 通过组织查询人员
	 * @param post
	 * @return
	 */
	List<PubSysUserBean> selectPubSysUserByOrgId(String orgId);

}
