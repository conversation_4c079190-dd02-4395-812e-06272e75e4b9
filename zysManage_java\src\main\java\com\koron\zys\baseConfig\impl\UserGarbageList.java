package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.GarbageUniversalBean;
import com.koron.zys.baseConfig.mapper.GarbageMapper;
import com.koron.zys.baseConfig.mapper.WaterTypeMapper;
import com.koron.zys.baseConfig.queryBean.UserGarbageQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
import com.koron.util.Tools;

public class UserGarbageList implements ServerInterface{
	private static Logger logger = LoggerFactory.getLogger(UserGarbageList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
		try {
			UserGarbageQueryBean bean = JsonUtils.objectToPojo(req.getData(), UserGarbageQueryBean.class);
			GarbageMapper mapper = factory.getMapper(GarbageMapper.class);
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			List<GarbageUniversalBean> list = mapper.selectList(bean);
			WaterTypeMapper waterTypeMapper = factory.getMapper(WaterTypeMapper.class);
			for(GarbageUniversalBean garbageBean:list) {				
				garbageBean.setUseWaterTypeName(waterTypeMapper.findWaterTypeById(garbageBean.getUseWaterType()).getWaterTypeName());
				if(garbageBean!=null && StringUtils.isNotBlank(garbageBean.getStatus())) {
					garbageBean.setStatus(Tools.getDicNameByCodeAndValue(factory, "CUS", garbageBean.getStatus()));
				}
			}
			info.setData(new PageInfo<>(list));
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}
