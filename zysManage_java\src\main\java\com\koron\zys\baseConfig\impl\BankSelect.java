package com.koron.zys.baseConfig.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.koron.util.DBSourceUtils;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.BankMapper;
import com.koron.zys.baseConfig.vo.SelectVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

/**
 * 开户行下拉框
 * 
 * <AUTHOR>
 *
 */
public class BankSelect implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			String source = "";
			if (Objects.equals("001", userInfo.getCurService())){
				Map<String, String> map = JsonUtils.objectToPojo(req.getData(), Map.class);
				source = DBSourceUtils.getDbEnv(map.get("companyNo"));
			}else if (Objects.equals("002", userInfo.getCurService())){
				source = DBSourceUtils.getDbEnv(userInfo.getCurWaterCode());
			}
			BankMapper mapper = factory.getMapper(BankMapper.class, source);
			// 获取下拉框
			List<SelectVO> list = mapper.bankSelect();
			info.setData(list);
			return info;
		} catch (Exception e) {
			logger.error("开户行查询失败" + e.getMessage(), e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "开户行查询失败", null);
		}
	}
}
