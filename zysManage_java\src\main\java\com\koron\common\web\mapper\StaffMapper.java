package com.koron.common.web.mapper;

import com.koron.common.bean.query.StaffQueryBean;
import com.koron.common.web.bean.StaffBean;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.koron.ebs.mybatis.EnvSource;

import java.util.List;

@EnvSource("_default")
public interface StaffMapper {

	StaffBean getStaff(String loginid);

	StaffBean getStaffById(int id);

	StaffBean getStaffByCode(@Param("code") String code);

	List<StaffBean> selectList(StaffQueryBean query);

	/**
	 * 删除所有的员工信息
	 * @return
	 */
	@Delete("DELETE FROM tblstaff")
	int deleteAll();

	/**
	 * 删除所有的员工信息
	 * @return
	 */
	@Delete("DELETE FROM tblstaff WHERE id = #{id}")
	int deleteById(int id);

	/**
	 * 批量插入员工信息
	 * @param list
	 * @return
	 */
	int batchInsertStaff(List<StaffBean> list);

	/**
	 * 批量插入员工信息
	 * @param list
	 * @return
	 */
	int insertStaff(StaffBean staff);

	int updateStaff(StaffBean staff);

	/**
	 * 批量插入员工部门信息
	 * @param list
	 * @return
	 */
	int batchInsertStaffDepartment(List<StaffBean> list);

	/**
	 * 获取所有用户信息
	 * @return
	 */
	List<StaffBean> getAllStaff(StaffQueryBean query);
}
