webpackJsonp([9],{LMAC:function(e,t){},cKTx:function(e,t,a){"use strict";(function(e){var i=a("mvHQ"),o=a.n(i);t.a={name:"upgradeScriptManage",data:function(){return{tableShow:!0,indexShow:!0,isApprove:!1,isEdit:!0,showRevirwedName:!1,upgradeRangeDiaVisible:!1,showExecuteDetail:!1,maxHeight:0,recordTableHeight:0,operate:"PubScriptManageAdd",publicOptionDisabled:!1,allCompanyOptionDisabled:!1,companyOptionDisabled:!1,allUpgradeRangeName:[],tableData:{list:[]},recordDetail:{list:[]},tenantList:[{label:"公共库",options:[{companyNo:"public",shortName:"公共库"}]},{label:"全部水司",options:[{companyNo:"all",shortName:"全部水司"}]},{label:"水司",options:[]}],publicTenantList:[{companyNo:"public",shortName:"公共库"}],companyList:[],tableQuery:{page:1,pageCount:50,scriptDescribe:"",scriptContent:"",status:"",iterativeVersion:"",createName:""},formData:{createName:"",iterativeVersion:"",upgradeRange:[],scriptDescribe:"",scriptContent:"",status:"0",reviewedName:"",reviewedTime:""},batchDiaVisible:!1,batchData:{batchExecVersion:"",companyScope:[]},batchType:1,batchGroupCode:"",batchExecuteDiaTitle:"批量执行",registerRow:{},exceptionRegisterDiaVisible:!1,exceptionRegisterData:{solution:""},formRules:{createName:{required:!0,message:"请输入创建人",trigger:"blur"},iterativeVersion:[{required:!0,message:"请输入所属迭代",trigger:"blur"},{min:6,max:6,message:"请输入六位数的所属迭代",trigger:"blur"}],upgradeRange:{required:!0,message:"请输入升级范围",trigger:"change"},scriptDescribe:{required:!0,message:"请输入描述",trigger:"blur"},scriptContent:{required:!0,message:"请输入脚本",trigger:"blur"},status:{required:!0,message:"请选择状态",trigger:"change"},reviewedName:{required:!0,message:"请输入审批人",trigger:"change"}},statusDict:[{label:"待审批",value:"0"},{label:"已审批",value:"1"},{label:"已执行",value:"2"},{label:"已激活",value:"3"},{label:"已归档",value:"4"}],crumbsData:{titleList:[{title:"租户管理",path:"/tenant"},{title:"升级脚本管理",method:function(){window.histroy.back()}}]},companyData:[],checkAll:!1,isIndeterminate:!1,allDisabled:!1}},mounted:function(){this.getTenantList(),this.common.changeTable(this,".upgradeScriptManage .index-table",[".upgradeScriptManage .block",".upgradeScriptManage .toolbar"]),this.maxHeight+=50},watch:{maxHeight:function(e){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}},computed:{scriptText:function(){return this.lightHeightText(this.formData.scriptContent)}},methods:{init:function(){var e=this,t=this,a={busicode:"PubScriptManageList",data:t.tableQuery};this.$api.fetch({params:a}).then(function(a){console.log(a),t.tableData=a,t.tableData.list.forEach(function(e){var a="";a="public"!=e.upgradeRange?t.upgradeRangeFormate(e.upgradeRange):"公共库",e.upgradeRangeName=a}),console.log(t.tableData.list),e.common.changeTable(e,".upgradeScriptManage .index-table",[".upgradeScriptManage .block",".upgradeScriptManage .toolbar"]),e.maxHeight+=50})},upgradeRangeChange:function(e){console.log(e),"public"==e?(this.publicOptionDisabled=!1,this.companyOptionDisabled=!0,this.allCompanyOptionDisabled=!0):"all"==e?(this.publicOptionDisabled=!0,this.companyOptionDisabled=!0,this.allCompanyOptionDisabled=!1):""!=e?(this.publicOptionDisabled=!0,this.companyOptionDisabled=!1,this.allCompanyOptionDisabled=!0):(this.publicOptionDisabled=!1,this.companyOptionDisabled=!1,this.allCompanyOptionDisabled=!1)},add:function(){this.indexShow=!1,this.showRevirwedName=!1,this.operate="PubScriptManageAdd"},detail:function(e){this.indexShow=!1,this.formData=e,this.isEdit=!1,this.showRevirwedName=!0,console.log(this.formData.upgradeRange),this.formData.upgradeRange=""==this.formData.upgradeRange?[]:this.formData.upgradeRange.split(",")},lightHeightText:function(e){var t=new RegExp(/drop|delete/,"ig");return e.replace(t,'<span style="background:#f56c6c;color:#fff;padding:0 2px;">$&</span>')},update:function(e){this.indexShow=!1,this.operate="PubScriptManageUpdate",this.formData=e,this.formData.upgradeRange=""==this.formData.upgradeRange?[]:this.formData.upgradeRange.split(","),this.isEdit=!0,this.showRevirwedName=!1,this.isApprove=!1,this.upgradeRangeChange(e.upgradeRange)},approve:function(e){this.isApprove=!0,this.isEdit=!1,this.showRevirwedName=!0,this.formData=e,this.formData.upgradeRange=""==this.formData.upgradeRange?[]:this.formData.upgradeRange.split(","),this.indexShow=!1},showUpgradeRange:function(e){this.upgradeRangeDiaVisible=!0,this.allUpgradeRangeName=e.split(","),console.log(this.allUpgradeRangeName)},upgradeRangeFormate:function(e){var t=this,a="";if("public"==e)a="公共库";else if("all"==e)a="全部水司";else{var i=e.split(",");i.forEach(function(e,o){t.tenantList[2].options.forEach(function(t){t.companyNo==e&&(a+=o==i.length-1?t.shortName:t.shortName+",")})})}return a},statusFormate:function(e){var t=this;switch(e.status){case"0":return"待审批";case"1":return"已审批";case"2":var a=e.upgradeRange.split(","),i="全部执行";return"public"!=e.upgradeRange&&a.forEach(function(a){void 0!=t.tenantList[2].options.find(function(e){return e.companyNo==a})&&(!e[a]||e[a]&&""==e[a][a]||e[a]&&"异常登记"==e[a][a]&&""==e[a][a+"_solution"])&&(i="部分执行")}),void 0!=e.public&&""==e.public.public&&(i="部分执行"),i;case"3":return"已激活";case"4":return"已归档";default:return""}},tenantFormatter:function(e,t){var a="";return"public"==e.tenantId?a="公共库":this.tenantList[2].options.forEach(function(t){t.companyNo==e.tenantId&&(a=t.shortName)}),a},activate:function(e){var t=this,a=this;a.$confirm("确认激活该脚本？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var i={busicode:"PubScriptManageUpdate",data:{id:e.id,status:"3"}};t.$api.fetch({params:i}).then(function(e){console.log("激活",e),a.$message({type:"success",message:"激活成功!"}),a.back()})}).catch(function(){t.$message({type:"info",message:"已取消"})})},submit:function(){var e=this,t=this;this.$refs.form.validate(function(a){if(a){var i=JSON.parse(o()(t.formData));if(e.formData.id&&""!=e.formData.id){var n=i;i={createName:n.createName,id:n.id,iterativeVersion:n.iterativeVersion,reviewedName:n.reviewedName,reviewedTime:n.reviewedTime,scriptContent:n.scriptContent,scriptDescribe:n.scriptDescribe,status:n.status,upgradeRange:n.upgradeRange}}var s={busicode:t.operate,data:i};s.data.upgradeRange=s.data.upgradeRange.toString(),"all"==s.data.upgradeRange&&(s.data.upgradeRange=t.tenantList[2].options.map(function(e){return e.companyNo}).toString());var r="";"PubScriptManageAdd"==t.operate?(r="添加成功",s.data.status="0"):(r="修改成功","3"==t.formData.status?(s.data.status="0",s.data.reviewedName="",s.data.reviewedTime=""):t.formData.status),delete s.data.createTime,delete s.data.upgradeRangeName,console.log("params",s),e.$api.fetch({params:s}).then(function(e){console.log(e),t.$message({type:"success",message:r}),t.back()})}})},approveSubmit:function(){var e=this,t=this;this.$refs.form.validate(function(a){if(a){var i={busicode:"PubScriptManageUpdate",data:{id:t.formData.id,status:""}};e.$confirm("请确认审批","审批",{type:"warning",distinguishCancelAndClose:!0,confirmButtonText:"通过",cancelButtonText:"拒绝",cancelButtonClass:"refuse"}).then(function(){i.data.status="1",console.log("params",i),e.$api.fetch({params:i}).then(function(e){console.log(e),t.$message({type:"success",message:"审批通过！"}),t.back()})}).catch(function(a){"cancel"===a&&(i.data.status="3",console.log("params",i),e.$api.fetch({params:i}).then(function(e){console.log(e),t.$message({type:"success",message:"已拒绝！"}),t.back()}))})}})},executeByGroupCode:function(e,t){var a=this;a.$confirm("确定执行该脚本？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){a.toExecute(e,t)}).catch(function(e){console.log(e),a.$message({type:"info",message:"已取消执行"})})},toExecute:function(e,t){var a=this,i={busicode:"PubScriptManageExecute",data:{id:e,upgradeRange:t}};a.$api.fetch({params:i}).then(function(e){a.$message({type:"success",message:"sql脚本开始执行!"}),a.init()})},executeDetail:function(e){this.indexShow=!1,this.isEdit=!1,this.showRevirwedName=!0,this.showExecuteDetail=!0,this.getExecuteDetail(e.id),this.getExecuteRecordDetails(e.id)},fileDetail:function(e){var t=this,a={busicode:"PubScriptManageUpdate",data:{id:e.id,status:"4"}};this.$api.fetch({params:a}).then(function(e){t.$message({type:"success",message:"归档成功!"}),t.init()})},getExecuteDetail:function(e){var t=this,a={busicode:"PubScriptManageDetails",data:{id:e}};this.$api.fetch({params:a}).then(function(e){console.log(e),t.formData=e,t.formData.upgradeRange=t.formData.upgradeRange.split(",")})},getExecuteRecordDetails:function(e){var t=this,a={busicode:"PubScriptExecuteRecordDetails",data:{id:e}};this.$api.fetch({params:a}).then(function(e){console.log(e),t.recordDetail=e,t.$nextTick(function(){t.setHeight()})})},back:function(){this.isApprove=!1,this.isEdit=!0,this.showExecuteDetail=!1,this.operate="",this.formData={createName:"",iterativeVersion:"",upgradeRange:[],scriptDescribe:"",scriptContent:"",status:"0",reviewedName:"",reviewedTime:""},this.init(),this.indexShow=!0,this.publicOptionDisabled=!1,this.companyOptionDisabled=!1,this.allCompanyOptionDisabled=!1},changeParentItem:function(e,t){var a=this,i=!0;this.companyData.forEach(function(t){if(t.versionName==e){var i=!0;a.batchData.companyScope.some(function(t){return t==e})||a.batchData.companyScope.push(e),t.childrenCom.forEach(function(e){a.batchData.companyScope.some(function(t){return t==e.companyNo})||(a.batchData.companyScope.push(e.companyNo),i=!1)}),i&&(t.childrenCom.forEach(function(e){a.batchData.companyScope.findIndex(function(t){return t==e.companyNo})>-1&&a.batchData.companyScope.splice(a.batchData.companyScope.findIndex(function(t){return t==e.companyNo}),1)}),a.batchData.companyScope.findIndex(function(e){return e==t.versionName})>-1&&a.batchData.companyScope.splice(a.batchData.companyScope.findIndex(function(e){return e==t.versionName}),1))}});var o=0;this.companyData.forEach(function(e){a.batchData.companyScope.some(function(t){return t==e.versionName})||(i=!1),o=o+e.childrenCom.length+1}),this.checkAll=!!i,this.isIndeterminate=this.batchData.companyScope.length>0&&this.batchData.companyScope.length<o},handleCheckAllChange:function(e){var t=this;e?this.companyData.forEach(function(e){t.batchData.companyScope.some(function(t){return t==e.versionName})||t.batchData.companyScope.push(e.versionName),e.childrenCom.forEach(function(e){t.batchData.companyScope.some(function(t){return t==e.companyNo})||t.batchData.companyScope.push(e.companyNo)})}):this.$set(this.batchData,"companyScope",[]),this.isIndeterminate=!1},handleCheckedCitiesChange:function(e){var t=this;this.isIndeterminate=!1,this.checkAll=!1;var a=!0;this.companyData.forEach(function(t){var i=!0;t.childrenCom.forEach(function(t){e.some(function(e){return e==t.companyNo})||(i=!1,a=!1)}),i?e.some(function(e){return e==t.versionName})||e.push(t.versionName):e.findIndex(function(e){return e==t.versionName})>-1&&e.splice(e.findIndex(function(e){return e==t.versionName}),1)});var i=0;this.companyData.forEach(function(e){t.batchData.companyScope.some(function(t){return t==e.versionName})||(a=!1),i=i+e.childrenCom.length+1}),a&&(this.checkAll=!0),this.isIndeterminate=this.batchData.companyScope.length>0&&this.batchData.companyScope.length<i},getTenantList:function(){var e=this,t=this;this.$api.fetch({params:{busicode:"CompanyList",data:{page:1,pageCount:999}}}).then(function(a){var i=a.list.filter(function(e){return 1==e.status});t.tenantList[2].options=i,t.tenantList[2].options.forEach(function(e){var a;0==(a=t.companyData.filter(function(t){return e.versionName==t.versionName})).length?t.companyData.push({versionName:e.versionName,childrenCom:[e]}):t.companyData.forEach(function(t){a[0].versionName==t.versionName&&t.childrenCom.push(e)})}),e.init()})},batchExcution:function(){this.batchExecuteDiaTitle="批量执行",this.batchType=1,this.batchDiaVisible=!0},tobatchExecute:function(){var e=this,t=this.batchData.batchExecVersion;if(t){var a=JSON.parse(o()(this.batchData.companyScope)),i=!0;this.companyData.forEach(function(e){a.findIndex(function(t){return t==e.versionName})>-1&&a.splice(a.findIndex(function(t){return t==e.versionName}),1),e.childrenCom.forEach(function(e){a.some(function(t){return t==e.companyNo})||(i=!1)})});var n={busicode:"PubScriptManageBatchExecute",data:{iterativeVersion:t,upgradeRange:i?"":a.join(",")}};2===this.batchType&&(n.data.upgradeRange=this.batchGroupCode),console.log("parmas",n),this.$api.fetch({params:n}).then(function(t){e.$message.success("sql脚本开始执行!"),e.batchExecuteCancle(),e.init()})}else this.$message.warning("请输入升级范围！")},batchExecuteCancle:function(){this.batchDiaVisible=!1,this.batchData.companyScope=[],this.isIndeterminate=!1,this.allDisabled=!1},resetBatchDiaData:function(){this.batchData.batchExecVersion="",this.batchType=1,this.batchGroupCode="",this.batchExecuteDiaTitle="批量执行"},tobatchExecuteByGroupCode:function(e,t){this.allDisabled=!0,this.batchData.companyScope=[e],this.isIndeterminate=!0,this.batchType=2,this.batchGroupCode=e,this.batchExecuteDiaTitle="批量执行 - "+t,this.batchDiaVisible=!0},toExceptionRegister:function(e,t){this.registerRow={showStatus:e[t],showExceptionInfo:e[t+"_exception"],executeId:e[t+"_executeId"]},this.exceptionRegisterDiaVisible=!0},exceptionRegisterConfirm:function(){var e=this;if(""!=this.exceptionRegisterData.solution){var t={busicode:"PubScriptExecuteExceptionRegister",data:{id:this.registerRow.executeId,solution:this.exceptionRegisterData.solution}};this.$api.fetch({params:t}).then(function(t){e.$message({type:"success",message:"登记成功"}),e.exceptionRegisterCancle(),e.init()})}else this.$message.warning("请输入解决方案！")},exceptionRegisterCancle:function(){this.exceptionRegisterDiaVisible=!1},resetfailDiaData:function(){this.exceptionRegisterData.solution="",this.registerRow={}},retry:function(e,t,a){var i=this;this.$confirm("已进行异常登记，请选择你的操作","提示",{confirmButtonText:"重新执行",cancelButtonText:"查看登记",distinguishCancelAndClose:!0}).then(function(a){i.toExecute(e,t)}).catch(function(e){"cancel"===e&&i.executeDetail(a)})},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.init()},handleCurrentChange:function(e){this.tableQuery.page=e,this.init()},setHeight:function(){var t=e(".upgradeScriptManage").innerHeight()||0,a=e(".main-content > .bread-contain").innerHeight()||0,i=e(".detail > .formBill-Two").innerHeight()||0,o=e(".detail > .legendColumn").innerHeight()||0;console.log(t,a,i,o);var n=t-a-i-o-60;n=n<300?300:n,this.recordTableHeight=n}}}}).call(t,a("7t+N"))},"d//u":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("cKTx"),o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"upgradeScriptManage"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.indexShow,expression:"indexShow"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.batchExcution}},[e._v("批量执行")]),e._v(" "),a("el-button",{staticClass:"el-icon-plus",attrs:{size:"mini",type:"primary"},on:{click:e.add}},[e._v("创建")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.indexShow,expression:"!indexShow"}],staticClass:"bread-contain-right"},[e.isEdit?a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.submit}},[e._v("保存")]):e._e(),e._v(" "),e.isApprove?a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.approveSubmit}},[e._v("提交审批")]):e._e(),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.back}},[e._v("返回")])],1)],1),e._v(" "),e.indexShow?a("div",{staticClass:"kl-table index-table"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.tableQuery,size:"mini"}},[a("div",{staticClass:"toolbar-left"},[a("el-form-item",{attrs:{label:"创建人："}},[a("el-input",{staticClass:"default_class",attrs:{placeholder:"创建人",size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.init.apply(null,arguments)}},model:{value:e.tableQuery.createName,callback:function(t){e.$set(e.tableQuery,"createName",t)},expression:"tableQuery.createName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"迭代版本≤："}},[a("el-input",{staticClass:"default_class",attrs:{placeholder:"迭代版本",size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.init.apply(null,arguments)}},model:{value:e.tableQuery.iterativeVersion,callback:function(t){e.$set(e.tableQuery,"iterativeVersion",t)},expression:"tableQuery.iterativeVersion"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"执行状态："}},[a("el-select",{attrs:{placeholder:"执行状态",clearable:""},model:{value:e.tableQuery.status,callback:function(t){e.$set(e.tableQuery,"status",t)},expression:"tableQuery.status"}},e._l(e.statusDict,function(e,t){return a("el-option",{key:t,attrs:{value:e.value,label:e.label}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"sql内容："}},[a("el-input",{staticClass:"default_class",attrs:{placeholder:"sql内容",size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.init.apply(null,arguments)}},model:{value:e.tableQuery.scriptContent,callback:function(t){e.$set(e.tableQuery,"scriptContent",t)},expression:"tableQuery.scriptContent"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"sql描述："}},[a("el-input",{staticClass:"default_class",attrs:{placeholder:"sql描述",size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.init.apply(null,arguments)}},model:{value:e.tableQuery.scriptDescribe,callback:function(t){e.$set(e.tableQuery,"scriptDescribe",t)},expression:"tableQuery.scriptDescribe"}})],1),e._v(" "),a("el-form-item",{staticClass:"button-group"},[a("el-button",{staticClass:"compile",attrs:{type:"primary el-icon-search",size:"mini"},on:{click:e.init}})],1)],1)])],1),e._v(" "),e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.tableData.list,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间",width:"100","show-overflow-tooltip":"",fixed:"left"}}),e._v(" "),a("el-table-column",{attrs:{prop:"scriptDescribe",label:"描述",width:"120","show-overflow-tooltip":"",fixed:"left"}}),e._v(" "),a("el-table-column",{attrs:{prop:"iterativeVersion",label:"迭代版本",width:"70","show-overflow-tooltip":"",fixed:"left"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createName",label:"创建人",width:"80","show-overflow-tooltip":"",fixed:"left"}}),e._v(" "),a("el-table-column",{attrs:{prop:"scriptContent",label:"脚本",width:"100","show-overflow-tooltip":"",fixed:"left"}}),e._v(" "),a("el-table-column",{attrs:{prop:"status",label:"状态",width:"100","show-overflow-tooltip":"",fixed:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"==t.row.status?a("el-tag",{attrs:{effect:"dark",size:"small",type:"info"}},[e._v("\n              "+e._s(e.statusFormate(t.row)))]):e._e(),e._v(" "),"1"==t.row.status?a("el-tag",{attrs:{effect:"dark",size:"small"}},[e._v(e._s(e.statusFormate(t.row))+" ")]):e._e(),e._v(" "),"2"==t.row.status?a("div",["全部执行"==e.statusFormate(t.row)?a("el-tag",{attrs:{effect:"dark",size:"small",type:"success"}},[e._v("\n                "+e._s(e.statusFormate(t.row)))]):a("el-tag",{staticStyle:{"border-color":"#7e93f1"},attrs:{effect:"dark",size:"small",color:"#7e93f1"}},[e._v("\n                "+e._s(e.statusFormate(t.row)))])],1):e._e(),e._v(" "),"3"==t.row.status?a("el-tag",{attrs:{effect:"dark",size:"small",type:"warning"}},[e._v("\n              "+e._s(e.statusFormate(t.row)))]):e._e(),e._v(" "),"4"==t.row.status?a("el-tag",{staticStyle:{"border-color":"#ca8eff"},attrs:{effect:"dark",size:"small",color:"#ca8eff"}},[e._v("\n            "+e._s(e.statusFormate(t.row)))]):e._e()]}}],null,!1,1813636671)}),e._v(" "),a("el-table-column",{attrs:{prop:"public",label:"公共库","min-width":"130","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.status||2==t.row.status||4==t.row.status?a("div",[void 0!==t.row.public&&t.row.public&&""==t.row.public.public?a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.executeByGroupCode(t.row.id,"public")}}},[e._v("执行")]):void 0!==t.row.public&&t.row.public&&"异常登记"==t.row.public.public&&!t.row.public.public_solution?a("div",{staticClass:"executeFail",on:{click:function(a){return e.toExceptionRegister(t.row.public,"public")}}},[e._v("\n                  "+e._s(t.row.public.public))]):void 0!==t.row.public&&t.row.public&&"异常登记"==t.row.public.public&&t.row.public.public_solution&&""!=t.row.public.public_solution?a("div",{staticClass:"isRegister",on:{click:function(a){return e.retry(t.row.id,"public",t.row)}}},[e._v("\n                  已异常登记")]):void 0!==t.row.public&&""!=t.row.public&&""!=t.row.public.public?a("div",[e._v(e._s(t.row.public.public)+"\n                ")]):a("div",[e._v("-")])],1):e._e()]}}],null,!1,3586517651)}),e._v(" "),e._l(e.companyData,function(t,i){return a("el-table-column",{key:i},[a("template",{slot:"header"},[a("span",{style:{color:i%2==0?"rgb(35 14 213)":"rgb(224 3 227)"}},[e._v(e._s(t.versionName))])]),e._v(" "),e._l(t.childrenCom,function(t,o){return a("el-table-column",{key:o,attrs:{prop:t.companyNo,label:t.shortName,"min-width":"130","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(i){return[1==i.row.status||2==i.row.status||4==i.row.status?a("div",[void 0!==i.row[t.companyNo]&&i.row[t.companyNo]&&""==i.row[t.companyNo][t.companyNo]?a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.executeByGroupCode(i.row.id,t.companyNo)}}},[e._v("执行")]):void 0!==i.row[t.companyNo]&&i.row[t.companyNo]&&"异常登记"==i.row[t.companyNo][t.companyNo]&&!i.row[t.companyNo][t.companyNo+"_solution"]?a("div",{staticClass:"executeFail",on:{click:function(a){return e.toExceptionRegister(i.row[t.companyNo],t.companyNo)}}},[e._v("\n                "+e._s(i.row[t.companyNo][t.companyNo]))]):void 0!==i.row[t.companyNo]&&i.row[t.companyNo]&&"异常登记"==i.row[t.companyNo][t.companyNo]&&i.row[t.companyNo][t.companyNo+"_solution"]&&""!=i.row[t.companyNo][t.companyNo+"_solution"]?a("div",{staticClass:"isRegister",on:{click:function(a){return e.retry(i.row.id,t.companyNo,i.row)}}},[e._v("\n                已异常登记")]):void 0!==i.row[t.companyNo]&&""!=i.row[t.companyNo]&&i.row[t.companyNo][t.companyNo]?a("div",[e._v("\n                "+e._s(i.row[t.companyNo][t.companyNo]))]):a("div",[e._v("-")])],1):e._e()]}}],null,!0)},[a("template",{slot:"header"},[a("div",{staticClass:"groupCell"},[a("span",{style:{color:i%2==0?"rgb(35 14 213)":"rgb(224 3 227)"}},[e._v(e._s(t.shortName))]),e._v(" "),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"批量执行【"+t.shortName+"】脚本",placement:"top-start"}},[a("span",{staticClass:"excuteText",on:{click:function(a){return e.tobatchExecuteByGroupCode(t.companyNo,t.shortName)}}},[a("i",{staticClass:"el-icon-position"})])])],1)])],2)})],2)}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return["2"!=t.row.status?a("div",[1==t.row.status||4==t.row.status?a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.detail(t.row)}}},[e._v("详情\n              ")]):e._e(),e._v(" "),0==t.row.status||3==t.row.status?a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.update(t.row)}}},[e._v("修改")]):e._e(),e._v(" "),0==t.row.status?a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.approve(t.row)}}},[e._v("审批\n              ")]):e._e(),e._v(" "),1==t.row.status?a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.activate(t.row)}}},[e._v("激活\n              ")]):e._e()],1):a("div",[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.executeDetail(t.row)}}},[e._v("执行结果")]),e._v(" "),"全部执行"==e.statusFormate(t.row)?a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.fileDetail(t.row)}}},[e._v("归档")]):e._e()],1)]}}],null,!1,673231383)})],2):e._e(),e._v(" "),a("div",{staticClass:"block"},[e._m(0),e._v(" "),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1):e._e(),e._v(" "),e.indexShow?e._e():a("div",{staticClass:"detail"},[a("el-form",{ref:"form",staticClass:"formBill-Two",attrs:{rules:e.formRules,inline:!0,model:e.formData,"label-width":"120px"}},["PubScriptManageAdd"!=e.operate&&"PubScriptManageUpdate"!=e.operate?a("el-form-item",{attrs:{label:"创建人：",prop:"createName"}},[a("el-input",{attrs:{disabled:!e.isEdit,clearable:"",placeholder:"请输入创建人"},model:{value:e.formData.createName,callback:function(t){e.$set(e.formData,"createName",t)},expression:"formData.createName"}})],1):e._e(),e._v(" "),a("el-form-item",{attrs:{label:"所属迭代：",prop:"iterativeVersion"}},[a("el-input",{attrs:{oninput:"value=value.replace(/[^\\d]/g,'').slice(0,6)",disabled:!e.isEdit,clearable:"",placeholder:"请输入六位数字的迭代版本"},model:{value:e.formData.iterativeVersion,callback:function(t){e.$set(e.formData,"iterativeVersion",t)},expression:"formData.iterativeVersion"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"升级范围：",prop:"upgradeRange"}},[a("el-select",{attrs:{placeholder:"请选择",disabled:!e.isEdit,multiple:"",clearable:""},on:{change:e.upgradeRangeChange},model:{value:e.formData.upgradeRange,callback:function(t){e.$set(e.formData,"upgradeRange",t)},expression:"formData.upgradeRange"}},e._l(e.tenantList,function(t){return a("el-option-group",{key:t.label,attrs:{label:t.label}},["公共库"==t.label?a("div",e._l(t.options,function(t,i){return a("el-option",{key:i,attrs:{label:t.shortName,value:t.companyNo,disabled:e.publicOptionDisabled}})}),1):"全部水司"==t.label?a("div",e._l(t.options,function(t,i){return a("el-option",{key:i,attrs:{label:t.shortName,value:t.companyNo,disabled:e.allCompanyOptionDisabled}})}),1):a("div",e._l(t.options,function(t,i){return a("el-option",{key:i,attrs:{label:t.shortName,value:t.companyNo,disabled:e.companyOptionDisabled}})}),1)])}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"描述：",prop:"scriptDescribe"}},[a("el-input",{attrs:{disabled:!e.isEdit,clearable:"",placeholder:"请输入描述"},model:{value:e.formData.scriptDescribe,callback:function(t){e.$set(e.formData,"scriptDescribe",t)},expression:"formData.scriptDescribe"}})],1),e._v(" "),a("el-form-item",{staticClass:"f4",attrs:{label:"脚本：",prop:"scriptContent"}},[e.isEdit?a("el-input",{attrs:{type:"textarea",disabled:!e.isEdit,autosize:{minRows:3,maxRows:10},clearable:"",placeholder:"请输入脚本"},model:{value:e.formData.scriptContent,callback:function(t){e.$set(e.formData,"scriptContent",t)},expression:"formData.scriptContent"}}):a("div",{staticClass:"scriptTextContent",domProps:{innerHTML:e._s(e.scriptText)}})],1),e._v(" "),e.showRevirwedName&&!e.isApprove?a("el-form-item",{staticClass:"f4",attrs:{label:"审批人：",prop:"reviewedName"}},[a("el-input",{attrs:{disabled:!e.isApprove,clearable:"",placeholder:"请输入审批人"},model:{value:e.formData.reviewedName,callback:function(t){e.$set(e.formData,"reviewedName",t)},expression:"formData.reviewedName"}})],1):e._e()],1),e._v(" "),e.showExecuteDetail?a("div",[a("div",{staticClass:"legendColumn"},[e._v("执行结果")]),e._v(" "),a("el-table",{staticClass:"kl-table recordTable",attrs:{stripe:"",border:"",data:e.recordDetail.list,"max-height":e.recordTableHeight}},[a("el-table-column",{attrs:{prop:"tenantId",label:"执行租户","min-width":"130","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"executeTime",label:"执行时间","min-width":"130","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"executeName",label:"执行人","min-width":"130","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"executeStatus",label:"执行结果","min-width":"150","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return["成功"==t.row.executeStatus?a("el-tag",{attrs:{effect:"dark",size:"small",type:"success"}},[e._v("\n                "+e._s(t.row.executeStatus))]):a("el-tag",{attrs:{effect:"dark",size:"small",type:"danger"}},[e._v(e._s(t.row.executeStatus))])]}}],null,!1,3866938041)}),e._v(" "),a("el-table-column",{attrs:{prop:"solution",label:"解决方案","min-width":"130","show-overflow-tooltip":""}})],1)],1):e._e()],1)]),e._v(" "),a("el-dialog",{staticClass:"rangeDia",attrs:{title:"升级范围",visible:e.upgradeRangeDiaVisible,width:"50%"},on:{"update:visible":function(t){e.upgradeRangeDiaVisible=t}}},[a("div",{staticClass:"upgradeRangeList"},e._l(e.allUpgradeRangeName,function(t,i){return a("el-tag",{key:i,staticStyle:{margin:"10px"},attrs:{effect:"dark"}},[e._v(e._s(t)+"\n      ")])}),1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:function(t){e.upgradeRangeDiaVisible=!1}}},[e._v("关 闭")])],1)]),e._v(" "),a("el-dialog",{staticClass:"batchExecuteDia",attrs:{title:e.batchExecuteDiaTitle,visible:e.batchDiaVisible,width:"60%","close-on-click-modal":!1,"destroy-on-close":!0,"before-close":e.batchExecuteCancle},on:{"update:visible":function(t){e.batchDiaVisible=t},closed:e.resetBatchDiaData}},[a("div",{staticClass:"batchExecuteContent"},[a("el-form",{attrs:{size:"mini"},model:{value:e.batchData,callback:function(t){e.batchData=t},expression:"batchData"}},[a("el-form-item",{staticStyle:{width:"350px !important"},attrs:{label:"版本范围≤：",prop:"batchExecVersion"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入版本范围",oninput:"value=value.replace(/[^\\d]/g,'').slice(0,6)"},model:{value:e.batchData.batchExecVersion,callback:function(t){e.$set(e.batchData,"batchExecVersion",t)},expression:"batchData.batchExecVersion"}})],1),e._v(" "),a("el-form-item",{staticStyle:{height:"200px"},attrs:{label:"水司范围："}},[a("el-checkbox",{attrs:{indeterminate:e.isIndeterminate,disabled:e.allDisabled},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[a("span",{staticStyle:{"font-weight":"600"}},[e._v("全选")])]),e._v(" "),e._l(e.companyData,function(t,i){return a("el-checkbox-group",{key:i,staticStyle:{display:"flex"},attrs:{disabled:e.allDisabled},model:{value:e.batchData.companyScope,callback:function(t){e.$set(e.batchData,"companyScope",t)},expression:"batchData.companyScope"}},[a("el-checkbox",{staticClass:"checkStyle",attrs:{label:t.versionName,value:t.versionName},on:{change:function(a){return e.changeParentItem(t.versionName,a)}}},[a("span",{staticStyle:{"font-weight":"600"}},[e._v(e._s(t.versionName)+"：")])]),e._v(" "),a("div",e._l(t.childrenCom,function(t){return a("el-checkbox",{key:t.companyNo,staticStyle:{width:"100px"},attrs:{label:t.companyNo},on:{change:function(t){return e.handleCheckedCitiesChange(e.batchData.companyScope,t)}}},[e._v("\n                "+e._s(t.shortName)+"\n              ")])}),1)],1)})],2)],1)],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.tobatchExecute}},[e._v("批量执行")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:e.batchExecuteCancle}},[e._v("取 消")])],1)]),e._v(" "),a("el-dialog",{staticClass:"failDia",attrs:{title:"异常登记",visible:e.exceptionRegisterDiaVisible,width:"50%"},on:{"update:visible":function(t){e.exceptionRegisterDiaVisible=t},closed:e.resetfailDiaData}},[a("div",{staticClass:"failDiaContent"},[a("el-form",{attrs:{size:"mini","label-width":"100px"}},[a("el-form-item",{attrs:{label:"执行状态："}},[a("span",[e._v(e._s(e.registerRow.showStatus))])]),e._v(" "),a("el-form-item",{attrs:{label:"异常信息："}},[a("div",{staticClass:"exceptionInfoContent"},[e._v(e._s(e.registerRow.showExceptionInfo))])]),e._v(" "),a("el-form-item",{attrs:{label:"解决方案：",required:""}},[a("el-input",{attrs:{placeholder:"请输入解决方案"},model:{value:e.exceptionRegisterData.solution,callback:function(t){e.$set(e.exceptionRegisterData,"solution",t)},expression:"exceptionRegisterData.solution"}})],1)],1)],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.exceptionRegisterConfirm}},[e._v("登 记")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:e.exceptionRegisterCancle}},[e._v("取 消")])],1)])],1)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"tipInfo"},[t("p",[this._v("使用说明：1、批量执行：执行小于所选迭代版本所有水司所有脚本")]),this._v(" "),t("p",{staticStyle:{"margin-left":"60px"}},[this._v("2、单水司批量执行：执行小于所选迭代版本当前水司所有脚本")]),this._v(" "),t("p",{staticStyle:{"margin-left":"60px"}},[this._v("3、单个执行：执行当前所选脚本")]),this._v(" "),t("p",{staticStyle:{"margin-left":"60px"}},[this._v("4、异常登记：执行失败后，必须登记解决方案。可选再次执行，其他原因请反馈管理员")])])}]};var n=function(e){a("LMAC"),a("yzSk")},s=a("VU/8")(i.a,o,!1,n,"data-v-34d453ba",null);t.default=s.exports},yzSk:function(e,t){}});