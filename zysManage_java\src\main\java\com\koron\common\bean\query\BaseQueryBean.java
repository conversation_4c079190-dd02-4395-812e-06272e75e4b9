package com.koron.common.bean.query;

import java.util.List;

public class BaseQueryBean {
	/**
	 * 第几页
	 */
	private int page=1;
	/**
	 * 每页条数
	 */
	private int pageCount = 20;
	
	/**
	 * 模糊查询字段
	 */
	private String fuzzyQuery;
	
	/**
	 * 模糊查询字段组
	 */
	private List<String> fuzzyItems;

	public List<String> getFuzzyItems() {
		return fuzzyItems;
	}

	public void setFuzzyItems(List<String> fuzzyItems) {
		this.fuzzyItems = fuzzyItems;
	}

	private String where;
	
	public String getWhere() {
		return where;
	}
	public void setWhere(String where) {
		this.where = where;
	}
	public String getFuzzyQuery() {
		return fuzzyQuery;
	}
	public void setFuzzyQuery(String fuzzyQuery) {
		this.fuzzyQuery = fuzzyQuery;
	}
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public int getPageCount() {
		return pageCount;
	}
	public void setPageCount(int pageCount) {
		this.pageCount = pageCount;
	}

	@Override
	public String toString() {
		return "BaseQueryBean{" +
				"page=" + page +
				", pageCount=" + pageCount +
				", fuzzyQuery='" + fuzzyQuery + '\'' +
				", fuzzyItems=" + fuzzyItems +
				", where='" + where + '\'' +
				'}';
	}
}
