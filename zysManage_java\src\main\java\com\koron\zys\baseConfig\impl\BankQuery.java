package com.koron.zys.baseConfig.impl;

import com.koron.zys.baseConfig.queryBean.BankQueryBean;
import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BankBean;
import com.koron.zys.baseConfig.mapper.BankMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 银行信息-编辑初始化
 *
 * <AUTHOR>
 */
public class BankQuery implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(BankQuery.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        @SuppressWarnings("rawtypes")
        MessageBean<BankBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", BankBean.class);

        try {
            BankMapper mapper = factory.getMapper(BankMapper.class);
            BankQueryBean bean = JsonUtils.objectToPojo(req.getData(), BankQueryBean.class);
            if (StringUtils.isEmpty(bean.getId())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id不能为空", void.class);
            }
            BankBean Bankbean = mapper.findBankById(bean.getId());
            info.setData(Bankbean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }
}
