webpackJsonp([35],{BMNI:function(e,t){},nWPG:function(e,t){},"nuj+":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"bankLinkAdd"},[a("el-form",{ref:"bankLinkAddRuleForm",staticClass:"formBill",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px",inline:!0}},[a("el-form-item",{attrs:{label:"银行名称：",prop:"bankId"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.bankId,callback:function(t){e.$set(e.ruleForm,"bankId",t)},expression:"ruleForm.bankId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"银行驱动：",prop:"configName"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.configName,callback:function(t){e.$set(e.ruleForm,"configName",t)},expression:"ruleForm.configName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"驱动端口：",prop:"configPort"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.configPort,callback:function(t){e.$set(e.ruleForm,"configPort",t)},expression:"ruleForm.configPort"}})],1),e._v(" "),a("el-form-item",{staticClass:"remark f2",attrs:{label:"配置参数：",prop:"configParam"}},[a("el-input",{attrs:{type:"textarea","show-word-limit":"",maxlength:"2000",clearable:""},model:{value:e.ruleForm.configParam,callback:function(t){e.$set(e.ruleForm,"configParam",t)},expression:"ruleForm.configParam"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"通讯类型：",prop:"communicationType"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.communicationType,callback:function(t){e.$set(e.ruleForm,"communicationType",t)},expression:"ruleForm.communicationType"}},[a("el-option",{attrs:{label:"tcp",value:1}}),e._v(" "),a("el-option",{attrs:{label:"http",value:2}}),e._v(" "),a("el-option",{attrs:{label:"ftp",value:3}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"连接线程数：",prop:"connectThread"}},[a("el-input",{attrs:{maxlength:"6",clearable:""},model:{value:e.ruleForm.connectThread,callback:function(t){e.$set(e.ruleForm,"connectThread",e._n(t))},expression:"ruleForm.connectThread"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"工作线程数：",prop:"workThread"}},[a("el-input",{attrs:{maxlength:"6",clearable:""},model:{value:e.ruleForm.workThread,callback:function(t){e.$set(e.ruleForm,"workThread",e._n(t))},expression:"ruleForm.workThread"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态：",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),a("el-option",{attrs:{label:"停用",value:0}})],1)],1)],1)],1)},staticRenderFns:[]};var r={name:"bankLink",components:{bankLinkAdd:a("VU/8")({name:"bankLinkAdd",data:function(){return{databaseData:[],ruleForm:{},formData:{companyNo:"",configId:"",bankInterfaceId:"",bankId:"",configName:"",configPort:"",configParam:1,communicationType:1,connectThread:6,workThread:3,status:1},rules:{bankInterfaceId:[{required:!0,message:"请输入银行接口ID",trigger:"blur"}],bankId:[{required:!0,message:"请输入直连银行",trigger:"blur"}],configName:[{required:!0,message:"请输入配置名称",trigger:"blur"}],configPort:[{required:!0,message:"请输入配置端口",trigger:"blur"}],configParam:[{required:!0,message:"请输入配置参数",trigger:"blur"}],communicationType:[{required:!0,message:"请输入通讯类型",trigger:"blur"}],connectThread:[{required:!0,message:"请输入连接线程数",trigger:"blur"},{type:"number",message:"连接线程数必须为数字值"}],workThread:[{required:!0,message:"请输入工作线程数",trigger:"blur"},{type:"number",message:"工作线程数必须为数字值"}],status:[{required:!0,message:"请输入状态",trigger:"blur"}]}}},mounted:function(){},methods:{resetForm:function(){this.$refs.bankLinkAddRuleForm.resetFields()},submitForm:function(e,t,a){var n=this,r=this,i={};this.ruleForm.companyNo=a,this.ruleForm=this.common.handleData(this.ruleForm,this.formData),this.$refs[e].validate(function(e){if(!e)return!1;i="添加"===t?{busicode:"BankInterfaceConfigAdd",data:n.ruleForm}:{busicode:"BankInterfaceConfigUpdate",data:n.ruleForm},n.$api.fetch({params:i}).then(function(e){r.$message({showClose:!0,message:"保存成功",type:"success"}),r.$parent.selectTSubSystem(),r.$parent.closeDialog(),"添加"===t&&r.$parent.getTreeDatas()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","bankLinkAdd",this.$parent.closeDialog)},editData:function(e){this.ruleForm=e}}},n,!1,function(e){a("BMNI")},null,null).exports,autoTree:a("yJVD").a},data:function(){return{tableShow:!0,maxHeight:0,appServerData:[],formData:{bankInterfaceId:"",bankId:"",configName:"",configPort:"",configParam:"",communicationType:"",connectThread:"",workThread:1,status:""},crumbsData:{titleList:[{title:"水司配置",path:"/waterSet"},{title:"银行接口",method:function(){window.histroy.back()}},{title:"根目录",method:function(){window.histroy.back()}}]},bankLinkShow:!0,bankLinkAddVisible:!1,treeDatas:{tree:[{shortName:"根目录",id:"2",children:[]}],defaultProps:{label:"shortName",children:"children"},inputProp:{showSearch:!1,Inp_placeholder:"请输入节点"},sendTreeProp:["code","shortName","districtArr","children","companyNo","group","isLeaf","isParent","parent","name"],rootName:"根目录",defaultOpen:{nodeKey:"id"}},treeParantId:"",companyNo:"C000001"}},mounted:function(){var e=this;this.getTreeDatas(),this.$nextTick(function(){e.common.changeTable(e,".bankLink .kl-table",[])})},methods:{appAdd:function(e){var t=this;if(this.bankLinkShow=!1,this.bankLinkAddVisible=!0,"add"===e)this.$refs.bankLinkAdd.editData({bankInterfaceId:"",bankId:"",configName:"",configPort:"",configParam:"",communicationType:"",connectThread:"",workThread:1,status:""}),this.$set(this.crumbsData.titleList,"3",{title:"添加",method:function(){window.histroy.back()}}),this.common.chargeObjectEqual(this,this.formData,"set","bankLinkAdd");else{this.$set(this.crumbsData.titleList,"3",{title:"编辑",method:function(){window.histroy.back()}});var a={busicode:"BankInterfaceConfigQuery",data:{configId:e.row.configId,companyNo:this.companyNo}};this.$api.fetch({params:a}).then(function(e){t.$refs.bankLinkAdd.editData(e),t.common.chargeObjectEqual(t,e,"set","bankLinkAdd")})}},indexMethod:function(e){return e+1},selectTSubSystem:function(){var e=this,t={busicode:"BankInterfaceConfigList",data:{companyNo:this.companyNo}};this.$api.fetch({params:t}).then(function(t){e.appServerData=t,e.common.changeTable(e,".bankLink .kl-table",[])}).catch(function(e){})},getTreeDatas:function(){var e=this,t=this;this.$api.fetch({params:{busicode:"CompanyNameList",data:{}}}).then(function(a){t.treeDatas.tree[0].children=a,t.treeDatas.tree[0].companyNo=a[0].companyNo,t.companyNo=a[0].companyNo,e.$set(e.crumbsData.titleList,"2",{title:"根目录",method:function(){window.histroy.back()}})})},closeDialog:function(){this.bankLinkShow=!0,this.bankLinkAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.bankLinkAdd.handleClose()},backTreeData:function(e){var t=this;this.companyNo=e.companyNo,this.$set(this.crumbsData.titleList,"2",{title:e.shortName,method:function(){window.histroy.back()}});var a={busicode:"BankInterfaceConfigList",data:{companyNo:this.companyNo}};this.$api.fetch({params:a}).then(function(e){t.appServerData=e,t.common.changeTable(t,".bankLink .kl-table",[])})},submitForm:function(e){var t=this.crumbsData.titleList[3].title;this.$refs.bankLinkAdd.submitForm(e,t,this.companyNo)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},i={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"bankLink"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.bankLinkShow,expression:"bankLinkShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.bankLinkAddVisible,expression:"bankLinkAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("bankLinkAddRuleForm")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.bankLinkShow,expression:"bankLinkShow"}],staticClass:"company-content"},[a("div",{staticClass:"company-left"},[a("auto-tree",{attrs:{treeData:e.treeDatas},on:{sendTreeData:e.backTreeData}})],1),e._v(" "),a("div",{staticClass:"kl-table company-right"},[e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.appServerData,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"bankId",label:"银行名称","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"configName","min-width":"100",label:"银行驱动"}}),e._v(" "),a("el-table-column",{attrs:{prop:"configPort","min-width":"80",label:"配置端口","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"communicationType",label:"通讯类型","min-width":"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"connectThread",label:"连接线程数","min-width":"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"workThread","min-width":"120",label:"工作线程数"}}),e._v(" "),a("el-table-column",{attrs:{prop:"status","min-width":"80",label:"状态"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.appAdd(t)}}},[e._v("编辑")])]}}],null,!1,**********)})],1):e._e()],1)]),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.bankLinkAddVisible,expression:"bankLinkAddVisible"}]},[a("bankLinkAdd",{ref:"bankLinkAdd"})],1)])])},staticRenderFns:[]};var o=a("VU/8")(r,i,!1,function(e){a("nWPG")},null,null);t.default=o.exports}});