package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;
import com.koron.util.Check.Repeat;

/**
 * 全局参数配置表
 * <AUTHOR>
 *
 */
public class ConfigBean {
	
	private String configId;

	/**
	 *参数名称
	 */
	@Check(name = "参数名称",notEmpty = true ,repeat = @Repeat(tableName = "BASE_CONFIG",columnName = "config_name"))
    private String configName;
    /**
              * 参数值
     */
	@Check(name = "参数值", notEmpty = true)
    private String configValue;
 
    private String comments;
    
    @Check(name = "状态", notEmpty = true)
    private Integer status;
    
    /**
                *  预留字段
     */
    private String tenantId;
    

    /**
	 * 创建时间
	 */
	private String createTime;
	
	/**
	 * 创建人
	 */
	private String createName;
	/**
	 * 修改时间
	 */
	private String updateTime;
	/**
	 * 修改人
	 */
	private String updateName;
	public String getConfigId() {
		return configId;
	}
	public void setConfigId(String configId) {
		this.configId = configId;
	}
	public String getConfigName() {
		return configName;
	}
	public void setConfigName(String configName) {
		this.configName = configName;
	}
	public String getConfigValue() {
		return configValue;
	}
	public void setConfigValue(String configValue) {
		this.configValue = configValue;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getTenantId() {
		return tenantId;
	}
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getCreateName() {
		return createName;
	}
	public void setCreateName(String createName) {
		this.createName = createName;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public String getUpdateName() {
		return updateName;
	}
	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}
}
