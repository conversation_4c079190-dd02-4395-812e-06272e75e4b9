package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.common.bean.query.BaseQueryBean;
import com.koron.zys.baseConfig.bean.ProcessRecordBean;
import com.koron.zys.baseConfig.bean.ProcessTimeBean;

public interface ProcessTimeMapper {
	public List<ProcessTimeBean> selectList();//查询超时配置
	
	public List<ProcessRecordBean> selectRecordList(BaseQueryBean bean);//查询超时记录
	
	public ProcessTimeBean query(String id);
	
	public void insert(ProcessTimeBean bean);
	
	public void update(ProcessTimeBean bean);
	
	public void delete(ProcessTimeBean bean);
	
	public void deleteRecord(ProcessTimeBean bean); //删除对应超时记录
	
	
}
