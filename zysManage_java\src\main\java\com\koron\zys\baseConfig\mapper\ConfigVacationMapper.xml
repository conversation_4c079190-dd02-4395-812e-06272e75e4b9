<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.ConfigVacationMapper">

    <select id="selectConfigVacationList" parameterType="com.koron.zys.baseConfig.queryBean.ConfigVacationQueryBean"
            resultType="com.koron.zys.baseConfig.vo.ConfigVacationVO">
        select cv.VACATION_ID as id,cv.vacation_name, date_format(cv.vacation_begin_date, '%Y-%m-%d') vacationBeginDate,
        date_format(cv.vacation_end_date, '%Y-%m-%d') vacationEndDate, cv.vacation_comments
        from PUB_CONFIG_VACATION cv
        where 1=1
        <if test="vacationName != null and vacationName != ''">
            and cv.vacation_name LIKE CONCAT('%',#{vacationName},'%')
        </if>
        <if test="vacationBeginDate != null and vacationBeginDate != '' and vacationEndDate != null and vacationEndDate != ''">
            and (
            cv.vacation_begin_date between date_format(#{vacationBeginDate},'%Y-%m-%d') and
            date_format(#{vacationEndDate},'%Y-%m-%d')
            or cv.vacation_end_date between date_format(#{vacationBeginDate},'%Y-%m-%d') and
            date_format(#{vacationEndDate},'%Y-%m-%d')
            or date_format(#{vacationBeginDate},'%Y-%m-%d') between cv.vacation_begin_date and cv.vacation_end_date
            or date_format(#{vacationEndDate},'%Y-%m-%d') between cv.vacation_begin_date and cv.vacation_end_date
            )
        </if>
    </select>


	<select id="checkConfigVacation" parameterType="com.koron.zys.baseConfig.queryBean.ConfigVacationQueryBean"
			resultType="integer">
        select count(1)
        from PUB_CONFIG_VACATION cv
        where 1=1
        <if test="vacationBeginDate != null and vacationBeginDate != '' and vacationEndDate != null and vacationEndDate != ''">
            and (
            cv.vacation_begin_date between date_format(#{vacationBeginDate},'%Y-%m-%d') and
            date_format(#{vacationEndDate},'%Y-%m-%d')
            or cv.vacation_end_date between date_format(#{vacationBeginDate},'%Y-%m-%d') and
            date_format(#{vacationEndDate},'%Y-%m-%d')
            or date_format(#{vacationBeginDate},'%Y-%m-%d') between cv.vacation_begin_date and cv.vacation_end_date
            or date_format(#{vacationEndDate},'%Y-%m-%d') between cv.vacation_begin_date and cv.vacation_end_date
            )
        </if>
        <if test="id != null and id != '' ">
            and VACATION_ID &lt;&gt; #{id}
        </if>
    </select>

    <insert id="insertConfigVacation" parameterType="com.koron.zys.baseConfig.bean.ConfigVacationBean">
		insert into PUB_CONFIG_VACATION (VACATION_ID,vacation_name,vacation_begin_date, vacation_end_date,
		vacation_comments,create_time,create_account, create_name)
		values
		(
		#{id},
		#{vacationName},
		#{vacationBeginDate},
		#{vacationEndDate},
		#{vacationComments},
		#{createTime},
		#{createAccount},
		#{createName}
		)
	</insert>

    <update id="updateConfigVacation" parameterType="com.koron.zys.baseConfig.bean.ConfigVacationBean">
		update PUB_CONFIG_VACATION
		set vacation_name = #{vacationName},
		    vacation_begin_date =#{vacationBeginDate},
		    vacation_end_date =#{vacationEndDate},
			vacation_comments = #{vacationComments},
			update_time=#{updateTime},
			update_account = #{updateAccount},
			update_name = #{updateName}
		    where VACATION_ID = #{id}
	</update>
</mapper>