package com.koron.zys.baseConfig.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.CostMapper;
import com.koron.zys.baseConfig.vo.CostVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.Tools;
/**
 * 费用名称-列表初始化
 * <AUTHOR>
 */
public class CostList implements ServerInterface  {
	
	private static Logger logger = LoggerFactory.getLogger(CostList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);

		try {
			//先查询公共库的费用类型
			CostMapper commonMapper = factory.getMapper(CostMapper.class, "_default");
			List<CostVO> list = commonMapper.selectPubCostList(null);
			//水司库费用类型
 			CostMapper mapper = factory.getMapper(CostMapper.class);
			List<CostVO> comlist = mapper.selectCostList();
			List<String> costIds = comlist.stream().map(CostVO::getId).collect(Collectors.toList());
			//过滤出水司需要的费用类型
			List<CostVO> filterCostList = list.stream().filter(dto -> {
				String id = dto.getId();
				return costIds.contains(id);
			}).collect(Collectors.toList());

			Map<String,String> cou = Tools.mapDicByCode(factory, "COU");
 			//设置已经启用的费用类型
 			for(CostVO vo :filterCostList) {					//运维同步到水司
 				vo.setCostUnit(cou.get(vo.getCostUnit()));
 				vo.setUsed(false);
				if("1".equals(vo.getStatus())) {
					vo.setUsed(true);
 				}
 			}
			info.setData(filterCostList);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}
