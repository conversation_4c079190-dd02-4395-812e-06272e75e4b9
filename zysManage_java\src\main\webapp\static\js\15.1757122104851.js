webpackJsonp([15],{W1Sh:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"indexManAdd"},[a("el-form",{ref:"ruleForm",staticClass:"formBill",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px",inline:!0}},[a("el-form-item",{attrs:{label:"指标维键：",prop:"kpiUnique"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.kpiUnique,callback:function(t){e.$set(e.ruleForm,"kpiUnique",t)},expression:"ruleForm.kpiUnique"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"指标名称：",prop:"kpiName"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.kpiName,callback:function(t){e.$set(e.ruleForm,"kpiName",t)},expression:"ruleForm.kpiName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"指标描述：",prop:"kpiComments"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.kpiComments,callback:function(t){e.$set(e.ruleForm,"kpiComments",t)},expression:"ruleForm.kpiComments"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"指标级别：",prop:"kpiLevel"}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"指标级别为1~9",placement:"top-start"}},[a("el-input",{attrs:{type:"number",min:"1",max:"9"},on:{blur:function(t){return e.takeChange()}},model:{value:e.ruleForm.kpiLevel,callback:function(t){e.$set(e.ruleForm,"kpiLevel",t)},expression:"ruleForm.kpiLevel"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"所属子主题维键：",prop:"subThemeUnique"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.subThemeUnique,callback:function(t){e.$set(e.ruleForm,"subThemeUnique",t)},expression:"ruleForm.subThemeUnique"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"所属子主题名称：",prop:"subThemeName"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.subThemeName,callback:function(t){e.$set(e.ruleForm,"subThemeName",t)},expression:"ruleForm.subThemeName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"所属主题维键：",prop:"themeUnique"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.themeUnique,callback:function(t){e.$set(e.ruleForm,"themeUnique",t)},expression:"ruleForm.themeUnique"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"所属主题名称：",prop:"themeName"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.themeName,callback:function(t){e.$set(e.ruleForm,"themeName",t)},expression:"ruleForm.themeName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"单位：",prop:"unit"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.unit,callback:function(t){e.$set(e.ruleForm,"unit",t)},expression:"ruleForm.unit"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态:",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),a("el-option",{attrs:{label:"停用",value:2}})],1)],1),e._v(" "),a("el-form-item",{staticClass:"remark f2",attrs:{label:"备注:",prop:"comments"}},[a("el-input",{attrs:{clearable:"",type:"textarea",maxlength:"50","show-word-limit":""},model:{value:e.ruleForm.comments,callback:function(t){e.$set(e.ruleForm,"comments",t)},expression:"ruleForm.comments"}})],1)],1)],1)},staticRenderFns:[]};var l={name:"indexMan",components:{indexManAdd:a("VU/8")({name:"indexManAdd",props:["parentToChild"],data:function(){return{databaseData:[],ruleForm:{kpiUnique:"",kpiName:"",kpiComments:"",kpiLevel:"",subThemeUnique:"",subThemeName:"",themeUnique:"",themeName:"",unit:"",status:1,comments:""},rules:{kpiUnique:[{required:!0,message:"请输入指标维键",trigger:"blur"}],kpiName:[{required:!0,message:"请输入指标名称",trigger:"blur"}],kpiComments:[{required:!0,message:"请输入指标描述",trigger:"blur"}],kpiLevel:[{required:!0,message:"请输入指标级别",trigger:"blur"}],subThemeUnique:[{message:"请输入所属子主题维键",trigger:"blur",required:!0}]}}},mounted:function(){},methods:{resetForm:function(){this.$refs.ruleForm.resetFields()},takeChange:function(){var e=this.ruleForm.kpiLevel;e<=1?e=1:e>=9&&(e=9),this.ruleForm.kpiLevel=e},submitForm:function(e,t){var a=this,i="",l=this,r={};this.$refs[e].validate(function(e){if(!e)return!1;"新建"===t?(r={busicode:"kpiAdd",data:a.ruleForm},i="新建成功"):(r={busicode:"kpiUpdate",data:a.ruleForm},i="保存成功"),a.$api.fetch({params:r}).then(function(e){l.$message({showClose:!0,message:i,type:"success"}),l.$parent.selectTSubSystem(),l.$parent.closeDialog(),a.resetForm()}),a.$parent.indexManShow=!1,a.$parent.indexManAddVisible=!0})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","childIndexMan",this.$parent.closeDialog)},editData:function(e){this.ruleForm=e}}},i,!1,function(e){a("oyzK")},null,null).exports},data:function(){return{total:0,tableShow:!0,tableQuery:{page:1,pageCount:50},maxHeight:0,appServerData:[],showSelectIndex:-1,formData:{kpiName:"",kpiComments:"",kpiLevel:"",subThemeUnique:"",subThemeName:"",themeUnique:"",themeName:"",unit:"",status:1,comments:"",kpiUnique:""},crumbsData:{titleList:[{title:"基础配置",path:"/ChangeTables"},{title:"指标管理",method:function(){window.histroy.back()}}]},indexManShow:!0,indexManAddVisible:!1}},mounted:function(){eventBus.$emit("secondMenuShow","secondMenuShow2"),this.selectTSubSystem()},methods:{submitForm:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.child.submitForm(e,t)},clear:function(){this.formData.kpiUnique="",this.formData.kpiName="",this.formData.kpiComments="",this.formData.kpiLevel="",this.formData.subThemeUnique="",this.formData.subThemeName="",this.formData.themeUnique="",this.formData.themeName="",this.formData.unit="",this.formData.comments=""},formatStatus:function(e){return 1===e.status?"启用":"停用"},appAdd:function(e){var t=this;if(this.clear(),"add"===e)this.$set(this.crumbsData.titleList,"2",{title:"新建",method:function(){window.histroy.back()}}),this.$refs.child.editData(this.formData),this.common.chargeObjectEqual(this,this.formData,"set","childIndexMan");else{this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var a={busicode:"KpiQuery",data:{kpiId:e.row.id}};this.$api.fetch({params:a}).then(function(e){t.$refs.child.editData(e),t.common.chargeObjectEqual(t,e,"set","childIndexMan")})}this.indexManShow=!1,this.indexManAddVisible=!0},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+e+1},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(e){this.tableQuery.page=e,this.selectTSubSystem()},selectTSubSystem:function(){var e=this,t=this,a={busicode:"KpiList",data:t.tableQuery};this.$api.fetch({params:a}).then(function(a){t.$set(t.appServerData,"list",a.list),t.total=a.total,t.common.changeTable(e,".indexMan .kl-table",[".indexMan .block"])})},closeDialog:function(){this.indexManShow=!0,this.indexManAddVisible=!1,this.crumbsData.titleList.pop(),this.$refs.child.resetForm()},handleClose:function(){this.$refs.child.handleClose()}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},r={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"indexMan"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.indexManShow,expression:"indexManShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("新建")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.indexManAddVisible,expression:"indexManAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("ruleForm")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.indexManShow,expression:"indexManShow"}],staticClass:"kl-table"},[e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.appServerData.list,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"kpiUnique",label:"指标维键","min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{prop:"kpiName",label:"指标名称","min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{prop:"kpiComments","min-width":"80",label:"指标描述"}}),e._v(" "),a("el-table-column",{attrs:{prop:"kpiLevel","min-width":"80",label:"指标级别","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"subThemeUnique",formatter:e.formatStatus,"min-width":"150",label:"所属子主题维键"}}),e._v(" "),a("el-table-column",{attrs:{prop:"subThemeName",label:"所属子主题名称","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"themeUnique",label:"所属主题维键","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"themeName","min-width":"150",label:"所属主题名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"unit","min-width":"80",label:"单位","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"status",formatter:e.formatStatus,"min-width":"80",label:"状态"}}),e._v(" "),a("el-table-column",{attrs:{prop:"comments",label:"备注","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.appAdd(t)}}},[e._v("编辑")])]}}],null,!1,1191321586)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.indexManAddVisible,expression:"indexManAddVisible"}]},[a("indexManAdd",{ref:"child"})],1)])])},staticRenderFns:[]};var n=a("VU/8")(l,r,!1,function(e){a("nw3c")},null,null);t.default=n.exports},nw3c:function(e,t){},oyzK:function(e,t){}});