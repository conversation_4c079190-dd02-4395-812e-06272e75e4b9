<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.MeterModelMapper">

    <select id="selectMeterModelList" parameterType="com.koron.zys.baseConfig.queryBean.MeterModelQueryBean"
            resultType="com.koron.zys.baseConfig.vo.MeterModelVO">
        select m.id,m.model_name,model_no,trans_way,meter_sensor,meter_bore,factory_id factoryName,
        case when m.status=1 then '启用' else '停用' end status,
        case when m.VALVE_CONTROL=1 then '是' else '否' end valveControl,
        m.meter_type
        from PUB_METER_MODEL m
        <where>
	        <if test="modelName != null and modelName != ''">
	            and m.model_name LIKE CONCAT('%',#{modelName},'%')
	        </if>
	        <if test="fuzzyQuery != null and fuzzyQuery != ''">
	            and (m.model_no LIKE CONCAT('%', #{fuzzyQuery}, '%') or m.model_name LIKE CONCAT('%', #{fuzzyQuery}, '%'))
	        </if>
			<if test="factoryId != null and factoryId != ''">
				and m.FACTORY_ID = #{factoryId}
			</if>
			<if test="valveControl != null and valveControl != ''">
				and m.VALVE_CONTROL = #{valveControl}
			</if>
			<if test="tramsWay != null and tramsWay != ''">
				and m.TRANS_WAY = #{tramsWay}
			</if>
			<if test="meterType != null and meterType != ''">
				and m.METER_TYPE = #{meterType}
			</if>
        </where>
        order by m.sort_no asc
    </select>
    
     <select id="queryMeterModelList" parameterType="com.koron.zys.baseConfig.queryBean.MsInfoQueryBean"
            resultType="com.koron.zys.baseConfig.bean.MeterModelBean">
        select a.*,b.FACTORY_NAME,c.bore_name meterBoreName
        from PUB_METER_MODEL a,
        pub_meter_factory b,
        pub_meter_bore c
        where 
        a.factory_id=b.id
        and a.meter_bore=c.id
        <if test="meterFactory != null and meterFactory != ''">
            and factory_id=#{meterFactory}
        </if>
        <if test="meterType != null and meterType != ''">
            and meter_type=#{meterType}
        </if>
        <if test="meterModel != null and meterModel != ''">
            and id=#{meterModel}
        </if>
        <if test="meterBore != null and meterBore != ''">
            and meter_bore=#{meterBore}
        </if>
        <if test="modelNos != null">
			and meter_no in
			<foreach collection="modelNos" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="modelIds != null">
			and a.id in
			<foreach collection="modelIds" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
     </select>


    <insert id="insertMeterModel" parameterType="com.koron.zys.baseConfig.bean.MeterModelBean">
		insert into PUB_METER_MODEL (id,model_no,model_name,factory_id,device_type,meter_type,valve_control,
		trans_way,protocol,meter_form,accuracy,r,q,q_range, meter_sensor, meter_bore,max_value,reserve1,reserve2,
		comments,status, sort_no,first_check_life,check_life,shelf_life,tenant_id, create_time,create_account,create_name)
		values
		(
		#{id},
		#{modelNo},
		#{modelName},
		#{factoryId},
		#{deviceType},
		#{meterType},
		#{valveControl},
		#{transWay},
		#{protocol},
		#{meterForm},
		#{accuracy},
		#{r},
		#{q},
		#{qRange},
		#{meterSensor},
		#{meterBore},
		#{maxValue},
		#{reserve1},
		#{reserve2},
		#{comments},
		#{status},
		#{sortNo},
		#{firstCheckLife},
		#{checkLife},
		#{shelfLife},
		#{tenantId},
		#{createTime},
		#{createAccount},
		#{createName}
		)
	</insert>

    <update id="updateMeterModel" parameterType="com.koron.zys.baseConfig.bean.MeterModelBean">
		update PUB_METER_MODEL
		set model_no = #{modelNo},
			model_name=#{modelName},
			factory_id=#{factoryId},
			device_type=#{deviceType},
			meter_type=#{meterType},
			valve_control=#{valveControl},
			trans_way=#{transWay},
			protocol=#{protocol},
			meter_form=#{meterForm},
			accuracy=#{accuracy},
			r=#{r},
			q=#{q},
			q_range=#{qRange},
			meter_sensor= #{meterSensor},
			meter_bore=#{meterBore},
			max_value=#{maxValue},
			reserve1=#{reserve1},
			reserve2=#{reserve2},
			comments = #{comments},
			status = #{status},
			sort_no = #{sortNo},
			first_check_life=#{firstCheckLife},
			check_life=#{checkLife},
			shelf_Life=#{shelfLife},
			tenant_id = #{tenantId},
			update_time=#{updateTime},
			update_account = #{updateAccount},
			update_name = #{updateName}
		    where id = #{id}
	</update>

</mapper>