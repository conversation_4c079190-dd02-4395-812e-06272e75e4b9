<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.ConfigMapper">

	<select id="selectConfigByNames" parameterType="String" resultType="com.koron.zys.baseConfig.vo.ConfigVO">
		SELECT * 
		FROM BASE_CONFIG
		WHERE CONFIG_NAME IN
		<foreach collection="names" item="item" index="index" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	
 	<select id="selectConfigList" parameterType="com.koron.zys.baseConfig.queryBean.ConfigQueryBean" resultType="com.koron.zys.baseConfig.vo.ConfigVO" >
		select xx.config_id, xx.config_name, xx.config_value,xx.comments,case when xx.status=1 then '启用' else '停用' end status
		from BASE_CONFIG xx
	    where 1=1	   
	    	<if test="status != null">
	    		and status = #{status} 
	    	</if>
	    	<if test="searchContent !=null and searchContent !=''">     
           AND (xx.config_name like concat('%',#{searchContent},'%') 
           	or xx.config_value like concat('%',#{searchContent},'%') 
           	or xx.comments like concat('%',#{searchContent},'%') )
             </if>
              <if test="configName != null and configName != ''">
				  and xx.config_name = #{configName}
			  </if>
	</select>
	 
	<select id="selectConfigById" resultType="com.koron.zys.baseConfig.bean.ConfigBean">
		select *
		from BASE_CONFIG
		where config_id = #{configId}
	</select>
	<select id="getConfigValueByName" resultType="String">
		select CONFIG_VALUE
		from BASE_CONFIG
		where CONFIG_NAME = #{name}
	</select>
	<select id="getConfigValueByNameOn" resultType="String">
		select CONFIG_VALUE
		from BASE_CONFIG
		where CONFIG_NAME = #{name} and status=1
	</select>
	
	 	<insert id="insertConfig" parameterType="com.koron.zys.baseConfig.bean.ConfigBean">
		insert into BASE_CONFIG (config_id,config_name,config_value,  
		comments,status,create_time, create_name)
		values
		(
		#{configId,jdbcType=VARCHAR},
		#{configName,jdbcType=VARCHAR},
		#{configValue,jdbcType=VARCHAR},
		#{comments,jdbcType=VARCHAR},
		#{status,jdbcType=INTEGER},	
		date_format(#{createTime},'%Y-%m-%d %T'),
		#{createName,jdbcType=VARCHAR}
		)
	</insert>
	
		<update id="updateConfig" parameterType="com.koron.zys.baseConfig.bean.ConfigBean">
		update BASE_CONFIG
		set config_name = #{configName,jdbcType=VARCHAR},	
		    config_value = #{configValue,jdbcType=VARCHAR},	 		
			comments = #{comments,jdbcType=VARCHAR},
			status = #{status,jdbcType=INTEGER},		
			update_time=date_format(#{updateTime},'%Y-%m-%d %T'),
			update_name = #{updateName,jdbcType=VARCHAR}
		    where config_id = #{configId}
	</update>

	<update id="updateConfigValueByName" parameterType="string" >
		update BASE_CONFIG
		set
		    config_value = #{configValue,jdbcType=VARCHAR},

			update_time=now()

		    where config_name = #{configName}
	</update>
	
	
</mapper>