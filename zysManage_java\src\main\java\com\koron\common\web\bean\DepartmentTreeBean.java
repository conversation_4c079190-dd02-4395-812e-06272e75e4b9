package com.koron.common.web.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class DepartmentTreeBean extends DepartmentBean{
	/**
	 * 分级编码
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long seq;
	/**
	 * 父级掩位数
	 */
	private Integer parentmask;
	/**
	 * 父级ID
	 */
	private Integer parentId;
	public Integer getParentId() {
		return parentId;
	}
	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}
	/**|
	*设置分级编码
	*/
	public DepartmentTreeBean setSeq(Long seq){
		this.seq = seq;
	return this;
	}
	/**
	*获取分级编码
	*/
	public Long getSeq(){
		return seq;
	}
	/**
	*设置父级掩位数
	*/
	public DepartmentTreeBean setParentmask(Integer parentmask){
		this.parentmask = parentmask;
	return this;
	}
	/**
	*获取父级掩位数
	*/
	public Integer getParentmask(){
		return parentmask;
	}
}