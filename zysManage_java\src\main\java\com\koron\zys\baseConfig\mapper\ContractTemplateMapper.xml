<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.ContractTemplateMapper">

		
 	<select id="selectList" parameterType="java.lang.String"  resultType="com.koron.zys.baseConfig.bean.ContractTemplateBean" >
 		SELECT
			t.id,
			t.create_name,
			t.create_time,
			t.template_name,
			t.type
		FROM
			base_contract_template t where 1=1
			<if test="_parameter != null and _parameter != ''">
				 and t.type = #{_parameter}
			</if>
		 order by t.create_time desc 
	</select>
	
	<select id="selectById" parameterType="java.lang.String" resultType="com.koron.zys.baseConfig.bean.ContractTemplateBean">
		SELECT
			t.id,
			t.create_name,
			t.create_time,
			t.template_name,
			t.type
		FROM
			base_contract_template t
		WHERE t.id = #{_parameter, jdbcType=VARCHAR}
	</select>
	

	
	<insert id="templateAdd" parameterType="com.koron.zys.baseConfig.bean.ContractTemplateBean">
		insert into base_contract_template (
			id,
			template_name,
			create_name,
			create_time,
			type
		)values(
			#{id,jdbcType=VARCHAR},
			#{templateName,jdbcType=VARCHAR},
			#{createName,jdbcType=VARCHAR},
			now(),
			#{type,jdbcType=INTEGER}
		)
	</insert>
	
	<update id="templateUpdate" parameterType="com.koron.zys.baseConfig.bean.ContractTemplateBean">
		update base_contract_template 
		<set>
			<if test="templateName != null and templateName != ''">
				template_name = #{templateName, jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				type = #{type, jdbcType=VARCHAR},
			</if>		
			update_time = now(),
		</set>
		where id = #{id, jdbcType=VARCHAR}
	</update>
	
</mapper>