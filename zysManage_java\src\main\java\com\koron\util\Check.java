package com.koron.util;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import com.koron.util.Type;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Check {
	
	/**
	 * 描述
	 * @return
	 */
	String name();
	
	/**
	 * 是否可以为null
	 * @return
	 */
	boolean notNull() default false;
	
	/**
	 * 是否可以为 empty
	 * @return
	 */
	boolean notEmpty() default false;
	
	/**
	 * 最小长度
	 * @return
	 */
	int min() default -1;
	
	/**
	 * 最大长度
	 * @return
	 */
	int max() default -1;
	
	/**
	 * 正则表达式
	 * @return
	 */
	String pattern() default "";
	
	/**
	 * 是否可重复
	 * @return
	 */
	Repeat[] repeat() default {};
	
	/**
	 * 是否为数字
	 * @return
	 */
	boolean number() default false;
	
	/**
	 * 是否必须英文编码
	 * @return
	 */
	boolean code() default false;
	
	Type type() default Type.DEFAULT;
	
	@interface Repeat{
		
		/**
		 * 表名
		 * @return
		 */
		String tableName();
		
		/**
		 * 字段名称 （去掉）
		 * @return
		 */
		String fieldName() default "";
		
		/**
		 * 列名称
		 * @return
		 */
		String columnName() default "";
	}
	
	
}
