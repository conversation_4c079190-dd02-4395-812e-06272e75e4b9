webpackJsonp([41],{"0Rrb":function(t,e,i){"use strict";(function(t){e.a={name:"dataDictionary",data:function(){return{tableShow:!0,maxHeight:0,dialogFormVisible:!1,dialogGroup:!1,titleEquipparam:"数据字典组-添加",titleGroup:"分组参数-添加",titleGroupName:"",titleGroupChName:"",ruleForm:{code:"",name:"",id:""},rules:{code:[{message:"请输入组编号",trigger:"blur",required:!0}],name:[{message:"请输入组名称",trigger:"blur",required:!0}]},ruleFormGroup:{name:"",value:"",dictId:"",id:""},rulesGroup:{name:[{message:"请输入组名",trigger:"blur",required:!0}],value:[{message:"请输入值",trigger:"blur",required:!0}]},tableQuery:{page:1,pageCount:20},historyData:{},equipparamData:[],groupData:[],redelParamBtn:!0,redelBtn:!0,rowData:{},rowGroupData:{},getGroup:"",getGroupCh:"",dataDictionaryShow:!0,dataDictionaryAddVisible:!1,crumbsData:{titleList:[{title:"基础配置",path:"/ChangeTables"},{title:"数据字典",method:function(){window.histroy.back()}}]}}},mounted:function(){this.init(),this.redelBtn&&(t("#b0t0n0").css({background:"#a0cfff",border:"1px solid #a0cfff"}),t("#b1t1n1").css({background:"#a0cfff",border:"1px solid #a0cfff"}),t("#b2t2n2").css({background:"#a0cfff",border:"1px solid #a0cfff"}),this.redelParamBtn&&(t("#b3t3n3").css({background:"#a0cfff",border:"1px solid #a0cfff"}),t("#b4t4n4").css({background:"#a0cfff",border:"1px solid #a0cfff"})))},methods:{add:function(){this.ruleForm.code="",this.ruleForm.name="",this.ruleForm.id="",this.titleEquipparam="数据字典组-添加",this.dialogFormVisible=!0,this.common.chargeObjectEqual(this,this.ruleForm,"set","childDataDictionary")},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","childDataDictionary",this.closeDialog)},closeDialog:function(){this.ruleForm.code="",this.ruleForm.name="",this.ruleForm.id="",this.$refs.ruleForm.resetFields(),this.dialogFormVisible=!1},addGroup:function(){this.ruleFormGroup.name="",this.ruleFormGroup.value="",this.ruleFormGroup.id="",this.titleGroup=this.titleGroupName+"组-参数添加",this.dialogGroup=!0,this.common.chargeObjectEqual(this,this.ruleFormGroup,"set","childDataDictionary2")},GroupClassName:function(t){var e=t.row,i=t.rowIndex;e.index=i},GroupHighlight:function(e){e.row;var i=e.rowIndex;this.getGroup===i&&(t("#kr-table1 tbody").children(".el-table__row:odd").addClass("el-table__row--striped"),t("#kr-table1 tbody").children(".el-table__row").eq(this.getGroup).addClass("is-active").removeClass("el-table__row--striped").siblings().removeClass("is-active"))},GroupClassNameCh:function(t){var e=t.row,i=t.rowIndex;e.index=i},GroupHighlightCh:function(e){e.row;var i=e.rowIndex;this.getGroupCh===i&&(t("#kr-table2 tbody").children(".el-table__row:odd").addClass("el-table__row--striped"),t("#kr-table2 tbody").children(".el-table__row").eq(this.getGroupCh).addClass("is-active").removeClass("el-table__row--striped").siblings().removeClass("is-active"))},handleCloseGroup:function(){this.common.chargeObjectEqual(this,this.ruleFormGroup,"get","childDataDictionary2",this.closeDialogGroup)},closeDialogGroup:function(){this.ruleFormGroup.name="",this.ruleFormGroup.value="",this.ruleFormGroup.dictId="",this.$refs.ruleFormGroup.resetFields(),this.dialogGroup=!1},indexMethod:function(t){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(t+1)},init:function(){var t=this;this.$api.fetch({params:{busicode:"DictionaryTypeList",data:{}}}).then(function(e){t.$set(t,"equipparamData",e)})},initGroup:function(){var t=this,e={busicode:"DictionaryParamInfoList",data:{dictId:t.rowData.id}};this.$api.fetch({params:e}).then(function(e){t.groupData=e})},demand:function(e,i,r){this.rowGroupData={},this.redelParamBtn=!0,this.getGroupCh="",this.$nextTick(function(){t("#kr-table2").find(".is-active").removeClass("is-active")}),t("#b3t3n3").css({background:"rgb(160, 207, 255)",border:"rgb(160, 207, 255)",color:"#fff"}),t("#b4t4n4").css({background:"rgb(160, 207, 255)",border:"rgb(160, 207, 255)",color:"#fff"}),this.redelBtn=!1,t("#b0t0n0").css({background:"#409eff",border:"1px solid #409eff"}),t("#b1t1n1").css({background:"#409eff",border:"1px solid #409eff"}),t("#b2t2n2").css({background:"#409eff",border:"1px solid #409eff"}),this.rowData.code=e.code,this.rowData.name=e.name,this.rowData.id=e.id,this.ruleFormGroup.dictId=e.id,this.getGroup=e.index,this.titleGroupName=e.name,this.initGroup()},demandgroup:function(e,i,r){this.redelParamBtn=!1,t("#b3t3n3").css({background:"#409eff",border:"1px solid #409eff"}),t("#b4t4n4").css({background:"#409eff",border:"1px solid #409eff"}),this.rowGroupData.name=e.name,this.rowGroupData.value=e.value,this.rowGroupData.dictId=e.dictId,this.rowGroupData.id=e.id,this.getGroupCh=e.index,this.titleGroupChName=e.name},handleEdit:function(){this.dialogFormVisible=!0,this.titleEquipparam="数据字典组-修改";var t=this.ruleForm;t.name=this.rowData.name,t.code=this.rowData.code,t.id=this.rowData.id,this.common.chargeObjectEqual(this,t,"set","childDataDictionary")},handleEditGroup:function(){0!=this.rowGroupData.length?(this.dialogGroup=!0,this.titleGroup=this.titleGroupName+"组-参数修改",this.ruleFormGroup.name=this.rowGroupData.name,this.ruleFormGroup.value=this.rowGroupData.value,this.ruleFormGroup.dictId=this.rowGroupData.dictId,this.ruleFormGroup.id=this.rowGroupData.id,this.common.chargeObjectEqual(this,this.ruleFormGroup,"set","childDataDictionary2")):this.$message("请选择需要修改的行！")},submit:function(t){var e=this,i=void 0,r=void 0;delete this.ruleForm.index,"数据字典组-添加"===this.titleEquipparam?(i={busicode:"DictionaryTypeAdd",data:this.ruleForm},r="数据添加成功！"):(i={busicode:"DictionaryTypeUpdate",data:this.ruleForm},r="数据修改成功！"),e.$refs[t].validate(function(t){if(!t)return!1;e.$api.fetch({params:i}).then(function(t){e.$message({showClose:!0,message:r,type:"success"}),e.init(),e.dialogFormVisible=!1})})},del:function(){var t=this,e='该操作将删除"'+this.titleGroupName+'"分组以及分组下的所有参数信息,是否确定删除?';this.$confirm(e,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e=t,i={busicode:"DictionaryTypeDelete",data:{id:t.rowData.id}};e.$api.fetch({params:i}).then(function(t){e.init(),e.groupData=[],e.redelBtn=!0,e.rowData={},e.redelParamBtn=!0,e.rowGroupData={},e.getGroupCh="",e.getGroup=""})}).catch(function(){})},submitGroup:function(){var t=this,e=this,i=void 0,r=void 0;delete this.ruleFormGroup.index,this.titleGroup===this.titleGroupName+"组-参数添加"?(i={busicode:"DictionaryParamInfoAdd",data:this.ruleFormGroup},r="组-参数添加成功！"):(i={busicode:"DictionaryParamInfoUpdate",data:this.ruleFormGroup},r="组-参数修改成功！"),this.$refs.ruleFormGroup.validate(function(a){if(!a)return!1;e.$api.fetch({params:i}).then(function(i){e.$message({showClose:!0,message:r,type:"success"}),e.dialogGroup=!1,e.titleGroup="",t.titleGroupName="",e.initGroup()})})},delGroup:function(e,i){var r=this,a='该操作将删除"'+this.titleGroupName+"-"+this.titleGroupChName+'"参数，是否确定删除?';this.$confirm(a,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e=r,i={busicode:"DictionaryParamInfoDelete",data:{id:e.rowGroupData.id}};e.$api.fetch({params:i}).then(function(i){e.redelParamBtn=!0,e.$message({showClose:!0,message:"删除成功！",type:"success"}),setTimeout(function(){t("#b3t3n3").css({background:"#a0cfff !important",border:"1px solid #a0cfff !important"}),t("#b4t4n4").css({background:"#a0cfff !important",border:"1px solid #a0cfff !important"})},0),e.rowGroupData={},e.initGroup(),e.getGroupCh=""})}).catch(function(){})}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})},rowData:function(e,i){this.rowGroupData="",this.getGroupCh="",this.redelParamBtn=!0,setTimeout(function(){t("#b3t3n3").css({background:"#a0cfff !important",border:"1px solid #a0cfff !important"}),t("#b4t4n4").css({background:"#a0cfff !important",border:"1px solid #a0cfff !important"})},0)}}}}).call(e,i("7t+N"))},Yybr:function(t,e){},kWVZ:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=i("0Rrb"),a={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"dataDictionary"},[i("div",{staticClass:"bread-contain"},[i("publicCrumbs",{attrs:{crumbsData:t.crumbsData}})],1),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:t.dataDictionaryShow,expression:"dataDictionaryShow"}],staticClass:"main-content"},[i("el-dialog",{attrs:{width:"45%",title:t.titleEquipparam,"close-on-click-modal":!1,visible:t.dialogFormVisible,"before-close":t.handleClose},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[i("el-form",{ref:"ruleForm",staticClass:"demo-ruleForm",staticStyle:{"margin-left":"120px","margin-top":"50px"},attrs:{model:t.ruleForm,rules:t.rules,"label-width":"100px","label-position":"left"}},[i("el-form-item",{attrs:{label:"组编号:",prop:"code"}},[i("el-input",{staticStyle:{width:"400px"},model:{value:t.ruleForm.code,callback:function(e){t.$set(t.ruleForm,"code",e)},expression:"ruleForm.code"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"组名称:",prop:"name"}},[i("el-input",{staticStyle:{width:"400px"},attrs:{type:"text"},model:{value:t.ruleForm.name,callback:function(e){t.$set(t.ruleForm,"name",e)},expression:"ruleForm.name"}})],1)],1),t._v(" "),i("br"),t._v(" "),i("div",{ref:"ruleForm",staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"info",size:"mini",plain:""},on:{click:function(e){return t.submit("ruleForm")}}},[t._v("确 定")]),t._v(" "),i("el-button",{attrs:{type:"",size:"mini",plain:""},on:{click:t.handleClose}},[t._v("取 消")])],1)],1),t._v(" "),i("el-dialog",{attrs:{width:"45%",title:t.titleGroup,"close-on-click-modal":!1,center:"",visible:t.dialogGroup,"before-close":t.handleCloseGroup},on:{"update:visible":function(e){t.dialogGroup=e}}},[i("el-form",{ref:"ruleFormGroup",staticClass:"demo-ruleForm",staticStyle:{"margin-left":"120px","margin-top":"50px"},attrs:{model:t.ruleFormGroup,rules:t.rulesGroup,"label-width":"100px","label-position":"left"}},[i("el-form-item",{attrs:{label:"参数名:",prop:"name"}},[i("el-input",{staticStyle:{width:"400px"},model:{value:t.ruleFormGroup.name,callback:function(e){t.$set(t.ruleFormGroup,"name",e)},expression:"ruleFormGroup.name"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"参数值:",prop:"value"}},[i("el-input",{staticStyle:{width:"400px"},attrs:{type:"text"},model:{value:t.ruleFormGroup.value,callback:function(e){t.$set(t.ruleFormGroup,"value",e)},expression:"ruleFormGroup.value"}})],1),t._v(" "),i("br")],1),t._v(" "),i("div",{ref:"ruleFormGroup",staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"info",size:"mini",plain:""},on:{click:function(e){return t.submitGroup("ruleFormGroup")}}},[t._v("确 定")]),t._v(" "),i("el-button",{attrs:{type:"",size:"mini",plain:""},on:{click:t.handleCloseGroup}},[t._v("取 消")])],1)],1),t._v(" "),i("div",{staticClass:"kl-table"},[i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:12}},[i("div",{staticClass:"toolbar"},[i("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,size:"mini"}},[i("div",{staticClass:"toolbar-right"},[i("el-form-item",{staticClass:"button-group"},[i("el-button",{staticClass:"remove el-icon-edit",attrs:{disabled:t.redelBtn,id:"b0t0n0",size:"mini",type:"primary"},nativeOn:{click:function(e){return t.handleEdit.apply(null,arguments)}}},[t._v("修改")]),t._v(" "),i("el-button",{staticClass:"remove el-icon-delete",attrs:{disabled:t.redelBtn,id:"b1t1n1",size:"mini",type:"primary"},nativeOn:{click:function(e){return t.del.apply(null,arguments)}}},[t._v("删除")]),t._v(" "),i("el-button",{staticClass:"remove el-icon-plus",attrs:{size:"mini",type:"primary"},on:{click:t.add}},[t._v("添加")])],1)],1)])],1),t._v(" "),i("el-table",{attrs:{id:"kr-table1",stripe:"",center:"",border:"",data:t.equipparamData,"row-class-name":t.GroupClassName,"row-style":t.GroupHighlight},on:{"row-click":t.demand}},[i("el-table-column",{attrs:{type:"index",label:"NO.",width:"80",index:t.indexMethod}}),t._v(" "),i("el-table-column",{attrs:{prop:"code",label:"域编码"}}),t._v(" "),i("el-table-column",{attrs:{prop:"name",label:"域名称"}})],1)],1),t._v(" "),i("el-col",{attrs:{span:12}},[i("div",{staticClass:"toolbar"},[i("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.tableQuery,size:"mini"}},[i("div",{staticClass:"toolbar-right"},[i("el-form-item",{staticClass:"button-group"},[i("el-button",{staticClass:"remove el-icon-edit",attrs:{disabled:t.redelParamBtn,id:"b3t3n3",size:"mini",type:"primary"},nativeOn:{click:function(e){return t.handleEditGroup.apply(null,arguments)}}},[t._v("修改")]),t._v(" "),i("el-button",{staticClass:"remove el-icon-delete",attrs:{disabled:t.redelParamBtn,id:"b4t4n4",size:"mini",type:"primary"},nativeOn:{click:function(e){return t.delGroup.apply(null,arguments)}}},[t._v("删除")]),t._v(" "),i("el-button",{staticClass:"remove el-icon-plus",attrs:{disabled:t.redelBtn,id:"b2t2n2",type:"primary",size:"mini"},on:{click:t.addGroup}},[t._v("添加")])],1)],1)])],1),t._v(" "),i("el-table",{attrs:{id:"kr-table2",stripe:"",center:"",border:"",data:t.groupData,"row-class-name":t.GroupClassNameCh,"row-style":t.GroupHighlightCh},on:{"row-click":t.demandgroup}},[i("el-table-column",{attrs:{type:"index",label:"NO.",width:"80",index:t.indexMethod}}),t._v(" "),i("el-table-column",{attrs:{prop:"name",label:"参数名称"}}),t._v(" "),i("el-table-column",{attrs:{prop:"value",label:"参数值"}})],1)],1)],1)],1)],1)])},staticRenderFns:[]};var o=function(t){i("Yybr")},l=i("VU/8")(r.a,a,!1,o,null,null);e.default=l.exports}});