package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.koron.zys.baseConfig.bean.MatrBean;
import com.koron.zys.baseConfig.queryBean.MatrQueryBean;
import com.koron.zys.baseConfig.vo.MatrVO;

public interface MatrMapper {

    /**
     * 查询列表
     *
     * @return
     */
    List<MatrVO> selectMatrList(MatrQueryBean matrQueryBean);

    /**
     * 根据材料编码查询
     *
     * @param matrNo
     * @return
     */
    @Select("select * from BASE_MATR where matr_no = #{matrNo}")
    MatrBean selectMatrByMatrNo(@Param("matrNo") String matrNo);

    /**
     * 添加
     *
     * @param matrBean
     * @return
     */
    void insertMatr(MatrBean matrBean);

    /**
     * 校验字段内容重复
     */
    @Select("select count(1) from BASE_MATR where ${key} = #{val}")
    Integer check(@Param("key") String key, @Param("val") String val);

    /**
     * 校验字段内容重复-排除当前记录
     */
    @Select("select count(*) from BASE_MATR where ${key} = #{val} and id <> #{id}")
    Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);

    /**
     * 修改
     *
     * @param matrBean
     * @return
     */
    Integer updateMatr(MatrBean matrBean);

    @Select("SELECT id ,matr_no, MATR_PRICE FROM BASE_MATR")
    List<MatrBean> selectMatrPrice();
    @Delete("delete from BASE_MATR where id = #{id}")
    Integer delete(@Param("id") String id);
}
