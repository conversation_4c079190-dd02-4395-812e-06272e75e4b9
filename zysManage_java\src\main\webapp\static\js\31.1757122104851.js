webpackJsonp([31],{"cHb/":function(e,a){},"pX+p":function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var r={name:"dataBaseManAdd",data:function(){return{databaseData:[],ruleForm:{serverNo:"",serverName:"",dbType:1,dbIp:"",dbPort:"",dbSchema:"",managerAccount:"",managerPassword:"",comments:"",status:1,slaveDbIp:"",slaveDbPort:"",slaveManagerAccount:"",slaveManagerPassword:""},dbTypeOptions:[],rules:{serverNo:[{required:!0,message:"请输入服务器编号",trigger:"blur"}],serverName:[{required:!0,message:"请输入服务器名称",trigger:"blur"}],dbType:[{required:!0,message:"请选择数据库类型",trigger:"blur"}],dbIp:[{required:!0,message:"请输入数据库IP",trigger:"blur"}],dbPort:[{message:"请输入数据库端口",trigger:"blur",required:!0}],managerAccount:[{required:!0,message:"请输入管理员账号",trigger:"blur"}],managerPassword:[{required:!0,message:"请输入管理员密码",trigger:"blur"}],status:[{message:"请输入状态",trigger:"blur",required:!0}]}}},mounted:function(){this.getData()},methods:{getData:function(){var e=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"PDB"}}).then(function(a){e.dbTypeOptions=a.PDB})},submitForm:function(e,a){var t=this,r=this,s={};this.$refs[e].validate(function(e){if(!e)return!1;s="添加"===a?{busicode:"DbServerAdd",data:t.ruleForm}:{busicode:"DbServerUpdate",data:t.ruleForm},console.log("params",t.ruleForm),t.$api.fetch({params:s}).then(function(e){r.$message({showClose:!0,message:"保存成功",type:"success"}),r.$parent.selectTSubSystem(),r.$parent.closeDialog()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","dataBaseAdd",this.$parent.closeDialog)},editData:function(e){this.ruleForm=e}}},s={render:function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"dataBaseManAdd"},[t("el-form",{ref:"ruleFormDataBaseManAdd",staticClass:"formBill",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px",inline:!0}},[t("el-form-item",{attrs:{label:"服务器编号：",prop:"serverNo"}},[t("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.serverNo,callback:function(a){e.$set(e.ruleForm,"serverNo",a)},expression:"ruleForm.serverNo"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"服务器名称：",prop:"serverName"}},[t("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.serverName,callback:function(a){e.$set(e.ruleForm,"serverName",a)},expression:"ruleForm.serverName"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"数据库类型：",prop:"dbType"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.dbType,callback:function(a){e.$set(e.ruleForm,"dbType",a)},expression:"ruleForm.dbType"}},e._l(e.dbTypeOptions,function(e,a){return t("el-option",{key:a,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),t("el-form-item",{attrs:{label:"主库IP：",prop:"dbIp"}},[t("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.dbIp,callback:function(a){e.$set(e.ruleForm,"dbIp",a)},expression:"ruleForm.dbIp"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"主库端口：",prop:"dbPort"}},[t("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.dbPort,callback:function(a){e.$set(e.ruleForm,"dbPort",a)},expression:"ruleForm.dbPort"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"主库账号：",prop:"managerAccount"}},[t("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.managerAccount,callback:function(a){e.$set(e.ruleForm,"managerAccount",a)},expression:"ruleForm.managerAccount"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"主库密码：",prop:"managerPassword"}},[t("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.managerPassword,callback:function(a){e.$set(e.ruleForm,"managerPassword",a)},expression:"ruleForm.managerPassword"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"状态：",prop:"status"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.status,callback:function(a){e.$set(e.ruleForm,"status",a)},expression:"ruleForm.status"}},[t("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),t("el-option",{attrs:{label:"禁用",value:0}})],1)],1),e._v(" "),t("el-form-item",{attrs:{label:"备库IP：",prop:"slaveDbIp"}},[t("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.slaveDbIp,callback:function(a){e.$set(e.ruleForm,"slaveDbIp",a)},expression:"ruleForm.slaveDbIp"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"备库端口：",prop:"slaveDbPort"}},[t("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.slaveDbPort,callback:function(a){e.$set(e.ruleForm,"slaveDbPort",a)},expression:"ruleForm.slaveDbPort"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"备库账号：",prop:"slaveManagerAccount"}},[t("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.slaveManagerAccount,callback:function(a){e.$set(e.ruleForm,"slaveManagerAccount",a)},expression:"ruleForm.slaveManagerAccount"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"备库密码：",prop:"slaveManagerPassword"}},[t("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.slaveManagerPassword,callback:function(a){e.$set(e.ruleForm,"slaveManagerPassword",a)},expression:"ruleForm.slaveManagerPassword"}})],1),e._v(" "),t("el-form-item",{staticClass:"f2",attrs:{label:"描述:",clearable:""}},[t("el-input",{attrs:{type:"textarea",maxlength:"500","show-word-limit":""},model:{value:e.ruleForm.comments,callback:function(a){e.$set(e.ruleForm,"comments",a)},expression:"ruleForm.comments"}})],1)],1)],1)},staticRenderFns:[]};var l={name:"dataBaseMan",components:{dataBaseManAdd:t("VU/8")(r,s,!1,function(e){t("uizS")},null,null).exports},data:function(){return{tableShow:!0,tableQuery:{page:1,pageCount:50},maxHeight:0,appServerData:[],formData:{serverNo:"",serverName:"",dbType:1,dbIp:"",dbPort:"",dbSchema:"",managerAccount:"",managerPassword:"",comments:"",status:1},crumbsData:{titleList:[{title:"基础配置",path:"/ChangeTables"},{title:"数据库服务器",method:function(){window.histroy.back()}}]},dataBaseManShow:!0,dataBaseManAddVisible:!1}},mounted:function(){this.selectTSubSystem()},methods:{appAdd:function(e){var a=this;if("add"===e)this.$set(this.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),this.$refs.dataBaseManAdd.editData(this.formData),this.common.chargeObjectEqual(this,this.formData,"set","dataBaseAdd");else{this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var t={busicode:"DbServerList",data:{dbId:e.row.dbId}};this.$api.fetch({params:t}).then(function(e){a.$refs.dataBaseManAdd.editData(e[0]),a.common.chargeObjectEqual(a,e[0],"set","dataBaseAdd")})}this.dataBaseManShow=!1,this.dataBaseManAddVisible=!0},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(e){this.tableQuery.page=e,this.selectTSubSystem()},selectTSubSystem:function(){var e=this;this.$api.fetch({params:{busicode:"DbServerList",data:{}}}).then(function(a){e.appServerData=a,e.common.changeTable(e,".dataBaseMan .kl-table",[])})},closeDialog:function(){this.dataBaseManShow=!0,this.dataBaseManAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.dataBaseManAdd.handleClose()},submit:function(e){var a=this.crumbsData.titleList[2].title;this.$refs.dataBaseManAdd.submitForm(e,a)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},o={render:function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"dataBaseMan"},[t("div",{staticClass:"main-content"},[t("div",{staticClass:"bread-contain"},[t("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:e.dataBaseManShow,expression:"dataBaseManShow"}],staticClass:"bread-contain-right"},[t("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(a){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:e.dataBaseManAddVisible,expression:"dataBaseManAddVisible"}],staticClass:"bread-contain-right"},[t("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return e.submit("ruleFormDataBaseManAdd")}}},[e._v("保存")]),e._v(" "),t("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:e.dataBaseManShow,expression:"dataBaseManShow"}],staticClass:"kl-table"},[e.tableShow?t("el-table",{attrs:{stripe:"",border:"",data:e.appServerData,"max-height":e.maxHeight}},[t("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),t("el-table-column",{attrs:{prop:"serverNo",label:"服务器编号","min-width":"100"}}),e._v(" "),t("el-table-column",{attrs:{prop:"serverName",label:"服务器名称","min-width":"100"}}),e._v(" "),t("el-table-column",{attrs:{prop:"dbTypeName","min-width":"100",label:"数据库类型"}}),e._v(" "),t("el-table-column",{attrs:{prop:"dbIp","min-width":"100",label:"数据库IP","show-overflow-tooltip":""}}),e._v(" "),t("el-table-column",{attrs:{prop:"dbPort","min-width":"80",label:"数据库端口"}}),e._v(" "),t("el-table-column",{attrs:{prop:"managerAccount",label:"管理员账号","min-width":"120"}}),e._v(" "),t("el-table-column",{attrs:{prop:"managerPassword","min-width":"120",label:"管理员密码"}}),e._v(" "),t("el-table-column",{attrs:{prop:"statusName","min-width":"60",label:"状态"}}),e._v(" "),t("el-table-column",{attrs:{prop:"comments",label:"描述","min-width":"150"}}),e._v(" "),t("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.appAdd(a)}}},[e._v("编辑")])]}}],null,!1,**********)})],1):e._e()],1),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:e.dataBaseManAddVisible,expression:"dataBaseManAddVisible"}]},[t("dataBaseManAdd",{ref:"dataBaseManAdd"})],1)])])},staticRenderFns:[]};var n=t("VU/8")(l,o,!1,function(e){t("cHb/")},null,null);a.default=n.exports},uizS:function(e,a){}});