<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.WaterTypeMapper">
    <insert id="saveWaterType"
            parameterType="com.koron.zys.baseConfig.bean.WaterTypeBean">
		insert into
		BASE_WATER_TYPE
		(id,water_type_no,water_type_name,parent_Id,is_Leaf,
		group_code,status,sort_no,create_name,create_account,create_time)
		values
		(#{id},#{waterTypeNo},#{waterTypeName},#{parentId},#{isLeaf},
		#{groupCode},#{status},#{sortNo},#{createName},#{createAccount},now())
	</insert>
    <update id="updateWaterType"
            parameterType="com.koron.zys.baseConfig.bean.WaterTypeBean">
        update BASE_WATER_TYPE
        <trim prefix="SET" suffixOverrides=",">
            <if test="waterTypeName != null and waterTypeName != ''">
                water_type_name=#{waterTypeName},
            </if>
            <if test="parentId != null and parentId != ''">
                parent_Id=#{parentId},
            </if>
            <if test="isLeaf != null">
                is_Leaf=#{isLeaf},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
            <if test="sortNo != null">
                sort_no=#{sortNo},
            </if>
            <if test="groupCode != null and groupCode != ''">
                group_code=#{groupCode},
            </if>
            <if test="updateName != null and updateName != ''">
                update_name=#{updateName},
            </if>
            <if test="updateAccount != null and updateAccount != ''">
                update_account=#{updateAccount},
            </if>
            <if test="updateTime != null and updateTime != ''">
                update_time=#{updateTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <select id="findWaterType" parameterType="com.koron.zys.baseConfig.queryBean.WaterTypeQueryBean" resultType="com.koron.zys.baseConfig.vo.WaterTypeVO">
        select a.water_type_no,a.water_type_name,a.id,case when a.status = 1 then '启用' else '停用' end
        status,a.sort_no,a.parent_id
        from BASE_WATER_TYPE a left join BASE_WATER_TYPE b
        on a.parent_Id=b.id
        <where>
            <if test="waterTypeNo != null and waterTypeNo!='all'">
                and a.water_type_no LIKE CONCAT(#{waterTypeNo},'__')
            </if>
            <if test="waterTypeName != null and waterTypeName !=''">
                and a.water_type_name LIKE CONCAT('%',#{waterTypeName},'%')
            </if>
            <if test="status ==1">
                and a.status = 1
            </if>
            <if test="isLeaf ==0">
                and a.is_Leaf = 0
            </if>
        </where>
        order by a.sort_no asc
    </select>
    <select id="findWaterTypeid" parameterType="com.koron.zys.baseConfig.queryBean.WaterTypeQueryBean" resultType="com.koron.zys.baseConfig.vo.WaterTypeVO">
        select a.id as waterTypeId,a.water_type_no,a.water_type_name,a.id,case when a.status = 1 then '启用' else '停用' end
        status,a.sort_no
        from BASE_WATER_TYPE a left join BASE_WATER_TYPE b
        on a.parent_Id=b.id
        <where>
            <if test="waterTypeNo != null and waterTypeNo!='all'">
                and a.water_type_no LIKE CONCAT(#{waterTypeNo},'__')
            </if>
            <if test="waterTypeName != null and waterTypeName !=''">
                and a.water_type_name LIKE CONCAT('%',#{waterTypeName},'%')
            </if>
            <if test="status ==1">
                and a.status = 1
            </if>
            <if test="isLeaf ==0">
                and a.is_Leaf = 0
            </if>
        </where>
        order by a.sort_no asc
    </select>

    <select id="findAllByParentId" resultType="String">
        SELECT ID
        FROM BASE_WATER_TYPE
        where 1=1
        <if test="id != null and id != ''">
            and water_type_no like CONCAT((SELECT water_type_no
            FROM BASE_WATER_TYPE
            where
             id=#{id}),'%')
        </if>
    </select>
	<select id="findParentWaterType" resultType="com.koron.zys.baseConfig.vo.WaterTypeVO">
        select a.id,a.water_type_no,a.water_type_name,a.id,a.sort_no
        from BASE_WATER_TYPE a
        where a.parent_id is null OR a.parent_id = ''
        and a.status=1
        order by a.sort_no asc
    </select>
    <!-- 通过Code查询记录 -->
    <select id="findWaterTypeByCode"
            resultType="com.koron.zys.serviceManage.bean.SelectBean">
		SELECT
		water_type_no code, water_type_name name, parent_Id,id
		FROM
		BASE_WATER_TYPE
		WHERE
		water_type_no = #{waterTypeNo}
	</select>

    <!-- 查询最大的子级code编号 -->
    <select id="findMaxChild" resultType="java.lang.String">
		SELECT
		max(water_type_no) maxCode 
		FROM
		BASE_WATER_TYPE
		WHERE
		water_type_no like CONCAT(#{waterTypeNo},'__')
	</select>

    <!-- 查询最下级的code列表 -->
    <select id="findChildById" resultType="java.lang.String">
       select distinct a.id from base_water_type a
       join (select water_type_no from base_water_type b where b.id=#{waterTypeId}) b on a.water_type_no like concat(b.water_type_no,'%')
    </select>

    <!-- 查询下级的编号列表 -->
    <select id="findNextIdByParentId" resultType="com.koron.zys.serviceManage.bean.SelectBean">
        select water_type_no code, water_type_name name, parent_Id,id from base_water_type a where 1=1
        <choose>
            <when test="parentId == null or parentId == '' or parentId == '0'.toString()">
               and (a.parent_id is null or a.parent_id = '0' or a.parent_id = '' )
            </when>
            <otherwise>
                and a.parent_id=#{parentId}
            </otherwise>
        </choose>
    </select>
	
	<!-- 查询一级用水类型 -->
	<select id="findTopWaterType" resultType="com.koron.zys.serviceManage.bean.SelectBean">
		SELECT
		water_type_no code, water_type_name name, parent_Id,id
		FROM
		BASE_WATER_TYPE
		WHERE
		water_type_no like '__'
	</select>

    <select id="findChildByParentId" resultType="com.koron.zys.serviceManage.bean.SelectBean">
        SELECT *
        FROM BASE_WATER_TYPE
        where 1=1
        <if test="id != null and id != ''">
            and water_type_no like CONCAT((SELECT water_type_no
            FROM BASE_WATER_TYPE
            where
            id=#{id}),'%')
        </if>
    </select>
</mapper>