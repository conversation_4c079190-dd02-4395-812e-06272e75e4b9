package com.koron.common.bean;

import java.util.List;

import edu.emory.mathcs.backport.java.util.Arrays;

/**
 * 各表不能为空的字段列表
 * 
 * <AUTHOR>
 *
 */
public class CheckNullListBean {

	@SuppressWarnings("unchecked")
	public static final List<Integer> wmeterList = Arrays
			.asList(new Integer[] { 3, 4, 5, 6, 7, 8, 9, 12, 14, 15, 16, 17, 38 });

	@SuppressWarnings("unchecked")
	public static final List<Integer> concentratorList = Arrays
			.asList(new Integer[] { 0, 1, 2, 3, 4, 5, 6, 7, 8, 14, 15});

	@SuppressWarnings("unchecked")
	public static final List<Integer> collectorList = Arrays.asList(new Integer[] { 0, 1, 2, 3, 10 });

	@SuppressWarnings("unchecked")
	public static final List<Integer> rtuList = Arrays.asList(new Integer[] { 0, 1, 4, 5, 6, 16 });

	@SuppressWarnings("unchecked")
	public static final List<Integer> simList = Arrays.asList(new Integer[] { 0, 1, 2, 3 });

	public static final List<Integer> getList(int importType) {
		switch (importType) {
		case 1:
			return wmeterList;
		case 2:
			return concentratorList;
		case 3:
			return collectorList;
		case 4:
			return rtuList;
		case 5:
			return simList;
		default:
			return null;
		}
	}
}
