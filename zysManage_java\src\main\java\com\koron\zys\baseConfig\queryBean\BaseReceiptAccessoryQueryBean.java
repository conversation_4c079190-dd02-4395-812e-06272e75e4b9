package com.koron.zys.baseConfig.queryBean;

public class BaseReceiptAccessoryQueryBean {
	
	/**
	 * 主键
	 */
	private String id;
	
	/**
	 * 元数据外键ID
	 */
	private String metadataId;
	
	/**
	 * 附件名称
	 */
	private String accessoryName;
	
	/**
	 * 单据主键ID
	 */
	private String receiptId;
	
	/**
	 * 单据类型
	 */
	private String receiptType;
	
	/**
	 * 节点名称
	 */
	private String processNode;
	
	/**
	 * 客户编号
	 */
	private String ctmNo;
	/**
	 * 用户编号
	 */
	private String userNo;

	/**
	 *账户编号
	 */
	private String accountNo;
	
	/**
	 * 附件类型
	 */
	private String accessoryNo;
	
	/**
	 * 是否用户查询界面
	 */
	private int isUserQuery;
	/**
	 * 水表编号
	 */
	private String meterNo;
	public String getMeterNo() {
		return meterNo;
	}

	public void setMeterNo(String meterNo) {
		this.meterNo = meterNo;
	}

	public String getAccessoryNo() {
		return accessoryNo;
	}

	public void setAccessoryNo(String accessoryNo) {
		this.accessoryNo = accessoryNo;
	}

	public String getId() {
		return id;
	}

	public String getMetadataId() {
		return metadataId;
	}

	public String getAccessoryName() {
		return accessoryName;
	}

	public String getReceiptId() {
		return receiptId;
	}

	public String getReceiptType() {
		return receiptType;
	}

	public String getProcessNode() {
		return processNode;
	}

	public String getCtmNo() {
		return ctmNo;
	}

	public String getUserNo() {
		return userNo;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setMetadataId(String metadataId) {
		this.metadataId = metadataId;
	}

	public void setAccessoryName(String accessoryName) {
		this.accessoryName = accessoryName;
	}

	public void setReceiptId(String receiptId) {
		this.receiptId = receiptId;
	}

	public void setReceiptType(String receiptType) {
		this.receiptType = receiptType;
	}

	public void setProcessNode(String processNode) {
		this.processNode = processNode;
	}

	public void setCtmNo(String ctmNo) {
		this.ctmNo = ctmNo;
	}

	public void setUserNo(String userNo) {
		this.userNo = userNo;
	}

	public int getIsUserQuery() {
		return isUserQuery;
	}

	public void setIsUserQuery(int isUserQuery) {
		this.isUserQuery = isUserQuery;
	}

	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}
}
