<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BaseCodeRuleMapper">
	
 	<select id="selectCodeRule" resultType="com.koron.zys.baseConfig.bean.BaseCodeRuleBean" >
		SELECT
			t.id,
			t.rule_code,
			t.update_time,
			t.last_serial
		FROM
			base_code_rule t 
		WHERE
			t.rule_code = #{ruleCode, jdbcType=VARCHAR}
	</select>
	
	<update id="updateLastSerial">
		update base_code_rule set last_serial = #{lastSerial, jdbcType=INTEGER}, update_time = now() where rule_code = #{ruleCode, jdbcType=VARCHAR}
	</update>
	
	<insert id="addCodeRule">
		insert into base_code_rule (id, last_serial, rule_code)
		values (#{id, jdbcType=VARCHAR}, #{lastSerial, jdbcType=INTEGER}, #{ruleCode, jdbcType=VARCHAR})
	</insert>
	
</mapper>