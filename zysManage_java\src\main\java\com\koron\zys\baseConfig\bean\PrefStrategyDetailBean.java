package com.koron.zys.baseConfig.bean;

/**
 * 优惠策略明细实体类
 *
 * <AUTHOR>
 */
public class PrefStrategyDetailBean extends BaseBean {
    /**
     * 策略ID
     */
    private String prefStrategyId;
    /**
     * 费用类型ID
     */
    private String costId;
    /**
     * 最小生效范围
     */
    private Integer minRange;

    /**
     * 最大生效范围
     */
    private Integer maxRange;
    /**
     * 优惠类型
     */
    private String prefWay;
    /**
     * 优惠值
     */
    private Double prefValue;

    public String getPrefStrategyId() {
        return prefStrategyId;
    }

    public void setPrefStrategyId(String prefStrategyId) {
        this.prefStrategyId = prefStrategyId;
    }

    public String getCostId() {
        return costId;
    }

    public void setCostId(String costId) {
        this.costId = costId;
    }

    public Integer getMinRange() {
        return minRange;
    }

    public void setMinRange(Integer minRange) {
        this.minRange = minRange;
    }

    public Integer getMaxRange() {
        return maxRange;
    }

    public void setMaxRange(Integer maxRange) {
        this.maxRange = maxRange;
    }

    public String getPrefWay() {
        return prefWay;
    }

    public void setPrefWay(String prefWay) {
        this.prefWay = prefWay;
    }

    public Double getPrefValue() {
        return prefValue;
    }

    public void setPrefValue(Double prefValue) {
        this.prefValue = prefValue;
    }
}
