package com.koron.zys.baseConfig.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.PenaltyStrategyMapper;
import com.koron.zys.baseConfig.queryBean.PenaltyQueryBean;
import com.koron.zys.baseConfig.vo.PenaltyVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import java.util.List;

/**
 * 违约金策略-列表初始化
 * <AUTHOR>
 *
 */
public class PenaltyStrategyList implements ServerInterface  {
	
	private static Logger logger = LoggerFactory.getLogger(PenaltyStrategyList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
		try {
			PenaltyQueryBean bean = JsonUtils.objectToPojoIgnoreNone(req.getData(), PenaltyQueryBean.class);
			PenaltyStrategyMapper mapper = factory.getMapper(PenaltyStrategyMapper.class);	
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			List<PenaltyVO> list = mapper.selectPenaltyList(bean);
			info.setData(new PageInfo<>(list));
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}