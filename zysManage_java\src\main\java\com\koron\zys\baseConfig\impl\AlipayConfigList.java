package com.koron.zys.baseConfig.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.AlipayConfigBean;
import com.koron.zys.baseConfig.mapper.AlipayConfigMapper;
import com.koron.zys.baseConfig.queryBean.AlipayConfigQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import java.util.List;

/**
 * 支付宝编辑-列表初始化
 *
 * <AUTHOR>
 */
public class AlipayConfigList implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(AlipayConfigList.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        @SuppressWarnings("rawtypes")
        MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
        try {
            AlipayConfigQueryBean bean = JsonUtils.objectToPojo(req.getData(), AlipayConfigQueryBean.class);
            AlipayConfigMapper mapper = factory.getMapper(AlipayConfigMapper.class);
            PageHelper.startPage(bean.getPage(), bean.getPageCount());
            List<AlipayConfigBean> list = mapper.selectAlipayConfigList(bean);
            info.setData(new PageInfo<>(list));
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }

}