package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.koron.zys.baseConfig.bean.GarbageTypeBean;
import com.koron.zys.baseConfig.bean.GarbageTypeQueryBean;

public interface GarbageTypeMapper {
	List<GarbageTypeBean> list(GarbageTypeQueryBean bean);
	int add(GarbageTypeBean bean);
	int update(GarbageTypeBean bean);
	int delete(@Param("id") String id);
}
