package com.koron.util;

import java.util.List;

import com.koron.zys.serviceManage.utils.RedisUtils;
import org.apache.log4j.Logger;
import org.koron.ebs.mybatis.SessionFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import com.koron.zys.serviceManage.bean.LoginBean;
import com.koron.zys.serviceManage.mapper.LoginMapper;
import com.mysql.cj.util.StringUtils;

//import redis.clients.jedis.Jedis;
//import redis.clients.jedis.JedisPool;

/**
 * 定时任务/消息
 *
 * <AUTHOR>
 * @version 创建时间：2018年9月25日 上午9:39:24
 */
//@Component
public class TimeTask {
    //	@Autowired
//	private JedisPool JedisPool;
    private static final Logger log = Logger.getLogger(TimeTask.class);

    @Bean
    public TaskScheduler scheduledExecutorService() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(8);
        scheduler.setThreadNamePrefix("scheduled-thread-");
        return scheduler;
    }

    /**
     * 每15分钟,查30分钟之前的登录信息，token失效处理（清理redis数据和数据库数据）
     */
    @Scheduled(cron = "0 0/1 7-23 * * ?")
    public void loginInfoUpdate() {
        log.info("start：");
        SessionFactory sf = new SessionFactory();
        try {
            LoginMapper mapper = sf.getMapper(LoginMapper.class);
            List<LoginBean> loingInfo = mapper.getLoingInfo();
            for (LoginBean loginBean : loingInfo) {
                if (!StringUtils.isNullOrEmpty(loginBean.getKey())) {
                    mapper.delLoginInof(loginBean.getKey());
                    RedisUtils.del(loginBean.getKey());
                }
            }
//        } catch (Exception e) {
//            e.printStackTrace();
        } finally {
            if (sf != null) {
                sf.close();
            }
        }
        log.info("end：");
    }
}