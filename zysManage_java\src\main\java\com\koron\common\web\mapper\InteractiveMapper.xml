<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.common.web.mapper.InteractiveMapper">
	<insert id="satisfiedAdd" parameterType="com.koron.common.web.bean.UserSatisfiedBean" >
		insert into user_satisfaction(id,bill_no,receipt_type,satisfied,create_account,create_name,create_time) values (#{id},#{billNo},#{receiptType},#{satisfied},#{createAccount},#{createName},now())
	</insert>

	<select id="satisfiedList" parameterType="com.koron.common.web.bean.UserSatisfiedQueryBean"
			resultType="com.koron.common.web.bean.UserSatisfiedBean">
		select
			u.id,
			u.bill_no,
			u.receipt_type,
			u.satisfied,
			u.create_account,
			u.create_name,
			u.create_time
			from user_satisfaction u

	</select>

</mapper>