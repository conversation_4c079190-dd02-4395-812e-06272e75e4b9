package com.koron.zys.baseConfig.bean;

import java.util.List;

/**
 * 违约金策略表
 * <AUTHOR>
 *
 */
public class PenaltyBean extends BaseBean{

	private String waterTypeId;

	private String effectiveDate;

	private String remark;

	private int status;

	private List<PenaltyStrategyBean> list;

	public String getWaterTypeId() {
		return waterTypeId;
	}

	public void setWaterTypeId(String waterTypeId) {
		this.waterTypeId = waterTypeId;
	}

	public String getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public List<PenaltyStrategyBean> getList() {
		return list;
	}

	public void setList(List<PenaltyStrategyBean> list) {
		this.list = list;
	}
}
