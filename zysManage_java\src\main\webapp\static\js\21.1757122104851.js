webpackJsonp([21],{"7xmB":function(e,t){},"Fs+c":function(e,t){},QfyB:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"funModuleAdd"},[a("el-form",{ref:"funModuleAddruleForm",staticClass:"demo-form",attrs:{model:e.funModuleAddruleForm,rules:e.rules,"label-width":"100px",inline:!0}},[a("el-form-item",{attrs:{label:"模块名称:",prop:"moduleName"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入"},model:{value:e.funModuleAddruleForm.moduleName,callback:function(t){e.$set(e.funModuleAddruleForm,"moduleName",t)},expression:"funModuleAddruleForm.moduleName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"页面路径:"}},[a("el-input",{attrs:{type:"text",maxlength:"100",clearable:"",placeholder:"请输入"},model:{value:e.funModuleAddruleForm.pageAddr,callback:function(t){e.$set(e.funModuleAddruleForm,"pageAddr",t)},expression:"funModuleAddruleForm.pageAddr"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"排序:"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入"},model:{value:e.funModuleAddruleForm.sort,callback:function(t){e.$set(e.funModuleAddruleForm,"sort",t)},expression:"funModuleAddruleForm.sort"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态:",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.funModuleAddruleForm.status,callback:function(t){e.$set(e.funModuleAddruleForm,"status",t)},expression:"funModuleAddruleForm.status"}},e._l(e.optionStatus,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{staticClass:"remark",attrs:{label:"备注:"}},[a("el-input",{attrs:{type:"textarea",maxlength:"500",clearable:"","show-word-limit":"",placeholder:"请输入"},model:{value:e.funModuleAddruleForm.comments,callback:function(t){e.$set(e.funModuleAddruleForm,"comments",t)},expression:"funModuleAddruleForm.comments"}})],1)],1)],1)},staticRenderFns:[]};var o={name:"funModule-index",components:{funModuleAdd:a("VU/8")({name:"funModuleAdd",data:function(){return{funModuleAddruleForm:{moduleName:"",pageAddr:"",sort:"",status:1,comments:""},optionStatus:[{value:0,label:"停用"},{value:1,label:"启用"}],rules:{moduleName:[{required:!0,message:"请输入模块名称",trigger:"blur"}],status:[{required:!0,message:"请输入状态",trigger:"blur"}]}}},mounted:function(){},methods:{submitForm:function(e,t,a){var l=this,o=this,r={};this.$refs[e].validate(function(e){if(!e)return!1;"添加"===t?(l.funModuleAddruleForm.parent=a,r={busicode:"SysModuleAdd",data:l.funModuleAddruleForm}):r={busicode:"SysModuleUpdate",data:l.funModuleAddruleForm},l.$api.fetch({params:r}).then(function(e){o.$message({showClose:!0,message:"保存成功",type:"success"}),o.$parent.getData(),o.$parent.callBack()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.funModuleAddruleForm,"get","funModuleForm",this.boforeClose)},boforeClose:function(){this.$parent.callBack()},editData:function(e){this.funModuleAddruleForm=e}}},l,!1,function(e){a("7xmB")},null,null).exports},data:function(){return{tableData:{},crumbsData:{titleList:[{title:"系统管理",path:"/baseInfo"},{title:"功能模块"},{title:"根目录"}]},treeDatas:{tree:[{foreignkey:"全部",id:"222",_child:[{foreignkey:"客服平台",id:"222",_child:[{foreignkey:"首页",id:"222"},{foreignkey:"抄表中心",id:"222"},{foreignkey:"工程中心",id:"222"},{foreignkey:"服务中心",id:"222"}]}]}],defaultProps:{label:"name",children:"children"},inputProp:{showSearch:!1,Inp_placeholder:"请输入节点"},sendTreeProp:["code","name","districtArr","children","id","group","isLeaf","isParent","parent","sonData"],rootName:"根目录",defaultOpen:{nodeKey:"id"}},maxHeight:0,tableShow:!1,enterMeterDataShow:!1,enterMeterDate:"",funModuleShow:!0,total:5,formData:{moduleName:"",pageAddr:"",sort:"",status:1,comments:""},tableQuery:{pageCount:50,page:1},treeParantId:""}},mounted:function(){this.getData()},methods:{getData:function(){var e=this,t={busicode:"SysModuleList",data:this.tableQuery};this.$api.fetch({params:t}).then(function(t){e.tableData=t,e.common.changeTable(e,".funModule .kl-table",[".funModule .block"])}),this.getTreeDatas()},getTreeDatas:function(){var e=this;this.$api.fetch({params:{busicode:"SysModuleTree",data:{}}}).then(function(t){e.treeDatas.tree=t})},formatStatus:function(e){return 1===e.status?"启用":"停用"},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.getData()},handleCurrentChange:function(e){this.tableQuery.page=e,this.getData()},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},add:function(e){var t=this;if("add"===e)this.$set(this.crumbsData.titleList,"3",{title:"添加",method:function(){window.histroy.back()}}),this.$refs.funModuleAdds.editData({moduleName:"",pageAddr:"",sort:"",status:1,comments:""}),this.common.chargeObjectEqual(this,this.formData,"set","funModuleForm");else{this.$set(this.crumbsData.titleList,"3",{title:"编辑",method:function(){window.histroy.back()}});var a={busicode:"SysModuleList",data:{moduleId:e.row.moduleId}};this.$api.fetch({params:a}).then(function(e){t.$refs.funModuleAdds.editData(e.list[0]),t.common.chargeObjectEqual(t,e.list[0],"set","funModuleForm")})}this.funModuleShow=!1,this.enterMeterDataShow=!0},callBack:function(){this.crumbsData.titleList.pop(),this.enterMeterDataShow=!1,this.funModuleShow=!0},close:function(){this.$refs.funModuleAdds.handleClose()},backTreeData:function(e){var t=this;this.treeParantId=e.id,this.$set(this.crumbsData.titleList,"2",{title:e.name,method:function(){window.histroy.back()}});var a={busicode:"SysModuleList",data:{moduleCode:e.code}};this.$api.fetch({params:a}).then(function(e){t.tableData=e,t.common.changeTable(t,".funModule .kl-table",[".funModule .block"])})},submitForm:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.funModuleAdds.submitForm(e,t,this.treeParantId)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},r={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"funModule"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.enterMeterDataShow,expression:"enterMeterDataShow"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("funModuleAddruleForm")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:function(t){return e.close()}}},[e._v("返回")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.funModuleShow,expression:"funModuleShow"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:function(t){return e.add("add")}}},[e._v("添加")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.funModuleShow,expression:"funModuleShow"}],staticClass:"funModule-right-content"},[a("div",{staticClass:"kr-left"},[a("company-tree",{attrs:{treeData:e.treeDatas},on:{sendTreeData:e.backTreeData}})],1),e._v(" "),a("div",{staticClass:"kr-right kl-table"},[e.tableShow?a("el-table",{staticClass:"wuserInfo-table",attrs:{"max-height":e.maxHeight,stripe:"",border:"",data:e.tableData.list}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{"show-overflow-tooltip":"",prop:"moduleName","min-width":"80",label:"模块名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"moduleLevel","min-width":"120",label:"模块级别"}}),e._v(" "),a("el-table-column",{attrs:{prop:"pageAddr","min-width":"100",label:"页面路径"}}),e._v(" "),a("el-table-column",{attrs:{prop:"status","min-width":"100",label:"状态",formatter:e.formatStatus}}),e._v(" "),a("el-table-column",{attrs:{prop:"comments","min-width":"120",label:"备注"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},nativeOn:{click:function(a){return e.add(t)}}},[e._v("编辑")])]}}],null,!1,307527826)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)]),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.enterMeterDataShow,expression:"enterMeterDataShow"}],staticClass:"funModule-right-content1"},[a("funModuleAdd",{ref:"funModuleAdds"})],1)])},staticRenderFns:[]};var n=a("VU/8")(o,r,!1,function(e){a("Fs+c")},null,null);t.default=n.exports}});