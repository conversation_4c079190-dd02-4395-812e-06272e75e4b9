package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.vo.QuickConfigVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserFieldBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.dto.UserFieldConfigSelectBean;
import com.koron.zys.serviceManage.mapper.UserFieldConfigMapper;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import java.util.ArrayList;
import java.util.List;

public class UserChangeQuickConfigUserData implements ServerInterface {
    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
        try {
            UserFieldBean bean = JsonUtils.objectToPojo(req.getData(), UserFieldBean.class);
            bean.setGroupCode(userInfo.getCurWaterCode());
            //获取mapper
            UserFieldConfigMapper mapper = factory.getMapper(UserFieldConfigMapper.class);
            //先根据页面获取运维平台分组
            List<String> groups=mapper.userFieldGroupSelect(bean);
            List<UserFieldConfigSelectBean> list=new ArrayList<UserFieldConfigSelectBean>();

            //运维平台中取出的字段与快速变更配置中一样的，才展示
            for(String group:groups) {
                bean.setGroup(group);
                //根据分组信息获取各个组的字段
                List<UserFieldBean> fields=mapper.userFieldConfigSelect(bean);
                List<QuickConfigVO> configVOS = new ArrayList<>();
                fields.forEach(userFieldBean -> {
                    QuickConfigVO configVO = new QuickConfigVO();
                    configVO.setName(userFieldBean.getFieldName());
                    configVO.setValue(userFieldBean.getFieldCode());
                    configVOS.add(configVO);
                });
                UserFieldConfigSelectBean config=new UserFieldConfigSelectBean();
                config.setGroup(group);
                config.setConfigVOS(configVOS);
                list.add(config);
            }
            info.setData(list);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }
}
