package com.koron.zys.baseConfig.impl;


import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.WaterPriceBean;
import com.koron.zys.baseConfig.bean.WaterPriceDetailBean;
import com.koron.zys.baseConfig.bean.WaterPriceLadderBean;
import com.koron.zys.baseConfig.mapper.WaterPriceMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;
import com.mysql.cj.util.StringUtils;
/**
 * 用水价格-编辑
 * <AUTHOR>
 *
 */
public class WaterPriceUpdate implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(WaterPriceUpdate.class);

	@Override
	@ValidationKey(clazz = WaterPriceBean.class,method = "insert")
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			WaterPriceMapper mapper = factory.getMapper(WaterPriceMapper.class);
			WaterPriceBean bean = JsonUtils.objectToPojo(req.getData(), WaterPriceBean.class);
			if (StringUtils.isNullOrEmpty(bean.getId())) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
			}
			bean.setUpdateInfo(userInfo);
			
			MessageBean<?> retMsg = WaterPriceAdd.check(userInfo, bean);
			if(retMsg.getCode() != Constant.MESSAGE_INT_SUCCESS) {
				return retMsg;
			}

			mapper.deleteWaterPriceLadderByPriceId(bean.getId());
			mapper.deleteWaterPriceDetailByPriceId(bean.getId());
			mapper.updateWaterPrice(bean);
			for (WaterPriceDetailBean detailBean : bean.getDetails()) {
				if (org.apache.commons.lang3.StringUtils.isNotBlank(detailBean.getPenaltyStrategyId()) || detailBean.getFixedMoney() > 0 || detailBean.getFixedPrice() > 0 || detailBean.getLadders().size() > 0) {
					mapper.insertWaterPriceDetail(detailBean);
					for(int i =0;i<detailBean.getLadders().size();i++) {
						WaterPriceLadderBean ladderBean = detailBean.getLadders().get(i);
						mapper.insertWaterPriceLadder(ladderBean);
					}
				}
			}
			MessageBean<WaterPriceBean> ret = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", WaterPriceBean.class);
			ret.setData(bean);
			return ret;
		} catch (Exception e) {
			factory.close(false);
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
	}
}