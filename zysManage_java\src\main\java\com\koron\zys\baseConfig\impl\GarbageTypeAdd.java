package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.GarbageTypeBean;
import com.koron.zys.baseConfig.mapper.GarbageTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

public class GarbageTypeAdd implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub
		try {
			GarbageTypeBean bean = JsonUtils.objectToPojo(req.getData(), GarbageTypeBean.class);
			GarbageTypeMapper mapper = factory.getMapper(GarbageTypeMapper.class);
			bean.setCreateInfo(userInfo);
			mapper.add(bean);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "操作成功", void.class);
	}

}
