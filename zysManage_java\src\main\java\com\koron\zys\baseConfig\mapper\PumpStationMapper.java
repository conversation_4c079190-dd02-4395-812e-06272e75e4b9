package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.koron.zys.baseConfig.bean.PumpStationBean;
import com.koron.zys.baseConfig.queryBean.PumpStationQueryBean;
import com.koron.zys.baseConfig.vo.PumpStationVO;

public interface PumpStationMapper {
	
	/**
	 * 查询列表
	 * 
	 * @return
	 */
	List<PumpStationVO> selectPumpStationList(PumpStationQueryBean pumpStationQueryBean);
	
	/**
	 * 根据id查询
	 * @param factoryId
	 * @return
	 */
	PumpStationBean selectPumpStationById(@Param("pumpStationId") String pumpStationId);

	/**
	 * 添加
	 * 
	 * @param PumpStationBean
	 * @return
	 */
	void insertPumpStation(PumpStationBean pumpStationBean);
	
	/**
	 * 校验字段内容重复
	 */
	@Select("select count(*) from BASE_PUMP_STATION where ${key} = #{val}")
	Integer check(@Param("key") String key, @Param("val") String val);
	
	/**
	 * 校验字段内容重复-排除当前记录
	 */
	@Select("select count(*) from BASE_PUMP_STATION where ${key} = #{val} and pump_station_id <> #{id}")
	Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);
	
	
	/**
	 * 修改
	 * 
	 * @param PumpStationBean
	 * @return
	 */
	Integer updatePumpStation(PumpStationBean pumpStationBean);

}
