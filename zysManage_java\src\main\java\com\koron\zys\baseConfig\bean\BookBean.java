package com.koron.zys.baseConfig.bean;

/**
 * 册本信息
 *
 * <AUTHOR> 2020年1月26日
 */
public class BookBean extends BaseBean {

    /*
     * 册本号
     */
    private String bookNo;
    /*
     * 册本名称
     */
    private String bookName;
    /*
     * 营业所
     */
    private String businessAbode;
    /*
     * 营业区域
     */
    private String businessArea;
    /*
     * 抄表周期
     */
    private String meterReadingCycle;
    /*
     * 抄表员账号
     */
    private String meterReadingStaffAccount;
    /*
     * 抄表员名称
     */
    private String meterReadingStaffName;
    /*
     * 抄表日
     */
    private Integer meterReadingDay;
    /*
     * 状态
     */
    private String status;
    /*
     * 备注
     */
    private String comments;
    /*
     * 上期/本期抄表日
     */
    private String lastPeriodDate;
    private String currendPeriodDate;
    
    
    public String getCurrendPeriodDate() {
		return currendPeriodDate;
	}

	public void setCurrendPeriodDate(String currendPeriodDate) {
		this.currendPeriodDate = currendPeriodDate;
	}

	public String getLastPeriodDate() {
		return lastPeriodDate;
	}

	public void setLastPeriodDate(String lastPeriodDate) {
		this.lastPeriodDate = lastPeriodDate;
	}

	public String getBookNo() {
        return bookNo;
    }

    public void setBookNo(String bookNo) {
        this.bookNo = bookNo;
    }

    public String getBookName() {
        return bookName;
    }

    public void setBookName(String bookName) {
        this.bookName = bookName;
    }

    public String getBusinessAbode() {
        return businessAbode;
    }

    public void setBusinessAbode(String businessAbode) {
        this.businessAbode = businessAbode;
    }

    public String getBusinessArea() {
        return businessArea;
    }

    public void setBusinessArea(String businessArea) {
        this.businessArea = businessArea;
    }

    public String getMeterReadingCycle() {
        return meterReadingCycle;
    }

    public void setMeterReadingCycle(String meterReadingCycle) {
        this.meterReadingCycle = meterReadingCycle;
    }

    public String getMeterReadingStaffAccount() {
        return meterReadingStaffAccount;
    }

    public void setMeterReadingStaffAccount(String meterReadingStaffAccount) {
        this.meterReadingStaffAccount = meterReadingStaffAccount;
    }

    public String getMeterReadingStaffName() {
        return meterReadingStaffName;
    }

    public void setMeterReadingStaffName(String meterReadingStaffName) {
        this.meterReadingStaffName = meterReadingStaffName;
    }

    public Integer getMeterReadingDay() {
        return meterReadingDay;
    }

    public void setMeterReadingDay(Integer meterReadingDay) {
        this.meterReadingDay = meterReadingDay;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

}
