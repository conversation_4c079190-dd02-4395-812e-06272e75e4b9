package com.koron.util;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.List;

import org.koron.ebs.mybatis.ADOConnection;
import org.koron.ebs.mybatis.SessionFactory;
import org.koron.ebs.mybatis.SqlTask;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.BaseAccessoryMetadataMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;

public class AccessoryCheckUtils {
	public static MessageBean<?> checkField(ServerInterface serverInterface, Object data, String dbEnv) throws Exception {
		Class<?> clazz =  serverInterface.getClass();
		Method method = clazz.getMethod("exec", new Class[] {SessionFactory.class, UserInfoBean.class, RequestBean.class});
		AccessoryCheck accessoryCheck = method.getAnnotation(AccessoryCheck.class);
		Parameter[] parameters = method.getParameters();
		Parameter parameter = parameters[2];
		if(accessoryCheck == null) {
			accessoryCheck = parameter.getAnnotation(AccessoryCheck.class);
		}
		if(accessoryCheck == null) {
			return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "校验成功", Void.class);
		}
		String receiptType= accessoryCheck.receiptType();
		Class<?> accessoryClazz = accessoryCheck.clazz();
		
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "校验成功", Void.class);
	}
	
	@SuppressWarnings("unchecked")
	private static List<String> check(String receiptType, String id,String dbEnv){
		return ADOConnection.runTask(dbEnv, new SqlTask() {
			@Override
			public Object run(SessionFactory factory) {
				BaseAccessoryMetadataMapper mapper = factory.getMapper(BaseAccessoryMetadataMapper.class);
				return mapper.checkAccessoryRequired(receiptType,id);
			}
		}, List.class);
	}
}
