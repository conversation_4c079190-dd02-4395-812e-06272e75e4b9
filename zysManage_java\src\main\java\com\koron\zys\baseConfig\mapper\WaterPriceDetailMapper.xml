<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.WaterPriceDetailMapper">
 	<select id="selectByPriceIdAndCostId" resultType="com.koron.zys.baseConfig.bean.WaterPriceDetailBean">
 		SELECT 
			t.ID,
			t.WATER_PRICE_ID,
			t.COST_ID,
			t.PENALTY_STRATEGY_ID,
			t.FIXED_PRICE,
			t.FIXED_PRICE_UNIT,
			t.FIXED_MONEY,
			t.FIXED_MONEY_UNIT,
			t.LADDER_TYPE,
			t.LADDER_CALCULATE_WAY,
			t.PERSON_BASE,
			t.WATER_BASE,
			t.NO_CHARGING,
			t.TENANT_ID,
			t.CREATE_TIME,
			t.CREATE_ACCOUNT,
			t.CREATE_NAME,
			t.UPDATE_TIME,
			t.UPDATE_ACCOUNT,
			t.UPDATE_NAME
		FROM base_water_price_detail t
		WHERE t.WATER_PRICE_ID = #{priceId} AND t.COST_ID = #{costId}
 	</select>

	<select id="selectByPriceIdAndCostNo" resultType="com.koron.zys.baseConfig.bean.WaterPriceDetailBean">
 		SELECT
			t.ID,
			t.WATER_PRICE_ID,
			t.COST_ID,
			t.PENALTY_STRATEGY_ID,
			t.FIXED_PRICE,
			t.FIXED_PRICE_UNIT,
			t.FIXED_MONEY,
			t.FIXED_MONEY_UNIT,
			t.LADDER_TYPE,
			t.LADDER_CALCULATE_WAY,
			t.PERSON_BASE,
			t.WATER_BASE,
			t.NO_CHARGING,
			t.TENANT_ID,
			t.CREATE_TIME,
			t.CREATE_ACCOUNT,
			t.CREATE_NAME,
			t.UPDATE_TIME,
			t.UPDATE_ACCOUNT,
			t.UPDATE_NAME
		FROM base_water_price_detail t
		WHERE t.WATER_PRICE_ID = #{priceId} AND t.COST_ID = #{costNo}
 	</select>
</mapper>