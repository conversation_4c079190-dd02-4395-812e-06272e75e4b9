webpackJsonp([38],{"8f+r":function(e,t){},eb1A:function(e,t,a){"use strict";(function(e){var i=a("//Fk"),l=a.n(i);t.a={name:"SuperAdministrator",data:function(){return{allBtnList:[],companyList:[],defaultProps:{children:"children",label:"opName"},firstOpCode:"",userData:[],clickUserData:{},userAddShow:!1,empowerShow:!1,listAddShow:!1,formData:{roleName:""},treeData:[],companyData:[],tableShow:!1,maxHeight:0,btnRoleName:"",tableData:{},tableQuery:{page:1,pageCount:50,fuzzyQuery:""},tableDataDia:{list:[],total:0},tableQueryDia:{page:1,pageCount:50,fuzzyQuery:"",departmentCode:""},selectArray:[]}},mounted:function(){var e=this;this.init(),this.$nextTick(function(){e.common.changeTable(e,".SuperAdministrator",[".SuperAdministrator .toolbar",".SuperAdministrator .block"])})},methods:{init:function(){var e=this;this.$api.fetch({params:{busicode:"SuperAdminRoleList",data:{}}}).then(function(t){e.userData=t,e.getTreeData(e.userData[0])})},initTable:function(){var e=this,t={busicode:"RoleUserList",data:{roleCode:this.clickUserData.roleCode,fuzzyQuery:this.tableQuery.fuzzyQuery,page:this.tableQuery.page,pageCount:this.tableQuery.pageCount}};this.$api.fetch({params:t}).then(function(t){e.tableData=t,e.common.changeTable(e,".SuperAdministrator .kl-table",[".SuperAdministrator .block"])})},initDia:function(){var e=this,t=this;return new l.a(function(a){e.$axios({url:"/dep/getAllStaff.htm",method:"post",params:{departmentCode:"",page:t.tableQueryDia.page,pageCount:t.tableQueryDia.pageCount,fuzzyQuery:t.tableQueryDia.fuzzyQuery},headers:{returntype:"ajax/json"}}).then(function(e){console.log(e),t.tableDataDia.list=e.data.data,t.tableDataDia.total=Number(e.data.totalCount)})})},getTreeData:function(t){var a=this;setTimeout(function(){e(".role-item-name").css("color","black"),e("#"+t.roleCode).css("color","#3193f5")},0),this.clickUserData=t;var i={busicode:"RoleUserList",data:{roleCode:t.roleCode,fuzzyQuery:this.tableQuery.fuzzyQuery,page:this.tableQuery.page,pageCount:this.tableQuery.pageCount}};this.$api.fetch({params:i}).then(function(e){a.tableData=e,a.common.changeTable(a,".SuperAdministrator .kl-table",[".SuperAdministrator .block"])})},openAddDialog:function(){this.userAddShow=!0,this.initDia()},closeUserAdd:function(){this.userAddShow=!1,this.tableDataDia={list:[],total:0},this.tableQueryDia={page:1,pageCount:50,fuzzyQuery:"",departmentCode:""}},userAdd:function(){var e=this,t=this;if(0!=this.selectArray.length){var a={busicode:"AddRoleUser",data:{roleCode:this.clickUserData.roleCode,account:this.selectArray}};this.$api.fetch({params:a}).then(function(a){t.userAddShow=!1,e.getTreeData(e.clickUserData),e.$message({type:"success",message:"添加成功！"})})}else t.$message({type:"warning",message:"请先勾选用户！"})},treeNodeClick:function(e,t){var a=this,i=t.checkedNodes,l=(t.checkedKeys,this.$refs.tree.getNode(e.opCode)),o=this.$refs.tree.getCheckedKeys();if(l.checked)for(var n=l.level;n>1;n--)l.parent.checked||(l=l.parent,o.push(l.data.opCode));this.$refs.tree.setCheckedKeys(o),this.treeData.forEach(function(t){a.checkNodeClick(t,e,i.some(function(t){return t.opCode===e.opCode}))})},checkNodeClick:function(e,t,a){var i=this;e.children&&0!==e.children.length&&e.children.forEach(function(l){l.opCode!==t.opCode&&e.opCode!==t.opCode?i.checkNodeClick(l,t,a):i.toggleChildrenClick(l,a)})},toggleChildrenClick:function(e,t){var a=this;e.btnList&&e.btnList.length>0&&this.toggleBtnClick(e,t),this.$refs.tree.setChecked(e.opCode,t),e.children&&0!==e.children.length&&e.children.forEach(function(e){a.$refs.tree.setChecked(e.opCode,t),a.toggleChildrenClick(e,t)})},toggleBtnClick:function(e,t){var a=this;t?(e.btnList.forEach(function(e){a.allBtnList.push(e.opCode)}),e.companyData.forEach(function(e){a.companyList.push(e.companyNo)})):(this.allBtnList=this.allBtnList.filter(function(t){return e.btnList.every(function(e){return e.opCode!==t})}),this.companyList=this.companyList.filter(function(t){return e.companyData.every(function(e){return e.companyNo!==t})}))},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.getTreeData(this.clickUserData)},handleCurrentChange:function(e){this.tableQuery.page=e,this.getTreeData(this.clickUserData)},indexMethodDia:function(e){return(this.tableQueryDia.page-1)*this.tableQueryDia.pageCount+(e+1)},handleSizeChangeDia:function(e){this.tableQueryDia.pageCount=e,this.tableQueryDia.page=1,this.initDia()},handleCurrentChangeDia:function(e){this.tableQueryDia.page=e,this.initDia()},remove:function(e,t){var a=this;this.$confirm("是否确定删除人员："+t.userName+"?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var e={busicode:"DeleteRoleUser",data:{roleCode:a.clickUserData.roleCode,account:[t.loginid]}};a.$api.fetch({params:e}).then(function(e){a.getTreeData(a.clickUserData),a.$message({type:"success",message:"删除成功！"})})}).catch(function(){a.$message({type:"info",message:"已取消删除"})})},searchTable:function(){this.getTreeData(this.clickUserData)},search:function(){this.initDia()},handleSelectionChange:function(e){var t=this;this.selectArray=[],e.length>0&&e.forEach(function(e){t.selectArray.push(e.loginid)})}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}}}).call(t,a("7t+N"))},po6b:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("eb1A"),l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"SuperAdministrator"},[a("div",{staticClass:"role-left"},[a("div",{staticClass:"role-title"},[e._v("\n      角色\n    ")]),e._v(" "),a("div",{staticClass:"role-user"},e._l(e.userData,function(t){return a("dir",{key:t.roleCode,staticClass:"role-item"},[a("div",{staticClass:"role-item-name",attrs:{id:t.roleCode},on:{click:function(a){return e.getTreeData(t)}}},[e._v(e._s(t.roleName))])])}),1)]),e._v(" "),a("div",{staticClass:"role-right"},[a("div",{staticClass:"role-title"},[e._v("\n      人员列表\n      "),a("div",{staticClass:"save-btn"},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"},on:{click:function(t){return e.openAddDialog()}}},[e._v("添加")])],1)]),e._v(" "),a("div",{staticClass:"kl-table",staticStyle:{"padding-top":"10px"}},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.tableQuery,size:"mini"}},[a("div",{staticClass:"toolbar-left"},[a("el-form-item",{attrs:{label:""}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"名称、账号、手机号码",placement:"top"}},[a("el-input",{staticClass:"default_class",attrs:{size:"mini",placeholder:"名称、账号、手机号码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.searchTable.apply(null,arguments)}},model:{value:e.tableQuery.fuzzyQuery,callback:function(t){e.$set(e.tableQuery,"fuzzyQuery",t)},expression:"tableQuery.fuzzyQuery"}})],1)],1),e._v(" "),a("el-form-item",{staticClass:"button-group"},[a("el-button",{staticClass:"compile",attrs:{type:"primary el-icon-search",size:"mini"},on:{click:e.searchTable}})],1)],1)]),e._v(" "),e.tableShow?a("el-table",{staticClass:"change-tables-table",attrs:{"max-height":e.maxHeight,stripe:"",border:"",data:e.tableData.list}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"loginid","min-width":"100",label:"账号","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"userName","min-width":"100",label:"用户名","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"departmentName","min-width":"100",label:"部门","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"mobile","min-width":"100",label:"手机","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"email","min-width":"100",label:"邮箱","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{staticClass:"cell",attrs:{label:"操作",fixed:"right",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.remove(t.$index,t.row)}}},[e._v("删除")])]}}],null,!1,1323384961)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)]),e._v(" "),a("el-dialog",{staticClass:"button-dialog",attrs:{title:"添加人员",visible:e.userAddShow},on:{"update:visible":function(t){e.userAddShow=t},close:function(t){return e.closeUserAdd()}}},[a("el-form",{ref:"addForm",staticStyle:{display:"flex"},attrs:{model:e.tableQueryDia}},[a("el-form-item",{staticStyle:{width:"30%"},attrs:{label:"",prop:"roleName"}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"名称、账号、手机号码",placement:"top"}},[a("el-input",{staticStyle:{width:"calc(100% - 20px)"},attrs:{clearable:"",placeholder:"名称、账号、手机号码"},model:{value:e.tableQueryDia.fuzzyQuery,callback:function(t){e.$set(e.tableQueryDia,"fuzzyQuery",t)},expression:"tableQueryDia.fuzzyQuery"}})],1)],1),e._v(" "),a("el-form-item",[a("el-button",{staticClass:"searchBtn",staticStyle:{"margin-right":"10px"},attrs:{size:"mini",icon:"el-icon-search"},on:{click:e.search}})],1)],1),e._v(" "),a("div",{staticClass:"kl-table"},[a("el-table",{staticClass:"change-tables-table",attrs:{stripe:"",border:"",data:e.tableDataDia.list,"max-height":460},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",label:"NO.",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",index:e.indexMethodDia}}),e._v(" "),a("el-table-column",{attrs:{prop:"loginid","min-width":"140",label:"账号","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"name","min-width":"140",label:"用户名","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"departmentName","min-width":"140",label:"部门","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"mobile","min-width":"140",label:"手机","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"email","min-width":"140",label:"邮箱","show-overflow-tooltip":""}})],1),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQueryDia.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQueryDia.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.tableDataDia.total},on:{"size-change":e.handleSizeChangeDia,"current-change":e.handleCurrentChangeDia}})],1)],1),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.userAdd()}}},[e._v("确 定")]),e._v(" "),a("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.closeUserAdd()}}},[e._v("取 消")])],1)],1)],1)},staticRenderFns:[]};var o=function(e){a("8f+r")},n=a("VU/8")(i.a,l,!1,o,null,null);t.default=n.exports}});