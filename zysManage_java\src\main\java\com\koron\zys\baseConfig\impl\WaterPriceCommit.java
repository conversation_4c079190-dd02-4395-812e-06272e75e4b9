package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.workflow.ProcessInterface;

public class WaterPriceCommit implements ProcessInterface {

	@Override
	public MessageBean<?> before(SessionFactory factory, UserInfoBean userInfo, String billId, String fromNodeCode,
			int operation) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public MessageBean<?> after(SessionFactory factory, UserInfoBean userInfo, String billId, String fromNodeCode,
			String toNodeCode) {
		// TODO Auto-generated method stub
		return null;
	}

	

}
