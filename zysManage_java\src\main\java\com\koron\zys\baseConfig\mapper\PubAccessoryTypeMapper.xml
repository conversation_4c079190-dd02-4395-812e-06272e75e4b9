<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.PubAccessoryTypeMapper">

		
 	<select id="selectList" parameterType="com.koron.zys.baseConfig.queryBean.PubAccessoryTypeQueryBean" resultType="com.koron.zys.baseConfig.bean.PubAccessoryTypeBean" >
 		SELECT
			t.ACCESSORY_NAME,
			t.ACCESSORY_NO,
			t.ACCESSORY_TYPE_ID,
			t.CREATE_NAME,
			t.CREATE_TIME
		FROM
			css_public.pub_accessory_type t
		<where>
			<if test="accessoryTypeId != null and accessoryTypeId != ''">
				and ACCESSORY_TYPE_ID = #{accessoryTypeId, jdbcType=VARCHAR}
			</if>
			<if test="accessoryName != null and accessoryName != ''">
				and ACCESSORY_NAME = #{accessoryName, jdbcType=VARCHAR}
			</if>
			<if test="accessoryNo != null and accessoryNo != ''">
				and ACCESSORY_NO = #{accessoryNo, jdbcType=VARCHAR}
			</if>
		</where>
	</select>
	
	<select id="selectByAccessNo" resultType="com.koron.zys.baseConfig.bean.PubAccessoryTypeBean">
		SELECT
			t.ACCESSORY_NAME,
			t.ACCESSORY_NO,
			t.ACCESSORY_TYPE_ID,
			t.CREATE_NAME,
			t.CREATE_TIME
		FROM
			css_public.pub_accessory_type t
		WHERE ACCESSORY_NO = #{accessNo}
	</select>
	
	<insert id="insert" parameterType="com.koron.zys.baseConfig.bean.PubAccessoryTypeBean">
		insert into base_receipt_accessory (
			ACCESSORY_NAME,
			ACCESSORY_NO,
			ACCESSORY_TYPE_ID,
			CREATE_NAME,
			CREATE_TIME
		)values(
			#{accessoryName,jdbcType=VARCHAR},
			#{accessoryNo,jdbcType=VARCHAR},
			#{accessoryTypeId,jdbcType=VARCHAR},
			#{accessoryTypeId,jdbcType=VARCHAR},
			#{createName,jdbcType=VARCHAR},
			now()
		)
	</insert>
	
	<delete id="deleteById" parameterType="java.lang.String">
		delete from base_receipt_accessory where ACCESSORY_TYPE_ID = #{id}
	</delete>
	
</mapper>