package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ManageAreaBean;
import com.koron.zys.baseConfig.mapper.ManageAreaMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

/**
 * 修改管理区域
 * <AUTHOR>
 *
 */
public class ManageAreaUpdate implements ServerInterface {
	
	private static Logger logger = LoggerFactory.getLogger(ManageAreaUpdate.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		ManageAreaMapper mapper = factory.getMapper(ManageAreaMapper.class);
		ManageAreaBean bean = null;
		try {
			bean = JsonUtils.objectToPojo(req.getData(), ManageAreaBean.class);

			bean.setUpdateName(userInfo.getUserInfo().getName());
			bean.setUpdateTime(CommonUtils.getCurrentTime());
			if (StringUtils.isBlank(bean.getManageAreaId())) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
			}
			//查询原数据，比较是否修改父级目录
			ManageAreaBean oldBean = mapper.findManageAreaById(bean.getManageAreaId()+"");
			if(!(oldBean.getParentId()+"").equals((bean.getParentId()+""))) {
				//重新生成code
				ManageAreaAdd.handCode(mapper, bean);
			}
			mapper.updateManageArea(bean);
		} catch (Exception e) {
			logger.error("非法参数",e);
			factory.close(false);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
