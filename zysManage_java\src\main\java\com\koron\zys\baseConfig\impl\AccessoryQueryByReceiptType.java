package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BaseAccessoryConfigBean;
import com.koron.zys.baseConfig.bean.PubAccessoryTypeBean;
import com.koron.zys.baseConfig.mapper.BaseAccessoryCofingMapper;
import com.koron.zys.baseConfig.mapper.PubAccessoryTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;

import com.koron.util.Constant;
import com.koron.util.JsonUtils;

public class AccessoryQueryByReceiptType implements ServerInterface{

	
		@Override
		public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
			// TODO Auto-generated method stub
			 @SuppressWarnings("rawtypes")
			MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
			try {
				BaseAccessoryConfigBean bean = JsonUtils.objectToPojo(req.getData(), BaseAccessoryConfigBean.class); 
				BaseAccessoryCofingMapper mapper = factory.getMapper(BaseAccessoryCofingMapper.class);
				List<BaseAccessoryConfigBean> list = mapper.selectByReceiptType2(bean.getReceiptType());
				
				PubAccessoryTypeMapper pmapper =  factory.getMapper(PubAccessoryTypeMapper.class,"_default");
				//转换附件类型
				for (BaseAccessoryConfigBean baseAccessoryConfigBean : list) {
					PubAccessoryTypeBean access = pmapper.selectByAccessNo(baseAccessoryConfigBean.getAccessoryNo());
					if(access!=null && StringUtils.isNotEmpty(access.getAccessoryName())) {
						baseAccessoryConfigBean.setAccessoryType(access.getAccessoryName());
					}
				}
				info.setData(list);
			}catch(Exception e) {
				info.setCode(Constant.MESSAGE_INT_FAIL);
				info.setDescription("操作失败");
				logger.error("操作失败", e);
			}
			return info;			
		}
									
}
