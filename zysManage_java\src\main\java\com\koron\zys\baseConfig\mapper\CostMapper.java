package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.baseConfig.bean.BillQuotaBean;
import org.apache.ibatis.annotations.Param;

import com.koron.zys.baseConfig.bean.CostBean;
import com.koron.zys.baseConfig.bean.CostNameBean;
import com.koron.zys.baseConfig.vo.CostVO;

public interface CostMapper {
	
	/**
	 * 查询公共库的费用种类
	 * 
	 * @return
	 */
	List<CostVO> selectPubCostList(@Param("status") String status);
	
	/**
	 * 查询列表	状态启用
	 * 
	 * @return
	 */
	List<CostVO> selectCostList();
	List<CostVO> selectCostListNoLj();
	
	/**
	 * 查询列表	忽视状态
	 * 
	 * @return
	 */
	List<CostVO> selectCostListIgnoreStatus();
	
	/**
	 * 根据id从公共库查询
	 * @param CostId
	 * @return
	 */
	CostBean selectCostById(@Param("id") String id);


	List<String> selectCostForAllowRushRed();

	//查询非综合费用列表
	List<CostBean> selectCostForNotCompreHensive();
	//查询综合费用列表
	List<CostBean> selectCostForCompreHensive();

	/**
	 * 删除水司库费用类型
	 * @param id
	 * @return
	 */
	Integer deleteCostById(@Param("id") String id);
	
	/**
	 * 添加
	 * 
	 * @param costBean
	 * @return
	 */
	void insertCost(CostBean costBean);
	
	/**
	 * 停用费用类型
	 * @param id
	 * @return
	 */
	Integer stopCostById(@Param("id") String id);
	
	
	/**
	 * 查询费用名称下拉列表 启用的
	 * 
	 * @return
	 */
	List<CostNameBean>  selectCostNameList();
	
	int selectBaseCostById(@Param("id") String id);

	/**
	 * 通过id列表查询
	 * @param list
	 * @return
	 */
	List<CostVO> selectBaseCostByIds(List<String> list);
	
	Integer updateCostStatus(CostBean costBean);
	
	Integer updateCostUnit(CostBean costBean);

	List<BillQuotaBean> selectUserBillQuota(@Param("userNo") String userNo);

	/**
	 * 查询启用列表
	 *
	 * @return
	 */
	List<CostVO> selectUsingCostList();
}
