package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.PumpStationBean;
import com.koron.zys.baseConfig.mapper.PumpStationMapper;
import com.koron.zys.baseConfig.queryBean.PumpStationQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 泵站信息-编辑初始化
 * <AUTHOR>
 *
 */
public class PumpStationQuery implements ServerInterface {
	
private static Logger logger = LoggerFactory.getLogger(PumpStationQuery.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<PumpStationBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PumpStationBean.class);

		try { 
			PumpStationQueryBean bean = JsonUtils.objectToPojo(req.getData(), PumpStationQueryBean.class);
			PumpStationMapper mapper = factory.getMapper(PumpStationMapper.class);
			PumpStationBean PumpStationbean = mapper.selectPumpStationById(bean.getPumpStationId());
			info.setData( PumpStationbean);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}
