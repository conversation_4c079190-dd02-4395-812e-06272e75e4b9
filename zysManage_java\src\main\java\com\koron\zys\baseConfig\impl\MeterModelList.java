package com.koron.zys.baseConfig.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.MeterBoreMapper;
import com.koron.zys.baseConfig.mapper.MeterFactoryMapper;
import com.koron.zys.baseConfig.mapper.MeterModelMapper;
import com.koron.zys.baseConfig.queryBean.MeterBoreQueryBean;
import com.koron.zys.baseConfig.queryBean.MeterFactoryQueryBean;
import com.koron.zys.baseConfig.queryBean.MeterModelQueryBean;
import com.koron.zys.baseConfig.vo.MeterBoreVO;
import com.koron.zys.baseConfig.vo.MeterFactoryVO;
import com.koron.zys.baseConfig.vo.MeterModelVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
import com.koron.util.Tools;

/**
 * 水表型号-列表初始化
 *
 * <AUTHOR>
 */
public class MeterModelList implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MeterModelList.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        @SuppressWarnings("rawtypes")
        MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
        try {
            MeterModelQueryBean bean = JsonUtils.objectToPojo(req.getData(), MeterModelQueryBean.class);
            MeterModelMapper mapper = factory.getMapper(MeterModelMapper.class);
            PageHelper.startPage(bean.getPage(), bean.getPageCount());
            List<MeterModelVO> list = mapper.selectMeterModelList(bean);
            //获取通讯类型
            Map<String, String> transWay = Tools.mapDicByCode(factory, "TPW");
            //获取水表类型
            Map<String, String> meterType = Tools.mapDicByCode(factory, "MMT");
            //获取水表厂商
            MeterFactoryMapper meterFactoryMapper=factory.getMapper(MeterFactoryMapper.class);
            List<MeterFactoryVO> factorys=meterFactoryMapper.selectMeterFactoryList(new MeterFactoryQueryBean());
            // 水表口径
            MeterBoreMapper meterBoreMapper = factory.getMapper(MeterBoreMapper.class);
            List<MeterBoreVO> meterBores = meterBoreMapper.selectMeterBoreList(new MeterBoreQueryBean());
            for(MeterModelVO vo:list) {
            	//水表口径
	    		if (StringUtils.isNotBlank(vo.getMeterBore())) {
	    			MeterBoreVO meterBore = meterBores.stream().filter(k -> Objects.equals(vo.getMeterBore(), k.getId())).findFirst().orElse(new MeterBoreVO());
	    			vo.setMeterBore(meterBore.getBoreName());
	    		}
	    		//水表厂商
	    		if (StringUtils.isNotBlank(vo.getFactoryName())) {
	    			MeterFactoryVO factoryVo = factorys.stream().filter(k -> Objects.equals(vo.getFactoryName(), k.getId())).findFirst().orElse(new MeterFactoryVO());
	    			vo.setFactoryName(factoryVo.getFactoryName());
	    		}
	    		//通讯类型
	    		if (StringUtils.isNotBlank(vo.getTransWay())) {
	    			vo.setTransWay(transWay.get(vo.getTransWay()));
	    		}
	    		//水表类型
	    		if (StringUtils.isNotBlank(vo.getMeterType())) {
	    			vo.setMeterType(meterType.get(vo.getMeterType()));
	    		}
            }
            info.setData(new PageInfo<>(list));
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }

}