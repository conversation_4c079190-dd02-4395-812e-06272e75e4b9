package com.koron.zys.baseConfig.bean;
/**
 * 营业所信息实体类
 * <AUTHOR>
 *
 */
public class BusinessAbodeBean {
	/**
	 * 主键ID
	 */
	private String id;
	/**
	 * 编号
	 */
	private String abodeNo;
	/**
	 * 名称
	 */
	private String abodeName;
	/**
	 * 备注
	 */
	private String comments;
	/**
	 * 状态
	 */
	private int status;
	/**
	 * 排序号
	 */
	private String sortNo;
	/**
	 * 水司编号
	 */
	private String groupCode;
	/**
	 * 租户编号
	 */
	private String tenantId;
	/**
	 * 创建时间
	 */
	private String createTime;
	/**
	 * 创建人ID
	 */
	private String createAccount;
	/**
	 * 创建人
	 */
	private String createName;
	/**
	 * 更新时间
	 */
	private String updateTime;
	/**
	 * 最后修改人ID
	 */
	private String updateAccount;
	/**
	 * 最后修改人
	 */
	private String updateName;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getAbodeNo() {
		return abodeNo;
	}
	public void setAbodeNo(String abodeNo) {
		this.abodeNo = abodeNo;
	}
	public String getAbodeName() {
		return abodeName;
	}
	public void setAbodeName(String abodeName) {
		this.abodeName = abodeName;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public int getStatus() {
		return status;
	}
	public void setStatus(int status) {
		this.status = status;
	}
	public String getSortNo() {
		return sortNo;
	}
	public void setSortNo(String sortNo) {
		this.sortNo = sortNo;
	}
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	public String getTenantId() {
		return tenantId;
	}
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getCreateAccount() {
		return createAccount;
	}
	public void setCreateAccount(String createAccount) {
		this.createAccount = createAccount;
	}
	public String getCreateName() {
		return createName;
	}
	public void setCreateName(String createName) {
		this.createName = createName;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public String getUpdateAccount() {
		return updateAccount;
	}
	public void setUpdateAccount(String updateAccount) {
		this.updateAccount = updateAccount;
	}
	public String getUpdateName() {
		return updateName;
	}
	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

}
