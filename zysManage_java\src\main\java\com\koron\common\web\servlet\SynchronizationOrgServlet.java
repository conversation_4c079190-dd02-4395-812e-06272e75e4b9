package com.koron.common.web.servlet;

import com.koron.zys.systemManage.service.ServiceFactory;

import org.koron.ebs.mybatis.ADOConnection;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.swan.bean.MessageBean;

/**
 * UMA 统一用户平台对接入口
 * (因已对接竹云，这个入口已进行屏蔽)
 */
// @Controller
public class SynchronizationOrgServlet {
	
	@RequestMapping("/sys/synchronization.htm")
	@ResponseBody
	public String synchronization() {
		return ADOConnection.runTask(factory -> ServiceFactory.getSynchronizationOrgService().synchronization(factory), MessageBean.class).toJson();
	}
	
	@RequestMapping("/sys/synDepartment.htm")
	@ResponseBody
	public String synDepartment() {
		return ADOConnection.runTask(factory -> ServiceFactory.getSynchronizationOrgService().synDepartment(factory), MessageBean.class).toJson();
	}
	
	@RequestMapping("/sys/synOrg.htm")
	@ResponseBody
	public String synOrg() {
		return ADOConnection.runTask(factory -> ServiceFactory.getSynchronizationOrgService().synOrg(factory), MessageBean.class).toJson();
	}
	
	@RequestMapping("/sys/synStaff.htm")
	@ResponseBody
	public String synStaff() {
		return ADOConnection.runTask(factory -> ServiceFactory.getSynchronizationOrgService().synStaff(factory), MessageBean.class).toJson();
	}
	
	@RequestMapping("/sys/synStaffDepartmentRelation.htm")
	@ResponseBody
	public String synStaffDepartmentRelation() {
		return ADOConnection.runTask(factory -> ServiceFactory.getSynchronizationOrgService().synStaffDepartmentRelation(factory), MessageBean.class).toJson();
	}
}
