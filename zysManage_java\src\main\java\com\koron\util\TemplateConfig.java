package com.koron.util;

public class TemplateConfig {
	
	public BeanConfig beanConfig() {
		return new BeanConfig();
	} 
	
	public MapperConfig mapperConfig() {
		return new MapperConfig();
	} 
	
	public AddBusiImplConfig addBusiImplConfig() {
		return new AddBusiImplConfig();
	} 
	
	public UpdateBusiImplConfig updateBusiImplConfig() {
		return new UpdateBusiImplConfig();
	} 
	
	public DeleteBusiImplConfig deleteBusiImplConfig() {
		return new DeleteBusiImplConfig();
	} 
	
	public ListBusiImplConfig listBusiImplConfig() {
		return new ListBusiImplConfig();
	} 
	
	public static class BaseConfig {
		
		private String tableName;
		
		private String comments;
		
		private String className;
		
		private String author;
		
		private String moduleName;
		
		private String moduleType;
		
		private String fileSuffix;
		
		public String getModuleType() {
			return moduleType;
		}

		public void setModuleType(String moduleType) {
			this.moduleType = moduleType;
		}

		public String getFileSuffix() {
			return fileSuffix;
		}

		public void setFileSuffix(String fileSuffix) {
			this.fileSuffix = fileSuffix;
		}

		public String getClassName() {
			return className;
		}

		public void setClassName(String className) {
			this.className = className;
		}

		public String getTableName() {
			return tableName;
		}

		public BaseConfig setTableName(String tableName) {
			this.tableName = tableName;
			return this;
		}

		public String getModuleName() {
			return moduleName;
		}

		public BaseConfig setModuleName(String moduleName) {
			this.moduleName = moduleName;
			return this;
		}

		public String getComments() {
			return comments;
		}

		public String getAuthor() {
			return author;
		}

		public BaseConfig setComments(String comments) {
			this.comments = comments;
			return this;
		}

		public BaseConfig setAuthor(String author) {
			this.author = author;
			return this;
		}

	}
	
	public static class BeanConfig extends BaseConfig{
		
	}
	
	public static class MapperConfig extends BaseConfig{
		
	}
	
	public static abstract class BusiImplConfig extends BaseConfig{
		
		private String busiCode;
		
		public abstract String getTemplateName();

		public String getBusiCode() {
			return busiCode;
		}

		public BusiImplConfig setBusiCode(String busiCode) {
			this.busiCode = busiCode;
			return this;
		}
		
	}
	
	public static class AddBusiImplConfig extends BusiImplConfig{

		@Override
		public String getTemplateName() {
			return "impl-add.ftl";
		}
		
	}
	
	public static class UpdateBusiImplConfig extends BusiImplConfig{

		@Override
		public String getTemplateName() {
			return "impl-update.ftl";
		}
		
	}
	
	public static class DeleteBusiImplConfig extends BusiImplConfig{

		@Override
		public String getTemplateName() {
			return "impl-delete.ftl";
		}
		
	}
	
	public static class ListBusiImplConfig extends BusiImplConfig{

		@Override
		public String getTemplateName() {
			return "impl-list.ftl";
		}
		
	}

}
