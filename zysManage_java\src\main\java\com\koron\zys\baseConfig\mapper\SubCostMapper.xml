<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
		namespace="com.koron.zys.baseConfig.mapper.SubCostMapper">
	<select id="list"
			parameterType="com.koron.zys.baseConfig.bean.SubCostQueryBean"
			resultType="com.koron.zys.baseConfig.bean.SubCostBean">
		select a.id,a.cost_no,a.sub_cost_no,a.sub_cost_name,a.is_quota,a.managers,b.cost_name
		from base_sub_cost a, base_cost b
		where a.cost_no = b.cost_no
		<if test=" subCostName !='' and subCostName != null ">
			and sub_cost_name like concat('%',#{subCostName},'%')
		</if>

	</select>
	<insert id="add"
			parameterType="com.koron.zys.baseConfig.bean.SubCostBean">
		insert into base_sub_cost(id,cost_no,sub_cost_no,sub_cost_name,is_quota,managers,create_time,
										create_name,create_account,update_time,update_name,update_account)
		values(#{id},#{costNo},#{subCostNo},#{subCostName},#{isQuota},#{managers},#{createTime},
			   #{createName},#{createAccount},#{updateTime},#{updateName},#{updateAccount})
	</insert>
	<update id="update"
			parameterType="com.koron.zys.baseConfig.bean.SubCostBean">
		update base_sub_cost set
									   cost_no=#{costNo},
									   sub_cost_no=#{subCostNo},
									   sub_cost_name=#{subCostName},
									   is_quota=#{isQuota},
		                               managers = #{managers},
									   update_time=#{updateTime},
									   update_name=#{updateName},
									   update_account=#{updateAccount}
		where id=#{id}
	</update>

</mapper>
