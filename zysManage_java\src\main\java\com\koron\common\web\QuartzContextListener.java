package com.koron.common.web;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.apache.log4j.Logger;
import org.quartz.SchedulerException;
import org.springframework.web.context.WebApplicationContext;

import com.koron.zys.serviceManage.service.JobUtil;


public class QuartzContextListener implements ServletContextListener {
	private Logger log = Logger.getLogger(QuartzContextListener.class);

	@Override
	public void contextInitialized(ServletContextEvent sce) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void contextDestroyed(ServletContextEvent arg0) {
		WebApplicationContext webApplicationContext = (WebApplicationContext) arg0
                .getServletContext()
                .getAttribute(
                        WebApplicationContext.ROOT_WEB_APPLICATION_CONTEXT_ATTRIBUTE);
		
        if(JobUtil.scheduler != null) {
        	try {
        		JobUtil.scheduler.shutdown();
			} catch (SchedulerException e) {
				// TODO Auto-generated catch block
				log.error("关闭scheduler失败",e);
			}
        }
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
		
	}

}
