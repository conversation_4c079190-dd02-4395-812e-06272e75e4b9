package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterFactoryBean;
import com.koron.zys.baseConfig.mapper.MeterFactoryMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;

/**
 * 水表供应商-编辑
 *
 * <AUTHOR>
 */
public class MeterFactoryUpdate implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MeterFactoryUpdate.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            MeterFactoryMapper mapper = factory.getMapper(MeterFactoryMapper.class);
            MeterFactoryBean bean = JsonUtils.objectToPojo(req.getData(), MeterFactoryBean.class);
            // 校验字段重复
            if (mapper.check2("factory_name", bean.getFactoryName(), bean.getId()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "厂家名称：" + bean.getFactoryName() + "的信息已存在。", void.class);
            }
            if (mapper.check2("factory_full_name", bean.getFactoryFullName(), bean.getId()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "厂家全称：" + bean.getFactoryFullName() + "的信息已存在。", void.class);
            }
            if (StringUtils.isNullOrEmpty(bean.getId())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
            }
            bean.setUpdateInfo(userInfo);
            mapper.updateMeterFactory(bean);
        } catch (Exception e) {
            logger.error("水表供应商修改失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "水表供应商修改失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}