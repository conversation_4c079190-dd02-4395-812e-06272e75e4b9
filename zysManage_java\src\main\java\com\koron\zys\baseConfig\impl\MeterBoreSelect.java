package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.MeterBoreMapper;
import com.koron.zys.baseConfig.queryBean.MeterBoreQueryBean;
import com.koron.zys.baseConfig.vo.MeterBoreVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
/**
 * 水表口径-下拉框
 * <AUTHOR>
 *
 */
public class MeterBoreSelect implements ServerInterface  {
	
	private static Logger logger = LoggerFactory.getLogger(MeterBoreSelect.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);

		try {
			
			MeterBoreMapper mapper = factory.getMapper(MeterBoreMapper.class);		
			List<MeterBoreVO> list = mapper.selectMeterBoreList(new MeterBoreQueryBean());			
			info.setData(list);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}
