package com.koron.zys.baseConfig.impl;

import com.koron.zys.baseConfig.queryBean.WaterTypeQueryBean;
import com.mysql.cj.util.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.WaterTypeBean;
import com.koron.zys.baseConfig.mapper.WaterTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 用水类型-编辑初始化
 *
 * <AUTHOR>
 */
public class WaterTypeQuery implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(WaterTypeQuery.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        MessageBean<WaterTypeBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", WaterTypeBean.class);
        try {
            WaterTypeMapper mapper = factory.getMapper(WaterTypeMapper.class);
            WaterTypeQueryBean bean = JsonUtils.objectToPojo(req.getData(), WaterTypeQueryBean.class);
            if (StringUtils.isNullOrEmpty(bean.getWaterTypeId())){
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id不能为空", void.class);
            }
            WaterTypeBean WaterTypebean = mapper.findWaterTypeById(bean.getWaterTypeId());
            info.setData(WaterTypebean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }
}
