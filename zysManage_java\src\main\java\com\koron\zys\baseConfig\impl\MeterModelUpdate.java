package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterModelBean;
import com.koron.zys.baseConfig.mapper.MeterModelMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;

/**
 * 水表型号-编辑
 *
 * <AUTHOR>
 */
public class MeterModelUpdate implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MeterModelUpdate.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            MeterModelMapper mapper = factory.getMapper(MeterModelMapper.class);
            MeterModelBean bean = JsonUtils.objectToPojo(req.getData(), MeterModelBean.class);
            // 校验字段重复
            if (mapper.check2("model_name", bean.getModelName(), bean.getId()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "水表型号名称：" + bean.getModelName() + "的信息已存在。", void.class);
            }
            if (mapper.check2("model_no", bean.getModelNo(), bean.getId()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "水表型号编号：" + bean.getModelNo() + "的信息已存在。", void.class);
            }
            if (StringUtils.isNullOrEmpty(bean.getId())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
            }
            bean.setUpdateInfo(userInfo);
            mapper.updateMeterModel(bean);
        } catch (Exception e) {
            logger.error("水表型号更新失败", e);
            return MessageBean.create(Constant.ILLEGAL_PARAMETER, "水表型号更新失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}