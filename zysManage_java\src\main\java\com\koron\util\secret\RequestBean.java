package com.koron.util.secret;

import com.google.gson.GsonBuilder;
import com.google.gson.LongSerializationPolicy;

/**
 * 请求体参数
 * 
 * <AUTHOR>
 *
 */
public class RequestBean<T> {

	private String appid;//
	private String secret;// 加密认证
	private String url;// 请求地址
	private String token;
	private T data;// 请求数据

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getSecret() {
		return secret;
	}

	public void setSecret(String secret) {
		this.secret = secret;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return "RequestBean{" + "appid='" + appid + '\'' + ", url='" + url + '\'' + ", secret='" + secret + '\''
				+ ", data=" + data + '}';
	}

	public String toJson() {
		return new GsonBuilder().setLongSerializationPolicy(LongSerializationPolicy.STRING)
				.setDateFormat("yyyy-MM-dd HH:mm:ss SSS").create().toJson(this);
	}

}
