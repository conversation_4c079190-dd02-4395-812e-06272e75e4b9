package com.koron.common.web.bean;

/**
 * 上传图片附件实体
 *
 * @author: zhongxj
 * @date: 2020-07-24 19:26
 **/

public class PicAccessoryBean {

    private String token;

    /**
     * 单据id
     */
    private String receiptId;

    /**
     * 附件类型
     */
    private String accessoryNo;

    /**
     * 数据
     */
    private String data;

    /**
     * 单据类型
     */
    private String receiptType;

    private String userNo;

    public String getUserNo() {
        return userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getReceiptId() {
        return receiptId;
    }

    public void setReceiptId(String receiptId) {
        this.receiptId = receiptId;
    }

    public String getAccessoryNo() {
        return accessoryNo;
    }

    public void setAccessoryNo(String accessoryNo) {
        this.accessoryNo = accessoryNo;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getReceiptType() {
        return receiptType;
    }

    public void setReceiptType(String receiptType) {
        this.receiptType = receiptType;
    }
}
