<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BankMapper">
    <insert id="saveBank" parameterType="com.koron.zys.baseConfig.bean.BankBean">
		insert into
		BASE_BANK
		(id,bank_no,bank_name,bank_number,comments,parent_id,is_leaf,status,sort_no,link_man,link_tel,tenant_id,create_name,create_account,create_time)
		values
		(#{id},#{bankNo},#{bankName},#{bankNumber},#{comments},#{parentId},
		#{isLeaf},#{status},#{sortNo},
		#{linkMan},#{linkTel},#{tenantId},#{createName},#{createAccount},#{createTime})
	</insert>
    <update id="updateBank"
            parameterType="com.koron.zys.baseConfig.bean.BankBean">
        update BASE_BANK
        <trim prefix="SET" suffixOverrides=",">
            <if test="bankName != null and bankName != ''">
                bank_name=#{bankName},
            </if>
            <if test="bankNumber != null and bankNumber != ''">
                bank_number=#{bankNumber},
            </if>
            <if test="comments != null ">
                comments=#{comments},
            </if>
            <if test="linkMan != null and linkMan != ''">
                link_man=#{linkMan},
            </if>
            <if test="linkTel != null and linkTel != ''">
                link_tel=#{linkTel},
            </if>
            <if test="parentId != null and parentId != ''">
                parent_id=#{parentId},
            </if>
            <if test="isLeaf != null">
                is_leaf=#{isLeaf},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
            <if test="sortNo != null">
                sort_no=#{sortNo},
            </if>
            <if test="updateName != null and updateName != ''">
                update_name=#{updateName},
            </if>
            <if test="updateAccount != null and updateAccount != ''">
                update_account=#{updateAccount},
            </if>
            <if test="updateTime != null and updateTime != ''">
                update_time=#{updateTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <select id="findBank" parameterType="com.koron.zys.baseConfig.queryBean.BankQueryBean"
            resultType="com.koron.zys.baseConfig.vo.BankVO">
        select a.bank_no,a.bank_name,case when a.status=1 then '启用' else '停用' end status,
        a.sort_no,a.comments,a.link_man,a.link_tel, a.bank_number,a.id
        from BASE_BANK a left join BASE_BANK
        b
        on a.parent_id=b.id
        <where>
            <if test="bankNo != null and bankNo !='all'">
                and a.bank_no LIKE CONCAT(#{bankNo},'_____')
            </if>
            <if test="bankName != null and bankName !=''">
                and a.bank_name LIKE CONCAT('%',#{bankName},'%')
            </if>
            <if test="status != null and status ==1">
                and a.status = 1
            </if>
            <if test="isLeaf != null and isLeaf ==0">
                and a.is_leaf = 0
            </if>
        </where>
        order by a.sort_no
    </select>

    <select id="findBankByTree"
            resultType="com.koron.zys.serviceManage.bean.TreeBean">
		select bank_no as code,bank_name as name
		case 
		WHEN substr(bank_no,length(bank_no)-2) &lt;&gt; '000' THEN
		CONCAT (substr(bank_no, 1, 6), '000')
		WHEN substr(bank_no,length(bank_no)-5) &lt;&gt; '000000' THEN
		CONCAT (substr(bank_no, 1, 3), '000000') 
		END PARENT
		from BASE_BANK
		where is_leaf = 0 and status = 1 order by bank_no
	</select>

    <select id="findBankBySelect"
            resultType="com.koron.zys.serviceManage.bean.SelectBean">
		SELECT
		bank_no as code,
		CASE
		WHEN RIGHT (bank_no, 6) = '000000' THEN
		bank_name
		WHEN RIGHT (bank_no, 3) = '000' THEN
		CONCAT(' ', bank_name)
		ELSE
		CONCAT(' ', bank_name)
		END name
		FROM
		BASE_BANK
		WHERE
		status = 1
		ORDER BY
		bank_no
	</select>
    <!-- 通过id查询记录 编辑初始化  这里的返回值可以是BankVO,返回父级的名称-->
    <!--  a.comments as comments,a.status as status,a.sort_no as sortNo,a.link_man as linkMan,a.link_tel as linkTel,-->
    <select id="findBankInfoById"
    resultType="com.koron.zys.baseConfig.bean.BankBean">
    select a.bank_name as bankName,
    a.comments as comments,a.status as status,a.sort_no as sortNo,a.link_man as linkMan,a.link_tel as linkTel,
    a.bank_id as bankId,a.bank_number as bankNumber
    from BASE_BANK a left join BASE_BANK
    b
    on a.parent_id=b.id
    where
    a.id = #{id}
    </select>

    <!-- 通过Code查询记录 -->
    <select id="findBankByCode"
            resultType="com.koron.zys.serviceManage.bean.SelectBean">
		SELECT
		x.*, bank_no code, bank_name name, PARENT_ID parentId , id
		FROM
		BASE_BANK x
		WHERE
		bank_no = #{bankNo}
	</select>
    <!-- 查询最大的子级code编号 -->
    <select id="findMaxChild" resultType="java.lang.String">
		SELECT
		max(bank_no) maxCode 
		FROM
		BASE_BANK
		WHERE
		bank_no like CONCAT(#{bankNo},'_____')
	</select>

    <select id="SelectProvince"
            resultType="com.koron.zys.serviceManage.bean.SelectBean">
        select bank_no as code,bank_name as name
        from BASE_BANK
        <where>
            and bank_no LIKE '_____'
        </where>
        order by bank_no
    </select>
    <select id="bankSelect" resultType="com.koron.zys.baseConfig.vo.SelectVO">
		select
		id,bank_name name
		from
		base_bank
		where
		status=1
	</select>
</mapper>