package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterStatusBean;
import com.koron.zys.baseConfig.mapper.MeterStatusMapper;
import com.koron.zys.baseConfig.queryBean.MeterStatusQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 抄表状态-编辑初始化
 * <AUTHOR>
 *
 */
public class MeterStatusQuery implements ServerInterface {
	
private static Logger logger = LoggerFactory.getLogger(MeterStatusQuery.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<MeterStatusBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", MeterStatusBean.class);

		try { 
			MeterStatusQueryBean bean = JsonUtils.objectToPojo(req.getData(), MeterStatusQueryBean.class);
			MeterStatusMapper mapper = factory.getMapper(MeterStatusMapper.class);
			MeterStatusBean MeterStatusbean = mapper.selectMeterStatusById(bean.getStatusId());
			info.setData( MeterStatusbean);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}
