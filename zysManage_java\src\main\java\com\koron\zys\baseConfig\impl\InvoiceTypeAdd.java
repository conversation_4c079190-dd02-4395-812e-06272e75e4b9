package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.InvoiceTypeBean;
import com.koron.zys.baseConfig.mapper.InvoiceTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
/**
 * 册本信息-添加
 * <AUTHOR>
 */
public class InvoiceTypeAdd implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(InvoiceTypeAdd.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			InvoiceTypeMapper mapper = factory.getMapper(InvoiceTypeMapper.class);
			InvoiceTypeBean bean = JsonUtils.objectToPojo(req.getData(), InvoiceTypeBean.class);
			if(StringUtils.isBlank(bean.getInvoiceNo())){
				return MessageBean.create(Constant.MESSAGE_INT_FAIL, "票据编号不能为空。", void.class);
			}
			if(StringUtils.isBlank(bean.getInvoiceName())){
				return MessageBean.create(Constant.MESSAGE_INT_FAIL, "票据名称不能为空。", void.class);
			}
			bean.setId(new ObjectId().toHexString());
			bean.setStatus("1");
			bean.setCreateName(userInfo.getUserInfo().getName());
			bean.setCreateAccount(userInfo.getUserInfo().getAcount());
			bean.setCreateTime(CommonUtils.getCurrentTime());
			mapper.insert(bean);
		} catch (Exception e) {
			logger.error("操作失败", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
}