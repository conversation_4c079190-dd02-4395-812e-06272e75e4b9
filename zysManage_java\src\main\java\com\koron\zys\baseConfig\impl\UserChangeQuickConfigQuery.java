package com.koron.zys.baseConfig.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.koron.zys.baseConfig.bean.QuickChangeConfigBean;

import com.koron.zys.serviceManage.bean.UserFieldBean;
import com.koron.zys.serviceManage.mapper.UserFieldConfigMapper;
import org.apache.log4j.Logger;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.QuickChangeConfigMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

public class UserChangeQuickConfigQuery implements ServerInterface{
	
	private Logger log = Logger.getLogger(UserChangeQuickConfigQuery.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			UserFieldBean bean =new UserFieldBean();
			QuickChangeConfigMapper mapper = factory.getMapper(QuickChangeConfigMapper.class);
			UserFieldConfigMapper checkMeterMapper = factory.getMapper(UserFieldConfigMapper.class, "_default");
			//查询快速变更配置用户信息和用户水表信息
			List<QuickChangeConfigBean> listQuickChangeConfig = mapper.selectCodeNameByUser();
			Map<String, String> quickChangeConfig = listQuickChangeConfig.stream().collect(Collectors.toMap(QuickChangeConfigBean::getCode, QuickChangeConfigBean::getName));
			bean.setGroupCode(userInfo.getCurWaterCode());
			bean.setPageName("quickChangeMangement");
			//查询运维平台用户信息和用户水表信息
			List<UserFieldBean> listUser=checkMeterMapper.findUserField(bean);
			Map<String, String> monthWaterMap = listUser.stream().collect(Collectors.toMap(UserFieldBean::getFieldCode, UserFieldBean::getFieldName));
			//检查快速变更配置中字段是否存在玉运维配置中
			for (QuickChangeConfigBean quickChangeConfigBean:listQuickChangeConfig){
				if (!monthWaterMap.containsKey(quickChangeConfigBean.getCode())){
					//快速变更配置中字段不存在运维配置中，则删除
					mapper.delete(quickChangeConfigBean.getCode(),quickChangeConfigBean.getType());
				}
			}
			//检查运维配置字段是否存在于快速变更配置
			for (UserFieldBean userFieldBean:listUser){
				QuickChangeConfigBean quickChangeConfigBean =new QuickChangeConfigBean();
				if (!quickChangeConfig.containsKey(userFieldBean.getFieldCode())){
					//运维配置中字段不存在于快速变更配置，则添加
					quickChangeConfigBean.setCode(userFieldBean.getFieldCode());
					quickChangeConfigBean.setIsUse("0");
					quickChangeConfigBean.setName(userFieldBean.getFieldName());
					if (userFieldBean.getGroup().equals("用户信息")) {
						quickChangeConfigBean.setType("userInfoData");
					}else if (userFieldBean.getGroup().equals("用户-水表信息")){
						quickChangeConfigBean.setType("userWaterInfoData");
					}
					mapper.add(quickChangeConfigBean);
				}
			}
			List<QuickChangeConfigBean> list = mapper.select();
			info.setData(list);
		}catch(Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}
