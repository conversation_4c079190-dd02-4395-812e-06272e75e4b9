package com.koron.business.web;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.koron.business.bean.DealBean;

public interface DealMapper {
	@Select("SELECT `id` as msgid,`title` as msgtitle,`link` as msglink,`user` as useraccount,`type`,`sysid`,`createtime` as msgtimestamp,`msgstate` "
			+ "FROM `tblmessage` where `msgstate` = #{msgState} and `user` =#{userAccount} limit #{offset},#{pageSize}")
	public List<DealBean.Data> list(@Param("userAccount") String userAccount, @Param("offset") int offset,
			@Param("pageSize") int pageSize, @Param("msgState") int msgState);

	@Select("SELECT count(0) FROM `tblmessage` where `msgstate` = #{msgState} and `user` =#{userAccount} limit #{offset},#{pageSize}")
	public Integer listCount(@Param("userAccount") String userAccount, @Param("offset") int offset,
			@Param("pageSize") int pageSize, @Param("msgState") int msgState);
}