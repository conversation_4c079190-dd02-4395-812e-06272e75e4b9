package com.koron.zys.baseConfig.bean;

/**
 * 用料模板明细实体类
 *
 * <AUTHOR>
 */
public class UseMatrTemplateListBean extends BaseBean {

    /**
     * 模板ID
     */
    private String templateId;
    /**
     * 材料编号
     */
    private String matrNo;
    /**
     * 材料数量
     */
    private Integer matrNum;
    /**
     * 材料ID
     */
    private String matrId;
    /**
     * 材料名称
     */
    private String matrName;
    /**
     * 材料规格
     */
    private String matrMode;
    /**
     * 材料单位
     */
    private String matrUnit;
    /**
     * 材料单价
     */
    private Double matrPrice;
    
    /**
     * 材料总价
     */
    private Double matrMoney;
    /**
     * 备注
     */
    private String comments;
    

    public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getMatrNo() {
        return matrNo;
    }

    public void setMatrNo(String matrNo) {
        this.matrNo = matrNo;
    }

    public Integer getMatrNum() {
        return matrNum;
    }

    public void setMatrNum(Integer matrNum) {
        this.matrNum = matrNum;
    }

    public String getMatrId() {
        return matrId;
    }

    public void setMatrId(String matrId) {
        this.matrId = matrId;
    }

    public String getMatrName() {
        return matrName;
    }

    public void setMatrName(String matrName) {
        this.matrName = matrName;
    }

    public String getMatrMode() {
        return matrMode;
    }

    public void setMatrMode(String matrMode) {
        this.matrMode = matrMode;
    }

    public String getMatrUnit() {
        return matrUnit;
    }

    public void setMatrUnit(String matrUnit) {
        this.matrUnit = matrUnit;
    }

    public Double getMatrPrice() {
        return matrPrice;
    }

    public void setMatrPrice(Double matrPrice) {
        this.matrPrice = matrPrice;
    }

	public Double getMatrMoney() {
		return matrMoney;
	}

	public void setMatrMoney(Double matrMoney) {
		this.matrMoney = matrMoney;
	}
    
}
