webpackJsonp([18],{KpoN:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"taskMgtAdd"},[a("el-form",{ref:"formDatataskMgtAdd",staticClass:"formBill-One",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"150px",inline:!0}},[a("el-form-item",{attrs:{label:"任务名称：",prop:"jobName"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.jobName,callback:function(t){e.$set(e.ruleForm,"jobName",t)},expression:"ruleForm.jobName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"类名：",prop:"jobClassName"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.jobClassName,callback:function(t){e.$set(e.ruleForm,"jobClassName",t)},expression:"ruleForm.jobClassName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"cron表达式：",prop:"cronExpression"}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.cronExpression,callback:function(t){e.$set(e.ruleForm,"cronExpression",t)},expression:"ruleForm.cronExpression"}})],1)],1)],1)},staticRenderFns:[]};var i={name:"TaskMgt",components:{taskMgtAdd:a("VU/8")({name:"taskMgtAdd",data:function(){return{databaseData:[],ruleForm:{jobName:"",jobClassName:"",cronExpression:""},rules:{jobName:[{required:!0,message:"请输入任务名称",trigger:"blur"}],jobClassName:[{required:!0,message:"请输入类名",trigger:"blur"}],cronExpression:[{message:"请输入cron表达式",trigger:"blur",required:!0}]}}},mounted:function(){this.getData()},methods:{getData:function(){},submitForm:function(e,t){var a=this,s=this,i={};this.$refs[e].validate(function(e){if(!e)return!1;i="添加"===t?{busicode:"JobAdd",data:a.ruleForm}:{busicode:"JobUpdate",data:a.ruleForm},a.$api.fetch({params:i}).then(function(e){s.$message({showClose:!0,message:"保存成功",type:"success"}),s.$parent.getData(),s.$parent.closeDialog()})})},handleClose:function(){this.$parent.closeDialog()},editData:function(e){this.ruleForm=e}}},s,!1,function(e){a("lkCB")},null,null).exports},data:function(){return{tableShow:!1,tableQuery:{jobName:"",page:1,pageCount:50},maxHeight:0,selServicesData:{},taskMgtAddVisible:!1,TaskMgtShow:!0,crumbsData:{titleList:[{title:"系统管理",path:"/systemMan"},{title:"定时任务管理",method:function(){window.histroy.back()}}]},formData:{jobName:"",jobClassName:"",jobGroup:"",stateName:"",triggerState:"",cronExpression:"",preFireTime:"",nextFireTime:""},formDataService:{}}},mounted:function(){this.getData()},methods:{getData:function(){var e=this,t={busicode:"JobList",data:this.tableQuery};this.$api.fetch({params:t}).then(function(t){e.selServicesData=t,e.common.changeTable(e,".TaskMgt .kl-table",[".TaskMgt .toolbar",".TaskMgt .block"])})},search:function(){this.tableQuery.page=1,this.getData()},appAdd:function(e){var t=this;this.taskMgtAddVisible=!0,this.TaskMgtShow=!1,this.$nextTick(function(){if("add"===e)t.$set(t.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),t.$refs.taskMgtAdd.editData({jobName:"",jobClassName:"",cronExpression:""});else{t.$set(t.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var a=t;a.formDataService=e,a.$refs.taskMgtAdd.editData(a.formDataService,"edit")}})},handlePause:function(e,t){var a=this,s={busicode:"JobPause",data:t};this.$api.fetch({params:s}).then(function(e){a.$message({showClose:!0,message:"暂停成功",type:"success"})}),setTimeout(function(){a.getData()},2e3)},handleResume:function(e,t){var a=this,s={busicode:"JobResume",data:t};this.$api.fetch({params:s}).then(function(e){a.$message({showClose:!0,message:"恢复成功",type:"success"})}),setTimeout(function(){a.getData()},2e3)},handleDelete:function(e,t){var a=this,s={busicode:"JobDelete",data:t};this.$api.fetch({params:s}).then(function(e){a.$message({showClose:!0,message:"删除成功",type:"success"})}),setTimeout(function(){a.getData()},2e3)},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.getData()},handleCurrentChange:function(e){this.tableQuery.page=e,this.getData()},closeDialog:function(){this.TaskMgtShow=!0,this.taskMgtAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.taskMgtAddVisible=!1,this.TaskMgtShow=!0,this.crumbsData.titleList.pop()},close:function(){this.$refs.taskMgtAdd.handleClose()},submitForm:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.taskMgtAdd.submitForm(e,t)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"TaskMgt"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.TaskMgtShow,expression:"TaskMgtShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.taskMgtAddVisible,expression:"taskMgtAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("formDatataskMgtAdd")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.close}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.TaskMgtShow,expression:"TaskMgtShow"}],staticClass:"kl-table"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.tableQuery,size:"mini"}},[a("div",{staticClass:"toolbar-left"},[a("el-form-item",{attrs:{label:"任务名："}},[a("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.jobName,callback:function(t){e.$set(e.tableQuery,"jobName",t)},expression:"tableQuery.jobName"}})],1),e._v(" "),a("el-form-item",{staticClass:"button-group"},[a("el-button",{staticClass:"compile",attrs:{type:"primary el-icon-search",size:"mini"},on:{click:e.search}})],1)],1),e._v(" "),a("div",{staticClass:"toolbar-right"})])],1),e._v(" "),e.tableShow?a("el-table",{staticStyle:{width:"100%"},attrs:{stripe:"",center:"",border:"",data:e.selServicesData.list,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"jobName",label:"任务名称","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"jobGroup",label:"任务所在组"}}),e._v(" "),a("el-table-column",{attrs:{prop:"jobClassName",label:"任务类名"}}),e._v(" "),a("el-table-column",{attrs:{prop:"stateName",label:"状态"}}),e._v(" "),a("el-table-column",{attrs:{prop:"cronExpression",label:"表达式"}}),e._v(" "),a("el-table-column",{attrs:{prop:"nextFireTime",label:"下次执行时间"}}),e._v(" "),a("el-table-column",{attrs:{prop:"prevFireTime",label:"最后执行开始时间"}}),e._v(" "),a("el-table-column",{attrs:{prop:"executionEndTime",label:"最后执行结束时间"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"small",type:"warning"},on:{click:function(a){return e.handlePause(t.$index,t.row)}}},[e._v("暂停")]),e._v(" "),a("el-button",{attrs:{size:"small",type:"info"},on:{click:function(a){return e.handleResume(t.$index,t.row)}}},[e._v("恢复")]),e._v(" "),a("el-button",{attrs:{size:"small",type:"danger"},on:{click:function(a){return e.handleDelete(t.$index,t.row)}}},[e._v("删除")]),e._v(" "),a("el-button",{attrs:{size:"small",type:"success"},on:{click:function(a){return e.appAdd(t.row)}}},[e._v("修改")])]}}],null,!1,641857444)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.selServicesData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),e.taskMgtAddVisible?a("div",{staticClass:"taskAdd"},[a("taskMgtAdd",{ref:"taskMgtAdd"})],1):e._e()])])},staticRenderFns:[]};var o=a("VU/8")(i,l,!1,function(e){a("Rl0i")},null,null);t.default=o.exports},Rl0i:function(e,t){},lkCB:function(e,t){}});