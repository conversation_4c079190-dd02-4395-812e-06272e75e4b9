package com.koron.common.web.bean;

public class DBServerBean {
	
	private String driverName = "oracle.jdbc.driver.OracleDriver";
	
	public String getDriverName() {
		return driverName;
	}
	public void setDriverName(String driverName) {
		this.driverName = driverName;
	}
	/**
	 * 数据ip地址
	 */
	private String ipAddr;
	/**
	 * 端口
	 */
	private int port;
	/**
	 * 数据库名
	 */
	private String dbName;
	/**
	 * 用户名
	 */
	private String schema;
	/**
	 * 密码
	 */
	private String password;
	/**
	 * service_code，用户表示数据源
	 */
	private String serviceCode;
	
	public String getServiceCode() {
		return serviceCode;
	}
	public void setServiceCode(String serviceCode) {
		this.serviceCode = serviceCode;
	}
	public String getIpAddr() {
		return ipAddr;
	}
	public void setIpAddr(String ipAddr) {
		this.ipAddr = ipAddr;
	}
	public int getPort() {
		return port;
	}
	public void setPort(int port) {
		this.port = port;
	}
	public String getDbName() {
		return dbName;
	}
	public void setDbName(String dbName) {
		this.dbName = dbName;
	}
	public String getSchema() {
		return schema;
	}
	public void setSchema(String schema) {
		this.schema = schema;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	
	
}
