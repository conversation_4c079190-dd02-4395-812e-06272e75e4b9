<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.CostDetailMapper">

 	<!--列表初始化  -->
	<select id="selectCostDetailList" parameterType="com.koron.zys.baseConfig.queryBean.CostDetailQueryBean" resultType="com.koron.zys.baseConfig.vo.CostDetailVO" >
		select xx.cost_detail_id,xx.detail_name,cost.cost_name,xx.ladder_type ladderType,xx.person_base,xx.water_base,xx.comments,
			xx.status status ,  xx.calculate_unit calculateUnit
		from BASE_COST_DETAIL xx join BASE_COST cost on xx.COST_NAME=cost.COST_ID and cost.status=1
		   where 1=1	   
	    	<if test="status != null">
	    		and status = #{status} 
	    	</if>
	    	<if test="searchContent !=null and searchContent !=''">     
           AND (xx.detail_name || cost.cost_name )  like '%' || #{searchContent} || '%'
             </if>         	
	</select>

	
			<!--添加费用  -->
	 	<insert id="insertCostDetail" parameterType="com.koron.zys.baseConfig.bean.CostDetailBean">
		insert into BASE_COST_DETAIL(cost_detail_id,detail_name,cost_name, ladder_type,calculate_unit,
		person_base,water_base,status,comments,
		create_time, create_name)
		values
		(
		#{costDetailId,jdbcType=VARCHAR},
		#{detailName,jdbcType=VARCHAR},
		#{costName,jdbcType=VARCHAR},
		#{ladderType,jdbcType=INTEGER},
		#{calculateUnit,jdbcType=INTEGER},
		#{personBase,jdbcType=INTEGER},
		#{waterBase,jdbcType=INTEGER},
		#{status,jdbcType=INTEGER},
		#{comments,jdbcType=VARCHAR},
		date_format(#{createTime},'%Y-%m-%d %T'),
		#{createName,jdbcType=VARCHAR}
		)
	</insert>
		<!--修改费用  -->
		<update id="updateCostDetail" parameterType="com.koron.zys.baseConfig.bean.CostDetailBean">
		update BASE_COST_DETAIL
		set detail_name = #{detailName,jdbcType=VARCHAR},	
		cost_name = #{costName,jdbcType=VARCHAR},	
		ladder_type = #{ladderType,jdbcType=INTEGER},	
		calculate_unit = #{calculateUnit,jdbcType=INTEGER},	
		person_base = #{personBase,jdbcType=INTEGER},	
		water_base = #{waterBase,jdbcType=INTEGER},	
		status = #{status,jdbcType=INTEGER},	
		comments = #{comments,jdbcType=VARCHAR},
		update_time=date_format(#{updateTime},'%Y-%m-%d %T'),
		update_name = #{updateName,jdbcType=VARCHAR}
		where cost_detail_id = #{costDetailId}
	</update>

     <resultMap type="com.koron.zys.baseConfig.bean.CostDetailBean" id="CostDetailMap">
        <id column="cost_detail_id" property="costDetailId" jdbcType="VARCHAR" />
        <result column="detail_name" property="detailName" jdbcType="VARCHAR" />
        <result column="cost_name" property="costName" jdbcType="VARCHAR" />
        <result column="ladder_type" property="ladderType" javaType="INTEGER"/>
        <result column="calculate_unit" property="calculateUnit" jdbcType="INTEGER" />
        <result column="person_base" property="personBase" jdbcType="INTEGER" />
        <result column="water_base" property="waterBase" javaType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="comments" property="comments" jdbcType="VARCHAR" />
        <collection property="ladderlist" ofType="com.koron.zys.baseConfig.bean.CostDetailLadderBean">
            <id property="costDetailLadderId" column="cost_detail_ladder_id" jdbcType="VARCHAR"/> 
            <result property="costDetailId" column="cost_detail_id" jdbcType="VARCHAR"/>
            <result property="beginWater" column="begin_water" jdbcType="INTEGER"/>
            <result property="endWater" column="end_water" jdbcType="INTEGER"/>
            <result property="price" column="price" jdbcType="INTEGER"/>
            <result property="coefficient" column="coefficient" jdbcType="INTEGER"/>
            <result property="ladderBeginMoney" column="ladder_begin_money" jdbcType="INTEGER"/>
        </collection>
    </resultMap>
 	<!--编辑初始化  -->
     <select id="selectCostDetailById" resultMap="CostDetailMap" >       
        select 	
        a.cost_detail_id,
        a.detail_name,
        a.cost_name,
        a.ladder_type,
        a.calculate_unit,
        a.person_base,
        a.water_base,
        a.status,
        a.comments,
        b.cost_detail_ladder_id,
        b.cost_detail_id,
        b.begin_water,
        b.end_water,
        b.price,
        b.coefficient,
        b.ladder_begin_money
		from
		BASE_COST_DETAIL a
		left join  BASE_COST_DETAIL_LADDER b
		on a.cost_detail_id = b.cost_detail_id
		where a.cost_detail_id = #{costDetailId}
    </select>
    
        <!--批量添加List集合对象  -->
    <insert id="insertLadderList" parameterType="java.util.List" useGeneratedKeys="false"> 
    INSERT ALL 
    <foreach item="item" index="index" collection="ladderlist"> 
    INTO  BASE_COST_DETAIL_LADDER
    ( 
    cost_detail_ladder_id,cost_detail_id,begin_water,end_water,price,coefficient,ladder_begin_money
    ) VALUES 
    ( 
     #{item.costDetailLadderId,jdbcType=VARCHAR},
     #{item.costDetailId,jdbcType=VARCHAR},
     #{item.beginWater,jdbcType=INTEGER},
     #{item.endWater,jdbcType=INTEGER},
     #{item.price,jdbcType=INTEGER},
     #{item.coefficient,jdbcType=INTEGER},
     #{item.ladderBeginMoney,jdbcType=INTEGER}
    ) 
    </foreach> 
    SELECT 1 FROM DUAL 
  </insert> 

         <!--批量删除 从id集合List中删除多个对象  注意参数为集合 costDetailLadderIds-->
    <delete id="DeleteLadderList" parameterType="java.util.List">
        DELETE FROM
        BASE_COST_DETAIL_LADDER
        <where>
        cost_detail_ladder_id in
        <foreach collection="costDetailLadderIds" item="costDetailLadderId" index="index" open="(" close=")" separator=",">
            #{costDetailLadderId}
        </foreach>
        </where>
    </delete>
	
		<!--批量更新信息  -->
	<update id="updateLadderList" parameterType="java.util.List">
        <foreach collection="ladderlist" item="item" index="index" open="begin" close=";end;" separator=";">
            UPDATE BASE_COST_DETAIL_LADDER
            <set>
                <if test="item.beginWater != null">
                    begin_water = #{item.beginWater,jdbcType=INTEGER},
                </if>

                <if test="item.endWater != null">
                    end_water = #{item.endWater,jdbcType=INTEGER},
                </if>

                <if test="item.price != null">
                    price = #{item.price,jdbcType=INTEGER},
                </if>

                <if test="item.coefficient != null">
                    coefficient = #{item.coefficient,jdbcType=INTEGER},
                </if>

                <if test="item.ladderBeginMoney != null">
                    ladder_begin_money = #{item.ladderBeginMoney,jdbcType=INTEGER},
                </if>
            </set>
            where cost_detail_ladder_id = #{item.costDetailLadderId,jdbcType=VARCHAR}
        </foreach>
    </update>
    
      	<!--添加阶梯类型  -->
	 	<insert id="insertLadder" parameterType="com.koron.zys.baseConfig.bean.CostDetailLadderBean">
		insert into BASE_COST_DETAIL_LADDER(  
		cost_detail_ladder_id,cost_detail_id,begin_water,end_water,price,coefficient,ladder_begin_money
		)
		values
		(
		 #{costDetailLadderId},
         #{costDetailId},
         #{beginWater},
         #{endWater},
         #{price},
         #{coefficient},
         #{ladderBeginMoney}
		)
	</insert>
    <!--修改阶梯类型信息 -->
	<update id="updateLadder" parameterType="com.koron.zys.baseConfig.bean.CostDetailLadderBean" >
		update BASE_COST_DETAIL_LADDER
		set
		 begin_water = #{beginWater},	
		 end_water = #{endWater},
		 price = #{price},	
		 coefficient = #{coefficient},
	     ladder_begin_money = #{ladderBeginMoney}
		 where cost_detail_ladder_id = #{costDetailLadderId}
	</update>

	
    	<!--删除阶梯信息  -->
    <delete id="delLadder" >
		delete from BASE_COST_DETAIL_LADDER where cost_detail_ladder_id = #{costDetailLadderId}
	</delete>

    

</mapper>