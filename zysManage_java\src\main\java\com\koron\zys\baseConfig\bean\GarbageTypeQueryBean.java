package com.koron.zys.baseConfig.bean;

import com.koron.common.bean.query.BaseQueryBean;

public class GarbageTypeQueryBean extends BaseQueryBean{
	private String garbageTypeName;
	private String garbageTypeValue;
	private String status;
	public String getGarbageTypeName() {
		return garbageTypeName;
	}
	public void setGarbageTypeName(String garbageTypeName) {
		this.garbageTypeName = garbageTypeName;
	}
	public String getGarbageTypeValue() {
		return garbageTypeValue;
	}
	public void setGarbageTypeValue(String garbageTypeValue) {
		this.garbageTypeValue = garbageTypeValue;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	
	
}
