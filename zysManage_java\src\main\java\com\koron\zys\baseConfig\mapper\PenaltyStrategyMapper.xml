<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.PenaltyStrategyMapper">

	<select id="selectPenaltyList" parameterType="com.koron.zys.baseConfig.queryBean.PenaltyQueryBean" resultType="com.koron.zys.baseConfig.vo.PenaltyVO" >
		select x.id,y.WATER_TYPE_NO,y.WATER_TYPE_NAME,x.REMARK,x.EFFECTIVE_DATE,x.`STATUS`
		from base_penalty x join base_water_type y on x.water_type_id = y.id
		where 1=1
		<if test="status != null and status !=''">
			AND x.STATUS = #{status}
		</if>
		<if test="waterTypeName !=null and waterTypeName !=''">
			AND y.WATER_TYPE_NAME  LIKE concat('%',#{waterTypeName},'%')
		</if>
		<if test="code != null and code != ''">
			AND y.water_type_no like concat(#{code},'%')
		</if>
		ORDER BY x.effective_date DESC
	</select>
	<select id="selectPenaltyById" resultType="com.koron.zys.baseConfig.bean.PenaltyBean">
		SELECT * FROM BASE_PENALTY WHERE ID = #{id}
	</select>
	<select id="selectPenaltyStrategyListByPenaltyId" resultType="com.koron.zys.baseConfig.bean.PenaltyStrategyBean">
		select *
		from BASE_PENALTY_STRATEGY
		where penalty_id = #{penaltyId}
	</select>

	<select id="selectPenaltyStrategyById" resultType="com.koron.zys.baseConfig.bean.PenaltyStrategyBean">
		select *,id as penaltyId
		from BASE_PENALTY_STRATEGY
		where id = #{id}
	</select>

	<insert id="insertPenalty" parameterType="com.koron.zys.baseConfig.bean.PenaltyBean">
		insert into base_penalty(id,water_type_id,effective_date,`status`,remark,create_time,create_name,create_account)
		values(#{id},#{waterTypeId},#{effectiveDate},#{status},#{remark},now(),#{createName},#{createAccount})
	</insert>

	<insert id="insertPenaltyStrategy" parameterType="com.koron.zys.baseConfig.bean.PenaltyStrategyBean">
		insert into BASE_PENALTY_STRATEGY (
			id,penalty_id,cost_id,strategy_name,min_calculate_money,
			decimal_placse,min_penalty_money,max_penalty_money,
			daily_scaling,max_scaling,calculate_balance,
			calculate_way,calculate_value,vacation_flag,
			comments,status, create_time, create_name, CHARGE_WAY, CALCULATE_VALUE2)
		values
		(	#{id},
			 #{penaltyId},
			 #{costId},
			 #{strategyName},
			 #{minCalculateMoney},
			 #{decimalPlacse},
			 #{minPenaltyMoney},
			 #{maxPenaltyMoney},
			 #{dailyScaling},
			 #{maxScaling},
			 #{calculateBalance},
			 #{calculateWay},
			 #{calculateValue},
			 #{vacationFlag},
			 #{comments},
			 #{status},
			 now(),
			 #{createName,jdbcType=VARCHAR},
			 #{chargeWay},
			 #{calculateValue2}
		)
	</insert>

	<update id="updatePenalty" parameterType="com.koron.zys.baseConfig.bean.PenaltyBean">
		update base_penalty
		set water_type_id = #{waterTypeId},
			effective_date = #{effectiveDate},
			status = #{status},
			remark = #{remark}
		where id = #{id}
	</update>

	<update id="updatePenaltyStrategy" parameterType="com.koron.zys.baseConfig.bean.PenaltyStrategyBean">
		update BASE_PENALTY_STRATEGY
		set
			strategy_name = #{strategyName},
			min_calculate_money = #{minCalculateMoney},
			decimal_placse = #{decimalPlacse},
			min_penalty_money = #{minPenaltyMoney},
			max_penalty_money = #{maxPenaltyMoney},
			daily_scaling = #{dailyScaling},
			max_scaling = #{maxScaling},
			calculate_balance = #{calculateBalance},
			calculate_way = #{calculateWay},
			calculate_value = #{calculateValue},
			vacation_flag = #{vacationFlag},
			comments = #{comments},
			status = #{status},
			update_time=date_format(#{updateTime},'%Y-%m-%d %T'),
			update_name = #{updateName},
			charge_way = #{chargeWay},
			calculate_value2 = #{calculateValue2}
		where id = #{id}
	</update>

	<!--查询所有的已启用的违约金名称 -->
	<select id="selectStrategyNameList"  resultType="com.koron.zys.baseConfig.vo.SelectVO">
		select xx.strategy_name name,xx.id
		from BASE_PENALTY_STRATEGY xx
		where status = 1
	</select>


</mapper>