package com.koron.zys.baseConfig.bean;

import java.math.BigDecimal;

/**
 * 考核指标
 * <AUTHOR>
 *
 */
public class PubKpiBean extends BaseBean{
	
	/**指标维键*/
	private	String	kpiUnique;
	/**指标名称*/
	private	String	kpiName;
	/**指标描述*/
	private	String	kpiComments;
	/**指标级别*/
	private	BigDecimal	kpiLevel;
	/**所属子主题维键*/
	private	String	subThemeUnique;
	/**所属子主题名称*/
	private	String	subThemeName;
	/**所属主题维建*/
	private	String	themeUnique;
	/**所属主题名称*/
	private	String	themeName;
	/**单位*/
	private	String	unit;
	/**备注*/
	private	String	comments;
	/**状态*/
	private	Integer	status;
	public String getKpiUnique() {
		return kpiUnique;
	}
	public String getKpiName() {
		return kpiName;
	}
	public String getKpiComments() {
		return kpiComments;
	}
	public BigDecimal getKpiLevel() {
		return kpiLevel;
	}
	public String getSubThemeUnique() {
		return subThemeUnique;
	}
	public String getSubThemeName() {
		return subThemeName;
	}
	public String getThemeUnique() {
		return themeUnique;
	}
	public String getThemeName() {
		return themeName;
	}
	public String getUnit() {
		return unit;
	}
	public String getComments() {
		return comments;
	}
	public Integer getStatus() {
		return status;
	}
	public void setKpiUnique(String kpiUnique) {
		this.kpiUnique = kpiUnique;
	}
	public void setKpiName(String kpiName) {
		this.kpiName = kpiName;
	}
	public void setKpiComments(String kpiComments) {
		this.kpiComments = kpiComments;
	}
	public void setKpiLevel(BigDecimal kpiLevel) {
		this.kpiLevel = kpiLevel;
	}
	public void setSubThemeUnique(String subThemeUnique) {
		this.subThemeUnique = subThemeUnique;
	}
	public void setSubThemeName(String subThemeName) {
		this.subThemeName = subThemeName;
	}
	public void setThemeUnique(String themeUnique) {
		this.themeUnique = themeUnique;
	}
	public void setThemeName(String themeName) {
		this.themeName = themeName;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
}
