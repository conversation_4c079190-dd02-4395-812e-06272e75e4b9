webpackJsonp([32],{"+fSB":function(a,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o={render:function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("div",{staticClass:"AlipayConfigEdit"},[e("el-form",{ref:"AlipayConfigEditForm",staticClass:"formBill-Two",attrs:{inline:!0,size:"mini",model:a.formData,"label-width":"130px"}},[e("el-form-item",{attrs:{label:"水司编号：",prop:"companyNo"}},[e("el-select",{attrs:{placeholder:"请选择水司编号"},model:{value:a.formData.companyNo,callback:function(t){a.$set(a.formData,"companyNo",t)},expression:"formData.companyNo"}},a._l(a.CompanyNoData,function(a){return e("el-option",{key:a.companyNo,attrs:{label:a.companyNo,value:a.companyNo}})}),1)],1),a._v(" "),e("el-form-item",{attrs:{label:"清算单位编号：",prop:"orgNo"}},[e("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入机构清算单位编号"},model:{value:a.formData.orgNo,callback:function(t){a.$set(a.formData,"orgNo",t)},expression:"formData.orgNo"}})],1),a._v(" "),e("el-form-item",{attrs:{label:"机构英文名：",prop:"orgName"}},[e("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入机构英文名"},model:{value:a.formData.orgName,callback:function(t){a.$set(a.formData,"orgName",t)},expression:"formData.orgName"}})],1),a._v(" "),e("el-form-item",{attrs:{label:"机构英文简称：",prop:"orgEnShortName"}},[e("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入机构英文简称"},model:{value:a.formData.orgEnShortName,callback:function(t){a.$set(a.formData,"orgEnShortName",t)},expression:"formData.orgEnShortName"}})],1),a._v(" "),e("el-form-item",{attrs:{label:"是否自由缴：",prop:"freePay"}},[e("el-select",{attrs:{placeholder:"请选择是否自由缴"},model:{value:a.formData.freePay,callback:function(t){a.$set(a.formData,"freePay",t)},expression:"formData.freePay"}},[e("el-option",{attrs:{label:"是",value:1}}),a._v(" "),e("el-option",{attrs:{label:"否",value:0}})],1)],1),a._v(" "),e("el-form-item",{attrs:{label:"SFTP地址：",prop:"sftpIp"}},[e("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入SFTP地址"},model:{value:a.formData.sftpIp,callback:function(t){a.$set(a.formData,"sftpIp",t)},expression:"formData.sftpIp"}})],1),a._v(" "),e("el-form-item",{attrs:{label:"SFTP端口：",prop:"sftpPort"}},[e("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入SFTP端口"},model:{value:a.formData.sftpPort,callback:function(t){a.$set(a.formData,"sftpPort",t)},expression:"formData.sftpPort"}})],1),a._v(" "),e("el-form-item",{attrs:{label:"SFTP用户：",prop:"sftpUserName"}},[e("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入SFTP用户"},model:{value:a.formData.sftpUserName,callback:function(t){a.$set(a.formData,"sftpUserName",t)},expression:"formData.sftpUserName"}})],1),a._v(" "),e("el-form-item",{attrs:{label:"SFTP密码：",prop:"sftpUserPsw"}},[e("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入SFTP密码"},model:{value:a.formData.sftpUserPsw,callback:function(t){a.$set(a.formData,"sftpUserPsw",t)},expression:"formData.sftpUserPsw"}})],1),a._v(" "),e("el-form-item",{attrs:{label:"出账最后日期：",prop:"lastDate"}},[e("el-input",{attrs:{maxlength:"50",clearable:"",readonly:!0},model:{value:a.formData.lastDate,callback:function(t){a.$set(a.formData,"lastDate",t)},expression:"formData.lastDate"}})],1),a._v(" "),e("el-form-item",{attrs:{label:"出账规则：",prop:"ruleType"}},[e("el-select",{attrs:{placeholder:"请选择出账规则"},model:{value:a.formData.ruleType,callback:function(t){a.$set(a.formData,"ruleType",t)},expression:"formData.ruleType"}},[e("el-option",{attrs:{label:"开账后下个月",value:1}}),a._v(" "),e("el-option",{attrs:{label:"开账后第n天",value:2}})],1)],1),a._v(" "),e("el-form-item",{attrs:{label:"规则参考值：",prop:"ruleValue"}},[e("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入规则参考值"},model:{value:a.formData.ruleValue,callback:function(t){a.$set(a.formData,"ruleValue",t)},expression:"formData.ruleValue"}})],1)],1)],1)},staticRenderFns:[]};var r={components:{AlipayConfigEdit:e("VU/8")({name:"AlipayConfigEdit",data:function(){return{formData:{id:"",companyNo:"",orgNo:"",orgName:"",orgEnShortName:"",freePay:"",sftpIp:"",sftpPort:"",sftpUserName:"",sftpUserPsw:"",lastDate:"",ruleType:"",ruleValue:""},CompanyNoData:[]}},mounted:function(){this.getData()},methods:{getData:function(){var a=this;this.$api.fetch({params:{busicode:"AlipayCompanyNoData"}}).then(function(t){a.$set(a,"CompanyNoData",t)})},resetForm:function(){this.$refs.AlipayConfigEditForm.resetFields()},submitForm:function(a){var t=this,e={};e="AlipayConfigAdd"===a?{busicode:"AlipayConfigAdd",data:this.formData}:{busicode:"AlipayConfigUpdate",data:this.formData},this.$api.fetch({params:e}).then(function(a){t.$message({showClose:!0,message:"保存成功",type:"success"})})},editData:function(a){this.formData=a}}},o,!1,function(a){e("sbli")},"data-v-1abd07d8",null).exports,autoTree:e("yJVD").a},name:"AlipayConfig",data:function(){return{EditVisible:!1,formData:{id:"",companyNo:"",orgNo:"",orgName:"",orgEnShortName:"",freePay:"",sftpIp:"",sftpPort:"",sftpUserName:"",lastDate:"",ruleType:"",ruleValue:""},treeDatas:{tree:[{shortName:"根目录",id:"2",companyNo:"",children:[]}],defaultProps:{label:"shortName",children:"children"},inputProp:{showSearch:!1,Inp_placeholder:"请输入节点"},sendTreeProp:["code","shortName","districtArr","children","companyNo","group","isLeaf","isParent","parent","sonData"],rootName:"根目录",defaultOpen:{nodeKey:"id"}},crumbsData:{titleList:[{title:"水司配置",path:"/waterSet"},{title:"支付宝配置",method:function(){window.histroy.back()}}]},maxHeight:0,tableShow:!1,tableData:[],tableQuery:{page:1,pageCount:10,companyNo:"00",orgName:""},formType:"",CompanyNoData:[]}},mounted:function(){this.getTreeDatas(),this.getData()},methods:{init:function(){var a=this,t=this,e={busicode:"AlipayConfigList",data:this.tableQuery};this.$api.fetch({params:e}).then(function(e){if(t.tableData=e,0==(e=e.list).length)a.formType="AlipayConfigAdd";else{a.formType="AlipayConfigUpdate";var o={busicode:"AlipayConfigQuery",data:{id:e[0].id}};a.$api.fetch({params:o}).then(function(t){a.$refs.AlipayConfigEdit.editData(t)})}})},getTreeDatas:function(){var a=this,t=this;this.$api.fetch({params:{busicode:"CompanyNameList",data:{}}}).then(function(e){t.treeDatas.tree[0].children=e,t.tableQuery.companyNo=e[0].companyNo,a.$set(a.crumbsData.titleList,"2",{title:e[0].shortName,method:function(){window.histroy.back()}})})},backTreeData:function(a){if(this.$refs.AlipayConfigEdit.resetForm(),"根目录"!==a.shortName){this.tableQuery.companyNo=a.companyNo,this.$set(this.crumbsData.titleList,"2",{title:a.shortName,method:function(){window.histroy.back()}}),this.init()}},getData:function(){var a=this;this.$api.fetch({params:{busicode:"AlipayCompanyNoData"}}).then(function(t){a.$set(a,"CompanyNoData",t)})},submitForm:function(){this.$refs.AlipayConfigEdit.submitForm(this.formType)}}},l={render:function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("div",{staticClass:"AlipayConfig"},[e("div",{staticClass:"bread-contain"},[e("publicCrumbs",{attrs:{crumbsData:a.crumbsData}}),a._v(" "),e("div",{staticClass:"bread-contain-right"},[e("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return a.submitForm()}}},[a._v("保存")])],1)],1),a._v(" "),e("div",{staticClass:"company-content"},[e("div",{staticClass:"company-left"},[e("auto-tree",{attrs:{treeData:a.treeDatas},on:{sendTreeData:a.backTreeData}})],1),a._v(" "),e("div",{staticClass:"kl-table company-right"},[e("AlipayConfigEdit",{ref:"AlipayConfigEdit"})],1)])])},staticRenderFns:[]};var i=e("VU/8")(r,l,!1,function(a){e("Quz1")},null,null);t.default=i.exports},Quz1:function(a,t){},sbli:function(a,t){}});