package com.koron.common.web.dto;

import com.koron.common.web.bean.DepartmentBean;

public class DepartmentTreeDto extends DepartmentBean{
	/**
	 * 分级编码
	 */
	private String seq;
	/**
	 * 父级掩位数
	 */
	private Integer parentmask;
	
	public String getSeq() {
		return seq;
	}
	public Integer getParentmask() {
		return parentmask;
	}
	public void setSeq(String seq) {
		this.seq = seq;
	}
	public void setParentmask(Integer parentmask) {
		this.parentmask = parentmask;
	}
}
