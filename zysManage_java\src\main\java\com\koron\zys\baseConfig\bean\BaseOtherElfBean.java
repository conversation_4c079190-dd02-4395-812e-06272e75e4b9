package com.koron.zys.baseConfig.bean;

import org.bson.types.ObjectId;

import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;

public class BaseOtherElfBean extends BaseBean{
	
	//通知类型
	private String noticeType;
	//通知类型名称
	private String noticeTypeName;
	//通知人员
	private String noticeStaff;
	
	public String getNoticeType() {
		return noticeType;
	}
	public void setNoticeType(String noticeType) {
		this.noticeType = noticeType;
	}
	public String getNoticeTypeName() {
		return noticeTypeName;
	}
	public void setNoticeTypeName(String noticeTypeName) {
		this.noticeTypeName = noticeTypeName;
	}
	public String getNoticeStaff() {
		return noticeStaff;
	}
	public void setNoticeStaff(String noticeStaff) {
		this.noticeStaff = noticeStaff;
	}
	public BaseOtherElfBean(String noticeType, String noticeStaff,UserInfoBean userinfo) {
		super();
		this.noticeType = noticeType;
		this.noticeStaff = noticeStaff;
		this.setId(new ObjectId().toHexString());
		this.setCreateName(userinfo.getUserInfo().getName());
		this.setCreateTime(CommonUtils.getCurrentTime());
	}
	public BaseOtherElfBean() {
		
	}
}	
