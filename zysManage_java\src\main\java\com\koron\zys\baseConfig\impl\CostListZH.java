package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CostBean;
import com.koron.zys.baseConfig.mapper.CostMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.log4j.Logger;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 费用名称 lch
 * 综合水费 +其他费用类型 列表
 */
public class CostListZH implements ServerInterface {
    private static Logger log = Logger.getLogger(CostListZH.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
        try{
            CostMapper mapper = factory.getMapper(CostMapper.class);

            List<CostBean> result = new ArrayList();
            CostBean zh = new CostBean();
            zh.setCostNo("zh");
            zh.setCostName("综合水费");
            result.add(zh);
            //查询非综合水费
            List<CostBean> notZhList = mapper.selectCostForNotCompreHensive();
            if(CollectionUtils.isNotEmpty(notZhList))
                result.addAll(notZhList);
            info.setData(result);
            return info;
        }catch(Exception e) {
            log.error("查询费用列表失败",e);
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription("查询费用列表失败");
            return info;
        }
    }
}
