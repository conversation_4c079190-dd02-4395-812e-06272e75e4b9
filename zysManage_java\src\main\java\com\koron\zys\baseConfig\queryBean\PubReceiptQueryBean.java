package com.koron.zys.baseConfig.queryBean;

import com.koron.common.bean.query.BaseQueryBean;

public class PubReceiptQueryBean extends BaseQueryBean{
	
	private String receiptId;
	
	private String receiptNo;
	
	private String receiptName;
	
	private String processCode;
	
	private String prefix;
	
	private String companyNo;
	private String startAccess;
	
	public String getPrefix() {
		return prefix;
	}

	public void setPrefix(String prefix) {
		this.prefix = prefix;
	}

	public String getCompanyNo() {
		return companyNo;
	}

	public void setCompanyNo(String companyNo) {
		this.companyNo = companyNo;
	}

	public String getReceiptId() {
		return receiptId;
	}

	public void setReceiptId(String receiptId) {
		this.receiptId = receiptId;
	}

	public String getReceiptNo() {
		return receiptNo;
	}

	public String getReceiptName() {
		return receiptName;
	}

	public String getProcessCode() {
		return processCode;
	}

	public void setReceiptNo(String receiptNo) {
		this.receiptNo = receiptNo;
	}

	public void setReceiptName(String receiptName) {
		this.receiptName = receiptName;
	}

	public void setProcessCode(String processCode) {
		this.processCode = processCode;
	}

	public String getStartAccess() {
		return startAccess;
	}

	public void setStartAccess(String startAccess) {
		this.startAccess = startAccess;
	}
	
}
