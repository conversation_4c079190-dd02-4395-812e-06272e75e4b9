package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterModelBean;
import com.koron.zys.baseConfig.mapper.MeterModelMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

/**
 * 水表型号-添加
 *
 * <AUTHOR>
 */
public class MeterModelAdd implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MeterModelAdd.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            MeterModelMapper mapper = factory.getMapper(MeterModelMapper.class);
            MeterModelBean bean = JsonUtils.objectToPojo(req.getData(), MeterModelBean.class);
            // 校验字段重复
            if (mapper.check("model_name", bean.getModelName()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "水表型号名称：" + bean.getModelName() + "的信息已存在。", void.class);
            }
            // 校验字段重复
            if (mapper.check("model_no", bean.getModelNo()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "水表型号编号：" + bean.getModelNo() + "的信息已存在。", void.class);
            }
            bean.setCreateInfo(userInfo);
            mapper.insertMeterModel(bean);
        } catch (Exception e) {
            logger.error("水表型号添加失败", e);
            return MessageBean.create(Constant.ILLEGAL_PARAMETER, "水表型号添加失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}