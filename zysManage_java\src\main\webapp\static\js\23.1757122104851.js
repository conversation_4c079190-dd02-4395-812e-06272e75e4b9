webpackJsonp([23],{"7svc":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"userInfoAdd"},[a("el-form",{ref:"userInfoAddForm",staticClass:"formBill-Two",attrs:{model:e.modelData,rules:e.rules,"label-width":"100px",inline:!0}},[a("el-form-item",{attrs:{label:"字段名称：",prop:"fieldName"}},[a("el-input",{attrs:{clearable:""},model:{value:e.modelData.fieldName,callback:function(t){e.$set(e.modelData,"fieldName",t)},expression:"modelData.fieldName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"字段标识：",prop:"fieldCode"}},[a("el-input",{attrs:{clearable:""},model:{value:e.modelData.fieldCode,callback:function(t){e.$set(e.modelData,"fieldCode",t)},expression:"modelData.fieldCode"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"占位宽：",prop:"length"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.modelData.length,callback:function(t){e.$set(e.modelData,"length",t)},expression:"modelData.length"}},[a("el-option",{attrs:{label:"一个字段宽度",value:1}}),e._v(" "),a("el-option",{attrs:{label:"二个字段宽度",value:2}}),e._v(" "),a("el-option",{attrs:{label:"三个字段宽度",value:3}}),e._v(" "),a("el-option",{attrs:{label:"四个字段宽度",value:4}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"字段类型：",prop:"fieldType"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.modelData.fieldType,callback:function(t){e.$set(e.modelData,"fieldType",t)},expression:"modelData.fieldType"}},[a("el-option",{attrs:{label:"日期",value:1}}),e._v(" "),a("el-option",{attrs:{label:"输入",value:2}}),e._v(" "),a("el-option",{attrs:{label:"枚举",value:3}}),e._v(" "),a("el-option",{attrs:{label:"接口",value:4}}),e._v(" "),a("el-option",{attrs:{label:"级联菜单",value:5}}),e._v(" "),a("el-option",{attrs:{label:"双框输入",value:6}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"接口名称：",prop:"interfaceName"}},[a("el-input",{attrs:{clearable:""},model:{value:e.modelData.interfaceName,callback:function(t){e.$set(e.modelData,"interfaceName",t)},expression:"modelData.interfaceName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"分组：",prop:"group"}},[a("el-input",{attrs:{clearable:""},model:{value:e.modelData.group,callback:function(t){e.$set(e.modelData,"group",t)},expression:"modelData.group"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"页面名称：",prop:"pageName"}},[a("el-select",{model:{value:e.modelData.pageName,callback:function(t){e.$set(e.modelData,"pageName",t)},expression:"modelData.pageName"}},e._l(e.dictionaryData.YMMC,function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"状态：",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.modelData.status,callback:function(t){e.$set(e.modelData,"status",t)},expression:"modelData.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),a("el-option",{attrs:{label:"停用",value:0}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否必填：",prop:"mandatory"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.modelData.mandatory,callback:function(t){e.$set(e.modelData,"mandatory",t)},expression:"modelData.mandatory"}},[a("el-option",{attrs:{label:"是",value:1}}),e._v(" "),a("el-option",{attrs:{label:"否",value:0}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"是否可编辑：",prop:"editable"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.modelData.editable,callback:function(t){e.$set(e.modelData,"editable",t)},expression:"modelData.editable"}},[a("el-option",{attrs:{label:"是",value:1}}),e._v(" "),a("el-option",{attrs:{label:"否",value:0}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"排序：",prop:"sortNo"}},[a("el-input-number",{attrs:{min:1},model:{value:e.modelData.sortNo,callback:function(t){e.$set(e.modelData,"sortNo",t)},expression:"modelData.sortNo"}})],1)],1)],1)},staticRenderFns:[]};var o={name:"userInfo",components:{userInfoAdd:a("VU/8")({name:"userInfoAdd",data:function(){return{dictionaryData:[],businessData:[],modelData:{id:"",pageName:"",fieldName:"",fieldCode:"",sortNo:"",length:"",fieldType:"",interfaceName:"",group:"",status:1,groupCode:"",mandatory:0,editable:0},formData:{id:"",fieldName:"",pageName:"",fieldCode:"",sortNo:"",length:"",fieldType:"",interfaceName:"",group:"",status:1,groupCode:"",mandatory:0,editable:0},rules:{fieldName:[{required:!0,message:"请输入字段名称",trigger:"blur"}],fieldCode:[{required:!0,message:"请输入字段标识",trigger:"blur"}],length:[{required:!0,message:"请选择占位宽",trigger:"change"}],fieldType:[{required:!0,message:"请选择字段类型",trigger:"change"}],pageName:[{required:!0,message:"请输入页面名称",trigger:"blur"}],group:[{required:!0,message:"请输入分组",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}],mandatory:[{required:!0,message:"请选择是否必填",trigger:"change"}],editable:[{required:!0,message:"请选择是否可编辑",trigger:"change"}],sortNo:[{required:!0,message:"请输入排序",trigger:"blur"}]}}},mounted:function(){this.getDictionary()},methods:{resetForm:function(){this.$refs.userInfoAddForm.resetFields()},submitForm:function(e,t,a){var l=this,o=this,i={};this.$refs[e].validate(function(e){if(!e)return!1;o.modelData=o.common.handleData(o.modelData,o.formData),i="添加"===t?{busicode:"UserFieldConfigAdd",data:l.modelData}:{busicode:"UserFieldConfigUpdate",data:l.modelData},l.$api.fetch({params:i}).then(function(e){o.$message({showClose:!0,message:"保存成功",type:"success"}),o.$parent.getData(),o.$parent.closeDialog()})})},getDictionary:function(){var e=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"YMMC"}}).then(function(t){e.dictionaryData=t})},handleClose:function(){this.common.chargeObjectEqual(this,this.modelData,"get","userInfoAdd",this.$parent.closeDialog)},editData:function(e){this.modelData=e}}},l,!1,function(e){a("uJEr")},null,null).exports,autoTree:a("yJVD").a},data:function(){return{dictionaryData:[],printStyleShow:!1,tableShow:!0,maxHeight:0,selServicesData:{},tableQuery:{fieldName:"",fieldCode:"",groupCode:"",group:"",pageName:"",page:1,pageCount:50},crumbsData:{titleList:[{title:"水司配置",path:"/waterSet"},{title:"用户资料字段",method:function(){window.histroy.back()}},{title:"根目录",method:function(){window.histroy.back()}}]},userInfoShow:!0,userInfoAddVisible:!1,treeDatas:{tree:[{shortName:"根目录",id:"2",children:[]}],defaultProps:{label:"shortName",children:"children"},inputProp:{showSearch:!1,Inp_placeholder:"请输入节点"},sendTreeProp:["code","shortName","districtArr","children","companyNo","group","isLeaf","isParent","parent","sonData"],rootName:"根目录",defaultOpen:{nodeKey:"id"}},treeParantId:"",ruleForm:{printStyle:""}}},mounted:function(){this.common.changeTable(this,".userInfo .company-content",[".userInfo .toolbar"]),this.getTreeDatas(),this.getDictionary()},methods:{appAdd:function(e){var t=this;if(this.userInfoShow=!1,this.userInfoAddVisible=!0,"add"===e){var a={id:"",fieldName:"",fieldCode:"",sortNo:"",length:"",fieldType:"",interfaceName:"",group:"",pageName:"",status:1,groupCode:this.tableQuery.groupCode,mandatory:0,editable:0};this.$refs.userInfoAdd.editData(a),this.$set(this.crumbsData.titleList,"3",{title:"添加",method:function(){window.histroy.back()}}),this.common.chargeObjectEqual(this,a,"set","userInfoAdd")}else{this.$set(this.crumbsData.titleList,"3",{title:"编辑",method:function(){window.histroy.back()}});var l=this,o={busicode:"UserFieldConfigQuery",data:{id:e.row.id}};this.$api.fetch({params:o}).then(function(e){e.groupCode=l.tableQuery.groupCode,t.$refs.userInfoAdd.editData(e),t.common.chargeObjectEqual(t,e,"set","userInfoAdd")})}},getDictionary:function(){var e=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"YMMC"}}).then(function(t){e.dictionaryData=t})},search:function(){this.tableQuery.page=1,this.getData()},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.getData()},handleCurrentChange:function(e){this.tableQuery.page=e,this.getData()},getData:function(){var e=this,t={busicode:"UserFieldConfigList",data:this.tableQuery};this.$api.fetch({params:t}).then(function(t){e.selServicesData=t}).catch(function(e){})},getTreeDatas:function(){var e=this,t=this;this.$api.fetch({params:{busicode:"CompanyNameList",data:{}}}).then(function(a){t.treeDatas.tree[0].children=a,t.tableQuery.groupCode=a[0].companyNo,e.$set(e.crumbsData.titleList,"2",{title:a[0].shortName,method:function(){window.histroy.back()}}),t.getData()})},closeDialog:function(){this.userInfoShow=!0,this.userInfoAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.userInfoAdd.handleClose()},close:function(){this.printStyleShow=!1},backTreeData:function(e){this.tableQuery.groupCode=e.companyNo,this.$set(this.crumbsData.titleList,"2",{title:e.shortName,method:function(){window.histroy.back()}}),this.getData()},submitForm:function(e){var t=this.crumbsData.titleList[3].title;this.$refs.userInfoAdd.submitForm(e,t,this.tableQuery.groupCode)},del:function(e){var t=this,a=this;this.$confirm("此操作将删除“"+e.fieldName+"”字段， 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var l={busicode:"UserFieldConfigDelete",data:{id:e.id}};t.$api.fetch({params:l}).then(function(e){a.$message({type:"success",message:"删除成功！"}),a.getData()})}).catch(function(){t.$message({type:"info",message:"已取消删除"})})}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},i={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"userInfo"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.userInfoShow,expression:"userInfoShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.userInfoAddVisible,expression:"userInfoAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("userInfoAddForm")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.userInfoShow,expression:"userInfoShow"}],staticClass:"company-content"},[a("div",{staticClass:"company-left"},[a("auto-tree",{attrs:{treeData:e.treeDatas},on:{sendTreeData:e.backTreeData}})],1),e._v(" "),a("div",{staticClass:"kl-table company-right"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.tableQuery,size:"mini"}},[a("div",{staticClass:"toolbar-left"},[a("el-form-item",{attrs:{label:"字段名称："}},[a("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.fieldName,callback:function(t){e.$set(e.tableQuery,"fieldName",t)},expression:"tableQuery.fieldName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"字段标识："}},[a("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.fieldCode,callback:function(t){e.$set(e.tableQuery,"fieldCode",t)},expression:"tableQuery.fieldCode"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"分组："}},[a("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.group,callback:function(t){e.$set(e.tableQuery,"group",t)},expression:"tableQuery.group"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"页面名称："}},[a("el-select",{attrs:{clearable:""},model:{value:e.tableQuery.pageName,callback:function(t){e.$set(e.tableQuery,"pageName",t)},expression:"tableQuery.pageName"}},e._l(e.dictionaryData.YMMC,function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{staticClass:"button-group"},[a("el-button",{staticClass:"compile",attrs:{type:"primary el-icon-search",size:"mini"},on:{click:e.search}})],1)],1),e._v(" "),a("div",{staticClass:"toolbar-right"})])],1),e._v(" "),e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.selServicesData.list,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"fieldName",label:"字段名称",width:"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"fieldCode",label:"字段标识"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sortNo",label:"排序号"}}),e._v(" "),a("el-table-column",{attrs:{prop:"length",label:"占位宽"}}),e._v(" "),a("el-table-column",{attrs:{prop:"fieldType",label:"字段类型"}}),e._v(" "),a("el-table-column",{attrs:{prop:"interfaceName",label:"接口名称","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"groupCode",label:"水司编号"}}),e._v(" "),a("el-table-column",{attrs:{prop:"group",label:"分组"}}),e._v(" "),a("el-table-column",{attrs:{prop:"pageName",label:"页面名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"status",label:"状态"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.appAdd(t)}}},[e._v("编辑")]),e._v(" "),a("span",{staticStyle:{color:"#e6e6e6"}},[e._v("|")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.del(t.row)}}},[e._v("删除")])]}}],null,!1,992652659)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.selServicesData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)]),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.userInfoAddVisible,expression:"userInfoAddVisible"}]},[a("userInfoAdd",{ref:"userInfoAdd"})],1)])])},staticRenderFns:[]};var r=a("VU/8")(o,i,!1,function(e){a("eLX1")},null,null);t.default=r.exports},eLX1:function(e,t){},uJEr:function(e,t){}});