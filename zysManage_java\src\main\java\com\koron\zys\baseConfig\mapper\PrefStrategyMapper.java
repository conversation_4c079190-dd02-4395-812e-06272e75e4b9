package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.koron.zys.baseConfig.bean.PrefStrategyBean;
import com.koron.zys.baseConfig.bean.PrefStrategyDetailBean;
import com.koron.zys.baseConfig.queryBean.PrefStrategyQueryBean;
import com.koron.zys.baseConfig.vo.PrefStrategyVO;
import com.koron.zys.baseConfig.vo.SelectVO;

public interface PrefStrategyMapper {

	/**
	 * 查询列表
	 * 
	 * @return
	 */
	List<PrefStrategyVO> selectPrefStrategyList(PrefStrategyQueryBean PrefStrategyQueryBean);

	/**
	 * 根据id查询 编辑初始化
	 * 
	 * @param id
	 * @return
	 */
	PrefStrategyBean selectPrefStrategyDetailById(@Param("id") String id);

	/**
	 * 根据id查询优惠策略
	 * selectPrefStrategyByname
	 * @param id
	 * @return
	 */
	PrefStrategyBean selectPrefStrategyById(@Param("id") String id);
	/**
	 * 根据name查询优惠策略id
	 * 
	 * @param id
	 * @return
	 */
	PrefStrategyBean selectPrefStrategyByname(@Param("strategyName") String strategyName);

	/**
	 * 根据name模糊查询优惠策略id
	 *
	 * @param id
	 * @return
	 */
	List<PrefStrategyBean> selectPrefStrategysByname(@Param("strategyName") String strategyName);
	/**
	 * 添加
	 * 
	 * @param prefStrategyBean
	 * @return
	 */
	Integer insertPrefStrategy(PrefStrategyBean prefStrategyBean);

	/**
	 * 校验字段内容重复
	 */
	@Select("select count(*) from BASE_PREF_STRATEGY where ${key} = #{val}")
	Integer check(@Param("key") String key, @Param("val") String val);

	/**
	 * 校验字段内容重复-排除当前记录
	 */
	@Select("select count(*) from BASE_PREF_STRATEGY where ${key} = #{val} and id <> #{id}")
	Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);

	/**
	 * 修改
	 * 
	 * @param prefStrategyBean
	 * @return
	 */
	Integer updatePrefStrategy(PrefStrategyBean prefStrategyBean);

	/**
	 * 批量添加从表信息
	 * 
	 * @param prefStrategyList
	 */
	Integer insertPrefStrategyDetailList(@Param("prefStrategyList") List<PrefStrategyDetailBean> prefStrategyList);

	/**
	 * 批量删除从表信息
	 * 
	 * @param list
	 */
	Integer DeletePrefStrategyDetailList(@Param("list") List<PrefStrategyDetailBean> list);

	/**
	 * 批量更新从表信息
	 * 
	 * @param prefStrategylList
	 * @return
	 * @throws Exception
	 */
	Integer UpdatePrefStrategyDetailList(@Param(value = "prefStrategylList") List<PrefStrategyDetailBean> prefStrategylList);

	/**
	 * 根据策略id删除优惠明细信息
	 * 
	 * @param prefStrategyId
	 */
	@Delete("delete from base_pref_strategy_detail where pref_strategy_id = #{prefStrategyId}")
	Integer delPrefStrategyDetail(@Param("prefStrategyId") String prefStrategyId);

	/**
	 * 下拉框
	 * 
	 * @return
	 */
	List<SelectVO> prefStrategySelect();

}
