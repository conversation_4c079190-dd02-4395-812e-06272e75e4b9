package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.MeterFactoryMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.baseConfig.queryBean.MeterFactoryQueryBean;
import com.koron.zys.baseConfig.vo.MeterFactoryVO;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 水表供应商-初始化列表
 *
 * <AUTHOR>
 */
public class MeterFactoryList implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MeterFactoryList.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        @SuppressWarnings("rawtypes")
        MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
        try {
            MeterFactoryQueryBean Bean = JsonUtils.objectToPojo(req.getData(), MeterFactoryQueryBean.class);
            MeterFactoryMapper mapper = factory.getMapper(MeterFactoryMapper.class);
            PageHelper.startPage(Bean.getPage(), Bean.getPageCount());
            List<MeterFactoryVO> list = mapper.selectMeterFactoryList(Bean);
            info.setData(new PageInfo<>(list));
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }

}
