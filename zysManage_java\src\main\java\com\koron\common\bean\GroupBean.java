package com.koron.common.bean;


public class GroupBean {
	
	/**
	 * id
	 */
	private Integer id;
	/**
	 * 应用id
	 */
	private String appid;
	
	/**
	 * 名称
	 */
	private String name;
	
	/**
	 * 编码
	 */
	private String code;
	
	/**
	 * 群组属性
	 */
	private String attr;
	
	/**
	 * 更新时间
	 */
	private String lastupdate;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getAttr() {
		return attr;
	}

	public void setAttr(String attr) {
		this.attr = attr;
	}

	public String getLastupdate() {
		return lastupdate;
	}

	public void setLastupdate(String lastupdate) {
		this.lastupdate = lastupdate;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	@Override
	public String toString() {
		return "GroupBean [id=" + id + ", appid=" + appid + ", name=" + name + ", code=" + code + ", attr=" + attr
				+ ", lastupdate=" + lastupdate + "]";
	}

}
