webpackJsonp([22],{"0G7E":function(t,e){},"36ns":function(t,e){},ExFb:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i,r=a("Gu7T"),s=a.n(r),n=a("woOf"),o=a.n(n),d=a("Dd8w"),l=a.n(d),c=a("bOdI"),h=a.n(c),m=(i={name:"adminAreaAdd",props:["addForm","id","itemArr","addFormRules","status","node","parentListName"],data:function(){var t;return t={markerIcon:"",parentName:"根目录",isShowMap:!1,placeSearch:null,isInitForm:!1,zoom:13,center:[114.161054492,22.8356011687],events:this.eventsFun(),iconImg:[]},h()(t,"zoom",13),h()(t,"markers",[]),h()(t,"windows",[]),h()(t,"window",""),h()(t,"polygons",[]),h()(t,"district",null),h()(t,"editForm",{}),h()(t,"point",null),h()(t,"editFormRules",{fullName:{required:!0,message:"请输入"},sname:{required:!0,message:"请输入"},areaName:{required:!0,message:"请输入"},status:{required:!0,message:"请输入"},position:{required:!0,message:"请选择"}}),t},mounted:function(){"add"==this.status&&(this.isInitForm=!1)},watch:{"addForm.city":{handler:function(t){this.handleChange(t,2)},deep:!0},"addForm.district":{handler:function(t){this.handleChange(t,3)},deep:!0},"addForm.street":{handler:function(t){this.handleChange(t,4)},deep:!0}}},h()(i,"mounted",function(){"edit"==this.status&&(this.editForm=this.$parent.editForm)}),h()(i,"methods",{changeMethod:function(t){},openMap:function(){this.isShowMap=!0},confirmMap:function(){this.point?(this.mapCity=!0,"add"==this.status?this.addForm.position=this.point:(this.addForm.position=this.point,this.editForm.position=this.point),this.isShowMap=!1):this.$message({type:"error",message:"请选择一个位置"})},closeMap:function(){this.mapCity=!0,this.isShowMap=!1,this.markers=[],this.placeSearch=null},eventsFun:function(){var t=this;return{click:function(e){var a=e.lnglat,i=a.lng,r=a.lat;t.lng=i,t.lat=r,t.center=[e.lnglat.lng,e.lnglat.lat],t.getAddress(t.center)},complete:function(){t.drawBounds("银川市")}}},getAddress:function(t){var e=this,a=new AMap.Geocoder({radius:1e3,extensions:"all"});e.point=t.toString(),e.markers=[];var i={position:t,offset:[0,-50]};e.markers.push(i),e.getNearBy("",t,1e4),a.getAddress(e.center,function(t,a){"complete"===t&&"OK"===a.info&&a&&a.regeocode&&(e.address=a.regeocode.formattedAddress,e.$set(e.markers[0],"text",e.address),e.$nextTick())})},getNearBy:function(t,e,a){var i=this.$refs.amap.$amap,r=this;r.placeSearch&&(r.placeSearch.render&&r.placeSearch.render.markerList.clear(),r.placeSearch.clear()),AMap.service(["AMap.PlaceSearch"],function(){r.placeSearch=new AMap.PlaceSearch({pageSize:5,pageIndex:1,type:"",extensions:"all",fillColor:"0",map:i,panel:"addressInfo",autoFitView:!0}),r.placeSearch.searchNearBy(t,e,a,function(t,e){var a={};"OK"==e.info&&e.poiList.pois.forEach(function(t){a[t.address]=t.pname+t.cityname+t.adname+t.name,[t.location.lng,t.location.lat]})}),r.placeSearch.on("selectChanged",function(t){var e=[t.selected.data.location.lng,t.selected.data.location.lat];r.point=e.toString(),r.markers=[]})})},drawBounds:function(t){var e=this;AMap.service("AMap.DistrictSearch",function(){if("银川市"!==t)return e.center=[114.161054492,22.8356011687],e.zoom=12,void(e.polygons=[]);if(e.center=[106.230909,38.487193],e.zoom=10,!e.district){e.district=new AMap.DistrictSearch({subdistrict:0,extensions:"all",level:"city"})}e.district.search(t,function(t,a){e.polygons=[];var i=[new AMap.LngLat(-360,90,!0),new AMap.LngLat(-360,-90,!0),new AMap.LngLat(360,-90,!0),new AMap.LngLat(360,90,!0)],r=a.districtList[0].boundaries;if(r)for(var s=0,n=r.length;s<n;s++){var o=new AMap.Polygon({path:[i,r[s]]});e.polygons.push(o)}AMap.Polygon.bind(e.polygons)})})},handleChange:function(t,e){t?this.isInitForm||(t.id?(this.$set(this.itemArr[e],"disabled",!1),this.clearData(e),this.getDataByParentId(t.id,e)):this.disableItem(e,!0)):this.disableItem(e,!0)},clearData:function(t){this.isInitForm=!0;for(var e=["city","district","street","road"],a=t;a<=5;a++)this.addForm[e[a-1]]="",this.$set(this.itemArr[a],"options",[]);this.isInitForm=!1},getDataByParentId:function(t,e){var a=this,i={busicode:"AdminAreaList",data:{id:t}};this.$api.fetch({params:i}).then(function(t){var i=t;i.map(function(t){t.label=t.areaName,t.value=t.areaName}),a.$set(a.itemArr[e],"options",i)}).catch(function(t){a.treeLoading=!1})},disableItem:function(t,e){this.isInitForm=!0;for(var a=["city","district","street","road"],i=t;i<=5;i++)this.addForm[a[i-1]]="",this.$set(this.itemArr[i],"disabled",e),this.$set(this.itemArr[i],"options",[]);this.isInitForm=!1},submitForm:function(){var t=this;if("edit"!=this.status){var e=this;e.$refs.validateForm.validate(function(a){if(a){var i=e.getLastInputData(),r=i.parentId,s=i.areaNo,n=i.areaName,o=e.addForm.position.split(","),d={busicode:"AdminAreaAdd",data:{parentId:r,areaNo:s,areaName:n,status:t.addForm.status,longitude:o[0],latitude:o[1]}};e.$api.fetch({params:d}).then(function(a){t.$message({type:"success",message:"保存成功"}),t.markers=[],t.poinit=null,e.$parent.handleClose()})}})}else{var a=this;a.$refs.editForm.validate(function(e){if(e){var i=a.editForm.position.split(","),r=t.parentListName+t.editForm.sname,s={busicode:"AdminAreaUpdate",data:{id:a.editForm.id,status:a.editForm.status,fullName:r,areaName:a.editForm.sname,sname:a.editForm.sname,longitude:i[0],latitude:i[1]}};console.log("params",s),a.$api.fetch({params:s}).then(function(e){a.$message({type:"success",message:"保存成功"}),t.markers=[],t.poinit=null,a.$parent.handleClose()})}})}},getLastInputData:function(){var t=this.node,e=[],a=this.node.areaNameList;for(var i in this.addForm)"province"===i?e[0]=this.addForm[i]:"city"===i?e[1]=this.addForm[i]:"district"===i?e[2]=this.addForm[i]:"street"===i?e[3]=this.addForm[i]:"road"===i&&(e[4]=this.addForm[i]);for(var r={},s=-1,n={},o={},d=a.length;d<=5;d++){if(e[d])this.checkIsObj(e[d])&&0,d,r=e[d],n=e[d-1],s=d-1}return s+1===a.length?(o.areaNo=t.areaNo,o.parentId=t.id,this.checkIsObj(r)?o.areaName=r.areaName:o.areaName=r):(o.areaNo=n.areaNo,o.parentId=n.id,this.checkIsObj(r)?o.areaName=r.areaName:o.areaName=r),o},checkIsObj:function(t){return t.constructor===Object}}),i),u={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"adminAreaAdd"},["edit"==t.status?a("el-form",{ref:"editForm",staticClass:"demo-ruleForm",staticStyle:{width:"50%","margin-left":"350px","margin-top":"50px"},attrs:{model:t.editForm,rules:t.editFormRules,"label-width":"120px","label-position":"right"}},[a("el-row",[a("el-form-item",{attrs:{label:"区域简称:",prop:"sname"}},[a("el-input",{attrs:{type:"text",placeholder:"请输入"},model:{value:t.editForm.sname,callback:function(e){t.$set(t.editForm,"sname",e)},expression:"editForm.sname"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"状态:",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.editForm.status,callback:function(e){t.$set(t.editForm,"status",e)},expression:"editForm.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),t._v(" "),a("el-option",{attrs:{label:"禁用",value:0}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"坐标:",prop:"position"}},[a("el-input",{attrs:{type:"text",placeholder:"请选择",readonly:!0},model:{value:t.editForm.position,callback:function(e){t.$set(t.editForm,"position",e)},expression:"editForm.position"}},[a("i",{staticClass:"el-input__icon el-icon-location",attrs:{slot:"suffix"},on:{click:t.openMap},slot:"suffix"})])],1)],1)],1):a("el-form",{ref:"validateForm",staticClass:"formBill",staticStyle:{width:"100%","margin-left":"500px"},attrs:{model:t.addForm,rules:t.addFormRules,"label-width":"120px","label-position":"right"}},[a("el-row",[t._l(t.itemArr,function(e,i){return a("el-form-item",{key:i,attrs:{label:e.label,prop:e.prop}},[a("el-select",{attrs:{"allow-create":"","default-first-option":"","value-key":"id",clearable:"",filterable:"",placeholder:"请输入",disabled:e.disabled||!1},on:{change:function(a){return t.changeMethod(e,i)}},model:{value:t.addForm[e.prop],callback:function(a){t.$set(t.addForm,e.prop,a)},expression:"addForm[formItem.prop]"}},t._l(e.options||[],function(t){return a("el-option",{key:t.id,attrs:{label:t.label,value:t}})}),1)],1)}),t._v(" "),a("el-form-item",{attrs:{label:"状态:",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.addForm.status,callback:function(e){t.$set(t.addForm,"status",e)},expression:"addForm.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),t._v(" "),a("el-option",{attrs:{label:"禁用",value:0}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"坐标:",prop:"position"}},[a("el-input",{attrs:{type:"text",placeholder:"请选择"},model:{value:t.addForm.position,callback:function(e){t.$set(t.addForm,"position",e)},expression:"addForm.position"}},[a("i",{staticClass:"el-input__icon el-icon-location",attrs:{slot:"suffix"},on:{click:t.openMap},slot:"suffix"})])],1)],2)],1),t._v(" "),a("el-dialog",{attrs:{title:"选择坐标",fullscreen:!0,visible:t.isShowMap,"close-on-click-modal":!1,"before-close":t.closeMap},on:{"update:visible":function(e){t.isShowMap=e}}},[t.isShowMap?a("el-amap",{ref:"amap",attrs:{vid:"amap",center:t.center,zoom:t.zoom,events:t.events}},[t._l(t.markers,function(e,i){return a("el-amap-marker",{key:"marker"+i,attrs:{position:e.position,icon:t.markerIcon}})}),t._v(" "),t._l(t.markers,function(t,e){return a("el-amap-text",{key:"text"+e,attrs:{offset:t.offset,position:t.position,text:t.text}})}),t._v(" "),t._l(t.polygons,function(t,e){return a("el-amap-polygon",{key:e+"polygons",attrs:{strokeColor:"rgb(0,139,0)",strokeOpacity:"1",fillColor:"rgba(245,245,220)",fillOpacity:"1",strokeStyle:"solid",strokeWeight:"2",path:t.Ce.path}})})],2):t._e(),t._v(" "),a("div",{ref:"ruleForm",staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini",type:"info"},on:{click:t.confirmMap}},[t._v("确 定")]),t._v(" "),a("el-button",{attrs:{size:"mini",type:""},on:{click:t.closeMap}},[t._v("取 消")])],1)],1)],1)},staticRenderFns:[]};var p={name:"adminArea-index",components:{adminAreaAdd:a("VU/8")(m,u,!1,function(t){a("36ns")},null,null).exports},data:function(){return{treeLoading:!1,areaNameList:[],defaultProps:{children:"children",label:"areaName",isLeaf:function(t,e){return 1===t.isLeaf}},tableQuery:{code:"",page:1,pageCount:50},id:"id",tableData:{},status:"add",crumbsData:{titleList:[{title:"基础配置",path:"/baseInfo"},{title:"结构地址管理",method:function(){window.histroy.back()}}]},addForm:{status:1,position:"",province:"",city:"",district:"",street:"",road:""},isInitForm:!1,treeData:[],maxHeight:0,tableShow:!1,enterMeterDataShow:!1,enterMeterDate:"",adminAreaShow:!0,companyNo:"",areaNo:"",node:{},resolve:{},currentNode:{},addFormRules:{status:[{required:!0,message:"请输入"}],position:[{required:!0,message:"请选择"}]},itemArr:[{label:"省/市/自治区",prop:"province",disabled:!0,options:[]},{label:"市",prop:"city",disabled:!0,options:[]},{label:"区/县",prop:"district",disabled:!0,options:[]},{label:"道路/巷/街",prop:"street",disabled:!0,options:[]},{label:"路号-小区",prop:"road",disabled:!0,options:[]}],parentName:""}},mounted:function(){var t=this;this.init(),this.$nextTick(function(){t.common.changeTable(t,".adminArea .kl-table",[".adminArea .block"])})},methods:{init:function(){this.getTreeData()},getEditData:function(){return this.editForm},getTreeData:function(){var t=this;t.$api.fetch({params:{busicode:"AdminAreaList",data:{}}}).then(function(e){t.treeData=e,e.length>0&&(t.areaNo=e[0].areaNo,t.node=e[0],t.$nextTick(function(){t.$refs.asyncTree&&t.$refs.asyncTree.setCurrentKey(t.node.id)}),t.getTableData())})},handleNodeClick:function(t){this.areaNo=t.areaNo||-1,this.node=l()({},t,{areaNameList:this.areaNameList}),this.getTableData()},getLeafKeys:function(t,e){var a=[],i=[e.data.areaName];e.data.areaNo.length>5&&function t(e){console.log(e),e.parent&&!Array.isArray(e.parent.data)?(e.parent.data instanceof Object&&a.push(e.parent.data.id),e.parent.data instanceof Object&&i.unshift(e.parent.data.areaName),t(e.parent)):0===a.length&&(a.push(e.data.id),i.unshift(e.data.areaName))}(e),console.log(a),console.log(i),this.areaNameList=i},getTableData:function(){var t=this;if(-1!==this.areaNo){var e={busicode:"AdminAreaListPading",data:{areaNo:this.areaNo,page:this.tableQuery.page,pageCount:this.tableQuery.pageCount}};t.$api.fetch({params:e}).then(function(e){t.tableData=e,t.tableData.length>0&&t.$refs.asyncTree.setCurrentKey(t.tableData[0].parentId)})}},loadNode:function(t,e){if(this.resolve=e,0===t.level)return e(this.treeData);this.getChildrenNode(t,e)},nodeExpand:function(t){this.handleNodeClick(t),this.refreshNodeBy(t.id)},refreshNodeBy:function(t){var e=this.$refs.asyncTree.getNode(t);e.loaded=!1,e.expand()},getChildrenNode:function(t,e){var a={busicode:"AdminAreaList",data:{id:t.data?t.data.id:t.id}};this.$api.fetch({params:a}).then(function(t){e(t)})},handleEdit:function(t,e){var a=this;this.status="edit",this.editForm=o()({},e);var i={busicode:"AdminAreaParentList",data:{id:this.node.id}};this.$api.fetch({params:i}).then(function(t){a.parentName="",t.forEach(function(t){a.parentName+=t.areaName})});var r=e.latitude,s=e.longitude;this.editForm.position=r&&s?[r,s].toString():"",this.adminAreaShow=!1,this.enterMeterDataShow=!0},addChild:function(t,e){var a=this.getLevelIdx(e.areaNo),i=[].concat(s()(this.areaNameList));i[a]=e.areaName,console.log(i),this.currentNode=l()({},e,{areaNameList:i}),this.status="addChild",this.addFun()},formatStatus:function(t){return 1===t.status?"启用":"停用"},handleSizeChange:function(t){this.tableQuery.pageCount=t,this.tableQuery.page=1},handleCurrentChange:function(t){this.tableQuery.page=t},indexMethod:function(t){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(t+1)},submitForm:function(t){this.$refs.child.submitForm()},handleClose:function(){this.addForm={status:1,position:"",province:"",city:"",district:"",street:"",road:""},this.addFormRules={status:{required:!0,message:"请输入"},position:{required:!0,message:"请选择"}},this.itemArr.map(function(t){t.disabled=!0,t.options=[]}),this.adminAreaShow=!0,this.enterMeterDataShow=!1,this.refreshNodeBy(this.node.id),this.getTableData()},add:function(){this.status="add",this.addFun()},getLevelIdx:function(t){return t.length/5-1},addFun:function(){var t=[],e=void 0;e="add"===this.status?this.node:this.currentNode,this.id=e.id,console.log("node：",e),(t=[].concat(s()(this.areaNameList))).splice(this.getLevelIdx(e.areaNo),t.length,e.areaName),console.log("areaNameList",this.areaNameList),t.length>=1&&(this.addForm.province=t[0],this.$set(this.itemArr[0],"disabled",!0)),t.length>=2&&(this.addForm.city=t[1],this.$set(this.itemArr[1],"disabled",!0)),t.length>=3&&(this.addForm.district=t[2],this.$set(this.itemArr[2],"disabled",!0)),t.length>=4&&(this.addForm.street=t[3],this.$set(this.itemArr[3],"disabled",!0)),t.length>=5&&(this.addForm.road=t[4],this.$set(this.itemArr[4],"disabled",!0)),t.length<=4&&(this.$set(this.itemArr[t.length],"disabled",!1),this.$set(this.addFormRules,this.itemArr[t.length].prop,[{required:!0,message:"请输入或选择",trigger:"change"}]),this.getDataByParentId(e.id,t.length)),this.adminAreaShow=!1,this.enterMeterDataShow=!0},getDataByParentId:function(t,e){var a=this,i={busicode:"AdminAreaList",data:{id:t}};this.$api.fetch({params:i}).then(function(t){var i=t;i.map(function(t){t.label=t.areaName,t.value=t.areaName}),a.$set(a.itemArr[e],"options",i)}).catch(function(t){a.treeLoading=!1})},closeDialog:function(){this.enterMeterDataShow=!1,this.crumbsData.titleList.pop(),this.$refs.child.resetForm(),this.adminAreaShow=!0}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},f={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"adminArea"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:t.crumbsData}}),t._v(" "),t.enterMeterDataShow?a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.submitForm()}}},[t._v("保存")]),t._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:function(e){return t.handleClose()}}},[t._v("返回")])],1):t._e(),t._v(" "),t.adminAreaShow?a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:function(e){return t.add("add")}}},[t._v("添加")])],1):t._e()],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.adminAreaShow,expression:"adminAreaShow"}],staticClass:"company-content"},[a("div",{staticClass:"company-left",staticStyle:{height:"100%",overflow:"auto"}},[a("el-tree",{ref:"asyncTree",attrs:{"check-strictly":!0,accordion:"","icon-class":"none",data:t.treeData,props:t.defaultProps,"highlight-current":!0,load:t.loadNode,"node-key":t.id,lazy:""},on:{"node-click":t.handleNodeClick,"node-expand":t.nodeExpand,"current-change":t.getLeafKeys}})],1),t._v(" "),a("div",{staticClass:"kl-table company-right"},[t.tableShow?a("el-table",{staticClass:"wuserInfo-table",attrs:{"max-height":t.maxHeight,stripe:"",border:"",data:t.tableData.list}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"55",index:t.indexMethod}}),t._v(" "),a("el-table-column",{attrs:{"show-overflow-tooltip":"",prop:"fullName","min-width":"170",label:"区域全称",align:"left"}}),t._v(" "),a("el-table-column",{attrs:{prop:"sname","min-width":"100",label:"区域简称",align:"left"}}),t._v(" "),a("el-table-column",{attrs:{prop:"status","min-width":"80",label:"状态",formatter:t.formatStatus}}),t._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"80",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},nativeOn:{click:function(a){return t.handleEdit(e.$index,e.row)}}},[t._v("修改")]),t._v(" "),a("el-button",{directives:[{name:"show",rawName:"v-show",value:e.row.areaNo.length<25,expression:"scope.row.areaNo.length<25"}],attrs:{type:"text"},nativeOn:{click:function(a){return t.addChild(e.$index,e.row)}}},[t._v("添加下级")])]}}],null,!1,1695121492)})],1):t._e(),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":1,"page-sizes":[50,100,500,1e3],"page-size":t.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)]),t._v(" "),t.enterMeterDataShow?a("div",{staticClass:"adminArea-right-content1"},[a("adminAreaAdd",{ref:"child",attrs:{node:"add"==t.status?t.node:t.currentNode,itemArr:t.itemArr,id:t.id,addForm:t.addForm,addFormRules:t.addFormRules,status:t.status,parentListName:t.parentName}})],1):t._e()])},staticRenderFns:[]};var g=a("VU/8")(p,f,!1,function(t){a("0G7E")},null,null);e.default=g.exports}});