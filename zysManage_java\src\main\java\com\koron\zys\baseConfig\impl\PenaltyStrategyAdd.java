package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.PenaltyBean;
import com.koron.zys.baseConfig.bean.PenaltyStrategyBean;
import com.koron.zys.baseConfig.mapper.PenaltyStrategyMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

/**
 * 违约金策略-添加
 * <AUTHOR>
 *
 */
public class PenaltyStrategyAdd implements ServerInterface{

	private static Logger logger = LoggerFactory.getLogger(PenaltyStrategyAdd.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		MessageBean<PenaltyStrategyBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PenaltyStrategyBean.class);
		try {
			PenaltyBean bean = JsonUtils.objectToPojo(req.getData(), PenaltyBean.class);
			PenaltyStrategyMapper mapper = factory.getMapper(PenaltyStrategyMapper.class);
			bean.setId(new ObjectId().toHexString());
			bean.setCreateName(userInfo.getUserInfo().getName());
			bean.setCreateTime(CommonUtils.getCurrentTime());
			mapper.insertPenalty(bean);
			for (PenaltyStrategyBean penaltyStrategyBean : bean.getList()) {
				if (penaltyStrategyBean.getCalculateValue() != null) {
					penaltyStrategyBean.setId(new ObjectId().toHexString());
					penaltyStrategyBean.setPenaltyId(bean.getId());
					mapper.insertPenaltyStrategy(penaltyStrategyBean);
				}
			}
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
			factory.close(false);
		}
		return info;
	}
}