package com.koron.common.web.dto;

public class OrgStaffDto {
	
	private Integer id;
	
	private String createTime;
	
	private String creator;
	
	private String lastModificationTime;
	
	private String lastModifier;
	
	private String code;
	
	private String name;
	
	private Integer status;
	
	private Integer weight;
	
	private String phone;
	
	private String mobile;
	
	private String email;
	
	private Integer sex;
	
	private String cardno;
	
	private String logo;
	
	private String shortName;
	
	private String orgNodeCode;
	
	private String orgNodeName;
	
	private String title;
	
	private Integer syncFlag;

	public Integer getId() {
		return id;
	}

	public String getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public String getLastModificationTime() {
		return lastModificationTime;
	}

	public String getLastModifier() {
		return lastModifier;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public Integer getStatus() {
		return status;
	}

	public Integer getWeight() {
		return weight;
	}

	public String getPhone() {
		return phone;
	}

	public String getMobile() {
		return mobile;
	}

	public String getEmail() {
		return email;
	}

	public Integer getSex() {
		return sex;
	}

	public String getCardno() {
		return cardno;
	}

	public String getLogo() {
		return logo;
	}

	public String getShortName() {
		return shortName;
	}

	public String getOrgNodeCode() {
		return orgNodeCode;
	}

	public String getOrgNodeName() {
		return orgNodeName;
	}

	public String getTitle() {
		return title;
	}

	public Integer getSyncFlag() {
		return syncFlag;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setLastModificationTime(String lastModificationTime) {
		this.lastModificationTime = lastModificationTime;
	}

	public void setLastModifier(String lastModifier) {
		this.lastModifier = lastModifier;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public void setWeight(Integer weight) {
		this.weight = weight;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public void setCardno(String cardno) {
		this.cardno = cardno;
	}

	public void setLogo(String logo) {
		this.logo = logo;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public void setOrgNodeCode(String orgNodeCode) {
		this.orgNodeCode = orgNodeCode;
	}

	public void setOrgNodeName(String orgNodeName) {
		this.orgNodeName = orgNodeName;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public void setSyncFlag(Integer syncFlag) {
		this.syncFlag = syncFlag;
	} 
}
