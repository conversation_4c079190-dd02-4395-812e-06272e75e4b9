package com.koron.zys.baseConfig.bean;

/**
 * 泵站信息表
 * <AUTHOR>
 *
 */
public class PumpStationBean {
	
	
	private String pumpStationId;

    private String pumpStationName;

    private String pumpStationAddress;
 
	private String comments;
 
    private Integer status;
    
    private Integer sortNo;

    /**
	 * 创建时间
	 */
	private String createTime;
	
	/**
	 * 创建人
	 */
	private String createName;
	/**
	 * 修改时间
	 */
	private String updateTime;
	/**
	 * 修改人
	 */
	private String updateName;
	public String getPumpStationId() {
		return pumpStationId;
	}
	public String getPumpStationName() {
		return pumpStationName;
	}
	public String getPumpStationAddress() {
		return pumpStationAddress;
	}
	public String getComments() {
		return comments;
	}
	public Integer getStatus() {
		return status;
	}
	public Integer getSortNo() {
		return sortNo;
	}
	public String getCreateTime() {
		return createTime;
	}
	public String getCreateName() {
		return createName;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public String getUpdateName() {
		return updateName;
	}
	public void setPumpStationId(String pumpStationId) {
		this.pumpStationId = pumpStationId;
	}
	public void setPumpStationName(String pumpStationName) {
		this.pumpStationName = pumpStationName;
	}
	public void setPumpStationAddress(String pumpStationAddress) {
		this.pumpStationAddress = pumpStationAddress;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public void setSortNo(Integer sortNo) {
		this.sortNo = sortNo;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public void setCreateName(String createName) {
		this.createName = createName;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}
	
}
