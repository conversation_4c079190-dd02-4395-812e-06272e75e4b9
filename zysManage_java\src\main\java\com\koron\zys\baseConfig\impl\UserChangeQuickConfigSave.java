package com.koron.zys.baseConfig.impl;

import org.apache.log4j.Logger;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.QuickChangeConfigBean;
import com.koron.zys.baseConfig.mapper.QuickChangeConfigMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

import java.util.List;

public class UserChangeQuickConfigSave  implements ServerInterface{
	
	private Logger log = Logger.getLogger(UserChangeQuickConfigSave.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			List<QuickChangeConfigBean> beans = JsonUtils.jsonToList(JsonUtils.objectToJson(req.getData()),QuickChangeConfigBean.class);
			QuickChangeConfigMapper mapper = factory.getMapper(QuickChangeConfigMapper.class);
			for (QuickChangeConfigBean str : beans){
				QuickChangeConfigBean bean = new QuickChangeConfigBean();
				bean.setCode(str.getCode());
				bean.setIsUse(str.getIsUse());
				mapper.update(bean);
			}
		}catch(Exception e) {
			log.error("快速变更配置失败",e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "快速变更配置失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
