webpackJsonp([51],{"4bwZ":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n={name:"useStat",components:{},data:function(){return{crumbsData:{titleList:[{title:"系统监控",path:"/systemMan"},{title:"运行分析",method:function(){window.histroy.back()}}]},tableShow:!0,maxHeight:0,historyData:{list:[{name:"在用水司",num:"16",errorNum:"0"},{name:"数据库服务器",num:"16",errorNum:"0"},{name:"应用服务器",num:"16",errorNum:"0"},{name:"营收中台微服务",num:"16",errorNum:"0"},{name:"客户认证微服务",num:"16",errorNum:"0"}]}}},mounted:function(){eventBus.$emit("secondMenuShow","secondMenuShow1"),this.getData()},methods:{getData:function(){},indexMethod:function(t){return t+1}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},r={render:function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"InterfaceLog"},[e("div",{staticClass:"bread-contain"},[e("publicCrumbs",{attrs:{crumbsData:this.crumbsData}})],1),this._v(" "),e("div",{staticClass:"kl-table"},[e("el-table",{attrs:{stripe:"",center:"",border:"",data:this.historyData.list}},[e("el-table-column",{attrs:{fixed:"left",type:"index",width:"80",label:"NO.",index:this.indexMethod}}),this._v(" "),e("el-table-column",{attrs:{prop:"name",label:"项目"}}),this._v(" "),e("el-table-column",{attrs:{prop:"num",label:"数量"}}),this._v(" "),e("el-table-column",{attrs:{prop:"errorNum",label:"异常数量"}})],1)],1)])},staticRenderFns:[]};var i=a("VU/8")(n,r,!1,function(t){a("fFwo")},null,null);e.default=i.exports},fFwo:function(t,e){}});