package com.koron.zys.baseConfig.impl;

import java.util.ArrayList;
import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CostNameBean;
import com.koron.zys.baseConfig.bean.InvoiceTypeBean;
import com.koron.zys.baseConfig.mapper.CostInvoiceMapper;
import com.koron.zys.baseConfig.mapper.CostMapper;
import com.koron.zys.baseConfig.mapper.InvoiceTypeMapper;
import com.koron.zys.baseConfig.vo.CostInvoiceVO;
import com.koron.zys.baseConfig.vo.InvoiceTypeVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
/**
 * 费用票据关系-列表
 * <AUTHOR>
 */
public class CostInvoiceList implements ServerInterface {
	private static Logger logger = LoggerFactory.getLogger(CostInvoiceList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			//获取费用类型
			CostMapper costMapper = factory.getMapper(CostMapper.class);
			List<CostNameBean> costs = costMapper.selectCostNameList();
			//获取票据类型
			InvoiceTypeMapper invoiceTypeMapper = factory.getMapper(InvoiceTypeMapper.class);
			List<InvoiceTypeBean> invoiceTypes = invoiceTypeMapper.select();
			//根据费用类型查询
			List<CostInvoiceVO> result = new ArrayList<CostInvoiceVO>();
			CostInvoiceMapper mapper = factory.getMapper(CostInvoiceMapper.class);
			
			for (CostNameBean cost : costs) {
				List<InvoiceTypeVO> invoiceTypeList = new ArrayList<InvoiceTypeVO>();
				CostInvoiceVO ci = new CostInvoiceVO();
				for (InvoiceTypeBean invoiceType : invoiceTypes) {
					InvoiceTypeVO ivo = new InvoiceTypeVO();
					ivo.setInvoiceName(invoiceType.getInvoiceName());
					ivo.setCostInvoiceList(mapper.select(cost.getId(), invoiceType.getId()));
					invoiceTypeList.add(ivo);
				}
				ci.setCostName(cost.getName());
				ci.setInvoiceTypeList(invoiceTypeList);
				result.add(ci);
			}
			info.setCode(Constant.MESSAGE_INT_SUCCESS);
			info.setDescription("success");
			info.setData(result);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription("操作失败");
			logger.error("操作失败", e);
		}
		return info;
	}

}
