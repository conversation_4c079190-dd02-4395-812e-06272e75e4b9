package com.koron.zys.baseConfig.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.SubCostBean;
import com.koron.zys.baseConfig.bean.SubCostQueryBean;
import com.koron.zys.baseConfig.mapper.SubCostMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import java.util.List;

public class SubCostList implements ServerInterface {
    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
        try {
            SubCostQueryBean bean = JsonUtils.objectToPojo(req.getData(), SubCostQueryBean.class);
            SubCostMapper mapper = factory.getMapper(SubCostMapper.class);
            PageHelper.startPage(bean.getPage(), bean.getPageCount());
            List<SubCostBean> list = mapper.list(bean);
            for (SubCostBean subCostBean : list) {
                subCostBean.setIsQuotaName("1".equals(subCostBean.getIsQuota())?"是":"否");
            }
            info.setData(new PageInfo<>(list));

        } catch (Exception e) {
            // TODO Auto-generated catch block
            logger.error("非法参数", e);
            return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
        }
        return info;
    }
}
