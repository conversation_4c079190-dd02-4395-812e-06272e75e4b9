<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.PumpStationMapper">
	
 		<select id="selectPumpStationList" parameterType="com.koron.zys.baseConfig.queryBean.PumpStationQueryBean" resultType="com.koron.zys.baseConfig.vo.PumpStationVO" >
		select xx.pump_station_id,xx.pump_station_name,xx.pump_station_address,xx.comments,xx.sort_no,case when xx.status=1 then '启用' else '停用' end status
		from BASE_PUMP_STATION xx
		  where 1=1
	    	<if test="pumpStationName != null and pumpStationName != ''">
	    		and xx.pump_station_name  LIKE  '%' || #{pumpStationName} || '%'
	    	</if>
	    order by xx.sort_no asc  
	</select>
	 
	<select id="selectPumpStationById" resultType="com.koron.zys.baseConfig.bean.PumpStationBean">
		select *
		from BASE_PUMP_STATION
		where pump_station_id = #{pumpStationId}
	</select>
	
	 	<insert id="insertPumpStation" parameterType="com.koron.zys.baseConfig.bean.PumpStationBean">
		insert into BASE_PUMP_STATION (pump_station_id,pump_station_name,pump_station_address, 
		comments,status, sort_no, create_time, create_name)
		values
		(
		#{pumpStationId,jdbcType=VARCHAR},
		#{pumpStationName,jdbcType=VARCHAR},
		#{pumpStationAddress,jdbcType=VARCHAR},
		#{comments,jdbcType=VARCHAR},
		#{status,jdbcType=INTEGER},
		#{sortNo,jdbcType=INTEGER},		
		date_format(#{createTime},'%Y-%m-%d %T'),
		#{createName,jdbcType=VARCHAR}
		)
	</insert>
	
		<update id="updatePumpStation" parameterType="com.koron.zys.baseConfig.bean.PumpStationBean">
		update BASE_PUMP_STATION
		set pump_station_name = #{pumpStationName,jdbcType=VARCHAR},	
		    pump_station_address = #{pumpStationAddress,jdbcType=VARCHAR},	 			
			comments = #{comments,jdbcType=VARCHAR},
			status = #{status,jdbcType=INTEGER},
			sort_no = #{sortNo,jdbcType=INTEGER},		
			update_time=date_format(#{updateTime},'%Y-%m-%d %T'),
			update_name = #{updateName,jdbcType=VARCHAR}
		    where pump_station_id = #{pumpStationId}
	</update>
	
	
</mapper>