package com.koron.zys.baseConfig.mapper;

import com.koron.zys.baseConfig.bean.BaseArrearageElfBean;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

public interface ArrearageElfMapper {
    @Delete("delete from base_arrearage_elf")
    Integer deleteAllArrearageElf();

    Integer insertArrearageElf(BaseArrearageElfBean baseArrearageElfBean);

    @Select("select * from base_arrearage_elf")
    BaseArrearageElfBean selectArrearageElf();

}
