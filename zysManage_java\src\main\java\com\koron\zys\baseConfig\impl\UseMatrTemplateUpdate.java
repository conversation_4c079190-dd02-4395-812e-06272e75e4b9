package com.koron.zys.baseConfig.impl;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.koron.zys.baseConfig.bean.UseMatrTemplateListBean;
import com.koron.zys.baseConfig.bean.UseMatrTemplateBean;
import com.koron.zys.baseConfig.mapper.UseMatrTemplateMapper;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;
import com.mysql.cj.util.StringUtils;

/**
 * 用料模板-编辑
 *
 * <AUTHOR>
 */
public class UseMatrTemplateUpdate implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(UseMatrTemplateUpdate.class);

    @Override
    @ValidationKey(clazz = UseMatrTemplateBean.class,method = "insert")
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            UseMatrTemplateMapper mapper = factory.getMapper(UseMatrTemplateMapper.class);
            UseMatrTemplateBean bean = JsonUtils.objectToPojo(req.getData(), UseMatrTemplateBean.class);
            // 校验字段重复
            if (mapper.check3("template_name", bean.getTemplateName(), bean.getId()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "模板名称：" + bean.getTemplateName() + "的信息已存在。", void.class);
            }
            if (StringUtils.isNullOrEmpty(bean.getId())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id不能为空", void.class);
            }
            bean.setUpdateInfo(userInfo);
            // 前端传过来的明细数据
            List<UseMatrTemplateListBean> useMatrTemplateList = bean.getUseMatrTemplateList();
            if (null == useMatrTemplateList || useMatrTemplateList.size() == 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "模板明细不能为空！", void.class);
            }
            // 删除修改前的的模板明细数据
            mapper.deleteMatrTemplateList(bean.getId());
            // 判断名称是否重复
            Set<String> set = new HashSet<>();
            for (UseMatrTemplateListBean templateListBean : useMatrTemplateList) {
                if (templateListBean.getMatrNo() == null || templateListBean.getMatrMode() == null || templateListBean.getMatrName() == null || templateListBean.getMatrNum() == null || templateListBean.getMatrUnit() == null) {
                    return MessageBean.create(Constant.ILLEGAL_PARAMETER, "模板明细填写内容不能为空！", void.class);
                }
                if (!set.add(templateListBean.getMatrName())) {
                    return MessageBean.create(Constant.ILLEGAL_PARAMETER, "材料名称：" + templateListBean.getMatrName() + "的信息已存在。", void.class);
                }
                templateListBean.setCreateInfo(userInfo);
                templateListBean.setTemplateId(bean.getId());
            }
            mapper.updateUseMatrTemplate(bean);
            mapper.insertUseMatrTemplateList(useMatrTemplateList);// 批量添加
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("用料模板修改失败", e);
            factory.close(false);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "用料模板修改失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}