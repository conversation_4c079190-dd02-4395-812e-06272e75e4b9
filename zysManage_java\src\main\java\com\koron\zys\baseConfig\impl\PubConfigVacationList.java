package com.koron.zys.baseConfig.impl;

import java.util.HashMap;
import java.util.List;

import com.koron.zys.ApplicationConfig;
import com.koron.zys.baseConfig.bean.ConfigVacationBean;
import com.koron.zys.serviceManage.mapper.DbServerMapper;
import com.koron.util.DBSourceUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.ConfigVacationMapper;
import com.koron.zys.baseConfig.queryBean.ConfigVacationQueryBean;
import com.koron.zys.baseConfig.vo.ConfigVacationVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 假期设置-列表初始化
 *
 * <AUTHOR>
 */
public class PubConfigVacationList implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(PubConfigVacationList.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        @SuppressWarnings("rawtypes")
        MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
        try {
            ConfigVacationQueryBean bean = JsonUtils.objectToPojo(req.getData(), ConfigVacationQueryBean.class);
            ConfigVacationMapper mapper = factory.getMapper(ConfigVacationMapper.class,"_default");
            PageHelper.startPage(bean.getPage(), bean.getPageCount());
            List<ConfigVacationVO> list = mapper.selectConfigVacationList(bean);
            info.setData(new PageInfo<>(list));

        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }

    //数据同步处理
    public static void synData(SessionFactory factory, ConfigVacationBean bean) {

        DbServerMapper dbMapper = factory.getMapper(DbServerMapper.class, "_default");
        List<String> companyList = dbMapper.selectCompany(ApplicationConfig.getWaterCodeFilter());
        for (String companyNo : companyList) {

            ConfigVacationMapper groupMapper = factory.getMapper(ConfigVacationMapper.class, DBSourceUtils.getDbEnv(companyNo));
            List<ConfigVacationVO> volist = groupMapper.selectConfigVacationList(new ConfigVacationQueryBean());
            HashMap<String, ConfigVacationVO> voHashMap = new HashMap<>();
            for (ConfigVacationVO vo : volist) {
                voHashMap.put(vo.getId(), vo);
            }
            if (voHashMap.containsKey(bean.getId())) {
                groupMapper.updateConfigVacation(bean);
            } else {
                groupMapper.insertConfigVacation(bean);
            }


        }
    }
}