<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.QuickChangeConfigMapper">
	<insert id="update">
		UPDATE
			user_quick_change_config
		SET
			isUse =#{isUse}
		WHERE
			`code`	= #{code}
	</insert>

	<select id="select" resultType="com.koron.zys.baseConfig.bean.QuickChangeConfigBean">
		select * from user_quick_change_config
	</select>

	<select id="selectCodeNameByType" resultType="com.koron.zys.baseConfig.bean.QuickChangeConfigBean">
		select * from user_quick_change_config where type = #{0}  and isUse = 1
	</select>
	<select id="selectCodeNameByUser" resultType="com.koron.zys.baseConfig.bean.QuickChangeConfigBean">
		select * from user_quick_change_config where type ='userInfoData'   or  type ='userWaterInfoData'
	</select>
	<delete id="delete">
		delete from user_quick_change_config where `code` = #{code} and `type` =#{type}
	</delete>
	<insert id="add">
		insert  into user_quick_change_config (`name`,`code`,`isUse`,`type`) value (#{name},#{code},#{isUse},#{type})
	</insert>
</mapper>