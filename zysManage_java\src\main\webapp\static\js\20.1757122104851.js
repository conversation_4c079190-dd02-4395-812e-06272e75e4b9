webpackJsonp([20],{DY2B:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={render:function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"codeRuleAdd"},[l("el-form",{ref:"codeRuleAddRuleForm",staticClass:"formBill-One",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"100px",inline:!0}},[l("el-form-item",{attrs:{label:"编号：",prop:"ruleCode"}},[l("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.ruleCode,callback:function(t){e.$set(e.ruleForm,"ruleCode",t)},expression:"ruleForm.ruleCode"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"名称：",prop:"ruleName"}},[l("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.ruleName,callback:function(t){e.$set(e.ruleForm,"ruleName",t)},expression:"ruleForm.ruleName"}})],1),e._v(" "),l("el-form-item",{staticClass:"expClass",attrs:{label:"编码规则：",prop:"startSerialNumber"}},[l("el-input",{staticClass:"expInputClass",attrs:{maxlength:"100"},model:{value:e.expForm.f1,callback:function(t){e.$set(e.expForm,"f1",t)},expression:"expForm.f1"}}),e._v(" "),l("el-select",{staticStyle:{width:"17% !important"},attrs:{placeholder:""},model:{value:e.expForm.s1,callback:function(t){e.$set(e.expForm,"s1",t)},expression:"expForm.s1"}},e._l(e.optionsRuleExp,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1),e._v(" "),l("el-input",{staticClass:"expInputClass",attrs:{maxlength:"100"},model:{value:e.expForm.f2,callback:function(t){e.$set(e.expForm,"f2",t)},expression:"expForm.f2"}}),e._v(" "),l("el-select",{staticStyle:{width:"17% !important"},attrs:{placeholder:""},model:{value:e.expForm.s2,callback:function(t){e.$set(e.expForm,"s2",t)},expression:"expForm.s2"}},e._l(e.optionsRuleExp,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1),e._v(" "),l("el-input",{staticClass:"expInputClass",attrs:{maxlength:"100"},model:{value:e.expForm.f3,callback:function(t){e.$set(e.expForm,"f3",t)},expression:"expForm.f3"}}),e._v(" "),l("el-select",{staticStyle:{width:"17%  !important"},attrs:{placeholder:""},model:{value:e.expForm.s3,callback:function(t){e.$set(e.expForm,"s3",t)},expression:"expForm.s3"}},e._l(e.optionsRuleExp,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1),e._v(" "),l("el-input",{staticClass:"expInputClass",attrs:{maxlength:"100"},model:{value:e.expForm.f4,callback:function(t){e.$set(e.expForm,"f4",t)},expression:"expForm.f4"}}),e._v(" "),l("el-select",{staticStyle:{width:"17%  !important"},attrs:{placeholder:""},model:{value:e.expForm.s4,callback:function(t){e.$set(e.expForm,"s4",t)},expression:"expForm.s4"}},e._l(e.optionsRuleExp,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),l("el-form-item",{attrs:{label:"起始流水号：",prop:"startSerialNumber"}},[l("el-input",{attrs:{maxlength:"100",clearable:""},model:{value:e.ruleForm.startSerialNumber,callback:function(t){e.$set(e.ruleForm,"startSerialNumber",t)},expression:"ruleForm.startSerialNumber"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"增量步长：",prop:"serialNumberIncrement"}},[l("el-input",{attrs:{maxlength:"100",clearable:""},model:{value:e.ruleForm.serialNumberIncrement,callback:function(t){e.$set(e.ruleForm,"serialNumberIncrement",t)},expression:"ruleForm.serialNumberIncrement"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"重置周期：",prop:"resetCycle"}},[l("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.resetCycle,callback:function(t){e.$set(e.ruleForm,"resetCycle",t)},expression:"ruleForm.resetCycle"}},e._l(e.optionsResetCycle,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)],1)],1)},staticRenderFns:[]};var r={name:"codeRule",components:{codeRuleAdd:l("VU/8")({name:"codeRuleAdd",data:function(){return{ruleForm:{ruleCode:"",ruleName:"",ruleExp:"",ruleExpRemark:"",startSerialNumber:"1",serialNumberIncrement:"1",resetCycle:0},expForm:{f1:"",s1:"",f2:"",s2:"",f3:"",s3:"",f4:"",s4:""},optionsResetCycle:[{value:0,label:"无"},{value:1,label:"年"},{value:2,label:"月"},{value:3,label:"日"}],optionsRuleExp:[{value:"",label:""},{value:"{date yy}",label:"年年"},{value:"{date MM}",label:"月月"},{value:"{date dd}",label:"日日"},{value:"{date HH:mm:ss}",label:"时时:分分:秒秒"},{value:"{date HHmmss}",label:"时时分分秒秒"},{value:"{date yyyy}",label:"年年年年"},{value:"{date yyyyMMdd}",label:"年年年年月月日日"},{value:"{date yyyy-MM-dd}",label:"年年年年-月月-日日"},{value:"{date yyyy/MM/dd}",label:"年年年年/月月/日日"},{value:"{companySample}",label:"水司简化编号"},{value:"{company}",label:"水司编号"},{value:"{2serial}",label:"2位流水"},{value:"{3serial}",label:"3位流水"},{value:"{4serial}",label:"4位流水"},{value:"{5serial}",label:"5位流水"},{value:"{6serial}",label:"6位流水"},{value:"{7serial}",label:"7位流水"},{value:"{8serial}",label:"8位流水"},{value:"{9serial}",label:"9位流水"},{value:"{10serial}",label:"10位流水"}],rules:{code:[{required:!0,message:"请输入编号",trigger:"blur"}],name:[{required:!0,message:"请输入名称",trigger:"blur"}]}}},mounted:function(){},methods:{resetForm:function(){this.$refs.codeRuleAddRuleForm.resetFields()},getExpRemark:function(e){for(var t=0;t<this.optionsRuleExp.length;t++)if(e==this.optionsRuleExp[t].value)return this.optionsRuleExp[t].label;return""},submitForm:function(e,t){var l=this,a=this,r={};this.$refs[e].validate(function(e){if(!e)return!1;l.ruleForm.ruleExp=l.expForm.f1+l.expForm.s1+l.expForm.f2+l.expForm.s2+l.expForm.f3+l.expForm.s3+l.expForm.f4+l.expForm.s4,l.ruleForm.ruleExpRemark=l.expForm.f1+l.getExpRemark(l.expForm.s1)+l.expForm.f2+l.getExpRemark(l.expForm.s2)+l.expForm.f3+l.getExpRemark(l.expForm.s3)+l.expForm.f4+l.getExpRemark(l.expForm.s4),r="添加"===t?{busicode:"CodeRuleAdd",data:l.ruleForm}:{busicode:"CodeRuleUpdate",data:l.ruleForm},l.$api.fetch({params:r}).then(function(e){a.$message({showClose:!0,message:"保存成功",type:"success"}),a.$parent.selectList(),a.$parent.closeDialog(),l.resetForm()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","codeRuleAdd",this.$parent.closeDialog)},editData:function(e){this.ruleForm=e;for(var t=e.ruleExp,l=1;t.indexOf("}")>-1;){var a=t.substring(0,t.indexOf("}")+1),r=a.substring(0,a.indexOf("{")),s=a.substring(a.indexOf("{"));this.expForm["f"+l]=r,this.expForm["s"+l]=s,l++,t=t.substring(t.indexOf("}")+1)}t.length>0&&(this.expForm["f"+l]=t,l++);for(var i=l;i<=4;i++)this.expForm["f"+i]="",this.expForm["s"+i]=""}}},a,!1,function(e){l("WAV9")},null,null).exports},data:function(){return{tableShow:!1,tableQuery:{code:"",name:"",page:1,pageCount:50},maxHeight:0,listData:[],crumbsData:{titleList:[{title:"基础配置",path:"/ChangeTables"},{title:"编码规则",method:function(){window.histroy.back()}}]},codeRuleShow:!0,codeRuleAddVisible:!1,formData:{ruleCode:"",ruleName:"",ruleExp:"",ruleExpRemark:"",startSerialNumber:"1",serialNumberIncrement:"1",resetCycle:0}}},mounted:function(){this.selectList()},methods:{formatStatus:function(e){return 1===e.status?"启用":"禁用"},formatResetCycle:function(e){return 1==e.resetCycle?"年":2==e.resetCycle?"月":3==e.resetCycle?"日":0==e.resetCycle?"无":void 0},search:function(){this.tableQuery.page=1,this.selectList()},appAdd:function(e){var t=this;if(this.codeRuleShow=!1,this.codeRuleAddVisible=!0,"add"===e)this.$refs.codeRuleAdd.editData({ruleCode:"",ruleName:"",ruleExp:"",ruleExpRemark:"",startSerialNumber:"1",serialNumberIncrement:"1",resetCycle:0}),this.$set(this.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),this.common.chargeObjectEqual(this,this.formData,"set","codeRuleAdd");else{this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var l={busicode:"CodeRuleQuery",data:{id:e.row.id}};this.$api.fetch({params:l}).then(function(e){t.$refs.codeRuleAdd.editData(e),t.common.chargeObjectEqual(t,e,"set","codeRuleAdd")})}},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.selectList()},handleCurrentChange:function(e){this.tableQuery.page=e,this.selectList()},selectList:function(){var e=this,t={busicode:"CodeRuleList",data:this.tableQuery};this.$api.fetch({params:t}).then(function(t){e.listData=t,e.common.changeTable(e,".codeRule .kl-table",[".codeRule .block"])})},closeDialog:function(){this.codeRuleShow=!0,this.codeRuleAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.codeRuleAdd.handleClose()},submitForm:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.codeRuleAdd.submitForm(e,t)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},s={render:function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"codeRule"},[l("div",{staticClass:"main-content"},[l("div",{staticClass:"bread-contain"},[l("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:e.codeRuleShow,expression:"codeRuleShow"}],staticClass:"bread-contain-right"},[l("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:e.codeRuleAddVisible,expression:"codeRuleAddVisible"}],staticClass:"bread-contain-right"},[l("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("codeRuleAddRuleForm")}}},[e._v("保存")]),e._v(" "),l("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:e.codeRuleShow,expression:"codeRuleShow"}],staticClass:"kl-table"},[l("div",{staticClass:"toolbar"},[l("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.tableQuery,size:"mini"}},[l("div",{staticClass:"toolbar-left"},[l("el-form-item",{attrs:{label:"编号："}},[l("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.code,callback:function(t){e.$set(e.tableQuery,"code",t)},expression:"tableQuery.code"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"名称："}},[l("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.name,callback:function(t){e.$set(e.tableQuery,"name",t)},expression:"tableQuery.name"}})],1),e._v(" "),l("el-form-item",{staticClass:"button-group"},[l("el-button",{staticClass:"compile",attrs:{type:"primary el-icon-search",size:"mini"},on:{click:e.search}})],1)],1),e._v(" "),l("div",{staticClass:"toolbar-right"})])],1),e._v(" "),e.tableShow?l("el-table",{attrs:{stripe:"",border:"",data:e.listData.list,"max-height":e.maxHeight}},[l("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),l("el-table-column",{attrs:{prop:"ruleCode",align:"left",label:"编号","min-width":"100"}}),e._v(" "),l("el-table-column",{attrs:{prop:"ruleName",align:"left",label:"名称","min-width":"100"}}),e._v(" "),l("el-table-column",{attrs:{prop:"ruleExpRemark",align:"left",label:"表达式"}}),e._v(" "),l("el-table-column",{attrs:{prop:"startSerialNumber",label:"起始编号","min-width":"50"}}),e._v(" "),l("el-table-column",{attrs:{prop:"serialNumberIncrement",label:"增量步长","min-width":"50"}}),e._v(" "),l("el-table-column",{attrs:{prop:"resetCycle",label:"重置周期","min-width":"50",formatter:e.formatResetCycle}}),e._v(" "),l("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{attrs:{type:"text"},on:{click:function(l){return e.appAdd(t)}}},[e._v("编辑")])]}}],null,!1,1191321586)})],1):e._e(),e._v(" "),l("div",{staticClass:"block"},[l("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.listData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),l("div",{directives:[{name:"show",rawName:"v-show",value:e.codeRuleAddVisible,expression:"codeRuleAddVisible"}]},[l("codeRuleAdd",{ref:"codeRuleAdd"})],1)])])},staticRenderFns:[]};var i=l("VU/8")(r,s,!1,function(e){l("LE15")},null,null);t.default=i.exports},LE15:function(e,t){},WAV9:function(e,t){}});