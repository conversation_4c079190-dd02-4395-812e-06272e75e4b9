package com.koron.zys.baseConfig.bean;

/**
 * 附件元数据
 * <AUTHOR>
 *
 */
public class BaseAccessoryMetadataBean extends BaseBean{
	
	/**
	 * 主键
	 */
	private String id;
	
	/**
	 * 附件名称
	 */
	private String accessoryName;
	
	/**
	 * 附件类型
	 */
	private String accessoryType;
	
	/**附件路径
	 * 
	 */
	private String accessoryPath;
	
	/**
	 * 附件大小
	 */
	private Long accessorySize;
	
	/**
	 * 附件来源
	 */
	private String sourceFlag;
	
	/**
	 * 附件来源
	 */
	private String sourceFlagName;
	
	public String getSourceFlagName() {
		return sourceFlagName;
	}

	public void setSourceFlagName(String sourceFlagName) {
		this.sourceFlagName = sourceFlagName;
	}

	public String getId() {
		return id;
	}

	public String getAccessoryName() {
		return accessoryName;
	}

	public String getAccessoryType() {
		return accessoryType;
	}

	public String getAccessoryPath() {
		return accessoryPath;
	}

	public Long getAccessorySize() {
		return accessorySize;
	}

	public String getSourceFlag() {
		return sourceFlag;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setAccessoryName(String accessoryName) {
		this.accessoryName = accessoryName;
	}

	public void setAccessoryType(String accessoryType) {
		this.accessoryType = accessoryType;
	}

	public void setAccessoryPath(String accessoryPath) {
		this.accessoryPath = accessoryPath;
	}

	public void setAccessorySize(Long accessorySize) {
		this.accessorySize = accessorySize;
	}

	public void setSourceFlag(String sourceFlag) {
		this.sourceFlag = sourceFlag;
	}
	
}
