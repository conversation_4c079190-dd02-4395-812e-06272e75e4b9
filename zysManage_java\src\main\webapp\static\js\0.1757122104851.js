webpackJsonp([0],{BO1k:function(e,t,n){e.exports={default:n("fxRn"),__esModule:!0}},Cdx3:function(e,t,n){var r=n("sB3e"),s=n("lktj");n("uqUo")("keys",function(){return function(e){return s(r(e))}})},Syrd:function(e,t){},fZjL:function(e,t,n){e.exports={default:n("jFbC"),__esModule:!0}},fxRn:function(e,t,n){n("+tPU"),n("zQR9"),e.exports=n("g8Ux")},g8Ux:function(e,t,n){var r=n("77Pl"),s=n("3fs2");e.exports=n("FeBl").getIterator=function(e){var t=s(e);if("function"!=typeof t)throw TypeError(e+" is not iterable!");return r(t.call(e))}},jFbC:function(e,t,n){n("Cdx3"),e.exports=n("FeBl").Object.keys},yJVD:function(e,t,n){"use strict";var r=n("Dd8w"),s=n.n(r),i=n("fZjL"),o=n.n(i),a={props:["treeData"],components:{},data:function(){return{treedata:[],defaultProps:{},sendTreeProp:[],filterText:"",loading:!0,inputProp:{Inp_placeholder:"",inputshow:null},nodeKey:"id",expanded:null,checkedNode:null,show:!0}},watch:{treeData:{immediate:!0,handler:function(e){e&&this.init()},deep:!0},filterText:function(e){this.$refs.tree2.filter(e)}},created:function(){},mounted:function(){},methods:{init:function(){var e=this,t=this.treeData;if(t){if(!t.hasOwnProperty("defaultProps"))return this.$set(this,"defaultProps",{children:"_child",label:"name"}),console.error("请传入树的默认label和children配置");if(0!==o()(t.defaultProps).length?this.$set(this,"defaultProps",t.defaultProps):this.$set(this,"defaultProps",{children:"_child",label:"name"}),t.hasOwnProperty("tree"))if(t.tree){if(t.tree.constructor==Object){if(0!==o()(t.tree).length){var n=this.gettree([t.tree]);if(n[0].hasOwnProperty("name")||(n[0].name=t.rootName?t.rootName:"根节点"),this.$set(this,"treedata",n),t.hasOwnProperty("defaultOpen"))if(0!=o()(t.defaultOpen).length){this.$set(this,"nodeKey",t.defaultOpen.nodeKey?t.defaultOpen.nodeKey:"id");var r=t.defaultOpen.nodeItem?t.defaultOpen.nodeItem:n[0][this.nodeKey]?[n[0][this.nodeKey]]:[n[0][this.defaultProps.children][0][this.nodeKey]];this.$set(this,"expanded",r)}else this.$set(this,"expanded",[n[0][this.nodeKey]]);else this.$set(this,"expanded",[n[0][this.nodeKey]]);setTimeout(function(){e.$refs.tree2.setCurrentKey(e.expanded[0])},0)}else this.$set(this,"treedata",[]);this.$set(this,"loading",!1)}else if(t.tree.constructor==Array){if(0!==t.tree.length){var s=void 0;if((s=t.tree[0].hasOwnProperty(t.defaultProps.children)?t.tree:this.gettree(t.tree))[0].hasOwnProperty("name")||(s[0].name=t.rootName?t.rootName:"根节点"),this.$set(this,"treedata",s),t.hasOwnProperty("defaultOpen"))if(0!=o()(t.defaultOpen).length){this.$set(this,"nodeKey",t.defaultOpen.nodeKey?t.defaultOpen.nodeKey:"id");var i=t.defaultOpen.nodeItem?t.defaultOpen.nodeItem:s[0][this.nodeKey]?[s[0][this.nodeKey]]:[s[0][this.defaultProps.children][0][this.nodeKey]];this.$set(this,"expanded",i)}else this.$set(this,"expanded",[s[0][this.nodeKey]]);else this.$set(this,"expanded",[s[0][this.nodeKey]]);setTimeout(function(){e.$refs.tree2.setCurrentKey(e.expanded[0])},0)}else this.$set(this,"treedata",[]);this.$set(this,"loading",!1)}}else this.$set(this,"treedata",[]);else this.$set(this,"treedata",[]);if(t.hasOwnProperty("sendTreeProp")&&0!==t.sendTreeProp.length?this.$set(this,"sendTreeProp",t.sendTreeProp):this.$set(this,"sendTreeProp",[]),t.hasOwnProperty("inputProp"))0!==o()(t.inputProp).length?(this.$set(this.inputProp,"inputshow",""===t.inputProp.showSearch||t.inputProp.showSearch),this.$set(this.inputProp,"Inp_placeholder",""!==t.inputProp.Inp_placeholder?t.inputProp.Inp_placeholder:"请输入部门")):(this.$set(this.inputProp,"inputshow",!0),this.$set(this.inputProp,"Inp_placeholder","请输入"));else this.$set(this.inputProp,"inputshow",!0),this.$set(this.inputProp,"Inp_placeholder","请输入")}},handleNodeClick:function(e){console.log("6666",e);var t={};0!==this.sendTreeProp.length?this.sendTreeProp.map(function(n){null!==e[n]&&e.hasOwnProperty(n)?t[n]=e[n]:t[n]="暂无"}):t=s()({},e),console.log("7777",e),this.$emit("sendTreeData",t)},refreshChecked:function(e){var t=this;e&&(this.expanded=[e],this.$nextTick(function(){t.$refs.tree2.setCurrentKey(e)}))},filterNode:function(e,t){return!e||-1!==t[this.defaultProps.label].indexOf(e)},gettree:function(e){var t=new Array,n=new Array;return n.push(e[0]),t.push(e[0]),e.forEach(function(e,n){0!=n&&function e(n){n.parentMask>t[t.length-1].parentMask?(t[t.length-1]._child||(t[t.length-1]._child=new Array),t[t.length-1]._child.push(n),t.push(n)):n.parentMask<=t[t.length-1].parentMask&&(t.pop(),e(n))}(e)}),n},handle:function(e){console.log(e)}}},d={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{id:"tree"}},[e.inputProp.inputshow?n("div",{staticClass:"search"},[n("el-input",{attrs:{placeholder:e.inputProp.Inp_placeholder,"suffix-icon":"el-icon-search"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}})],1):e._e(),e._v(" "),e.show?n("el-tree",{ref:"tree2",staticClass:"tree",style:{height:e.inputProp.inputshow?"calc(100% - 80px)":"100%",paddingBottom:e.inputProp.inputshow?"50px":"0px"},attrs:{"highlight-current":!0,data:e.treedata,props:e.defaultProps,"node-key":e.nodeKey,accordion:"",indent:0,"default-expanded-keys":e.expanded,"expand-on-click-node":!0,"filter-node-method":e.filterNode,"render-after-expand":!1},on:{"node-click":e.handleNodeClick},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.node;return n("div",{staticClass:"node-label",domProps:{innerHTML:e._s(r.label)}})}}],null,!1,696479709)}):e._e()],1)},staticRenderFns:[]};var l=n("VU/8")(a,d,!1,function(e){n("Syrd")},"data-v-55bea3de",null);t.a=l.exports}});