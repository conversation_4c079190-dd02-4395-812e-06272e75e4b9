webpackJsonp([42],{O14M:function(e,t){},sJN2:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("mvHQ"),s=a.n(r),i={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"areaManAdd"},[a("el-form",{ref:"ruleForm",staticClass:"formBill",attrs:{model:e.ruleForm,"label-width":"120px",inline:!0}},[a("el-form-item",{attrs:{label:"片区编号："}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.areaNo,callback:function(t){e.$set(e.ruleForm,"areaNo",t)},expression:"ruleForm.areaNo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"片区名称："}},[a("el-input",{attrs:{clearable:""},model:{value:e.ruleForm.areaName,callback:function(t){e.$set(e.ruleForm,"areaName",t)},expression:"ruleForm.areaName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态："}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),a("el-option",{attrs:{label:"停用",value:2}})],1)],1),e._v(" "),a("el-form-item",{staticClass:"f2",attrs:{label:"备注:"}},[a("el-input",{attrs:{type:"textarea",maxlength:"50","show-word-limit":""},model:{value:e.ruleForm.comments,callback:function(t){e.$set(e.ruleForm,"comments",t)},expression:"ruleForm.comments"}})],1)],1)],1)},staticRenderFns:[]},n={name:"areaMan",components:{areaManAdd:a("VU/8")({name:"areaManAdd",data:function(){return{databaseData:[],ruleForm:{areaNo:"",areaName:"",status:1,comments:""}}},mounted:function(){},methods:{resetForm:function(){this.$refs.ruleForm.resetFields()},submitForm:function(e,t){var a=this,r=this,s="",i={};this.$refs[e].validate(function(e){if(!e)return!1;"新建"===t?(i={busicode:"CompanyAreaAdd",data:a.ruleForm},s="新建成功"):(i={busicode:"CompanyAreaUpdate",data:a.ruleForm},s="保存成功"),a.$api.fetch({apiUrl:"interface.api",method:"post",params:i}).then(function(e){r.$message({showClose:!0,message:s,type:"success"}),r.$parent.selectTSubSystem(),r.resetForm(),r.$parent.areaManAddVisible=!1,r.$parent.areaManShow=!0,r.$parent.crumbsData.titleList.pop()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","childAreaMan",this.$parent.closeDialog)},editData:function(e){this.ruleForm=e}}},i,!1,null,null,null).exports},data:function(){return{total:0,tableShow:!0,tableQuery:{page:1,pageCount:50},maxHeight:0,appServerData:[],showSelectIndex:-1,formData:{areaNo:"",areaName:"",status:"",comments:""},crumbsData:{titleList:[{title:"基础配置",path:"/ChangeTables"},{title:"片区管理",method:function(){window.histroy.back()}}]},areaManShow:!0,areaManAddVisible:!1}},mounted:function(){this.selectTSubSystem()},methods:{submitForm:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.child.submitForm(e,t)},clear:function(){this.formData.areaNo="",this.formData.areaName="",this.formData.status=1,this.formData.comments=""},formatStatus:function(e){return 1===e.status?"启用":"停用"},appAdd:function(e){var t=this;if(this.clear(),"add"===e)this.$set(this.crumbsData.titleList,"2",{title:"新建",method:function(){window.histroy.back()}}),this.$refs.child.editData(this.formData),this.common.chargeObjectEqual(this,this.formData,"set","childAreaMan");else{this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var a={busicode:"CompanyAreaQuery",data:{companyAreaId:e.row.companyAreaId}};this.$api.fetch({params:a}).then(function(e){t.$refs.child.editData(e),sessionStorage.setItem("formData",s()(t.formData)),t.common.chargeObjectEqual(t,e,"set","childAreaMan")})}this.areaManShow=!1,this.areaManAddVisible=!0},showSelect:function(e){this.showSelectIndex=e.$index},editor:function(e){this.formData=e.row,this.parentAppDialogVisible=!0},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+e+1},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(e){this.tableQuery.page=e,this.selectTSubSystem()},selectTSubSystem:function(){var e=this,t=this,a={busicode:"CompanyAreaList",data:t.tableQuery};this.$api.fetch({params:a}).then(function(a){t.$set(t.appServerData,"list",a.list),t.total=a.total,t.common.changeTable(e,".areaMan .kl-table",[".areaMan .block"])})},closeDialog:function(){this.areaManShow=!0,this.areaManAddVisible=!1,this.crumbsData.titleList.pop(),this.$refs.child.resetForm()},handleClose:function(){this.$refs.child.handleClose()}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"areaMan"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.areaManShow,expression:"areaManShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("新建")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.areaManAddVisible,expression:"areaManAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("ruleForm")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.areaManShow,expression:"areaManShow"}],staticClass:"kl-table"},[e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.appServerData.list,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"areaNo",label:"片区编号","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"areaName",label:"片区名称","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"status",formatter:e.formatStatus,"min-width":"80",label:"状态"}}),e._v(" "),a("el-table-column",{attrs:{prop:"comments",label:"备注","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.appAdd(t)}}},[e._v("编辑")])]}}],null,!1,1191321586)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.areaManAddVisible,expression:"areaManAddVisible"}]},[a("areaManAdd",{ref:"child"})],1)])])},staticRenderFns:[]};var o=a("VU/8")(n,l,!1,function(e){a("O14M")},null,null);t.default=o.exports}});