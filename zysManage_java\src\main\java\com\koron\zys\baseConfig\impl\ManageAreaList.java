package com.koron.zys.baseConfig.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.ManageAreaMapper;
import com.koron.zys.baseConfig.queryBean.ManageAreaQueryBean;
import com.koron.zys.baseConfig.vo.ManageAreaVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.SelectBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;

import com.koron.util.Constant;
import com.koron.util.JsonUtils;

/**
 * 查询管理区域
 * <AUTHOR>
 */
public class ManageAreaList implements ServerInterface {
	private static Logger logger = LoggerFactory.getLogger(ManageAreaList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<HashMap> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", HashMap.class);
		try {
			List<ManageAreaVO> beans = new ArrayList<ManageAreaVO>();
			ManageAreaMapper mapper = factory.getMapper(ManageAreaMapper.class);
			ManageAreaQueryBean bean = JsonUtils.objectToPojo(req.getData(), ManageAreaQueryBean.class);
			bean.setIsLeaf(-1);//查所有数据
			beans = mapper.findManageArea(bean);
			
			HashMap<String,Object> map = new HashMap<>();
			map.put("pageInfo", new PageInfo<>(beans));
			//找出所有上级目录，显示分级导航
			ArrayList<HashMap<String,String>> navList = new ArrayList<>();
			recParent(mapper, bean.getAreaNo(), navList);
			map.put("navData", navList); 
			info.setCode(Constant.MESSAGE_INT_SUCCESS);
			info.setDescription("success");
			info.setData(map);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_DBFAIL);
			info.setDescription("数据库异常");
			logger.error("数据库异常",e);
			factory.close(false);
		}
		return info;
	}
	
	/**
	 * 递归查询每一级目录
	 * @param code
	 * @param sb
	 */
	private void recParent(ManageAreaMapper mapper,String code,ArrayList<HashMap<String,String>> navList) {
		if(code.length()>0){
			SelectBean selb = mapper.findManageAreaByCode(code);
			HashMap<String,String> m = new HashMap<>();
			m.put("code", code);
			m.put("name", selb.getName());
			//存储id和parentId
			m.put("id", selb.getId());
			m.put("parentId", selb.getParentId());
			navList.add(0,m);
			recParent(mapper,code.substring(0, code.length()-5),navList);
		}
	}

}
