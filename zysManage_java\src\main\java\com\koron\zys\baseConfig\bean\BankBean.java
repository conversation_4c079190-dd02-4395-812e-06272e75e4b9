package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;

public class BankBean extends BaseBean{
	
	private String parentId;
	/**
	 * 银行编号（国标）code
	 */
	private String bankNo;
	/**
	 * 银行名称name
	 */
	@Check(name = "银行名称", notEmpty = true)
	private String bankName;
	
	/**
	 * 银行行号
	 */
	@Check(name = "银行行号", notEmpty = true)
	private String bankNumber;
	
	/**
	 * 联系人
	 */
	@Check(name = "联系人")
	private String linkMan;
	
	/**
	 * 联系电话
	 */
	@Check(name = "联系电话")
	private String linkTel;
	
	/**
	 * 备注
	 */
	private String comments;

	/**
	 * 是否叶子节点 1是0否
	 */
	private Integer isLeaf;
	
	/**
	 * 状态启用状态 [ 0 停用 1 启用 ]
	 */
	@Check(name = "状态", notEmpty = true)
	private Integer status;
	
	/**
	 * 排序号
	 */
	private Integer sortNo;

	/**
	 *
	 */
	private String serviceCode;

	public String getServiceCode() {
		return serviceCode;
	}

	public void setServiceCode(String serviceCode) {
		this.serviceCode = serviceCode;
	}

	public String getParentId() {
		return parentId;
	}
	public void setParentId(String parentId) {
		this.parentId = parentId;
	}
	public String getBankNo() {
		return bankNo;
	}
	public void setBankNo(String bankNo) {
		this.bankNo = bankNo;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getBankNumber() {
		return bankNumber;
	}
	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}
	public String getLinkMan() {
		return linkMan;
	}
	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}
	public String getLinkTel() {
		return linkTel;
	}
	public void setLinkTel(String linkTel) {
		this.linkTel = linkTel;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public Integer getIsLeaf() {
		return isLeaf;
	}
	public void setIsLeaf(Integer isLeaf) {
		this.isLeaf = isLeaf;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Integer getSortNo() {
		return sortNo;
	}
	public void setSortNo(Integer sortNo) {
		this.sortNo = sortNo;
	}
}
