<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.ProjectPayeeMapper">
    <select id="selectList" parameterType="com.koron.zys.baseConfig.bean.ProjectPayeeBean"
    	resultType="com.koron.zys.baseConfig.bean.ProjectPayeeBean">
        select id,subject_name,comments,
        case when status = 1 then '启用' else '停用' end status
        from
        base_project_payee
        where 1=1
        <if test="subjectName != null and subjectName != ''">
            and subject_name = #{subjectName}
        </if>
    </select> 
    
    <select id="query" parameterType="com.koron.zys.baseConfig.bean.ProjectPayeeBean"
    	resultType="com.koron.zys.baseConfig.bean.ProjectPayeeBean">
    	select id,subject_name,comments, status from base_project_payee where id =#{id}
    </select> 
    
    <insert id="insert" parameterType="com.koron.zys.baseConfig.bean.ProjectPayeeBean">
    	insert into base_project_payee(id,subject_name,status,comments,tenant_id,create_time,create_name,create_account) 
    	values
    	(#{id},#{subjectName},#{status},#{comments},#{tenantId},#{createTime},#{createName},#{createAccount}) 
    </insert> 
    
    <update id="update" parameterType="com.koron.zys.baseConfig.bean.ProjectPayeeBean">
    	update base_project_payee set subject_name=#{subjectName},status=#{status},comments=#{comments},
    		update_time=#{updateTime},update_name=#{updateName},update_account=#{updateAccount} where id = #{id}
    </update> 
    
    <delete id="delete" parameterType="com.koron.zys.baseConfig.bean.ProjectPayeeBean">
    	delete from base_project_payee where id = #{id}
    </delete>
</mapper>