<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.PrefStrategyMapper">

    <!--列表初始化 -->
    <select id="selectPrefStrategyList"
            parameterType="com.koron.zys.baseConfig.queryBean.PrefStrategyQueryBean"
            resultType="com.koron.zys.baseConfig.vo.PrefStrategyVO">
        select ps.id,ps.strategy_name,ps.comments, case when ps.status=1 then
        '启用' else '停用' end status,ps.tenant_id
        from BASE_PREF_STRATEGY ps
        where 1=1
        <if test="status != null">
            and ps.status = #{status}
        </if>
        <if test="searchContent !=null and searchContent !=''">
            and ps.strategy_name LIKE CONCAT('%',#{searchContent},'%')
        </if>
    </select>
    <!--下拉框 -->
    <select id="prefStrategySelect" resultType="com.koron.zys.baseConfig.vo.SelectVO">
		select id,strategy_name name
		from BASE_PREF_STRATEGY 
		where status=1
	</select>
    <!--添加 -->
    <insert id="insertPrefStrategy" parameterType="com.koron.zys.baseConfig.bean.PrefStrategyBean">
		insert into BASE_PREF_STRATEGY(id,strategy_name,status,comments,tenant_id,create_time,create_account, create_name)
		values
		(
		#{id},
		#{strategyName},
		#{status},
		#{comments},
		#{tenantId},
		#{createTime},
		#{createAccount},
		#{createName}
		)
	</insert>
    <!--修改 -->
    <update id="updatePrefStrategy" parameterType="com.koron.zys.baseConfig.bean.PrefStrategyBean">
		update
		BASE_PREF_STRATEGY
		set
		strategy_name = #{strategyName },
		status = #{status },
		comments = #{comments },
		update_time= #{updateTime},
		update_account= #{updateAccount},
		update_name = #{updateName }
		where id = #{id}
	</update>

    <resultMap type="com.koron.zys.baseConfig.bean.PrefStrategyBean"
               id="PrefStrategy_map">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="strategy_name" property="strategyName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="comments" property="comments" jdbcType="VARCHAR"/>
        <collection property="prefStrategyList" ofType="com.koron.zys.baseConfig.bean.PrefStrategyDetailBean">
            <id property="id" column="pref_strategy_detail_id" jdbcType="VARCHAR"/>
            <result property="prefStrategyId" column="pref_strategy_id" jdbcType="VARCHAR"/>
            <result property="costId" column="cost_id" jdbcType="VARCHAR"/>
            <result property="minRange" column="min_range" jdbcType="INTEGER"/>
            <result property="maxRange" column="max_range" jdbcType="INTEGER"/>
            <result property="prefWay" column="pref_way" jdbcType="VARCHAR"/>
            <result property="prefValue" column="pref_value" jdbcType="INTEGER"/>
        </collection>
    </resultMap>
    <!--编辑初始化 -->
    <select id="selectPrefStrategyDetailById" resultMap="PrefStrategy_map">
		select
		a.id,
		a.strategy_name,
		a.status,
		a.comments,
		b.id pref_strategy_detail_id,
		b.pref_strategy_id,
		b.cost_id,
		b.min_range,
		b.max_range,
		b.pref_way,
		b.pref_value
		from
		BASE_PREF_STRATEGY a
		left join BASE_PREF_STRATEGY_DETAIL b
		on a.id =
		b.pref_strategy_id
		where a.id = #{id}
		order by b.id
	</select>
    <!--编辑初始化 -->
    <select id="selectPrefStrategyById" resultType="com.koron.zys.baseConfig.bean.PrefStrategyBean">
		select
		id,
		strategy_name,
		status
		from
		BASE_PREF_STRATEGY
		where id = #{id}
	</select>
	<!--根据名称查询id -->
    <select id="selectPrefStrategyByname" resultType="com.koron.zys.baseConfig.bean.PrefStrategyBean">
		select
		id,
		strategy_name,
		status
		from
		BASE_PREF_STRATEGY
		where strategy_name = #{strategyName}
	</select>

    <!--根据名称模糊查询id -->
    <select id="selectPrefStrategysByname" resultType="com.koron.zys.baseConfig.bean.PrefStrategyBean">
		select
		id,
		strategy_name,
		status
		from
		BASE_PREF_STRATEGY
		where strategy_name like  concat('%',#{strategyName},'%')
	</select>

    <!--批量添加List集合对象 -->
    <insert id="insertPrefStrategyDetailList">
        INSERT INTO BASE_PREF_STRATEGY_DETAIL
        (id,pref_strategy_id,cost_id,min_range,max_range,pref_way,pref_value,tenant_id,create_time,create_account,create_name)
        VALUES
        <foreach item="item" separator="," collection="prefStrategyList">
          (#{item.id},
            #{item.prefStrategyId},
            #{item.costId},
            #{item.minRange},
            #{item.maxRange},
            #{item.prefWay},
            #{item.prefValue},
            #{item.tenantId},
            #{item.createTime},
            #{item.createAccount},
            #{item.createName})
        </foreach>
    </insert>

    <!--批量删除 从id集合List中删除多个对象 注意参数为集合 PrefStrategyDetailIds -->
    <delete id="DeletePrefStrategyDetailList" parameterType="java.util.List">
        DELETE FROM
        BASE_PREF_STRATEGY_DETAIL
        <where>
            id in
            <foreach collection="list" item="item" index="index" open="("
                     close=")" separator=",">
                #{item.id,jdbcType=VARCHAR}
            </foreach>
        </where>
    </delete>

    <!--批量更新从表信息 -->
    <update id="UpdatePrefStrategyDetailList" parameterType="java.util.List">
        <foreach collection="prefStrategylList" item="item" index="index"
                 open="begin" close=";end;" separator=";">
            UPDATE BASE_PREF_STRATEGY_DETAIL
            <set>
                <if test="item.costName != null">
                    cost_name = #{item.costName,jdbcType=VARCHAR},
                </if>

                <if test="item.minRange != null">
                    min_range = #{item.minRange,jdbcType=INTEGER},
                </if>

                <if test="item.maxRange != null">
                    max_range = #{item.maxRange,jdbcType=INTEGER},
                </if>

                <if test="item.prefWay!= null">
                    pref_way = #{item.prefWay,jdbcType=INTEGER},
                </if>

                <if test="item.prefValue != null">
                    pref_value = #{item.prefValue,jdbcType=INTEGER},
                </if>

            </set>
            where id = #{item.id}
        </foreach>
    </update>
</mapper>