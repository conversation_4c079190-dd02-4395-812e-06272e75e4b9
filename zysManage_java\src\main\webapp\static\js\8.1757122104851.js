webpackJsonp([8],{"7dXs":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("aFK5"),r=a.n(i),s=a("mvHQ"),l=a.n(s),n={name:"InterfaceManAdd",data:function(){return{formDataService:{serviceCode:"",className:"",comments:"",importExample:"",outputExample:"",status:1,tServiceDetails:[]},funcData:[],appServerData:[],optionsTest:[],options:[{value:"1",label:"增"},{value:"2",label:"删"},{value:"3",label:"改"},{value:"4",label:"查"},{value:"5",label:"导出"}],rules:{serviceCode:[{required:!0,message:"请输入服务标识",trigger:"blur"}],className:[{required:!0,message:"请输入服务类名",trigger:"blur"}],dbMaster:[{required:!0,message:"请选择主从数据源",trigger:"blur"}],ipAddr:[{required:!0,message:"请输入活动名称",trigger:"blur"}],port:[{required:!0,message:"请输入编号ip地址",trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"blur"}]},props:{label:"name",children:"children",value:"code",checkStrictly:!0},multipleSelection:"",maxHeight:0,tableShow:!1}},mounted:function(){var e=this;this.getData(),this.$nextTick(function(){e.common.changeTable(e,".InterfaceManAdd",[".InterfaceManAdd .formInterfaceManAdd"])})},methods:{getData:function(){var e=this;this.$api.fetch({params:{busicode:"TSubSystemList",data:{}}}).then(function(t){e.appServerData=t})},handleSelectionChange:function(e){this.multipleSelection=e},changeCode:function(e,t){var a=this,i=e.row.systemCode,r={busicode:"ServiceModuleList",data:{systemCodes:[i]}};this.$api.fetch({params:r}).then(function(e){"{}"==l()(e)?a.$set(a.optionsTest,t,[]):a.$set(a.optionsTest,t,a.treeData(e[i].children)),a.$set(a.formDataService.tServiceDetails[t],"moduleId",[])})},treeData:function(e){for(var t=0;t<e.length;t++)e[t].children&&(0===e[t].children.length?e[t].children=void 0:this.treeData(e[t].children));return e},handleDataChange:function(e,t,a){for(var i={arr:[],name:""},r=0;r<t.length;r++){var s=t[r];i.arr[r]=[];for(var l=0;l<s.length;l++){if(s[l].code==e[r]){i.arr[r].push(s[l].code);break}if(void 0!==s[l].children)for(var n=s[l].children,o=0;o<n.length;o++){if(n[o].code==e[r]){i.arr[r].push(s[l].code),i.arr[r].push(n[o].code);break}if(void 0!==n[o].children)for(var c=n[o].children,d=0;d<c.length;d++)if(c[d].code==e[r]){i.arr[r].push(s[l].code),i.arr[r].push(n[o].code),i.arr[r].push(c[d].code);break}}}}return i},handleDataChange1:function(e,t){for(var a={arr:[],name:""},i=0;i<t.length;i++){if(a.arr[i]=[],t[i].code==e[i]){a.arr[i].push(t[i].code),a.name=t[i].name;break}if(void 0!==t[i].children)for(var r=t[i].children,s=0;s<r.length;s++){if(r[s].code==e[i]){a.arr[i].push(t[i].code),a.arr[i].push(r[s].code),a.name=r[s].name;break}if(void 0!==r[s].children)for(var l=r[s].children,n=0;n<l.length;n++)if(l[n].code==e[i]){a.arr[i].push(t[i].code),a.arr[i].push(r[s].code),a.arr[i].push(l[n].code),a.name=l[n].name;break}}}return a},delRow:function(){for(var e in this.formDataService.tServiceDetails)if(0===this.multipleSelection.length)this.$message({showClose:!0,message:"请选择要移除的数据"});else for(var t in this.multipleSelection)this.formDataService.tServiceDetails[e]==this.multipleSelection[t]&&(this.formDataService.tServiceDetails.splice(e,1),this.optionsTest.splice(e,1))},addRow:function(){this.formDataService.tServiceDetails.push({systemCode:"",moduleId:[],moduleName:null}),this.optionsTest.push([])},delAll:function(){this.formDataService.tServiceDetails=[]},submitForm:function(e,t){var a=this;if(0!==this.formDataService.tServiceDetails.length)for(var i in this.formDataService.tServiceDetails)if("[object Array]"==Object.prototype.toString.call(this.formDataService.tServiceDetails[i].moduleId))if(this.formDataService.tServiceDetails[i].moduleId.length>0){var r=this.formDataService.tServiceDetails[i].moduleId.length,s=this.handleDataChange1(this.formDataService.tServiceDetails[i].moduleId,this.optionsTest[i]);this.formDataService.tServiceDetails[i].moduleName=s.name,this.formDataService.tServiceDetails[i].moduleId=this.formDataService.tServiceDetails[i].moduleId[r-1]}else this.formDataService.tServiceDetails[i].moduleId="";var l=this,n={};this.$refs[e].validate(function(e){if(!e)return!1;n="添加"===t?{busicode:"ServiceAdd",data:a.formDataService}:{busicode:"ServiceUpdate",data:a.formDataService},a.$api.fetch({apiUrl:"interface.api",method:"post",params:n}).then(function(e){l.$message({showClose:!0,message:"保存成功",type:"success"}),l.$parent.getData(),l.$parent.handleClose(),l.formDataService.tServiceDetails=[]})})},handleClose:function(){this.common.chargeObjectEqual(this,this.formDataService,"get","InterfaceForm",this.boforeClose)},boforeClose:function(){this.$parent.handleClose()},handleOptionsTest:function(e,t){var a=this,i=r()(e),s=[],l=[];return t.forEach(function(t,r){i.includes(t)?(l=a.treeData(e[t].children),s.push(l)):(l=a.treeData([]),s.push(l))}),s},editData:function(e,t){this.formDataService=e;var a=[];if(this.formDataService.tServiceDetails||(this.formDataService.tServiceDetails=[]),0!==this.formDataService.tServiceDetails.length&&(this.formDataService.tServiceDetails.forEach(function(e,t){a.push(e.systemCode)}),"edit"===t)){var i=this,r={busicode:"ServiceModuleList",data:{systemCodes:a}};this.$api.fetch({params:r}).then(function(e){var t,r=[];i.formDataService.tServiceDetails.forEach(function(e){r.push(e.moduleId)}),i.optionsTest=i.handleOptionsTest(e,a),t=i.handleDataChange(r,i.optionsTest,a),i.formDataService.tServiceDetails.forEach(function(e,a){e.moduleId=t.arr[a]})})}},getcascader:function(e){},handleChange:function(e){}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"InterfaceManAdd"},[a("el-form",{ref:"formDataInterfaceManAdd",staticClass:"formInterfaceManAdd",attrs:{model:e.formDataService,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"服务标识：",prop:"serviceCode"}},[a("el-input",{model:{value:e.formDataService.serviceCode,callback:function(t){e.$set(e.formDataService,"serviceCode",t)},expression:"formDataService.serviceCode"}})],1),e._v(" "),a("el-form-item",{staticClass:"mark",attrs:{label:"服务类名：",prop:"className"}},[a("el-input",{model:{value:e.formDataService.className,callback:function(t){e.$set(e.formDataService,"className",t)},expression:"formDataService.className"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"主从数据源：",prop:"dbMaster"}},[a("el-select",{attrs:{placeholder:"选择主从数据源"},model:{value:e.formDataService.dbMaster,callback:function(t){e.$set(e.formDataService,"dbMaster",t)},expression:"formDataService.dbMaster"}},[a("el-option",{attrs:{label:"主数据源",value:1}}),e._v(" "),a("el-option",{attrs:{label:"从数据源",value:2}})],1)],1),e._v(" "),a("el-form-item",{staticClass:"remark",attrs:{label:"服务描述：",prop:"comments"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:2,maxRows:4},maxlength:"500",clearable:"","show-word-limit":""},model:{value:e.formDataService.comments,callback:function(t){e.$set(e.formDataService,"comments",t)},expression:"formDataService.comments"}})],1),e._v(" "),a("el-form-item",{staticClass:"remark",attrs:{label:"输入参数样例：",prop:"importExample"}},[a("el-input",{attrs:{type:"textarea",rows:2,maxlength:"200",clearable:"","show-word-limit":""},model:{value:e.formDataService.importExample,callback:function(t){e.$set(e.formDataService,"importExample",t)},expression:"formDataService.importExample"}})],1),e._v(" "),a("el-form-item",{staticClass:"remark",attrs:{label:"输出参数样例：",prop:"outputExample"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:2,maxRows:4},maxlength:"500",clearable:"","show-word-limit":""},model:{value:e.formDataService.outputExample,callback:function(t){e.$set(e.formDataService,"outputExample",t)},expression:"formDataService.outputExample"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态：",prop:"status"}},[a("el-select",{attrs:{placeholder:"选择状态"},model:{value:e.formDataService.status,callback:function(t){e.$set(e.formDataService,"status",t)},expression:"formDataService.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),a("el-option",{attrs:{label:"停用",value:2}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"子系统："}},[a("el-button-group",[a("el-button",{attrs:{size:"mini",type:"info",icon:"el-icon-minus"},on:{click:e.delRow}}),e._v(" "),a("el-button",{attrs:{size:"mini",type:"info",icon:"el-icon-plus"},on:{click:e.addRow}})],1)],1)],1),e._v(" "),a("div",{staticClass:"kl-table form-table"},[e.tableShow?a("el-table",{ref:"multipleTable",staticClass:"tableTSer",staticStyle:{width:"100%"},attrs:{data:e.formDataService.tServiceDetails,border:"","max-height":e.maxHeight,"highlight-current-row":!0},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),a("el-table-column",{attrs:{prop:"systemCode",label:"子系统","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{attrs:{placeholder:"请选择子系统"},on:{change:function(a){return e.changeCode(t,t.$index)}},model:{value:e.formDataService.tServiceDetails[t.$index].systemCode,callback:function(a){e.$set(e.formDataService.tServiceDetails[t.$index],"systemCode",a)},expression:"formDataService.tServiceDetails[scope.$index].systemCode"}},e._l(e.appServerData,function(e){return a("el-option",{key:e.systemCode,attrs:{label:e.systemName,value:e.systemCode}})}),1)]}}],null,!1,4241067568)}),e._v(" "),a("el-table-column",{attrs:{prop:"moduleName",label:"归属模块","min-width":"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-cascader",{attrs:{placeholder:"请选择模块",options:e.optionsTest[t.$index],props:e.props,"show-all-levels":!1,clearable:""},on:{change:e.handleChange},model:{value:e.formDataService.tServiceDetails[t.$index].moduleId,callback:function(a){e.$set(e.formDataService.tServiceDetails[t.$index],"moduleId",a)},expression:"formDataService.tServiceDetails[scope.$index].moduleId"}})]}}],null,!1,2470084078)})],1):e._e()],1)],1)},staticRenderFns:[]};var c={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"callPage"},[a("el-card",{staticClass:"box-card"},e._l(e.callPageData,function(t,i){return a("div",{key:i,staticClass:"text item"},[e._v("\n            "+e._s(t.pageAddr)+"\n        ")])}),0)],1)},staticRenderFns:[]};var d={name:"InterfaceMan",components:{InterfaceManAdd:a("VU/8")(n,o,!1,function(e){a("zrGN")},null,null).exports,callPage:a("VU/8")({name:"callPage",data:function(){return{callPageData:[]}},mounted:function(){},methods:{selPageToServices:function(e){var t=this,a={busicode:"ServicePageLogList",data:{serviceCode:e}};this.$api.fetch({params:a}).then(function(e){t.callPageData=e.list})}}},c,!1,function(e){a("sy2f")},null,null).exports},data:function(){return{tableShow:!1,tableQuery:{comments:"",serviceCode:"",page:1,pageCount:50},maxHeight:0,selServicesData:{},InterfaceManAddVisible:!1,InterfaceManShow:!0,callPageVisible:!1,crumbsData:{titleList:[{title:"系统管理",path:"/systemMan"},{title:"接口管理",method:function(){window.histroy.back()}}]},formData:{serviceCode:"",className:"",comments:"",importExample:"",outputExample:"",status:1,tServiceDetails:[]},formDataService:{}}},mounted:function(){eventBus.$emit("secondMenuShow","secondMenuShow4"),this.getData()},methods:{getData:function(){var e=this,t={busicode:"ServiceList",data:this.tableQuery};this.$api.fetch({params:t}).then(function(t){e.selServicesData=t,e.common.changeTable(e,".InterfaceMan .kl-table",[".InterfaceMan .toolbar",".InterfaceMan .block"])})},search:function(){this.tableQuery.page=1,this.getData()},callPages:function(e){this.$set(this.crumbsData.titleList,"2",{title:"调用页面",method:function(){window.histroy.back()}}),this.$refs.callPage.selPageToServices(e.row.serviceCode),this.callPageVisible=!0,this.InterfaceManShow=!1},appAdd:function(e){var t=this;this.InterfaceManAddVisible=!0,this.InterfaceManShow=!1,this.$nextTick(function(){if("add"===e)t.$set(t.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),t.$refs.InterfaceManAdd.editData({serviceCode:"",className:"",comments:"",importExample:"",outputExample:"",status:1,tServiceDetails:[]}),t.common.chargeObjectEqual(t,t.formData,"set","InterfaceForm");else{t.$set(t.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var a=t,i={busicode:"ServiceQuery",data:{serviceId:e.row.serviceId}};t.$api.fetch({params:i}).then(function(e){a.formDataService=e,a.$refs.InterfaceManAdd.editData(a.formDataService,"edit"),a.common.chargeObjectEqual(a,e,"set","InterfaceForm")})}})},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.getData()},handleCurrentChange:function(e){this.tableQuery.page=e,this.getData()},handleClose:function(){this.InterfaceManAddVisible=!1,this.InterfaceManShow=!0,this.callPageVisible=!1,this.crumbsData.titleList.pop()},close:function(){this.$refs.InterfaceManAdd.handleClose()},submitForm:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.InterfaceManAdd.submitForm(e,t)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},u={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"InterfaceMan"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.InterfaceManShow,expression:"InterfaceManShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.InterfaceManAddVisible,expression:"InterfaceManAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm("formDataInterfaceManAdd")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.close}},[e._v("返回")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.callPageVisible,expression:"callPageVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.InterfaceManShow,expression:"InterfaceManShow"}],staticClass:"kl-table"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.tableQuery,size:"mini"}},[a("div",{staticClass:"toolbar-left"},[a("el-form-item",{attrs:{label:"服务标识："}},[a("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.serviceCode,callback:function(t){e.$set(e.tableQuery,"serviceCode",t)},expression:"tableQuery.serviceCode"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"服务描述："}},[a("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.comments,callback:function(t){e.$set(e.tableQuery,"comments",t)},expression:"tableQuery.comments"}})],1),e._v(" "),a("el-form-item",{staticClass:"button-group"},[a("el-button",{staticClass:"compile",attrs:{type:"primary el-icon-search",size:"mini"},on:{click:e.search}})],1)],1),e._v(" "),a("div",{staticClass:"toolbar-right"})])],1),e._v(" "),e.tableShow?a("el-table",{staticStyle:{width:"100%"},attrs:{stripe:"",center:"",border:"",data:e.selServicesData.list,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"serviceCode",label:"服务标识",width:"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"className",label:"服务类名","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"comments",label:"服务描述"}}),e._v(" "),a("el-table-column",{attrs:{prop:"callCount",width:"120",label:"调用次数"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.appAdd(t)}}},[e._v("修改")]),e._v(" "),a("span",{staticStyle:{color:"#e6e6e6"}},[e._v("|")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.callPages(t)}}},[e._v("调用页面")])]}}],null,!1,3417135120)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.selServicesData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),e.InterfaceManAddVisible?a("div",{staticClass:"interfaceAdd"},[a("InterfaceManAdd",{ref:"InterfaceManAdd"})],1):e._e(),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.callPageVisible,expression:"callPageVisible"}],staticClass:"interfaceCallPage"},[a("callPage",{ref:"callPage"})],1)])])},staticRenderFns:[]};var m=a("VU/8")(d,u,!1,function(e){a("Z875")},null,null);t.default=m.exports},Z875:function(e,t){},sy2f:function(e,t){},zrGN:function(e,t){}});