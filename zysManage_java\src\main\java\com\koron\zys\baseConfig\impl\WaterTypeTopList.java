package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.apache.log4j.Logger;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.WaterTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.SelectBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

public class WaterTypeTopList implements ServerInterface{
	private Logger log = Logger.getLogger(WaterTypeTopList.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		 @SuppressWarnings("rawtypes")
	        MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
	        try {
	        	WaterTypeMapper mapper = factory.getMapper(WaterTypeMapper.class);
	        	List<SelectBean> list = mapper.findTopWaterType();
	        	info.setData(list);
	        }catch (Exception e) {
	            info.setCode(Constant.MESSAGE_DBFAIL);
	            info.setDescription("操作异常");
	            logger.error("操作异常", e);
	            factory.close(false);
	        }
	        return info;
	}

}
