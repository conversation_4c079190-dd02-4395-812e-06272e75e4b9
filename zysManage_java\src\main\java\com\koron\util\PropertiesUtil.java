package com.koron.util;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PropertiesUtil {

	private static Logger logger = LoggerFactory.getLogger(PropertiesUtil.class);
    private static Map<String, Properties> propertyMap = new HashMap<String, Properties>();

    private static Properties objProperties = null;

    private PropertiesUtil() {
    }

    /**
     * 通过fileName、key获取value
     */
    public static String getValueByNameAndKey(String fileName, String key) {
        String rt = null;
        if (objProperties == null) {
            objProperties = gerProperties(fileName);
        }
        if (objProperties != null) {
            rt = objProperties.getProperty(key);
        }
        return rt;
    }
    
    /**
     * 通过property文件名：加载properties
     */
    public synchronized static Properties gerProperties(String fileName) {
        Properties properties = propertyMap.get(fileName);
        if (properties == null) {
            properties = new Properties();
            // 加载
            InputStream in = PropertiesUtil.class.getClassLoader()
                    .getResourceAsStream(fileName);
            System.out.println(in);
            try {
                properties.load(in);
            } catch (Exception e) {
                e.printStackTrace();
                logger.equals(e);;
            }
        }
        return properties;
    }
}
