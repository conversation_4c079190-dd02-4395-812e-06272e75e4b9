<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.common.web.mapper.StaffMapper">
	<select id="selectList" parameterType="com.koron.common.bean.query.StaffQueryBean" resultType="com.koron.common.web.bean.StaffBean">
		SELECT
		a.id,
		a.NAME,
		a.CODE,
		a.departmentCode,
		a.departmentName,
		a.position,
		a.phone,
		a.mobile,
		a.email,
		a.sex,
		a.idcard,
		a.STATUS,
		a.loginid,
		a.weighting,
		a.photourl,
		a.openid,
		a.userid
		FROM
		tblstaff AS a, tblstaff_department AS b, tbldepartment AS c
		WHERE a.CODE = b.user_code AND b.department_code = c.code
		<if test="code != null and code != ''">
			AND c.code = #{code}
		</if>
		<if test="name != null and name != ''">
			AND (a.name like concat('%', #{name}, '%') OR a.loginid = #{name})
		</if>
		<if test="departmentCode != null and departmentCode != ''">
			AND c.code = #{departmentCode}
		</if>
	</select>

	<select id="getStaff" parameterType="String" resultType="com.koron.common.web.bean.StaffBean">
		SELECT
			a.id,
			a.NAME,
			a.CODE,
			a.departmentCode,
			a.departmentName,
			a.position,
			a.phone,
			a.mobile,
			a.email,
			a.sex,
			a.idcard,
			a.STATUS,
			a.loginid,
			a.weighting,
			a.photourl,
			a.openid,
			a.userid
		FROM
			tblstaff AS a, tblstaff_department AS b, tbldepartment AS c
		WHERE a.CODE = b.user_code AND b.department_code = c.code
		  AND   a.loginid = #{loginid}
		LIMIT 1
	</select>

	<select id="getStaffById" parameterType="int" resultType="com.koron.common.web.bean.StaffBean">
		SELECT
			a.id,
			a.NAME,
			a.CODE,
			a.departmentCode,
			a.departmentName,
			a.position,
			a.phone,
			a.mobile,
			a.email,
			a.sex,
			a.idcard,
			a.STATUS,
			a.loginid,
			a.weighting,
			a.photourl,
			a.openid,
			a.userid
		FROM
			tblstaff AS a, tblstaff_department AS b, tbldepartment AS c
		WHERE a.CODE = b.user_code AND b.department_code = c.code
		  AND   a.id = #{id}
		LIMIT 1
	</select>

	<select id="getStaffByCode" parameterType="String" resultType="com.koron.common.web.bean.StaffBean">
		SELECT
			a.id,
			a.NAME,
			a.CODE,
			a.departmentCode,
			a.departmentName,
			a.position,
			a.phone,
			a.mobile,
			a.email,
			a.sex,
			a.idcard,
			a.STATUS,
			a.loginid,
			a.weighting,
			a.photourl,
			a.openid,
			a.userid
		FROM
			tblstaff AS a, tblstaff_department AS b, tbldepartment AS c
		WHERE a.CODE = b.user_code AND b.department_code = c.code
		  AND   a.CODE = #{code}
		LIMIT 1
	</select>

	<insert id="batchInsertStaffDepartment" parameterType="com.koron.common.web.bean.StaffBean">
		INSERT IGNORE INTO tblstaff_department	(
		user_code,
		department_code
		) VALUES
		<foreach collection="list" item="staff" separator=",">
			(
			#{staff.code,jdbcType=VARCHAR},
			#{staff.departmentCode,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--批量新增用户信息-->
	<insert id="batchInsertStaff" parameterType="com.koron.common.web.bean.StaffBean">
		INSERT IGNORE INTO tblstaff	(
		name,
		code,
		departmentCode,
		departmentName,
		position,
		phone,
		mobile,
		email,
		sex,
		idcard,
		status,
		loginid,
		weighting,
		photourl,
		openid,
		userid
		) VALUES
		<foreach collection="list" item="staff" separator=",">
			(
			#{staff.name,jdbcType=VARCHAR},
			#{staff.code,jdbcType=VARCHAR},
			#{staff.departmentCode,jdbcType=VARCHAR},
			#{staff.departmentName,jdbcType=VARCHAR},
			#{staff.position,jdbcType=INTEGER},
			#{staff.phone,jdbcType=VARCHAR},
			#{staff.mobile,jdbcType=VARCHAR},
			#{staff.email,jdbcType=VARCHAR},
			#{staff.sex,jdbcType=INTEGER},
			#{staff.idcard,jdbcType=VARCHAR},
			#{staff.status,jdbcType=INTEGER},
			#{staff.loginid,jdbcType=VARCHAR},
			#{staff.weight,jdbcType=INTEGER},
			#{staff.photo,jdbcType=VARCHAR},
			#{staff.openid,jdbcType=VARCHAR},
			#{staff.userid,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--批量新增用户信息-->
	<insert id="insertStaff" useGeneratedKeys="true" keyProperty="id" parameterType="com.koron.common.web.bean.StaffBean">
		INSERT IGNORE INTO tblstaff	(
		name,
		code,
		departmentCode,
		departmentName,
		position,
		phone,
		mobile,
		email,
		sex,
		idcard,
		status,
		loginid,
		weighting,
		photourl,
		openid,
		userid
		) VALUES
			(
				#{name,jdbcType=VARCHAR},
				#{code,jdbcType=VARCHAR},
				#{departmentCode,jdbcType=VARCHAR},
				#{departmentName,jdbcType=VARCHAR},
				#{position,jdbcType=INTEGER},
				#{phone,jdbcType=VARCHAR},
				#{mobile,jdbcType=VARCHAR},
				#{email,jdbcType=VARCHAR},
				#{sex,jdbcType=INTEGER},
				#{idcard,jdbcType=VARCHAR},
				#{status,jdbcType=INTEGER},
				#{loginid,jdbcType=VARCHAR},
				#{weight,jdbcType=INTEGER},
				#{photo,jdbcType=VARCHAR},
				#{openid,jdbcType=VARCHAR},
				#{userid,jdbcType=VARCHAR}
			)
	</insert>

	<!--批量新增用户信息-->
	<update id="updateStaff" parameterType="com.koron.common.web.bean.StaffBean">
		UPDATE tblstaff SET
							name = #{name},
							code = #{code},
							departmentCode = #{departmentCode},
							departmentName = #{departmentName},
							position = #{position},
							phone = #{phone},
							mobile = #{mobile},
							email = #{email},
							sex = #{sex},
							idcard = #{idcard},
							status = #{status},
							loginid = #{loginid},
							weighting = #{weight}
		WHERE id = #{id}
	</update>

	<select id="getAllStaff" parameterType="com.koron.common.bean.query.StaffQueryBean" resultType="com.koron.common.web.bean.StaffBean">
		SELECT
		a.id,
		a.NAME,
		a.CODE,
		a.departmentCode,
		a.departmentName,
		a.position,
		a.phone,
		a.mobile,
		a.email,
		a.sex,
		a.idcard,
		a.STATUS,
		a.loginid,
		a.weighting,
		a.photourl,
		a.openid,
		a.userid
		FROM
		tblstaff AS a
		WHERE a.loginid is not null
		AND `status` = 0
		<if test="fuzzyQuery != null and fuzzyQuery != ''">
			AND (a.loginid = #{fuzzyQuery} OR a.mobile = #{fuzzyQuery} OR a.NAME like concat('%', #{fuzzyQuery}, '%'))
		</if>
	</select>
</mapper>