<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://xmlns.jcp.org/xml/ns/javaee" xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd" version="3.1">
	<display-name>css2_xnyh</display-name>
	<welcome-file-list>
		<welcome-file>index.html</welcome-file>
	</welcome-file-list>
	<context-param>
		<description>profile.default</description>
		<param-name>spring.profiles.default</param-name>
		<param-value>test</param-value>
	</context-param>
	<context-param>
		<description>profile.active</description>
		<param-name>spring.profiles.active</param-name>
		<param-value>test</param-value>
	</context-param>
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath*:/spring.xml</param-value>
	</context-param>
	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<listener>
		<listener-class>com.koron.common.web.KoronListener</listener-class>
	</listener>

	<servlet>
		<servlet-name>workflow</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath*:/SampleMVC.xml</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<!--<servlet-mapping>
		<servlet-name>workflow</servlet-name>
		<url-pattern>*.htm</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>workflow</servlet-name>
		<url-pattern>*.api</url-pattern>
	</servlet-mapping>-->
	<servlet-mapping>
		<servlet-name>workflow</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>
	<session-config>
		<session-timeout>720</session-timeout>
	</session-config>
	<error-page>
		<error-code>400</error-code>
		<location>/400.html</location>
	</error-page>
	<error-page>
		<error-code>405</error-code>
		<location>/405.html</location>
	</error-page>
	<error-page>
		<error-code>500</error-code>
		<location>/500.html</location>
	</error-page>
</web-app>