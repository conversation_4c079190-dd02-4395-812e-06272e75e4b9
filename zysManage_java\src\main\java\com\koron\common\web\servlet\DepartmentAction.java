package com.koron.common.web.servlet;

import org.swan.bean.MessageBean;

import java.util.*;

import javax.servlet.http.HttpServletResponse;

import org.koron.ebs.mybatis.SessionFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.koron.common.web.bean.StaffBean;
import com.koron.common.bean.query.StaffQueryBean;
import com.koron.common.web.bean.DepartmentBean;
import com.koron.common.web.mapper.DepartmentMapper;
import com.koron.common.web.mapper.StaffMapper;
import com.koron.common.web.service.SynchronizationOrgService;
import com.koron.util.TreeUtils;

@Controller
public class DepartmentAction {
	
	@RequestMapping("/dep/json.htm")
	@ResponseBody
	public String departmentJson(@RequestParam("id") Integer[] ids, @RequestParam(value = "type", required = false) Integer type) {
		try (SessionFactory factory = new SessionFactory()) {
			DepartmentMapper mapper = factory.getMapper(DepartmentMapper.class);
			List<DepartmentBean> departments = mapper.getDepartmentInfo();
			DepartmentBean root = mapper.getDepartmentInfoByCode(SynchronizationOrgService.getRoot());
			departments = TreeUtils.list2TreeList(departments, "code", "parentcode", "children");
			try {
				root.setChildren(departments);
				ObjectMapper objMapper = new ObjectMapper();
				objMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
				List<DepartmentBean> result = new ArrayList<DepartmentBean>();
				result.add(root);
				return objMapper.writeValueAsString(result);
			} catch (JsonProcessingException ex) {
				ex.printStackTrace();
			}
		}
		return "";
	}
	
	/**
	 * 获取
	 * @return
	 */
	@RequestMapping("/dep/staffOfPage.htm")
	@ResponseBody
	public String staffOfPage(StaffQueryBean query) {
		try (SessionFactory factory = new SessionFactory()) {
			Map<String, Object> data = new HashMap<String, Object>();
			StaffMapper mapper = factory.getMapper(StaffMapper.class);
			Page<StaffBean> page = PageHelper.startPage(query.getPage(), query.getPageCount());
			List<StaffBean> staff = mapper.selectList(query);
			try {
				data.put("data", staff);
				data.put("totalCount", page.getTotal());
				ObjectMapper objMapper = new ObjectMapper();
				objMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
				return objMapper.writeValueAsString(data);
			} catch (JsonProcessingException ex) {
				ex.printStackTrace();
			}
		}
		return "";
	}
	
	/**
	 * 获取
	 * @param query
	 * @return
	 */
	@RequestMapping("/dep/staff.htm")
	@ResponseBody
	public String staffOfDepartment(StaffQueryBean query) {
		try (SessionFactory factory = new SessionFactory()) {
			StaffMapper mapper = factory.getMapper(StaffMapper.class);
			List<StaffBean> staff = mapper.selectList(query);
			try {
				ObjectMapper objMapper = new ObjectMapper();
				objMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
				return objMapper.writeValueAsString(staff);
			} catch (JsonProcessingException ex) {
				ex.printStackTrace();
			}
		}
		return "";
	}

	/**
	 * 获取所有的组织架构人员
	 * @return
	 */
	@RequestMapping("/dep/getAllStaff.htm")
	@ResponseBody
	public String getAllStaff(StaffQueryBean query) {
		try (SessionFactory factory = new SessionFactory()) {
			Map<String, Object> data = new HashMap<String, Object>();
			StaffMapper mapper = factory.getMapper(StaffMapper.class);
			Page<StaffBean> page = PageHelper.startPage(query.getPage(), query.getPageCount());
			List<StaffBean> staff = mapper.getAllStaff(query);
			try {
				data.put("data", staff);
				data.put("totalCount", page.getTotal());
				ObjectMapper objMapper = new ObjectMapper();
				objMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
				return objMapper.writeValueAsString(data);
			} catch (JsonProcessingException ex) {
				ex.printStackTrace();
			}
		}
		return "";
	}
	
	@RequestMapping("/staffquery.htm")
	@ResponseBody
	public String query(@RequestParam("q") String name,HttpServletResponse  response){
		MessageBean<List<StaffBean>> ret = new MessageBean<>();
		ret.setCode(0);
		ret.setDescription("获取成功");
		try (SessionFactory factory = new SessionFactory()) {
			StaffMapper mapper = factory.getMapper(StaffMapper.class);
			StaffQueryBean query = new StaffQueryBean();
			query.setName(name);
			List<StaffBean> staff = mapper.selectList(query);
			ret.setData(staff);
		}
		return ret.toJson();
	}
}
