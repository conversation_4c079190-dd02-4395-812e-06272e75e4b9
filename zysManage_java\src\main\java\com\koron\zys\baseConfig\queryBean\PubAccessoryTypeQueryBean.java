package com.koron.zys.baseConfig.queryBean;

import com.koron.common.bean.query.BaseQueryBean;

public class PubAccessoryTypeQueryBean extends BaseQueryBean{
	
	private String accessoryTypeId;
	
	private String accessoryNo;
	
	private String accessoryName;

	public String getAccessoryTypeId() {
		return accessoryTypeId;
	}

	public String getAccessoryNo() {
		return accessoryNo;
	}

	public String getAccessoryName() {
		return accessoryName;
	}

	public void setAccessoryTypeId(String accessoryTypeId) {
		this.accessoryTypeId = accessoryTypeId;
	}

	public void setAccessoryNo(String accessoryNo) {
		this.accessoryNo = accessoryNo;
	}

	public void setAccessoryName(String accessoryName) {
		this.accessoryName = accessoryName;
	}
	
}
