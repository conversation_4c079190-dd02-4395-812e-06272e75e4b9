package com.koron.common.bean;

import java.io.Serializable;

public class DeptBean implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 836553953425763968L;
	/**
	 * 部门id
	 */
	private Integer id;
	/**
	 * 部门名称
	 */
	private String name;
	/**
	 * 部门编码
	 */
	private String code;
	/**
	 * 组织架构编码
	 */
	private String orgCode;
	/**
	 * 部门权重
	 */
	private Integer weight;
	/**
	 * 部门类型
	 */
	private Integer type;
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
	public Integer getWeight() {
		return weight;
	}
	public void setWeight(Integer weight) {
		this.weight = weight;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	@Override
	public String toString() {
		return "DeptBean [id=" + id + ", name=" + name + ", code=" + code + ", orgCode=" + orgCode + ", weight="
				+ weight + ", type=" + type + "]";
	}
	
}
