package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BankBean;
import com.koron.zys.baseConfig.mapper.BankMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 银行信息-编辑
 *
 * <AUTHOR>
 */
public class BankUpdate implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(BankUpdate.class);

    @Override
    @ValidationKey(clazz = BankBean.class,method = "insert")
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            BankMapper mapper = factory.getMapper(BankMapper.class);
            BankBean bean = JsonUtils.objectToPojo(req.getData(), BankBean.class);
            if (StringUtils.isBlank(bean.getId())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id不能为空", void.class);
            }
            // 获取修改前的数据
            BankBean oldInfo = mapper.findBankById(bean.getId());
            // TODO 修改状态从启用到停用，节点下存在子节点时，子节点是否停用？
            if (oldInfo.getStatus() == 1 && bean.getStatus() == 0) {
                // 查看节点下是否存在未停用的子节点
                List<BankBean> childs = mapper.getChild(bean.getBankNo());
                if (null != childs && childs.size() > 0) {
                    List<String> collect = childs.stream().map(child -> child.getId()).collect(Collectors.toList());
                    if (!collect.contains(bean.getId())) {
                        childs.add(bean);
                    }
                    // 停用节点以及节点下所有的子节点
                    for (BankBean bankBean : childs) {
                        bankBean.setUpdateInfo(userInfo);
                        bankBean.setStatus(0);
                        mapper.updateBank(bankBean);
                    }
                }
            } else if (oldInfo.getStatus() == 0 && bean.getStatus() == 1) {// TODO 修改状态从停用到启用，只有节点的父节点为启用状态时方可启用
                // 获取节点父节点数据
                BankBean parentInfo = mapper.findBankById(bean.getParentId());
                if (null != parentInfo && parentInfo.getStatus() == 0) {
                    return MessageBean.create(Constant.ILLEGAL_PARAMETER, "父节点停用，请先启用父节点", void.class);
                }
                bean.setUpdateInfo(userInfo);
                mapper.updateBank(bean);
            } else {
                bean.setUpdateInfo(userInfo);
                mapper.updateBank(bean);
            }
        } catch (Exception e) {
            logger.error("银行信息修改失败", e);
            factory.close(false);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "银行信息修改失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }

}
