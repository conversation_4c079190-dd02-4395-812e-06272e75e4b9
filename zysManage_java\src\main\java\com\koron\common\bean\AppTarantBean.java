package com.koron.common.bean;

import java.io.Serializable;

public class AppTarantBean implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	/**
	 * 主键id
	 */
	private Integer id;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 唯一编码
	 */
	private String code;
	/**
	 * git路径
	 */
	private String git;
	/**
	 * 存储分枝
	 */
	private String config_branch;
	/**
	 * 过期日期
	 */
	private String expireDate;
	/**
	 * 组织架构code
	 */
	private String orgCode;
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getGit() {
		return git;
	}
	public void setGit(String git) {
		this.git = git;
	}
	public String getConfig_branch() {
		return config_branch;
	}
	public void setConfig_branch(String config_branch) {
		this.config_branch = config_branch;
	}
	public String getExpireDate() {
		return expireDate;
	}
	public void setExpireDate(String expireDate) {
		this.expireDate = expireDate;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	@Override
	public String toString() {
		return "AppTarantBean [id=" + id + ", name=" + name + ", code=" + code + ", git=" + git + ", config_branch="
				+ config_branch + ", expireDate=" + expireDate + ", orgCode=" + orgCode + "]";
	}
	
}
