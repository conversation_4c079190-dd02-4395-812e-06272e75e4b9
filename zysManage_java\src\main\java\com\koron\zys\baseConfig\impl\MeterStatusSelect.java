package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterStatusBean;
import com.koron.zys.baseConfig.mapper.MeterStatusMapper;
import com.koron.zys.baseConfig.vo.SelectVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 抄表状态-下拉框
 * <AUTHOR>
 *
 */
public class MeterStatusSelect implements ServerInterface  {
	
	private static Logger logger = LoggerFactory.getLogger(MeterStatusSelect.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			MeterStatusBean bean = JsonUtils.objectToPojo(req.getData(), MeterStatusBean.class);
			if(bean.getAbnormalFlag() == null) {
				info.setCode(Constant.MESSAGE_INT_FAIL);
				info.setDescription("异常类型不能为空。");
				return info;
			}
			MeterStatusMapper mapper = factory.getMapper(MeterStatusMapper.class);	
			List<SelectVO> list = mapper.selectComboBox(bean.getAbnormalFlag());			
			info.setData(list);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription("查询抄表状态失败");
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}