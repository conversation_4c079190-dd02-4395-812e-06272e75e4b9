package com.koron.zys.baseConfig.impl;

import java.util.List;
import java.util.Map;

import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.GarbageUniversalBean;
import com.koron.zys.baseConfig.mapper.GarbageMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.Tools;
import com.koron.util.ValidationKey;

public class UserGarbageUpdate implements ServerInterface{

	@Override
    @ValidationKey(clazz = GarbageUniversalBean.class,method = "insert")
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			List<GarbageUniversalBean> list = JsonUtils.jsonToList(JsonUtils.objectToJson(req.getData()),GarbageUniversalBean.class);
			GarbageMapper mapper = factory.getMapper(GarbageMapper.class);
			// 获取用户、账户、客户字段中英文
			Map<String, String> userField =Tools.getFiledName(factory,"garbage_universal");
			for(GarbageUniversalBean bean : list) {
				if(bean.getId() != null && bean.getId() != "") {
					GarbageUniversalBean oldBean = mapper.selectById(bean.getId());
					// 获取两个对象属性值的不同数据
					int index = 0;
					String arrs = "";
					Map<String, String> items = Tools.getChangeItems(oldBean, bean, index, userField);
					if (items != null && items.size() != 0) {
						index = Integer.valueOf(items.get("index"));
						if (index != 0) {
							arrs += items.get("arrs");
						}
					}
					bean.setChangeItem(arrs);
		            bean.setUpdateName(userInfo.getUserInfo().getName());
					bean.setUpdateTime(CommonUtils.getCurrentTime());
					bean.setUid(new ObjectId().toHexString());
					mapper.insertOld(bean);
					mapper.update(bean);
				}else {
					bean.setId(new ObjectId().toHexString());
		            bean.setCreateName(userInfo.getUserInfo().getName());
		    		bean.setCreateTime(CommonUtils.getCurrentTime());
		            mapper.insert(bean);
				}				
			}
						
		}catch(Exception e){
			logger.error("操作失败", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
