package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BasePayMentElfBean;
import com.koron.zys.baseConfig.mapper.PayMentElfMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

/**
 * 缴费通知精灵保存
 */
public class PayMentElfSave implements ServerInterface {
    @Override
    @ValidationKey(clazz = BasePayMentElfBean.class,method="insert")
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            PayMentElfMapper mapper = factory.getMapper(PayMentElfMapper.class);
            //获取短信模板信息
            BasePayMentElfBean payMentElfBean=JsonUtils.objectToPojo(req.getData(), BasePayMentElfBean.class);
            if (null == payMentElfBean) {
                return MessageBean.create(Constant.NOT_NULL, "参数为空", void.class);
            }
            //先清空历史记录
            mapper.deletePayMentElf();
            // 再添加新纪录
            payMentElfBean.setCreateInfo(userInfo);
            Integer integer = mapper.insertPayMentElf(payMentElfBean);
        } catch (Exception e) {
            factory.close(false);
            logger.error("缴费通知精灵保存失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "缴费通知精灵保存失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}
