package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.baseConfig.bean.GarbageUniversalBean;
import com.koron.zys.baseConfig.queryBean.GarbageHisQueryBean;
import com.koron.zys.baseConfig.queryBean.UserGarbageQueryBean;
import org.apache.ibatis.annotations.Param;

public interface GarbageMapper {
	public List<GarbageUniversalBean> selectList(UserGarbageQueryBean bean);	//垃圾费定价列表
	
	public GarbageUniversalBean selectById(String id);
	
	public List<GarbageUniversalBean> selectHisList(GarbageHisQueryBean bean);
	
	public void insert(GarbageUniversalBean bean);
	
	public void update(GarbageUniversalBean bean);
	
	public void insertOld(GarbageUniversalBean bean);
	
	public List<GarbageUniversalBean> selectMSList(UserGarbageQueryBean bean);		//垃圾费免收列表
	
	public void insertMS(GarbageUniversalBean bean);
	
	public void updateMS(GarbageUniversalBean bean);

	/**
	 * 查询垃圾费信息
	 * @param userNo
	 * @return
	 */
	GarbageUniversalBean selectGarbageUniversalBean(@Param("userNo") String userNo);
}
