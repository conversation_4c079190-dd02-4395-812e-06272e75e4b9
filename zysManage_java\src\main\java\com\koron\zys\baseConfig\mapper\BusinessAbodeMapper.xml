<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BusinessAbodeMapper">
	<select id="businessAbodeSelect" parameterType="String"
		resultType="com.koron.zys.baseConfig.vo.SelectVO">
		select
		id,abode_name name
		from
		pub_business_abode
		where
		status=1
		and group_code=#{groupCode}
		order by sort_no
	</select>
	<select id="findBusinessAbodeById" parameterType="String"
		resultType="com.koron.zys.baseConfig.bean.BusinessAbodeBean">
		select
		*
		from
		pub_business_abode
		where status=1
		and id=#{id}
	</select>

</mapper>