package com.koron.zys.baseConfig.bean;

import java.util.List;

/**
 * 费用明细表
 * <AUTHOR>
 *
 */
public class CostDetailBean {
	
	private String costDetailId;

	/**
	 * 明细名称
	 */
    private String detailName;
    /**
              * 费用名称
     */
    private String costName;
    /**
               * 阶梯类型
     */
    private Integer ladderType;
    /**
                * 计算单位
     */
    private Integer calculateUnit;
    
    /**
               * 人口基数
     */
	private Integer personBase;
   
    /**
              水量基数
    */   
	private Integer waterBase;
 
    private Integer status;
    
    private String comments;
    
    /**
     * 明细
     */
    private List<CostDetailLadderBean> ladderlist;

    /**
	 * 创建时间
	 */
	private String createTime;
	
	/**
	 * 创建人
	 */
	private String createName;
	/**
	 * 修改时间
	 */
	private String updateTime;
	/**
	 * 修改人
	 */
	private String updateName;
	public String getCostDetailId() {
		return costDetailId;
	}
	public void setCostDetailId(String costDetailId) {
		this.costDetailId = costDetailId;
	}
	public String getDetailName() {
		return detailName;
	}
	public void setDetailName(String detailName) {
		this.detailName = detailName;
	}
	public String getCostName() {
		return costName;
	}
	public void setCostName(String costName) {
		this.costName = costName;
	}
	public Integer getLadderType() {
		return ladderType;
	}
	public void setLadderType(Integer ladderType) {
		this.ladderType = ladderType;
	}
	public Integer getCalculateUnit() {
		return calculateUnit;
	}
	public void setCalculateUnit(Integer calculateUnit) {
		this.calculateUnit = calculateUnit;
	}
	public Integer getPersonBase() {
		return personBase;
	}
	public void setPersonBase(Integer personBase) {
		this.personBase = personBase;
	}
	public Integer getWaterBase() {
		return waterBase;
	}
	public void setWaterBase(Integer waterBase) {
		this.waterBase = waterBase;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getCreateName() {
		return createName;
	}
	public void setCreateName(String createName) {
		this.createName = createName;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public String getUpdateName() {
		return updateName;
	}
	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}
	public List<CostDetailLadderBean> getLadderlist() {
		return ladderlist;
	}
	public void setLadderlist(List<CostDetailLadderBean> ladderlist) {
		this.ladderlist = ladderlist;
	}
}
