package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;

public class BaseInvoiceTypeBean extends BaseBean{
	@Check(name = "票据编号", notEmpty = true)
	private String invoiceNo;
	
	@Check(name = "票据名称", notEmpty = true)
	private String invoiceName;
	private int type;
	private int isPro;
	
	private String comments;
	
	@Check(name = "状态", notEmpty = true)
	private String status;
	
	private String statusName;

	private String autoPrint;


	public String getAutoPrint() {
		return autoPrint;
	}

	public void setAutoPrint(String autoPrint) {
		this.autoPrint = autoPrint;
	}

	public String getStatusName() {
		return statusName;
	}
	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}
	public String getInvoiceNo() {
		return invoiceNo;
	}
	public void setInvoiceNo(String invoiceNo) {
		this.invoiceNo = invoiceNo;
	}
	public String getInvoiceName() {
		return invoiceName;
	}
	public void setInvoiceName(String invoiceName) {
		this.invoiceName = invoiceName;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public int getIsPro() {
		return isPro;
	}
	public void setIsPro(int isPro) {
		this.isPro = isPro;
	}
	
}
