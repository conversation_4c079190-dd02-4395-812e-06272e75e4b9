<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.MsgPhraseMapper">
    <select id="selectMsgPhrase" resultType="com.koron.zys.baseConfig.bean.MsgPhraseBean">
        select *
        from base_msg_phrase
    </select>
    <!--批量添加List集合对象 -->
    <insert id="insertMsgPhraseList">
        INSERT INTO base_msg_phrase
        (id,phrase_no,phrase_name,phrase_content,status,tenant_id,create_time,create_account,create_name)
        VALUES
        <foreach item="item" separator="," collection="list">
            (#{item.id},
            #{item.phraseNo},
            #{item.phraseName},
            #{item.phraseContent},
            #{item.status},
            #{item.tenantId},
            #{item.createTime},
            #{item.createAccount},
            #{item.createName})
        </foreach>
    </insert>

    <delete id="deleteAllMsgPhrase">
        delete
        from base_msg_phrase
    </delete>

    <select id="phraseFieldQuery" resultType="com.koron.zys.baseConfig.bean.PhraseFieldBean">
        SELECT phrase_no phraseNo, field, field_name fieldName
        FROM base_phrase_field
        <where>
            <if test="phraseNo != null and phraseNo != ''">
                phrase_no = #{phraseNo}
            </if>
        </where>
        ORDER BY sort_no
    </select>
</mapper>