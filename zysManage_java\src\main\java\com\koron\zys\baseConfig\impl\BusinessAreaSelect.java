package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BusinessAreaBean;
import com.koron.zys.baseConfig.mapper.BusinessAreaMapper;
import com.koron.zys.baseConfig.vo.SelectVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
/**
 * 营业区域下拉框
 * <AUTHOR>
 * 2020年3月23日
 */
public class BusinessAreaSelect implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(BusinessAreaSelect.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			BusinessAreaBean bean = JsonUtils.objectToPojo(req.getData(), BusinessAreaBean.class);
			BusinessAreaMapper mapper = factory.getMapper(BusinessAreaMapper.class);
			List<SelectVO> list = mapper.select(bean.getBusinessAbodeId(), userInfo.getCurWaterCode());
			info.setData(list);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}
