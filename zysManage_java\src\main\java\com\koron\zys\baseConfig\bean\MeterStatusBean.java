package com.koron.zys.baseConfig.bean;

/**
 * 抄表状态表
 * <AUTHOR>
 *
 */
public class MeterStatusBean {
	
	private String id;

    private Integer statusValue;

    private String statusName;

    private Integer abnormalFlag;
 
	private String comments;
 
    private Integer status;
    
    private Integer sortNo;

    /**
	 * 创建时间
	 */
	private String createTime;
	
	/**
	 * 创建人
	 */
	private String createName;
	/**
	 * 修改时间
	 */
	private String updateTime;
	/**
	 * 修改人
	 */
	private String updateName;
	public Integer getStatusValue() {
		return statusValue;
	}
	public void setStatusValue(Integer statusValue) {
		this.statusValue = statusValue;
	}
	public String getId() {
		return id;
	}
	public String getStatusName() {
		return statusName;
	}
	public Integer getAbnormalFlag() {
		return abnormalFlag;
	}
	public String getComments() {
		return comments;
	}
	public Integer getStatus() {
		return status;
	}
	public Integer getSortNo() {
		return sortNo;
	}
	public String getCreateTime() {
		return createTime;
	}
	public String getCreateName() {
		return createName;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public String getUpdateName() {
		return updateName;
	}
	public void setId(String id) {
		this.id = id;
	}
	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}
	public void setAbnormalFlag(Integer abnormalFlag) {
		this.abnormalFlag = abnormalFlag;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public void setSortNo(Integer sortNo) {
		this.sortNo = sortNo;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public void setCreateName(String createName) {
		this.createName = createName;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}
	
}
