package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.koron.zys.baseConfig.bean.BaseAccessoryMetadataBean;
import com.koron.zys.baseConfig.queryBean.BaseAccessoryMetadataQueryBean;

public interface BaseAccessoryMetadataMapper {
	
	List<BaseAccessoryMetadataBean> selectList(BaseAccessoryMetadataQueryBean query);
	
	BaseAccessoryMetadataBean selectById(String id);
	
	int insert(BaseAccessoryMetadataBean bean);
	
	int deleteById(String id);
	
	List<String> checkAccessoryRequired(@Param("receiptType") String receiptType,@Param("id") String id);

}
