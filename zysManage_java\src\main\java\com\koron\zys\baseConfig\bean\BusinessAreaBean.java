package com.koron.zys.baseConfig.bean;

/**
 * 营业区域
 * <AUTHOR>
 * 2020年3月23日
 */
public class BusinessAreaBean extends BaseBean {

	private String id;
	private String businessAbodeId;
	private String areaNo;
	private String areaName;
	private String areaComments;
	private Integer status;
	private Integer sortNo;
	private String groupCode;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getBusinessAbodeId() {
		return businessAbodeId;
	}
	public void setBusinessAbodeId(String businessAbodeId) {
		this.businessAbodeId = businessAbodeId;
	}
	public String getAreaNo() {
		return areaNo;
	}
	public void setAreaNo(String areaNo) {
		this.areaNo = areaNo;
	}
	public String getAreaName() {
		return areaName;
	}
	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}
	public String getAreaComments() {
		return areaComments;
	}
	public void setAreaComments(String areaComments) {
		this.areaComments = areaComments;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Integer getSortNo() {
		return sortNo;
	}
	public void setSortNo(Integer sortNo) {
		this.sortNo = sortNo;
	}
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	
}
