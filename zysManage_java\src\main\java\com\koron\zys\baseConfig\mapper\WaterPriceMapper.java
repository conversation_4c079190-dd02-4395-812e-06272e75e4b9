package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.serviceManage.bean.SelectBean;
import org.apache.ibatis.annotations.Param;

import com.koron.zys.baseConfig.bean.WaterPriceBean;
import com.koron.zys.baseConfig.bean.WaterPriceDetailBean;
import com.koron.zys.baseConfig.bean.WaterPriceLadderBean;
import com.koron.zys.baseConfig.queryBean.WaterPriceQueryBean;
import com.koron.zys.baseConfig.vo.WaterPriceVO;

public interface WaterPriceMapper {
	/**
	 * 查询列表
	 * 
	 * @return
	 */
	List<WaterPriceVO> selectWaterPriceList(WaterPriceQueryBean waterPriceQueryBean);

	/**
	 * 查询所有
	 */
	List<WaterPriceVO> queryList(WaterPriceQueryBean waterPriceQueryBean);

	/**
	 * 添加用水价格
	 * 
	 * @param waterPriceBean
	 * @return
	 */
	void insertWaterPrice(WaterPriceBean waterPriceBean);
	 /**
	  * 添加用水价格从表信息
	  * @param waterPriceDetailBean
	  */
	void insertWaterPriceDetail(WaterPriceDetailBean waterPriceDetailBean);
	 /**
	  * 添加用水价格阶梯
	  * @param waterPriceLadderBean
	  */
	void insertWaterPriceLadder(WaterPriceLadderBean waterPriceLadderBean);
	/**
	 * 修改用水价格
	 * 
	 * @param waterPriceBean
	 * @return
	 */
	Integer updateWaterPrice(WaterPriceBean waterPriceBean);
	/**
	 * 修改用水价格状态
	 * 
	 * @param waterPriceBean
	 * @return
	 */
	Integer updateWaterPriceStatus(WaterPriceBean waterPriceBean);
	/**
	 * 根据id查询
	 * @param id
	 * @return
	 */
	WaterPriceBean selectWaterPriceById(@Param("id") String id);
	List<WaterPriceDetailBean> selectWaterPriceBetailByIdForRedRush(@Param("id") String id);
	/**
	 * 根据water_type_id查询
	 * @param waterTypeId
	 * @return
	 */
	WaterPriceBean selectWaterPriceByWaterTypeId(@Param("waterTypeId") String waterTypeId);
	
	Integer updateWaterPriceRemark(WaterPriceBean bean);
	/**
	 * 根据id查询 编辑初始化
	 * @param waterPriceId
	 * @return
	 */                                   
	List<WaterPriceDetailBean> selectWaterPriceBetailById(@Param("waterPriceId") String waterPriceId);
	/**
	 * 根据id查询 编辑初始化
	 * @param waterPriceDetailId
	 * @return
	 */                                   
	List<WaterPriceLadderBean> selectWaterPriceLadderById(@Param("waterPriceDetailId") String waterPriceDetailId);
	/**
	 * 根据id查询 编辑初始化
	 * @param waterPriceDetailId
	 * @return
	 */
	List<WaterPriceLadderBean> selectWaterPriceLadderByIdWithOrder(@Param("waterPriceDetailId") String waterPriceDetailId);
	/**
	 * 根据id查询 编辑初始化 带费用类型编号
	 * @param waterPriceId
	 * @return
	 */
	List<WaterPriceDetailBean> selectWaterPriceBetailByIdWithCostNo(@Param("waterPriceId") String waterPriceId);
	/**
	 * 根据id查询 编辑初始化
	 * @param id
	 * @return
	 */                                   
	Integer deleteWaterPriceLadderByPriceId(@Param("id") String id);
	/**
	 * 根据id查询 编辑初始化
	 * @param id
	 * @return
	 */                                   
	Integer deleteWaterPriceDetailByPriceId(@Param("id") String id);
	/**
	 * 根据用水类型获取用水价格
	 * @param useWaterType
	 * @return
	 */
	String getWaterPriceIdByType(@Param("useWaterType") String useWaterType);

	/**
	 * 查询下级列表
	 * @param useWaterType
	 * @param parentId
	 * @return
	 */
	List<SelectBean> findNextIdByParentId(@Param("useWaterType") String useWaterType, @Param("parentId") String parentId);

	List<String> selectAllWaterPrice(@Param("useWaterType") String useWaterType);
}
