package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.GarbageTypeBean;
import com.koron.zys.baseConfig.bean.GarbageTypeQueryBean;
import com.koron.zys.baseConfig.bean.GarbageUniversalBean;
import com.koron.zys.baseConfig.mapper.GarbageMapper;
import com.koron.zys.baseConfig.mapper.GarbageTypeMapper;
import com.koron.zys.baseConfig.mapper.WaterTypeMapper;
import com.koron.zys.baseConfig.queryBean.GarbageHisQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

public class UserGarbageHisList implements ServerInterface{

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
		try {
			GarbageHisQueryBean bean = JsonUtils.objectToPojo(req.getData(), GarbageHisQueryBean.class);
			GarbageMapper mapper = factory.getMapper(GarbageMapper.class);
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			List<GarbageUniversalBean> list = mapper.selectHisList(bean);
			WaterTypeMapper waterTypeMapper = factory.getMapper(WaterTypeMapper.class);
			GarbageTypeMapper garbageMapper = factory.getMapper(GarbageTypeMapper.class);
			List<GarbageTypeBean> typeList = garbageMapper.list(new GarbageTypeQueryBean());
			for(GarbageUniversalBean garbageBean:list) {				
				garbageBean.setUseWaterTypeName(waterTypeMapper.findWaterTypeById(garbageBean.getUseWaterType()).getWaterTypeName());
				if(typeList!=null && typeList.size() > 0 && StringUtils.isNotBlank(garbageBean.getGarbateType())) {
					for(int i=0;i<typeList.size();i++) {			//设置垃圾费类型
						if(garbageBean.getGarbateType().equals(typeList.get(i).getId()))
							garbageBean.setGarbateType(typeList.get(i).getGarbageTypeName());
					}
				}
				
			}
			info.setData(new PageInfo<>(list));
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}
