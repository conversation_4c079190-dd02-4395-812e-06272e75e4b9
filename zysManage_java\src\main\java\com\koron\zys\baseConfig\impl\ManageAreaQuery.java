package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ManageAreaBean;
import com.koron.zys.baseConfig.mapper.ManageAreaMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 管理区域，编辑初始化
 * <AUTHOR>
 */
public class ManageAreaQuery implements ServerInterface {
	
private static Logger logger = LoggerFactory.getLogger(ManageAreaQuery.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<ManageAreaBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", ManageAreaBean.class);

		try {
			ManageAreaBean bean = JsonUtils.objectToPojo(req.getData(), ManageAreaBean.class);
			ManageAreaMapper mapper = factory.getMapper(ManageAreaMapper.class);
			ManageAreaBean ManageAreabean = mapper.findManageAreaInfoById(bean.getManageAreaId());
			info.setData( ManageAreabean);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}
