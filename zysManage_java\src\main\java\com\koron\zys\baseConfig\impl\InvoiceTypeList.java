package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.InvoiceTypeBean;
import com.koron.zys.baseConfig.mapper.InvoiceTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
/**
 * 册本信息-列表
 * <AUTHOR>
 */
public class InvoiceTypeList implements ServerInterface {
	private static Logger logger = LoggerFactory.getLogger(InvoiceTypeList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			InvoiceTypeMapper mapper = factory.getMapper(InvoiceTypeMapper.class);
			List<InvoiceTypeBean> beans = mapper.select();
			
			info.setCode(Constant.MESSAGE_INT_SUCCESS);
			info.setDescription("success");
			info.setData(beans);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription("操作失败");
			logger.error("操作失败", e);
		}
		return info;
	}

}
