webpackJsonp([27],{EoKA:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"appServerAdd"},[r("el-form",{ref:"ruleFormappServerAdd",staticClass:"formBill-One",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px",inline:!0}},[r("el-form-item",{attrs:{label:"服务器编号：",prop:"serverCode"}},[r("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.serverCode,callback:function(t){e.$set(e.ruleForm,"serverCode",t)},expression:"ruleForm.serverCode"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"服务器名称：",prop:"serverName"}},[r("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.serverName,callback:function(t){e.$set(e.ruleForm,"serverName",t)},expression:"ruleForm.serverName"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"应用程序版本：",prop:"appVersion"}},[r("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.appVersion,callback:function(t){e.$set(e.ruleForm,"appVersion",t)},expression:"ruleForm.appVersion"}},e._l(e.appVersionOptions,function(e,t){return r("el-option",{key:e.versionId,attrs:{label:e.versionName,value:e.versionId}})}),1)],1),e._v(" "),r("el-form-item",{attrs:{label:"服务器IP：",prop:"appIp"}},[r("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.appIp,callback:function(t){e.$set(e.ruleForm,"appIp",t)},expression:"ruleForm.appIp"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"服务器端口：",prop:"appPort"}},[r("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.appPort,callback:function(t){e.$set(e.ruleForm,"appPort",t)},expression:"ruleForm.appPort"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"状态：",prop:"status"}},[r("el-select",{attrs:{placeholder:"请选择"},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[r("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),r("el-option",{attrs:{label:"禁用",value:0}})],1)],1),e._v(" "),r("el-form-item",{attrs:{label:"描述:",clearable:""}},[r("el-input",{attrs:{type:"textarea",maxlength:"500","show-word-limit":""},model:{value:e.ruleForm.comments,callback:function(t){e.$set(e.ruleForm,"comments",t)},expression:"ruleForm.comments"}})],1)],1)],1)},staticRenderFns:[]};var s={name:"appServer",components:{appServerAdd:r("VU/8")({name:"appServerAdd",data:function(){return{databaseData:[],ruleForm:{serverCode:"",serverName:"",appVersion:"",appIp:"",appPort:"",comments:"",status:1},appVersionOptions:[],rules:{serverCode:[{required:!0,message:"请输入服务器编号",trigger:"blur"}],serverName:[{required:!0,message:"请输入服务器名称",trigger:"blur"}],appVersion:[{required:!0,message:"请选择程序版本类型",trigger:"blur"}],appIp:[{required:!0,message:"请输入服务器IP",trigger:"blur"}],appPort:[{message:"请输入服务器端口",trigger:"blur",required:!0}],status:[{message:"请输入状态",trigger:"blur",required:!0}]}}},mounted:function(){this.getData()},methods:{getData:function(){var e=this;this.$api.fetch({params:{busicode:"appVersionList",data:{}}}).then(function(t){e.appVersionOptions=t})},submitForm:function(e,t){var r=this,a=this,s={};this.$refs[e].validate(function(e){if(!e)return!1;s="添加"===t?{busicode:"appServerAdd",data:r.ruleForm}:{busicode:"appServerUpdate",data:r.ruleForm},r.$api.fetch({params:s}).then(function(e){a.$message({showClose:!0,message:"保存成功",type:"success"}),a.$parent.selectTSubSystem(),a.$parent.closeDialog()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","appServerAdd",this.$parent.closeDialog)},editData:function(e){this.ruleForm=e}}},a,!1,function(e){r("HLXv")},null,null).exports},data:function(){return{tableShow:!0,tableQuery:{page:1,pageCount:50},maxHeight:0,appServerData:[],formData:{serverCode:"",serverName:"",appVersion:"",appIp:"",appPort:"",comments:"",status:1},crumbsData:{titleList:[{title:"系统管理",path:"/ChangeTables"},{title:"应用服务器",method:function(){window.histroy.back()}}]},appServerShow:!0,appServerAddVisible:!1}},mounted:function(){this.selectTSubSystem()},methods:{appAdd:function(e){var t=this;if("add"===e)this.$set(this.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),this.$refs.appServerAdd.editData(this.formData),this.common.chargeObjectEqual(this,this.formData,"set","appServerAdd");else{this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var r={busicode:"appServerList",data:{serverId:e.row.serverId}};this.$api.fetch({params:r}).then(function(e){t.$refs.appServerAdd.editData(e[0]),t.common.chargeObjectEqual(t,e[0],"set","appServerAdd")})}this.appServerShow=!1,this.appServerAddVisible=!0},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},formatStatus:function(e){return 1==e.status?"启用":"禁用"},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(e){this.tableQuery.page=e,this.selectTSubSystem()},selectTSubSystem:function(){var e=this;this.$api.fetch({params:{busicode:"appServerList",data:{}}}).then(function(t){e.appServerData=t,e.common.changeTable(e,".appServer .kl-table",[])})},closeDialog:function(){this.appServerShow=!0,this.appServerAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.appServerAdd.handleClose()},submit:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.appServerAdd.submitForm(e,t)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},i={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"appServer"},[r("div",{staticClass:"main-content"},[r("div",{staticClass:"bread-contain"},[r("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.appServerShow,expression:"appServerShow"}],staticClass:"bread-contain-right"},[r("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.appAdd("add")}}},[e._v("添加")])],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.appServerAddVisible,expression:"appServerAddVisible"}],staticClass:"bread-contain-right"},[r("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submit("ruleFormappServerAdd")}}},[e._v("保存")]),e._v(" "),r("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.handleClose}},[e._v("返回")])],1)],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.appServerShow,expression:"appServerShow"}],staticClass:"kl-table"},[e.tableShow?r("el-table",{attrs:{stripe:"",border:"",data:e.appServerData,"max-height":e.maxHeight}},[r("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),r("el-table-column",{attrs:{prop:"serverCode",label:"服务器编号","min-width":"100"}}),e._v(" "),r("el-table-column",{attrs:{prop:"serverName",label:"服务器名称","min-width":"100"}}),e._v(" "),r("el-table-column",{attrs:{prop:"appVersionName","min-width":"100",label:"应用程序版本"}}),e._v(" "),r("el-table-column",{attrs:{prop:"appIp","min-width":"80",label:"服务器IP","show-overflow-tooltip":""}}),e._v(" "),r("el-table-column",{attrs:{prop:"appPort","min-width":"100",label:"服务器端口"}}),e._v(" "),r("el-table-column",{attrs:{prop:"statusName","min-width":"80",formatter:e.formatStatus,label:"状态"}}),e._v(" "),r("el-table-column",{attrs:{prop:"comments",label:"描述","min-width":"150"}}),e._v(" "),r("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{type:"text"},on:{click:function(r){return e.appAdd(t)}}},[e._v("编辑")])]}}],null,!1,1191321586)})],1):e._e()],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.appServerAddVisible,expression:"appServerAddVisible"}]},[r("appServerAdd",{ref:"appServerAdd"})],1)])])},staticRenderFns:[]};var l=r("VU/8")(s,i,!1,function(e){r("jLDI")},null,null);t.default=l.exports},HLXv:function(e,t){},jLDI:function(e,t){}});