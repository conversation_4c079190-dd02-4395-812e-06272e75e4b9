package com.koron.util;

public class UserAuthorityException extends RuntimeException{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	public UserAuthorityException() {
		super();
	}

	public UserAuthorityException(String s) {
		super(s);
	}

	public UserAuthorityException(String message, Throwable cause) {
		super(message, cause);
	}

	public UserAuthorityException(Throwable cause) {
		super(cause);
	}

}
