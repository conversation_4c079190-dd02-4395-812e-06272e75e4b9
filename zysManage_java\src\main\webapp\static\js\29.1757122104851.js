webpackJsonp([29],{"9XSz":function(e,t){},QOwS:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i={name:"EquipmentEdit",props:["editNeedData"],data:function(){return{formData:{deviceType:"",deviceFactory:"",deviceModel:"",deviceSeries:"",machineSize:"",filterMethod:"",waterUrchins:"",outletFaucet:"",devicePower:"",waterFunction:""},formRules:{deviceType:[{required:!0,message:"请输入设备类型",trigger:"blur"}],deviceModel:[{required:!0,message:"请输入设备型号",trigger:"blur"}],deviceSeries:[{required:!0,message:"请输入设备系列",trigger:"blur"}],machineSize:[{required:!0,message:"请输入机器尺寸",trigger:"blur"}],filterMethod:[{required:!0,message:"请输入过滤方式",trigger:"blur"}],deviceFactory:[{required:!0,message:"请输入设备厂家",trigger:"blur"}]}}},mounted:function(){"edit"==this.editNeedData.type&&(this.init(),this.formData.id=this.editNeedData.id)},methods:{init:function(){var e=this,t={busicode:"DeviceTypeDetail",data:this.editNeedData.id};this.$api.fetch({params:t}).then(function(t){e.formData=t})},submitForm:function(e){var t=this,a=this;this.$refs.EquipmentEditForm.validate(function(e){if(console.log(e),e){var i={busicode:t.editNeedData.busicode,data:a.formData};t.$api.fetch({params:i}).then(function(e){a.$message({showClose:!0,message:"保存成功",type:"success"}),a.$parent.init(),a.$parent.closeDialog()})}else a.$message({showClose:!0,message:"必填项未填写",type:"error"})})}}},l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"EquipmentEdit"},[a("el-form",{ref:"EquipmentEditForm",staticClass:"formBill-Two",attrs:{inline:!0,size:"mini",model:e.formData,rules:e.formRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"设备类型：",prop:"deviceType"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入设备类型"},model:{value:e.formData.deviceType,callback:function(t){e.$set(e.formData,"deviceType",t)},expression:"formData.deviceType"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"设备系列：",prop:"deviceSeries"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入设备系列"},model:{value:e.formData.deviceSeries,callback:function(t){e.$set(e.formData,"deviceSeries",t)},expression:"formData.deviceSeries"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"设备型号：",prop:"deviceModel"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入设备型号"},model:{value:e.formData.deviceModel,callback:function(t){e.$set(e.formData,"deviceModel",t)},expression:"formData.deviceModel"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"过滤方式：",prop:"filterMethod"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入过滤方式"},model:{value:e.formData.filterMethod,callback:function(t){e.$set(e.formData,"filterMethod",t)},expression:"formData.filterMethod"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"设备厂家：",prop:"deviceFactory"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入设备厂家"},model:{value:e.formData.deviceFactory,callback:function(t){e.$set(e.formData,"deviceFactory",t)},expression:"formData.deviceFactory"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"机器尺寸：",prop:"machineSize"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入机器尺寸"},model:{value:e.formData.machineSize,callback:function(t){e.$set(e.formData,"machineSize",t)},expression:"formData.machineSize"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"功率：",prop:"devicePower"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入功率"},model:{value:e.formData.devicePower,callback:function(t){e.$set(e.formData,"devicePower",t)},expression:"formData.devicePower"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"水胆：",prop:"waterUrchins"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入水胆"},model:{value:e.formData.waterUrchins,callback:function(t){e.$set(e.formData,"waterUrchins",t)},expression:"formData.waterUrchins"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"出水龙头：",prop:"outletFaucet"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入出水龙头"},model:{value:e.formData.outletFaucet,callback:function(t){e.$set(e.formData,"outletFaucet",t)},expression:"formData.outletFaucet"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"制水能力：",prop:"waterFunction"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入制水能力"},model:{value:e.formData.waterFunction,callback:function(t){e.$set(e.formData,"waterFunction",t)},expression:"formData.waterFunction"}})],1)],1)],1)},staticRenderFns:[]};var r={components:{EquipmentEdit:a("VU/8")(i,l,!1,function(e){a("ewSn")},"data-v-21a83927",null).exports},name:"EquipmentTypeManagement",data:function(){return{EditVisible:!1,editNeedData:{},crumbsData:{titleList:[{title:"基础配置",path:"/baseInfo"},{title:"设备类型管理",method:function(){window.histroy.back()}}]},maxHeight:0,tableShow:!1,tableData:[],tableQuery:{page:1,pageCount:10,deviceType:"",deviceFactory:"",deviceModel:"",deviceSeries:"",filterMethod:""}}},mounted:function(){var e=this;this.init(),this.$nextTick(function(){e.common.changeTable(e,".EquipmentTypeManagement .EquipmentTypeManagementIndex",[".EquipmentTypeManagement .block",".EquipmentTypeManagement .toolbar"])})},methods:{init:function(){var e=this,t={busicode:"DeviceTypeList",data:this.tableQuery};this.$api.fetch({params:t}).then(function(t){e.tableData=t})},search:function(){this.tableQuery.page=1,this.init()},add:function(e,t){this.EditVisible=!0,"add"===e?(this.editNeedData.type="add",this.editNeedData.busicode="DeviceTypeAdd"):(this.editNeedData.type="edit",this.editNeedData.busicode="DeviceTypeEdit",this.editNeedData.id=t.id)},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.init()},handleCurrentChange:function(e){this.tableQuery.page=e,this.init()},del:function(e){var t=this;this.$confirm("此操作将永久删除该设备类型, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var a={busicode:"DeviceTypeDel",data:e.id};t.$api.fetch({params:a}).then(function(e){t.$message({message:"删除成功",type:"info"}),t.init()})}).catch(function(){t.$message({message:"已取消删除",type:"info"})})},closeDialog:function(){this.EditVisible=!1},submitForm:function(){this.$refs.EquipmentEdit.submitForm()}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"EquipmentTypeManagement"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),e.EditVisible?a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm()}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.closeDialog}},[e._v("返回")])],1):a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-plus",type:"primary"},on:{click:function(t){return e.add("add")}}},[e._v("添加")])],1)],1),e._v(" "),e.EditVisible?a("EquipmentEdit",{ref:"EquipmentEdit",attrs:{editNeedData:e.editNeedData}}):a("div",{staticClass:"EquipmentTypeManagementIndex"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,size:"mini",model:e.tableQuery}},[a("el-form-item",{staticClass:"form-left"},[a("el-form-item",{attrs:{label:"设备类型："}},[a("el-input",{attrs:{placeholder:"请输入设备类型",clearable:""},model:{value:e.tableQuery.deviceType,callback:function(t){e.$set(e.tableQuery,"deviceType",t)},expression:"tableQuery.deviceType"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"厂 家："}},[a("el-input",{attrs:{placeholder:"请输入厂家",clearable:""},model:{value:e.tableQuery.deviceFactory,callback:function(t){e.$set(e.tableQuery,"deviceFactory",t)},expression:"tableQuery.deviceFactory"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"设备系列："}},[a("el-input",{attrs:{placeholder:"请输入设备系列",clearable:""},model:{value:e.tableQuery.deviceSeries,callback:function(t){e.$set(e.tableQuery,"deviceSeries",t)},expression:"tableQuery.deviceSeries"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"设备型号："}},[a("el-input",{attrs:{placeholder:"请输入设备型号",clearable:""},model:{value:e.tableQuery.deviceModel,callback:function(t){e.$set(e.tableQuery,"deviceModel",t)},expression:"tableQuery.deviceModel"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"过滤方式："}},[a("el-input",{attrs:{placeholder:"请输入过滤方式",clearable:""},model:{value:e.tableQuery.filterMethod,callback:function(t){e.$set(e.tableQuery,"filterMethod",t)},expression:"tableQuery.filterMethod"}})],1),e._v(" "),a("el-form-item",[a("el-button",{staticClass:"searchBtn",attrs:{icon:"el-icon-search"},on:{click:e.search}})],1)],1)],1)],1),e._v(" "),a("div",{staticClass:"kl-table"},[e.tableShow?a("el-table",{staticClass:"wuserInfo-table",attrs:{"max-height":e.maxHeight,stripe:"",border:"",data:e.tableData.list,"highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"deviceType","min-width":"100",label:"设备类型","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"deviceFactory","min-width":"100",label:"厂家","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"deviceSeries","min-width":"100",label:"设备系列","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"deviceModel","min-width":"100",label:"设备型号","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"filterMethod","min-width":"100",label:"过滤方式","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"machineSize","min-width":"100",label:"机器尺寸","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"devicePower","min-width":"80",label:"功率"}}),e._v(" "),a("el-table-column",{attrs:{prop:"waterUrchins","min-width":"80",label:"水胆"}}),e._v(" "),a("el-table-column",{attrs:{prop:"outletFaucet","min-width":"120",label:"出水龙头"}}),e._v(" "),a("el-table-column",{attrs:{prop:"waterFunction","min-width":"120",label:"制水功能"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"mini"},nativeOn:{click:function(a){return e.add("edit",t.row)}}},[e._v("编辑")]),e._v(" "),a("el-button",{attrs:{type:"text",size:"mini"},nativeOn:{click:function(a){return e.del(t.row)}}},[e._v("删除")])]}}],null,!1,1164383087)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[10,50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)])],1)},staticRenderFns:[]};var n=a("VU/8")(r,o,!1,function(e){a("9XSz")},null,null);t.default=n.exports},ewSn:function(e,t){}});