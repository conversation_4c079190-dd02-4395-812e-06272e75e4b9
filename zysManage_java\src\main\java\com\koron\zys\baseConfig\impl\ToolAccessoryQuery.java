package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.ToolAccessoryBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.ToolAccessoryMapper;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

public class ToolAccessoryQuery implements ServerInterface {

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            MessageBean<ToolAccessoryBean> msg = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", ToolAccessoryBean.class);
            ToolAccessoryBean bean = JsonUtils.objectToPojo(req.getData(), ToolAccessoryBean.class);
            msg.setData(factory.getMapper(ToolAccessoryMapper.class,"_default").getToolAccessoryById(bean.getId()));
            return msg;
        } catch (Exception e) {
            logger.error("工具页附件根据id查询失败", e.getMessage(), e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "查询失败", null);
        }
    }
}
