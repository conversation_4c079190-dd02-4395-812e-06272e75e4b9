package com.koron.util.secret;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.swan.bean.MessageBean;

import com.koron.zys.serviceManage.utils.JsonUtils;

@Component
public class Tools {

	private static String apAppId;// 应用id

	public void setApAppId(String apAppId) {
		this.apAppId = apAppId;
	}

	private static Logger logger = LoggerFactory.getLogger(Tools.class);

	public static MessageBean callMgInterface(String url, Object data) {
		try {
			RequestBean reqBean = new RequestBean<>();
			reqBean.setUrl(url);
			reqBean.setData(data);
			return JsonUtils.jsonToPojo(SecretReqUtil.sendToGatWay(reqBean), MessageBean.class);
		} catch (Exception e) {
			logger.error("调用中台接口失败" + url, e);
			throw new RuntimeException("调用中台接口失败");
		}
	}

	public static MessageBean callDataPushInterface(Integer type, Integer opType, Object data) {
		try {
			RequestBean reqBean = new RequestBean<>();
			reqBean.setUrl(StaticInfo.dataPushUrl);
			Map<String, Object> map = new HashMap<>();
			map.put("type", type);
			map.put("opType", opType);
			map.put("data", JsonUtils.objectToJson(data));
			reqBean.setData(map);
			reqBean.setAppid(apAppId);
			return JsonUtils.jsonToPojo(SecretReqUtil.sendToGatWay(reqBean), MessageBean.class);
		} catch (Exception e) {
			logger.error("调用应用平台数据推送接口失败", e);
			throw new RuntimeException("调用数应用平台据推送接口失败");
		}
	}
}