package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CouponExpirationReminderBean;
import com.koron.zys.baseConfig.mapper.CouponExpirationReminderMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.swan.bean.MessageBean;
/**
 * 优惠到期提醒列表展示
 * */
@Service
public class CouponExpirationReminderQuery implements ServerInterface {
    private static Logger logger = LoggerFactory.getLogger(CouponExpirationReminderQuery.class);
    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        MessageBean<CouponExpirationReminderBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", CouponExpirationReminderBean.class);
        try {
            CouponExpirationReminderMapper mapper = factory.getMapper(CouponExpirationReminderMapper.class);
            CouponExpirationReminderBean couponExpirationReminder = mapper.getCouponExpirationReminder();
            info.setCode(Constant.MESSAGE_INT_SUCCESS);
            info.setDescription("success");
            info.setData(couponExpirationReminder);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription("操作失败");
            logger.error("操作失败", e);
        }
        return info;
    }
}
