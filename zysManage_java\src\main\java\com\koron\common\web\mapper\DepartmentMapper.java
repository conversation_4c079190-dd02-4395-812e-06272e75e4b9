package com.koron.common.web.mapper;

import com.koron.common.bean.StaffBean;
import com.koron.common.web.bean.DepartmentBean;
import com.koron.common.web.bean.DepartmentTreeBean;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.koron.ebs.mybatis.EnvSource;

import java.util.List;

/**
 * 后面删除
 */
@EnvSource("_default")
public interface DepartmentMapper {
	@Select("select tbltree.seq,tbltree.parentmask,tbldepartment.* from tbltree \n" +
			"inner join tbldepartment on tbltree.foreignkey=tbldepartment.id \n" +
			"inner join (\n" +
			"	select tbltree.* from tbltree \n" +
			"where type = 65536 and seq =#{id}) a on (tbltree.seq & ~((1 << (62 - a.parentMask-a.mask))-1)) = a.seq")
	List<DepartmentTreeBean> getDescendantByParentId(@Param("id") Long id);
	@Select("select tbltree.seq,tbltree.parentmask,tbldepartment.* from tbltree \n" +
			"inner join tbldepartment on tbltree.foreignkey=tbldepartment.id \n" +
			"where (#{seq} & ~((1 << (62 - parentmask-mask))-1)) = seq\n" +
			"order by seq\n")
	List<DepartmentTreeBean> getPathById(@Param("seq") Long seq);
	@Select("select a.*,b.name as departmentname,b.code as departmentCode from tblstaff a left join tbldepartment b on a.departmentCode = b.code where b.code = #{department}")
	List<StaffBean> getStaffOfDep(@Param("department")String code);
	@Select("select a.*,b.name as departmentname,b.code as departmentCode  from tblstaff a left join tbldepartment b on a.departmentCode = b.code")
	List<StaffBean> getStaffInfo();
	@Select("SELECT a.* FROM tbldepartment a WHERE state = 0 ORDER BY a.sn DESC")
	List<DepartmentBean> getDepartmentInfo();
	@Select("select tbldepartment.* from tbldepartment where state = 0 and code = #{parentCode}")
	DepartmentBean getDepartmentByParentCode(String parentCode);
	@Delete("DELETE FROM tbldepartment")
	int deleteAll();

	@Delete("DELETE FROM tbldepartment WHERE id = #{id}")
	int deleteById(int id);

	@Select("select tbltree.seq,tbltree.parentmask,tbldepartment.* from tbltree \n" +
			"inner join tbldepartment on tbltree.foreignkey=tbldepartment.id \n" +
			"where flag = #{flag} order by seq ")
	List<DepartmentTreeBean> getDepartmentInfoByFlag(@Param("flag") int flag);
	@Select("select * from tbldepartment where code = #{code}")
	DepartmentBean getDepartmentInfoByCode(@Param("code") String code);

	@Select("select * from tbldepartment where id = #{id}")
	DepartmentBean getDepartmentInfoById(int id);
	int batchInsertDepartment(List<DepartmentBean> list);

	int insertDepartment(DepartmentBean department);
	int updateDepartment(DepartmentBean bean);

}