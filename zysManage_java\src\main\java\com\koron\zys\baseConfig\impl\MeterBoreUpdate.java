package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterBoreBean;
import com.koron.zys.baseConfig.mapper.MeterBoreMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;
/**
 * 水表口径-编辑
 * <AUTHOR>
 *
 */
public class MeterBoreUpdate implements ServerInterface{
	
private static Logger logger = LoggerFactory.getLogger(MeterBoreUpdate.class);
	
@Override
public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
	MeterBoreMapper mapper = factory.getMapper(MeterBoreMapper.class);
	MeterBoreBean bean = null;
	try {
		bean = JsonUtils.objectToPojo(req.getData(), MeterBoreBean.class);
		// 校验字段重复
		if (mapper.check2("bore_name", bean.getBoreName(),bean.getId()) > 0) {
			return MessageBean.create(Constant.ILLEGAL_PARAMETER,"水表口径名称：" + bean.getBoreName() + "的信息已存在。", void.class);
		}
		bean.setUpdateName(userInfo.getUserInfo().getName());
		bean.setUpdateTime(CommonUtils.getCurrentTime());
		bean.setUpdateAccount(userInfo.getUserInfo().getAcount());
		if (StringUtils.isNullOrEmpty(bean.getId())) {
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
		}

		mapper.updateMeterBore(bean);
	} catch (Exception e) {
		logger.error("非法参数",e);
		return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
	}
	return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
}
}