webpackJsonp([61],{0:function(t,e,a){a("j1ja"),t.exports=a("NHnr")},"2Uyi":function(t,e,a){"use strict";(function(t){var n=a("woOf"),o=a.n(n),i=a("mvHQ"),r=a.n(i),c=a("aFK5"),s=a.n(c),l=a("pFYg"),p=a.n(l),u=(a("7+uW"),a("zL8q")),d=(a.n(u),this),m={};function h(e,a,n){var o=t(a).outerHeight(),i=0;if("object"===(void 0===n?"undefined":p()(n)))for(var r in n)t(n[r]).outerHeight()?i+=t(n[r]).outerHeight():i+=0;e.maxHeight=o-i-50}m.date=function(t){var e=new Date,a=void 0;e.setTime(e.getTime());var n=e.getFullYear(),o=e.getMonth(),i=e.getDate();if("month"===t)a=n+(o+1<10?"0"+(o+1):o+1);else if("lastMonth"===t)a=n+(o<10?"0"+o:o);else if("halfYear"===t)a=n+(o-5<10?"0"+(o-5):o-5);else if("number"==typeof t){e.setTime(e.getTime()+t);var r=e.getFullYear(),c=e.getMonth(),s=e.getDate();a=r+"-"+(c+1<10?"0"+(c+1):c+1)+"-"+(s<10?"0"+s:s)}else a=n+"-"+(o+1<10?"0"+(o+1):o+1)+"-"+(i<10?"0"+i:i);return a},m.changeTable=function(e,a,n){h(e,a,n),t(window).resize(function(){h(e,a,n)})},m.chargeObjectEqual=function(t,e,a,n,o){var i=!0;if("set"==a)sessionStorage.setItem(n,r()(e));else if("get"==a){n=JSON.parse(sessionStorage.getItem(n));var c=r()(e);(i=function t(e,a){var n=!0,o=s()(e),i=s()(a);if(o.length!=i.length)return!1;for(var r=0,c=o.length;r<c;r++){var l=o[r];if("[object Array]"==Object.prototype.toString.call(e[l]))if(e[l].length!=a[l].length)n=!1;else for(var p=0;p<e[l].length;p++)"[object Object]"==Object.prototype.toString.call(e[l][p])?t(e[l][p],a[l][p]):e[l][p]!==a[l][p]&&(n=!1);else"[object Object]"==Object.prototype.toString.call(e[l])?t(e[l],a[l]):e[l]!==a[l]&&(n=!1)}return n}(n,JSON.parse(c)))&&o?o():i||t.$confirm("您填写的信息尚未保存, 是否确认离开?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){o()}).catch(function(){return!0})}},m.getExportExcelIp=function(){return console.log("production",Object({NODE_ENV:"production",baseUrl:"/zysManage/"}).exportUrl),window.location.origin},m.downFile=function(t){var e=document.createElement("iframe");e.src=t,e.style.display="none",document.body.appendChild(e)},m.exportExcel=function(t,e,n,o,i,r,c){var s={};e.pageCount=0===r?20:r;var l={data:e};c.$api.fetch({apiUrl:t,method:"post",params:l,needErrorCallback:!0}).then(function(t){0===t.code&&((s=t.data).list[0]?a.e(1).then(a.bind(null,"zWO4")).then(function(t){var e=e,a=a,n=s.list.map(function(t){return a.map(function(e){return t[e]})});t.export_json_to_excel({header:e,data:n,filename:i,autoWidth:"100"})}):c.$notify({title:"警告",message:"表格没有相关数据",type:"error"}))})},m.handleExceed=function(t,e){d.$message.warning("当前限制选择3个文件，本次选择了 "+t.length+" 个文件，共选择了 "+(t.length+e.length)+" 个文件")},m.beforeRemove=function(t,e){return d.$confirm("确定移除 "+t.name+"？")},m.handleSuccess=function(t,e,a){0===t.code?d.$message({showClose:!0,type:"success",message:"文件上传成功"}):(d.$message({showClose:!0,message:t.description,type:"error"}),a.splice(a.length-1,1))},m.handleError=function(t,e,a){d.$message({showClose:!0,message:"上传文件错误！",type:"error"})},m.handleData=function(t,e){for(var a=s()(t),n=s()(e),o="",i=0,r=a.length;i<r;i++)o=a[i],n.includes(o)&&(null===t[o]||(e[o]=t[o]));return e},m.print=function(e,a,n,o,i,r){var c=localStorage.getItem("token");console.log(window.location.origin);var s=window.location.href.split("#")[0];o=""==o?c+"$$$businessCode_"+n:c+"$$$"+o,t.ajax({url:"http://localhost:25423/print?type="+e+"&key="+i+"&url="+s+"&formatFile="+a+"_"+o,type:"GET",success:function(t){"ok"!=t&&r.$notify({title:"警告",message:"打印不成功"+t,type:"error"})},error:function(t){r.$notify({title:"警告",message:"请启动打印服务",type:"error"})}})},m.getProcessStateData=function(t,e,a,n){var o={busicode:"ProcessNodeList",data:{receiptType:t,companyNo:e}};a.$api.fetch({params:o}).then(function(t){n(t)})},m.processCommit=function(t,e,a){var n=e,i={busicode:"ProcessCommit",data:t=o()({},{receiptType:"",billId:"",companyNo:"",comments:"",title:"",curTaskId:"",operation:"",subinfo:"",subuser:"",formstr:"",nextCandidateUsers:"",subject:"",attachmentId:""},t)};n.$api.fetch({params:i}).then(function(t){n.$message({showClose:!0,message:"提交成功",type:"success"}),a()})},m.getProcessShowView=function(e,a,n,i,c,s,l,p){var d=l,m={busicode:"ProcessShowView",data:{receiptType:e,processInstanceId:i,companyNo:s}};d.$api.fetch({params:m}).then(function(i){t("#"+c).html(i),t(".submit-opinion").on("click",function(){var t={},i=u.Loading.service({lock:!0,text:"正在上传......",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.3)"});try{t=getSubmitData()}catch(t){return i.close(),void d.$message({showClose:!0,message:t,type:"error"})}i.close();var c={};if(t.__nodeOperator)for(var l=0;l<t.__nodeOperator.length;l++)c=o()(c,t.__nodeOperator[l]);if(t.__nodeOperator)for(var m=0;m<t.__mnodeOperator.length;m++)c=o()(c,t.__mnodeOperator[m]);d.common.processCommit({receiptType:e,billId:n,title:a,operation:t.__operation,comments:t.__comment,curTaskId:t.taskid,subinfo:t.__subinfo,subuser:t.__subuser,nextCandidateUsers:r()(c),subject:t.__comment,attachmentId:t.__attachmentId,companyNo:s},d,function(){p()})})})},e.a=m}).call(e,a("7t+N"))},NHnr:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("7+uW"),o=a("xJD8"),i={render:function(){var t=this.$createElement,e=this._self._c||t;return e("div",{attrs:{id:"app"}},[e("router-view")],1)},staticRenderFns:[]};var r=function(t){a("kG+1")},c=a("VU/8")(o.a,i,!1,r,null,null).exports,s=a("Gu7T"),l=a.n(s),p=a("/ocq"),u=[{path:"/baseInfo",meta:{title:"基础配置"},component:function(t){return a.e(53).then(function(){var e=[a("Amdv")];t.apply(null,e)}.bind(this)).catch(a.oe)},children:[{path:"/",redirect:"indexMan"},{path:"indexMan",meta:{title:"指标管理"},component:function(t){return a.e(15).then(function(){var e=[a("W1Sh")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"adminArea",meta:{title:"结构地址管理"},component:function(t){return a.e(22).then(function(){var e=[a("ExFb")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"dataDictionary",meta:{title:"数据字典"},component:function(t){return a.e(41).then(function(){var e=[a("kWVZ")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"areaMan",meta:{title:"片区管理"},component:function(t){return a.e(42).then(function(){var e=[a("sJN2")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"postMan",meta:{title:"岗位管理"},component:function(t){return a.e(34).then(function(){var e=[a("LsLR")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"bankInterfaceMan",meta:{title:"银行驱动"},component:function(t){return a.e(28).then(function(){var e=[a("Oo+T")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"engMaterMan",meta:{title:"物料编码"},component:function(t){return Promise.all([a.e(0),a.e(54)]).then(function(){var e=[a("+9io")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"EnclosureMan",meta:{title:"附件管理"},component:function(t){return a.e(43).then(function(){var e=[a("W1s5")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"billTypeMan",meta:{title:"单据类型管理"},component:function(t){return a.e(47).then(function(){var e=[a("kxRM")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"pubCost",meta:{title:"费用种类"},component:function(t){return a.e(45).then(function(){var e=[a("OCti")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"printBusinessMan",meta:{title:"打印模板"},component:function(t){return a.e(25).then(function(){var e=[a("TCmE")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"codeRule",meta:{title:"编码规则"},component:function(t){return a.e(20).then(function(){var e=[a("DY2B")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"WatermeterModel",meta:{title:"水表口径"},component:function(t){return a.e(30).then(function(){var e=[a("ez0b")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"EquipmentTypeManagement",meta:{title:"设备类型管理"},component:function(t){return a.e(29).then(function(){var e=[a("QOwS")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"HolidayConfiguration",meta:{title:"假期配置"},component:function(t){return a.e(14).then(function(){var e=[a("5Z1r")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"WaterMeterFactory",meta:{title:"水表供应商"},component:function(t){return a.e(24).then(function(){var e=[a("ijcP")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"WaterMeterBore",meta:{title:"水表口径"},component:function(t){return a.e(11).then(function(){var e=[a("RuEX")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"TradeClassify",meta:{title:"行业分类"},component:function(t){return a.e(12).then(function(){var e=[a("5n4f")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"ToolAccessory",meta:{title:"下载页管理"},component:function(t){return a.e(46).then(function(){var e=[a("HFko")];t.apply(null,e)}.bind(this)).catch(a.oe)}}]}],d=a("NYxO"),m=a("bOdI"),h=a.n(m),f=a("mvHQ"),y=a.n(f),g=a("woOf"),b=a.n(g),v=a("//Fk"),w=a.n(v),k=a("mtWM"),C=a.n(k),T=a("zL8q"),E=a.n(T),M="get",S="post";C.a.interceptors.request.use(function(t){var e=window.location.hash.indexOf("?");return t.headers.url=-1!==e?window.location.hash.substring(0,e):window.location.hash,t},function(t){return w.a.reject(t)}),C.a.interceptors.response.use(function(t){return t},function(t){if(t&&t.response)switch(t.response.status){case 400:t.message="请求错误",vm.$message({showClose:!0,message:"请求错误！",type:"error"});break;case 401:t.message="未授权，请登录",vm.$message({showClose:!0,message:"未授权，请登录！",type:"error"});break;case 403:t.message="拒绝访问",vm.$message({showClose:!0,message:"拒绝访问！",type:"error"});break;case 404:t.message="请求地址出错: "+t.response.config.url,vm.$message({showClose:!0,message:"请求地址出错: "+t.response.config.url,type:"error"});break;case 408:t.message="请求超时",vm.$message({showClose:!0,message:"请求超时",type:"error"});break;case 500:t.message="服务器内部错误",vm.$message({showClose:!0,message:"服务器内部错误",type:"error"});break;case 501:t.message="服务未实现",vm.$message({showClose:!0,message:"服务未实现",type:"error"});break;case 502:t.message="网关错误",vm.$message({showClose:!0,message:"网关错误",type:"error"});break;case 503:t.message="服务不可用",vm.$message({showClose:!0,message:"服务不可用",type:"error"});break;case 504:t.message="网关超时",vm.$message({showClose:!0,message:"网关超时",type:"error"});break;case 505:t.message="HTTP版本不受支持",vm.$message({showClose:!0,message:"HTTP版本不受支持",type:"error"});break;default:t.message="未知错误",vm.$message({showClose:!0,message:"未知错误"+t,type:"error"})}return w.a.reject(t)});var N={fetch:function(t){var e=T.Loading.service({lock:!0,text:"加载中......",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.3)"}),a={apiUrl:"interface.api",method:S,params:null,isJson:!0,needErrorCallback:!0};(t=b()({},a,t)).params.token=localStorage.getItem("token"),"002"==t.params.sysType||(t.params.sysType="001"),C.a.defaults.baseURL=$baseUrl;var n={url:t.apiUrl,method:t.method,timeout:6e4,headers:{"Content-Type":"application/x-www-form-urlencoded"},withCredentials:!0};console.log(t),t.method===M?n.params=t.params:t.method===S&&(t.isJson?(n.headers["Content-Type"]="application/json;charset=UTF-8",n.data=t.params,console.log(n)):n.params=t.params);var o=window.location.host.search(":"),i=(window.location.host+"").slice(0,o);if(n.data&&void 0!==n.data.type&&"import"==n.data.type){n.url="importExcel.api",n.headers["Content-Type"]="multipart/form-data;charset=UTF-8";var r=new FormData;n.data.data.id?r.append("json",y()({token:n.data.token,sysType:n.data.sysType,busicode:n.data.busicode,data:{id:n.data.data.id}})):r.append("json",y()({token:n.data.token,sysType:n.data.sysType,busicode:n.data.busicode,data:{importFileName:n.data.data.importFileName,importFileContent:n.data.data.importFileContent,dbName:n.data.data.dbName}})),r.append("file",n.data.data.file),n.data=r}if(void 0!==n.data.type&&"upload"==n.data.type)n.url="accessoryUpload.api",n.headers["Content-Type"]="multipart/form-data;charset=UTF-8",(r=new FormData).append("json",y()({token:n.data.token,sysType:n.data.sysType,busicode:n.data.busicode,data:{receiptId:n.data.data.receiptId,receiptType:n.data.data.receiptType,accessoryNo:n.data.data.accessoryNo}})),r.append("file",n.data.data.file),n.data.data.uploadType&&r.append("uploadType",n.data.data.uploadType),n.data=r;else if(void 0!==n.data.type&&"signUpload"==n.data.type){var c;console.log("EEEEEEEEEEEEEEEEEEEEEE"),n.url="accessoryUpload.api",n.headers["Content-Type"]="multipart/form-data;charset=UTF-8",(r=new FormData).append("json",y()({token:n.data.token,sysType:n.data.sysType,busicode:n.data.busicode,data:(c={id:n.data.data.id,bid:n.data.data.bid,name:n.data.data.name,pluginId:n.data.data.pluginId,companyNo:n.data.data.companyNo,outBank:n.data.data.outBank,outBankName:n.data.data.outBankName,outType:n.data.data.outType,status:n.data.data.status,comments:n.data.data.comments,communicationType:n.data.data.communicationType,bankDriverClassName:n.data.data.bankDriverClassName,configParam:n.data.data.configParam,outExpSql:n.data.data.outExpSql,outExpFileType:n.data.data.outExpFileType,outAmount:n.data.data.outAmount,backAccountPeriodColumn:n.data.data.backAccountPeriodColumn,backUserNoColumn:n.data.data.backUserNoColumn,backMoneyColumn:n.data.data.backMoneyColumn,backResultColumn:n.data.data.backResultColumn,backDescColumn:n.data.data.backDescColumn,backSuccessFlag:n.data.data.backSuccessFlag,signExpSql:n.data.data.signExpSql,signExpFileType:n.data.data.signExpFileType,signUserNoColumn:n.data.data.signUserNoColumn,signEntrustContractColumn:n.data.data.signEntrustContractColumn,signResultCodeColumn:n.data.data.signResultCodeColumn,signResultDescColumn:n.data.data.signResultDescColumn,signSuccessFlag:n.data.data.signSuccessFlag,signBankAccountColumn:n.data.data.signBankAccountColumn,signBankOpenAccountNameColumn:n.data.data.signBankOpenAccountNameColumn},h()(c,"bankDriverClassName",n.data.data.bankDriverClassName),h()(c,"bankFtpIp",n.data.data.bankFtpIp),h()(c,"bankFtpPort",n.data.data.bankFtpPort),h()(c,"bankFtpPassword",n.data.data.bankFtpPassword),h()(c,"bankClientIp",n.data.data.bankClientIp),h()(c,"backFileColumn",n.data.data.backFileColumn),h()(c,"bankClientPort",n.data.data.bankClientPort),h()(c,"bankServerPort",n.data.data.bankServerPort),h()(c,"chargingType",n.data.data.chargingType),h()(c,"chargeStaffAccount",n.data.data.chargeStaffAccount),h()(c,"chargeStaffName",n.data.data.chargeStaffName),c)})),r.append("file",n.data.data.bankDriverClass),r.append("file",n.data.data.outExcelTemplateFile),r.append("file",n.data.data.bankSignTemplateFile),r.append("file",n.data.data.file),n.data.data.uploadType&&r.append("uploadType",n.data.data.uploadType),n.data=r}return t.responseType&&(n.responseType=t.responseType),"127.0.0.1"===i&&(i="localhost"),new w.a(function(a,o){C()(n).then(function(n){e.close(),200===n.status&&(void 0==n.data.code?t.needErrorCallback&&o(n.data):(1e4===n.data.code&&(window.location.href="localhost"==i?"http://localhost:8092":window.location.href.split("yw")[0]+"yw/"),0!==n.data.code?(vm.$notify({title:"错误",message:n.data.description,type:"error"}),t.needErrorCallback&&o(n.data)):a(n.data.data)))},function(a){e.close(),t.needErrorCallback&&o(a)}).catch(function(a){e.close(),t.needErrorCallback&&o(a)})})}};n.default.use(d.a);var P=new d.a.Store({state:{treeNode:"",treeBread:[],company:"",hostIP:"",companyInfo:{},user:"",platformVer:"",uid:"",url:"http://*************:8080/css2"},mutations:{setTreeNode:function(t,e){t.treeNode=e},setTreeBread:function(t,e){t.treeBread=e},setCompany:function(t,e){t.company=e},setPlatformVer:function(t,e){t.platformVer=e},setHostIP:function(t,e){var a=window.location.host.search(":"),n=(window.location.host+"").slice(0,a);"127.0.0.1"===n&&(n="localhost"),t.hostIP=n}},actions:{setPlatformVer:function(t){N.fetch({params:{busicode:"AuthorityPlatformVersion",data:{}}}).then(function(e){t.commit("setPlatformVer",e)})}}}),F=[{path:"/systemMan",meta:{title:"基础配置"},component:function(t){return a.e(49).then(function(){var e=[a("Xh+l")];t.apply(null,e)}.bind(this)).catch(a.oe)},children:[{path:"/",redirect:"InterfaceMan"},{path:"childSystem",meta:{title:"子系统"},component:function(t){return a.e(36).then(function(){var e=[a("gMLm")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"funModule",meta:{title:"功能模块"},component:function(t){return a.e(21).then(function(){var e=[a("QfyB")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"sysUser",meta:{title:"运维账户"},component:function(t){return a.e(37).then(function(){var e=[a("Jd7/")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"InterfaceMan",meta:{title:"接口管理"},component:function(t){return a.e(8).then(function(){var e=[a("7dXs")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"dataBaseMan",meta:{title:"数据库服务器"},component:function(t){return a.e(31).then(function(){var e=[a("pX+p")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"appVersion",meta:{title:"应用程序版本"},component:function(t){return a.e(33).then(function(){var e=[a("bT8g")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"appServer",meta:{title:"应用服务器"},component:function(t){return a.e(27).then(function(){var e=[a("EoKA")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"department",meta:{title:"组织架构"},component:function(t){return Promise.all([a.e(0),a.e(16)]).then(function(){var e=[a("7QMZ")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"staffMgt",meta:{title:"职员管理"},component:function(t){return Promise.all([a.e(0),a.e(19)]).then(function(){var e=[a("NUM3")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"taskMgt",meta:{title:"定时任务管理"},component:function(t){return a.e(18).then(function(){var e=[a("KpoN")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"MenuManagement",meta:{title:"菜单管理"},component:function(t){return a.e(56).then(function(){var e=[a("0hX7")];t.apply(null,e)}.bind(this)).catch(a.oe)},beforeEnter:function(t,e,a){"2"==P.state.platformVer&&a("/systemMan/MenuManagementOld"),a()}},{path:"RoleManagement",meta:{title:"权限管理"},component:function(t){return a.e(52).then(function(){var e=[a("vxaA")];t.apply(null,e)}.bind(this)).catch(a.oe)},beforeEnter:function(t,e,a){"2"==P.state.platformVer&&a("/systemMan/RoleManagementOld"),a()}},{path:"MenuManagementOld",meta:{title:"菜单管理-粤海"},component:function(t){return Promise.all([a.e(0),a.e(4)]).then(function(){var e=[a("hSyg")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"RoleManagementOld",meta:{title:"权限管理-粤海"},component:function(t){return a.e(39).then(function(){var e=[a("FBFU")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"CompanyEmpower",meta:{title:"水司授权"},component:function(t){return a.e(48).then(function(){var e=[a("zHSS")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"SuperAdministrator",meta:{title:"超级管理员"},component:function(t){return a.e(38).then(function(){var e=[a("po6b")];t.apply(null,e)}.bind(this)).catch(a.oe)}}]}],x=[{path:"/waterSet",meta:{title:"水司配置"},component:function(t){return a.e(57).then(function(){var e=[a("K02j")];t.apply(null,e)}.bind(this)).catch(a.oe)},children:[{path:"/",redirect:"bankLink"},{path:"bankLink",meta:{title:"银行直联"},component:function(t){return Promise.all([a.e(0),a.e(35)]).then(function(){var e=[a("nuj+")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"saleSet",meta:{title:"出回盘配置"},component:function(t){return Promise.all([a.e(0),a.e(26)]).then(function(){var e=[a("sZcC")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"printTemplate",meta:{title:"打印样式设置"},component:function(t){return Promise.all([a.e(0),a.e(7)]).then(function(){var e=[a("gyly")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"processConfig",meta:{title:"审批流设置"},component:function(t){return Promise.all([a.e(0),a.e(50)]).then(function(){var e=[a("NL0r")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"userInfo",meta:{title:"用户资料字段"},component:function(t){return Promise.all([a.e(0),a.e(23)]).then(function(){var e=[a("7svc")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"AlipayConfig",meta:{title:"支付宝配置"},component:function(t){return Promise.all([a.e(0),a.e(32)]).then(function(){var e=[a("+fSB")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"syplatformBuild",meta:{title:"水银平台配置"},component:function(t){return Promise.all([a.e(0),a.e(13)]).then(function(){var e=[a("vVzJ")];t.apply(null,e)}.bind(this)).catch(a.oe)}}]}],$=[{path:"/tenant",meta:{title:"租户管理"},component:function(t){return a.e(59).then(function(){var e=[a("GGUP")];t.apply(null,e)}.bind(this)).catch(a.oe)},children:[{path:"/",redirect:"waterMan"},{path:"waterMan",meta:{title:"租户管理"},component:function(t){return Promise.all([a.e(0),a.e(2)]).then(function(){var e=[a("quG+")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"mgCompany",meta:{title:"中台租户信息"},component:function(t){return Promise.all([a.e(0),a.e(10)]).then(function(){var e=[a("X4JQ")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"upgradeScriptManage",meta:{title:"升级脚本管理"},component:function(t){return a.e(9).then(function(){var e=[a("d//u")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"DataOperationModule",meta:{title:"数据操作"},component:function(t){return Promise.all([a.e(0),a.e(5)]).then(function(){var e=[a("64c0")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"DataImport",meta:{title:"数据导入"},component:function(t){return a.e(17).then(function(){var e=[a("sV20")];t.apply(null,e)}.bind(this)).catch(a.oe)}}]}],O=[{path:"/systemMonitor",meta:{title:"基础配置"},component:function(t){return a.e(44).then(function(){var e=[a("/DMO")];t.apply(null,e)}.bind(this)).catch(a.oe)},children:[{path:"/",redirect:"InterfaceLog"},{path:"runStatus",meta:{title:"运行分析"},component:function(t){return a.e(51).then(function(){var e=[a("4bwZ")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"InterfaceLog",meta:{title:"调用日志"},component:function(t){return a.e(6).then(function(){var e=[a("Us4N")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"ContractFilling",meta:{title:"合同填报统计"},component:function(t){return a.e(58).then(function(){var e=[a("NuXb")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"ProjectInfoStatistics",meta:{title:"项目信息统计"},component:function(t){return a.e(55).then(function(){var e=[a("VGo+")];t.apply(null,e)}.bind(this)).catch(a.oe)}}]}];n.default.use(p.a);var I=new p.a({routes:[{path:"/",meta:{title:"登录"},component:function(t){return a.e(40).then(function(){var e=[a("T+/8")];t.apply(null,e)}.bind(this)).catch(a.oe)}},{path:"/home",meta:{title:"主容器"},component:function(t){return Promise.all([a.e(0),a.e(3)]).then(function(){var e=[a("26dS")];t.apply(null,e)}.bind(this)).catch(a.oe)},children:[{path:"/",redirect:"/waterSet"}].concat(l()(x),l()(u),l()(F),l()($),l()(O))}]}),j=(a("j1ja"),a("tvR6"),a("7t+N"),a("Pvr6")),_=a.n(j);a("jj+1");a("EOXk");var A=a("2Uyi"),U=a("XLwt"),D=a.n(U),B=(a("bOa/"),a("/IwO")),L=a.n(B),V=a("d64e"),R=a.n(V);n.default.config.debug=!1,n.default.config.devtools=!1,n.default.config.productionTip=!1,n.default.use(_.a),n.default.prototype.$mp=function(t){return new w.a(function(e,a){window.init=function(){e(BMap)};var n=document.createElement("script");n.type="text/javascript",n.src="http://api.map.baidu.com/api?v=2.0&ak="+t+"&callback=init",n.onerror=a,document.head.appendChild(n)})},n.default.prototype.$axios=C.a,n.default.prototype.$api=N,window.eventBus=new n.default,n.default.prototype.common=A.a,n.default.prototype.$echarts=D.a,n.default.use(L.a),L.a.initAMapApiLoader({key:"6cd5aa0e6752ea8bfdc7984179395f51",plugin:["AMap.Scale","AMap.OverView","AMap.ToolBar","AMap.MapType","AMap.Geocoder","AMap.Geolocation"],uiVersion:"1.0.11"}),window.$baseUrl="/zysManage/",n.default.config.productionTip=!1,n.default.use(E.a),n.default.use(R.a),window.eventBus=new n.default,window.vm=new n.default({el:"#app",router:I,store:P,components:{App:c},template:"<App/>"})},"bOa/":function(t,e){},"jj+1":function(t,e){},"kG+1":function(t,e){},tvR6:function(t,e){},xJD8:function(t,e,a){"use strict";(function(t){var a,n,o,i,r;a=document,n=window,o=a.documentElement,i="orientationchange"in window?"orientationchange":"resize",r=function(){o.clientWidth&&(o.style.fontSize="100px")},a.addEventListener&&(n.addEventListener(i,r,!1),a.addEventListener("DOMContentLoaded",r,!1)),function(){function e(){document.body.style.height=(document.documentElement.clientHeight||document.body.clientHeight)+"px"}e(),t(window).resize(function(){e()})}(),e.a={name:"App"}}).call(e,a("7t+N"))}},[0]);