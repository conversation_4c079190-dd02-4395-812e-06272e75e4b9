webpackJsonp([55],{"VGo+":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=a("mvHQ"),i=a.n(l),s={name:"ProjectInfoStatistics",data:function(){return{isCheckPage:!1,crumbsData:{titleList:[{title:"系统监控",path:"/systemMan"},{title:"项目查询",method:function(){window.histroy.back()}}]},tableShow:!1,maxHeight:0,tableQuery:{district:"",companyNo:"",projectType:"",projectStatus:"",businessType:"",projectName:"",projectAddress:"",page:1,pageCount:50},tableData:"",companyList:[],exportData:"",companyAreaSelectList:[],dictionaryData:{}}},mounted:function(){var t=this;this.getCompanyList(),this.getCompanyAreaSelectList(),this.getDictionarySelect(),this.getData(),this.$nextTick(function(){t.common.changeTable(t,".ProjectInfoStatistics .kl-table",[".ProjectInfoStatistics .toolbar",".ProjectInfoStatistics .block"])}),eventBus.$emit("secondMenuShow","secondMenuShow1")},methods:{getCompanyAreaSelectList:function(){var t=this;this.$api.fetch({params:{busicode:"CompanyAreaSelect",data:{}}}).then(function(e){console.log(e),t.companyAreaSelectList=e})},getDictionarySelect:function(){var t=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"CONTRACT_BUSINESS_TYPE_1,CONTRACT_BUSINESS_TYPE_2,CONTRACT_PROJECT_TYPE,NEED_SIGN_CONTRACT_FLAG,CONTRACT_PROJECT_STATUS"}}).then(function(e){e.CONTRACT_BUSINESS_TYPE=e.CONTRACT_BUSINESS_TYPE_1.concat(e.CONTRACT_BUSINESS_TYPE_2),t.$set(t,"dictionaryData",e)}).catch(function(e){t.$set(t,"dictionaryData",[])})},exportExcelList:function(){if(""!=this.exportData){var t={busicode:"PubProjectINfoListExport",data:this.exportData,token:localStorage.getItem("token"),sysType:"002"};window.open(this.common.getExportExcelIp()+"/zysManage/exportExcel.api?json="+encodeURI(i()(t)))}else this.$message.warning("请先查询数据")},getCompanyList:function(){var t=this;this.$api.fetch({params:{busicode:"CompanyList",data:{page:1,pageCount:999}}}).then(function(e){t.companyList=e.list})},getData:function(){var t=this,e={busicode:"PubProjectInfoList",data:this.tableQuery};this.$api.fetch({params:e}).then(function(e){t.tableData=e,t.exportData=JSON.parse(i()(t.tableQuery)),t.common.changeTable(t,".ProjectInfoStatistics .kl-table",[".ProjectInfoStatistics .toolbar",".ProjectInfoStatistics .block"])})},demand:function(){this.tableQuery.page=1,this.getData()},indexMethod:function(t){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(t+1)},handleSizeChange:function(t){this.tableQuery.pageCount=t,this.tableQuery.page=1,this.getData()},handleCurrentChange:function(t){this.tableQuery.page=t,this.getData()}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"ProjectInfoStatistics"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:t.crumbsData}}),t._v(" "),a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:t.exportExcelList}},[t._v("导出")])],1)],1),t._v(" "),a("div",{staticClass:"kl-table"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,size:"mini","label-width":"100px"}},[a("el-form-item",{staticClass:"width-200",attrs:{label:"所属片区:"}},[a("el-select",{attrs:{clearable:"",placeholder:"全部"},model:{value:t.tableQuery.district,callback:function(e){t.$set(t.tableQuery,"district",e)},expression:"tableQuery.district "}},t._l(t.companyAreaSelectList,function(t){return a("el-option",{key:t.code,attrs:{label:t.name,value:t.code}})}),1)],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"所属水司:"}},[a("el-select",{attrs:{clearable:"",placeholder:"全部"},model:{value:t.tableQuery.companyNo,callback:function(e){t.$set(t.tableQuery,"companyNo",e)},expression:"tableQuery.companyNo"}},t._l(t.companyList,function(t,e){return a("el-option",{key:e,attrs:{value:t.companyNo,label:t.shortName}})}),1)],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"项目类型:"}},[a("el-select",{attrs:{clearable:"",placeholder:"全部"},model:{value:t.tableQuery.projectType,callback:function(e){t.$set(t.tableQuery,"projectType",e)},expression:"tableQuery.projectType"}},t._l(t.dictionaryData.CONTRACT_PROJECT_TYPE,function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"项目状态:"}},[a("el-select",{attrs:{clearable:"",placeholder:"全部"},model:{value:t.tableQuery.projectStatus,callback:function(e){t.$set(t.tableQuery,"projectStatus",e)},expression:"tableQuery.projectStatus"}},t._l(t.dictionaryData.CONTRACT_PROJECT_STATUS,function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"业务类型:"}},[a("el-select",{attrs:{clearable:"",placeholder:"全部"},model:{value:t.tableQuery.businessType,callback:function(e){t.$set(t.tableQuery,"businessType",e)},expression:"tableQuery.businessType "}},t._l(t.dictionaryData.CONTRACT_BUSINESS_TYPE,function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"项目名称:"}},[a("el-input",{attrs:{oninput:"value=value.replace(/\\ +/g,'').replace(/[\\r\\n]/g,'')",clearable:""},model:{value:t.tableQuery.projectName,callback:function(e){t.$set(t.tableQuery,"projectName",e)},expression:"tableQuery.projectName "}})],1),t._v(" "),a("el-form-item",{staticClass:"width-200",attrs:{label:"项目地址:"}},[a("el-input",{attrs:{oninput:"value=value.replace(/\\ +/g,'').replace(/[\\r\\n]/g,'')",clearable:""},model:{value:t.tableQuery.projectAddress,callback:function(e){t.$set(t.tableQuery,"projectAddress",e)},expression:"tableQuery.projectAddress"}})],1),t._v(" "),a("el-form-item",[a("el-button",{staticClass:"compile",attrs:{type:"primary el-icon-search",size:"mini"},on:{click:t.demand}})],1)],1)],1),t._v(" "),a("div",{staticClass:"kl-table",style:{height:t.maxHeight+"px"}},[a("el-table",{attrs:{stripe:"",center:"",border:"","max-height":t.maxHeight,data:t.tableData.list}},[a("el-table-column",{attrs:{fixed:"left",type:"index",width:"80",label:"NO.",index:t.indexMethod}}),t._v(" "),a("el-table-column",{attrs:{prop:"districtName",label:"所属片区","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"companyNoName",label:"所属水司","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"projectName",label:"项目名称","min-width":"120"}}),t._v(" "),a("el-table-column",{attrs:{prop:"projectStatusName",label:"项目状态",width:"120"}}),t._v(" "),a("el-table-column",{attrs:{prop:"projectTypeName",label:"项目类型","min-width":"120"}}),t._v(" "),a("el-table-column",{attrs:{prop:"businessTypeName",label:"业务类型","min-width":"120"}}),t._v(" "),a("el-table-column",{attrs:{prop:"projectAddress",label:"项目地址 ","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"quantity",label:"项目覆盖人数",width:"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"projectIntroduction",label:"项目介绍 ","min-width":"150"}})],1),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":t.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":t.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)])])},staticRenderFns:[]};var n=a("VU/8")(s,o,!1,function(t){a("gjYT")},null,null);e.default=n.exports},gjYT:function(t,e){}});