package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CostBean;
import com.koron.zys.baseConfig.mapper.CostMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;

/**
 * 费用名称 费用单位-编辑
 * <AUTHOR>
public class CostUpdateUnit implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(CostUpdateUnit.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			CostMapper mapper = factory.getMapper(CostMapper.class);
			CostBean bean = JsonUtils.objectToPojo(req.getData(), CostBean.class);
			if (StringUtils.isNullOrEmpty(bean.getId())) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
			}
			mapper.updateCostUnit(bean);
				
		} catch (Exception e) {
			factory.close(false);
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
