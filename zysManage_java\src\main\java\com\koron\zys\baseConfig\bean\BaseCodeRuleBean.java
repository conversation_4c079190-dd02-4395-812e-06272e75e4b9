package com.koron.zys.baseConfig.bean;

import java.util.Date;

public class BaseCodeRuleBean {
	
	private String id;
	
	private String ruleCode;
	
	private Integer lastSerial;
	
	private Date updateTime;

	public String getId() {
		return id;
	}

	public String getRuleCode() {
		return ruleCode;
	}

	public Integer getLastSerial() {
		return lastSerial;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setRuleCode(String ruleCode) {
		this.ruleCode = ruleCode;
	}

	public void setLastSerial(Integer lastSerial) {
		this.lastSerial = lastSerial;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((ruleCode == null) ? 0 : ruleCode.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		BaseCodeRuleBean other = (BaseCodeRuleBean) obj;
		if (ruleCode == null) {
			if (other.ruleCode != null)
				return false;
		} else if (!ruleCode.equals(other.ruleCode))
			return false;
		return true;
	}
	
}
