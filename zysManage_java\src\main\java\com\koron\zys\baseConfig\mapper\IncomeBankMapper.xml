<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.IncomeBankMapper">

    <select id="select" resultType="com.koron.zys.baseConfig.bean.IncomeBankBean">
        select
            `id`,
            `bank_name`,
            `bank_account`,
            `status`,
            `create_name`,
            `create_account`,
            `create_time`,
            `modify_name`,
            `modify_account`,
            `modify_time`,
            `is_default`
        from base_income_bank_info
        <where>
            <if test="status != null and status != ''">
                and `status` = #{status}
            </if>
        </where>
        order by `modify_time` desc
    </select>

    <select id="selectIsDefaultOne" resultType="com.koron.zys.baseConfig.bean.IncomeBankBean">
        select
            `id`,
            `bank_name`,
            `bank_account`,
            `status`,
            `create_name`,
            `create_account`,
            `create_time`,
            `modify_name`,
            `modify_account`,
            `modify_time`,
            `is_default`
        from base_income_bank_info
        where is_default = 1
        limit 1
    </select>

    <insert id="insert">
        insert into
        base_income_bank_info
        (
            `id`,
            `bank_name`,
            `bank_account`,
            `status`,
            `create_name`,
            `create_account`,
            `create_time`,
            `modify_name`,
            `modify_account`,
            `modify_time`,
            `is_default`
        )
        values(
            #{id, jdbcType=VARCHAR},
            #{bankName, jdbcType=VARCHAR},
            #{bankAccount, jdbcType=VARCHAR},
            #{status, jdbcType=VARCHAR},
            #{createName, jdbcType=VARCHAR},
            #{createAccount, jdbcType=VARCHAR},
            #{createTime, jdbcType=VARCHAR},
            #{modifyName, jdbcType=VARCHAR},
            #{modifyAccount, jdbcType=VARCHAR},
            #{modifyTime, jdbcType=VARCHAR},
            #{isDefault, jdbcType=INTEGER}
        )
    </insert>

    <update id="update">
        update base_income_bank_info
        <set>
            <if test="bankName != null and bankName != ''">
                `bank_name` = #{bankName},
            </if>
            <if test="bankAccount != null and bankAccount != ''">
                `bank_account` = #{bankAccount},
            </if>
            <if test="status != null and status != ''">
                `status` = #{status},
            </if>
            <if test="modifyName != null and modifyName != ''">
                `modify_name` = #{modifyName, jdbcType=VARCHAR},
            </if>
            <if test="modifyAccount != null and modifyAccount != ''">
                `modify_account` = #{modifyAccount, jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null and modifyTime != ''">
                `modify_time` = #{modifyTime, jdbcType=VARCHAR},
            </if>
            <if test="isDefault != null">
                `is_default` = #{isDefault, jdbcType=INTEGER}
            </if>
        </set>
        where `id` = #{id, jdbcType=VARCHAR}
    </update>

    <delete id="delete">
        delete from
        base_income_bank_info
        where `id` in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

</mapper>