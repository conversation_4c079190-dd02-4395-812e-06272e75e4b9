package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.baseConfig.bean.BoreSelectBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.koron.ebs.mybatis.EnvSource;

import com.koron.zys.baseConfig.bean.MeterBoreBean;
import com.koron.zys.baseConfig.queryBean.MeterBoreQueryBean;
import com.koron.zys.baseConfig.vo.MeterBoreVO;

@EnvSource("_default")
public interface MeterBoreMapper {
	
	/**
	 * 查询列表
	 * 
	 * @return
	 */
	List<MeterBoreVO> selectMeterBoreList(MeterBoreQueryBean meterBoreQueryBean );
	
	/**
	 * 根据id查询
	 * @param factoryId
	 * @return
	 */
	MeterBoreBean selectMeterBoreById(@Param("id") String id);

	/**
	 * 添加
	 * 
	 * @param MeterBoreBean
	 * @return
	 */
	void insertMeterBore(MeterBoreBean meterBoreBean);
	
	/**
	 * 校验字段内容重复
	 */
	@Select("select count(*) from PUB_METER_BORE where ${key} = #{val}")
	Integer check(@Param("key") String key, @Param("val") String val);
	
	/**
	 * 校验字段内容重复-排除当前记录
	 */
	@Select("select count(*) from PUB_METER_BORE where ${key} = #{val} and id <> #{id}")
	Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);
	
	/**
	 * 修改
	 * 
	 * @return
	 */
	Integer updateMeterBore(MeterBoreBean meterBoreBean);
	/**
	 * 根据条件获取口径id
	 * @param compare
	 * @param value
	 * @return
	 */
	List<String> getBoreByValue(@Param("compare") String compare,@Param ("value") int value);
	
	/**
	 * 根据条件获取水表型号
	 * @param compare
	 * @param value
	 * @return
	 */
	List<String> getModelByValue(@Param("compare") String compare,@Param ("value") int value);
	List<String> getBoreIdByValue(@Param("compare") String compare,@Param ("value") int value);
	/**
	 * 根据水表型号获取口径名称
	 * @param model
	 * @return
	 */
	@Select("select b.bore_name from pub_meter_model a join pub_meter_bore b where a.METER_BORE = b.id and a.id = #{model}")
	String getBoreNameByModel(@Param("model")String model);
	
	/**
	 * 获取小口径id
	 * @return
	 */
	public List<String> boresSelect();

	/**
	 * 获取口径列表
	 * @return
	 */
	public List<BoreSelectBean> boreSelectList();

	@Select("SELECT MAX(ID+1) FROM PUB_METER_BORE")
	Integer getBoreId();
}
