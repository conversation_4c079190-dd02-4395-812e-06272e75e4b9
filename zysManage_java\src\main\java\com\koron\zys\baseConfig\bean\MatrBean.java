package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;
import com.koron.util.Check.Repeat;

/**
 * 材料定价实体类
 *
 * <AUTHOR>
 */
public class MatrBean extends BaseBean {

    /**
     * 材料编号
     */
	@Check(name = "材料编号", repeat = @Repeat(tableName = "BASE_MATR",columnName = "matr_no"))
    private String matrNo;
    /**
     * 材料单价
     */
    private Double matrPrice;

    public String getMatrNo() {
        return matrNo;
    }

    public void setMatrNo(String matrNo) {
        this.matrNo = matrNo;
    }

    public Double getMatrPrice() {
        return matrPrice;
    }

    public void setMatrPrice(Double matrPrice) {
        this.matrPrice = matrPrice;
    }
}
