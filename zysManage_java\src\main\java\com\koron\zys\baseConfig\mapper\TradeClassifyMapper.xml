<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.TradeClassifyMapper">

	<resultMap id="baseResultMap" type="com.koron.zys.baseConfig.bean.TradeClassifyBean">
		<id column="id" jdbcType="VARCHAR" property="id" />
		<result column="trade_name" jdbcType="VARCHAR" property="tradeName" />		
		<result column="comments" jdbcType="VARCHAR" property="comments" />
		<result column="status" jdbcType="INTEGER" property="status" />
		<result column="sort_no" jdbcType="INTEGER" property="sortNo" />
		<result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
		<result column="create_name" jdbcType="VARCHAR" property="createName" />
		<result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="update_name" jdbcType="VARCHAR" property="updateName" />
	</resultMap>

 	<select id="selectTradeClassifyList" parameterType="com.koron.zys.baseConfig.queryBean.TradeClassifyQueryBean" resultType="com.koron.zys.baseConfig.vo.TradeClassifyVO" >
		select xx.id, xx.trade_name, xx.comments, xx.sort_no,case when xx.status=1 then '启用' else '停用' end status
		from PUB_TRADE_CLASSIFY xx
		where 1=1   
	    	<if test="tradeName != null and tradeName != ''">
	    		and xx.trade_name  LIKE  '%' || #{tradeName} || '%'
	    	</if>
	    order by xx.sort_no asc  
	</select>
	 
	<select id="selectTradeClassifyById" resultType="com.koron.zys.baseConfig.bean.TradeClassifyBean">
		select *
		from PUB_TRADE_CLASSIFY
		where id = #{id}
	</select>
	<insert id="insertTradeClassify" parameterType="com.koron.zys.baseConfig.bean.TradeClassifyBean">
		insert into PUB_TRADE_CLASSIFY (id,trade_name, comments,status, sort_no, create_time, create_name)
		values
		(
		#{id},
		#{tradeName},
		#{comments},
		#{status},
		#{sortNo},		
		now(),
		#{createName}
		)
	</insert>
	<update id="updateTradeClassify" parameterType="com.koron.zys.baseConfig.bean.TradeClassifyBean">
		update PUB_TRADE_CLASSIFY
		set trade_name = #{tradeName},		
			comments = #{comments},
			status = #{status},
			sort_no = #{sortNo},		
			update_time=now(),
			update_name = #{updateName}
		    where id = #{id}
	</update>
	<select id="tradeClassifySelect" resultType="com.koron.zys.baseConfig.vo.SelectVO">
		select
		id,trade_name name
		from
		PUB_TRADE_CLASSIFY
		where
		status=1
	</select>
	
</mapper>