package com.koron.zys.baseConfig.impl;

import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.GarbageUniversalBean;
import com.koron.zys.baseConfig.mapper.GarbageMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

public class UserGarbageAdd implements ServerInterface{

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
            GarbageMapper mapper = factory.getMapper(GarbageMapper.class);
            GarbageUniversalBean bean = JsonUtils.objectToPojo(req.getData(), GarbageUniversalBean.class);
            bean.setId(new ObjectId().toHexString());
            bean.setCreateName(userInfo.getUserInfo().getName());
    		bean.setCreateTime(CommonUtils.getCurrentTime());
            mapper.insert(bean);
        } catch (Exception e) {
            logger.error("操作失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
