webpackJsonp([11],{"9NB7":function(e,t){},RuEX:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"WatermeterBoreEdit"},[a("el-form",{ref:"WatermeterBoreEditForm",staticClass:"formBill-Two",attrs:{inline:!0,size:"mini",model:e.formData,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"口径名称：",prop:"boreName"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入口径名称"},model:{value:e.formData.boreName,callback:function(t){e.$set(e.formData,"boreName",t)},expression:"formData.boreName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"口径值：",prop:"boreValue"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入口径值"},model:{value:e.formData.boreValue,callback:function(t){e.$set(e.formData,"boreValue",t)},expression:"formData.boreValue"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"最大流量：",prop:"maxFlux"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入最大流量"},model:{value:e.formData.maxFlux,callback:function(t){e.$set(e.formData,"maxFlux",t)},expression:"formData.maxFlux"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"表码位数：",prop:"meterPlaces"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入表码位数"},model:{value:e.formData.meterPlaces,callback:function(t){e.$set(e.formData,"meterPlaces",t)},expression:"formData.meterPlaces"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"旧表换表周期：",prop:"oldChangeCycle"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入旧表换表周期"},model:{value:e.formData.oldChangeCycle,callback:function(t){e.$set(e.formData,"oldChangeCycle",t)},expression:"formData.oldChangeCycle"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"新表换表周期：",prop:"newChangeCycle"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入旧表换表周期"},model:{value:e.formData.newChangeCycle,callback:function(t){e.$set(e.formData,"newChangeCycle",t)},expression:"formData.newChangeCycle"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态：",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.formData.status,callback:function(t){e.$set(e.formData,"status",t)},expression:"formData.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),a("el-option",{attrs:{label:"停用",value:0}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"排序号：",prop:"sortNo"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",placeholder:"请输入排序号"},model:{value:e.formData.sortNo,callback:function(t){e.$set(e.formData,"sortNo",t)},expression:"formData.sortNo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"备注：",prop:"comments"}},[a("el-input",{attrs:{"show-word-limit":"",maxlength:"150",clearable:"",placeholder:"请输入备注",type:"textarea"},model:{value:e.formData.comments,callback:function(t){e.$set(e.formData,"comments",t)},expression:"formData.comments"}})],1)],1)],1)},staticRenderFns:[]};var l={components:{WatermeterBoreEdit:a("VU/8")({name:"WatermeterBoreEdit",data:function(){return{formData:{id:"",boreName:"",boreValue:"",maxFlux:"",meterPlaces:"",oldChangeCycle:"",newChangeCycle:"",comments:"",status:"",sortNo:""},dictionaryData:[]}},mounted:function(){this.getData()},methods:{getData:function(){var e=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"SBLX,MMT,TPW,XXGY,MMP,JD,MSS,TPW"}}).then(function(t){e.$set(e,"dictionaryData",t)})},resetForm:function(){this.$refs.WatermeterBoreEditForm.resetFields()},submitForm:function(e){var t=this,a={};a="MeterBoreAdd"===e?{busicode:"MeterBoreAdd",data:this.formData}:{busicode:"MeterBoreUpdate",data:this.formData},this.$api.fetch({params:a}).then(function(e){t.$message({showClose:!0,message:"保存成功",type:"success"}),t.$parent.init(),t.$parent.closeDialog()})},editData:function(e){this.formData=e}}},r,!1,function(e){a("9NB7")},"data-v-e107027a",null).exports},name:"WatermeterBore",data:function(){return{EditVisible:!1,formData:{id:"",modelName:"",meterType:"",transWay:"",meterSensor:"",comments:"",status:"",sortNo:"",tenantId:""},crumbsData:{titleList:[{title:"基础配置",path:"/baseInfo"},{title:"水表口径",method:function(){window.histroy.back()}}]},maxHeight:0,tableShow:!1,tableData:{},tableQuery:{page:1,pageCount:10,boreValue:""},formType:""}},mounted:function(){this.init()},methods:{init:function(){var e=this,t=this,a={busicode:"MeterBoreList",data:this.tableQuery};this.$api.fetch({params:a}).then(function(a){t.tableData=a,e.$nextTick(function(){e.common.changeTable(e,".WatermeterBore .WatermeterBoreIndex",[".WatermeterBore .block",".WatermeterBore .toolbar"])})})},search:function(){this.tableQuery.page=1,this.init()},add:function(e){var t=this;if(this.EditVisible=!0,"add"===e)this.formType="MeterBoreAdd";else{this.formType="MeterBoreUpdate";var a={busicode:"MeterBoreQuery",data:{boreId:e.id}};this.$api.fetch({params:a}).then(function(e){t.$refs.WatermeterBoreEdit.editData(e)})}},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.init()},handleCurrentChange:function(e){this.tableQuery.page=e,this.init()},closeDialog:function(){this.EditVisible=!1},submitForm:function(){this.$refs.WatermeterBoreEdit.submitForm(this.formType)}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"WatermeterBore"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),e.EditVisible?a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submitForm()}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.closeDialog}},[e._v("返回")])],1):a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-plus",type:"primary"},on:{click:function(t){return e.add("add")}}},[e._v("添加")])],1)],1),e._v(" "),e.EditVisible?a("WatermeterBoreEdit",{ref:"WatermeterBoreEdit"}):a("div",{staticClass:"WatermeterBoreIndex"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,size:"mini",model:e.tableQuery}},[a("el-form-item",{staticClass:"form-left"},[a("el-form-item",{attrs:{label:"口径值："}},[a("el-input",{attrs:{placeholder:"请输入口径名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.search.apply(null,arguments)}},model:{value:e.tableQuery.boreValue,callback:function(t){e.$set(e.tableQuery,"boreValue",t)},expression:"tableQuery.boreValue"}})],1),e._v(" "),a("el-form-item",[a("el-button",{staticClass:"searchBtn",attrs:{icon:"el-icon-search"},on:{click:e.search}})],1)],1)],1)],1),e._v(" "),a("div",{staticClass:"kl-table"},[e.tableShow?a("el-table",{staticClass:"wuserInfo-table",attrs:{"max-height":e.maxHeight,stripe:"",border:"",data:e.tableData.list}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"boreName","min-width":"100",label:"口径名称","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"boreValue","min-width":"100",label:"口径值","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"maxFlux","min-width":"100",label:"最大流量","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"meterPlaces","min-width":"100",label:"表码位数","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"newChangeCycle","min-width":"100",label:"换表周期","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{prop:"status","min-width":"80",label:"状态"}}),e._v(" "),a("el-table-column",{attrs:{prop:"comments","min-width":"100",label:"备注","show-overflow-tooltip":""}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"70",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"mini"},nativeOn:{click:function(a){return e.add(t.row)}}},[e._v("编辑")])]}}],null,!1,669204806)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[10,50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.tableData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)])],1)},staticRenderFns:[]};var i=a("VU/8")(l,o,!1,function(e){a("xE9C")},null,null);t.default=i.exports},xE9C:function(e,t){}});