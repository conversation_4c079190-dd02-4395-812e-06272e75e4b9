<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.common.web.mapper.OrgMapper">
	<select id="seletList" resultType="com.koron.common.web.bean.OrgBean">
		SELECT 
			id,
			name_path,
			name,
			flag,
			short_name,
			sort,
			status,
			class_code,
			code,
			parent_code
		FROM 
			tblorg
		WHERE status = 0
	</select>
	
	 <!--批量新增组织机构信息-->
    <insert id="batchInsertOrg" parameterType="com.koron.common.web.bean.OrgBean">
        INSERT IGNORE INTO tblorg	(
			name_path,
			name,
			flag,
			short_name,
			sort,
			status,
			class_code,
			code,
			parent_code
		) VALUES
        <foreach collection="list" item="org" separator=",">
            (
			#{org.namePath,jdbcType=VARCHAR},
			#{org.name,jdbcType=VARCHAR},
			#{org.flag,jdbcType=INTEGER},
			#{org.shortName,jdbcType=VARCHAR},
			#{org.sort,jdbcType=INTEGER},
			#{org.status,jdbcType=INTEGER},
			#{org.classCode,jdbcType=VARCHAR},
			#{org.code,jdbcType=VARCHAR},
			#{org.parentCode,jdbcType=VARCHAR}
			)
        </foreach>
    </insert>
</mapper>