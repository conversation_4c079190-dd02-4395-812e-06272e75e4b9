package com.koron.zys.baseConfig.queryBean;

import com.koron.common.bean.query.BaseQueryBean;

import java.util.List;

public class WaterPriceQueryBean extends BaseQueryBean{
	
	private String id;

	/**
	 * 用水类型
	 */
    private String waterTypeName;
 
    private String status;
    
    private String processState;
    
    private String waterTypeId;

    private Integer quotaWater;
    
    private String code ;

    private List<String> waterTypeIdList;

	public List<String> getWaterTypeIdList() {
		return waterTypeIdList;
	}

	public void setWaterTypeIdList(List<String> waterTypeIdList) {
		this.waterTypeIdList = waterTypeIdList;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Integer getQuotaWater() {
		return quotaWater;
	}

	public void setQuotaWater(Integer quotaWater) {
		this.quotaWater = quotaWater;
	}

	public String getWaterTypeId() {
		return waterTypeId;
	}

	public void setWaterTypeId(String waterTypeId) {
		this.waterTypeId = waterTypeId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getWaterTypeName() {
		return waterTypeName;
	}

	public void setWaterTypeName(String waterTypeName) {
		this.waterTypeName = waterTypeName;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getProcessState() {
		return processState;
	}

	public void setProcessState(String processState) {
		this.processState = processState;
	}
    
}
