package com.koron.zys.baseConfig.impl;


import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CouponExpirationReminderBean;
import com.koron.zys.baseConfig.mapper.CouponExpirationReminderMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.springframework.stereotype.Service;
import org.swan.bean.MessageBean;

/**
 * 优惠到期提醒保存
 * */
@Service
public class CouponExpirationReminderSave implements ServerInterface {
    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            CouponExpirationReminderMapper couponExpirationReminderMapper = factory.getMapper(CouponExpirationReminderMapper.class);
            //获取优惠到期模板信息
            CouponExpirationReminderBean couponExpirationReminderBean= JsonUtils.objectToPojo(req.getData(), CouponExpirationReminderBean.class);
            if (null == couponExpirationReminderBean) {
                return MessageBean.create(Constant.NOT_NULL, "参数为空", void.class);
            }
            //先清空历史记录
            couponExpirationReminderMapper.deleteCouponExpirationReminder();
            // 再添加新纪录
            couponExpirationReminderBean.setCreateInfo(userInfo);
            Integer integer = couponExpirationReminderMapper.insertCouponExpirationReminder(couponExpirationReminderBean);
        } catch (Exception e) {
            factory.close(false);
            logger.error("优惠到期提醒保存失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "优惠到期提醒保存失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}
