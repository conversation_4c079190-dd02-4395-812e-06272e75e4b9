package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.WaterPriceDetailBean;
import com.koron.zys.baseConfig.bean.WaterPriceLadderBean;
import com.koron.zys.baseConfig.mapper.WaterPriceMapper;
import com.koron.zys.baseConfig.queryBean.WaterPriceQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

public class WaterPriceQueryLadder implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);

		try { 
			WaterPriceQueryBean bean = JsonUtils.objectToPojo(req.getData(), WaterPriceQueryBean.class);
			WaterPriceMapper mapper = factory.getMapper(WaterPriceMapper.class);
			//查询用水价格明细
			List<WaterPriceDetailBean> waterPricDetailList = mapper.selectWaterPriceBetailByIdForRedRush(bean.getId());
			for (WaterPriceDetailBean waterPriceDetailBean : waterPricDetailList) {
				List<WaterPriceLadderBean> ladder = mapper.selectWaterPriceLadderById(waterPriceDetailBean.getId());
				waterPriceDetailBean.setLadders(ladder);
			}
			info.setData(waterPricDetailList);
		}catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			// TODO: handle exception
		}
		return info;
	}

}
