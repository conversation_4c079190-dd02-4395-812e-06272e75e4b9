package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.serviceManage.bean.BusinessAreaBean;
import com.koron.zys.serviceManage.bean.SelectBean;
import com.koron.zys.serviceManage.queryBean.BusinessAreaQueryBean;
import com.koron.zys.serviceManage.vo.BusinessAreaVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.koron.ebs.mybatis.EnvSource;

import com.koron.zys.baseConfig.vo.SelectVO;

/**
 * 营业区域
 * <AUTHOR>
 * 2020年3月23日
 */
@EnvSource("_default")
public interface BusinessAreaMapper {

	/**
	 * 营业所下拉框
	 * @param abodeId 可选
	 * @param groupCode
	 * @return
	 */
	public List<SelectVO> select(@Param("abodeId") String abodeId, @Param("groupCode") String groupCode);

	/**
	 * 营业所列表
	 * @param groupCode
	 * @return
	 */
	public List<BusinessAreaVo> selectList(@Param("groupCode") String groupCode);

	public BusinessAreaVo selectByCode(@Param("code") String areaCode);
	public BusinessAreaVo selectById(@Param("id") String id);
	@Select("select * from pub_business_area where id =#{id}")
	BusinessAreaBean findBusinessAreaInfoById(@Param("id") String id);
	/**
	 * 根据ID查询营业区域信息
	 * @param id
	 * @return
	 */
	public String findBusinessAreaById(@Param("id") String id, @Param("groupCode") String groupCode);
	/**
	 * 根据ID查询营业区域信息
	 * @param id
	 * @return
	 */
	public String findBusinessAreaByName(@Param("areaName") String areaName, @Param("groupCode") String groupCode);

	/**
	 * 根据父ID查询营业区域信息
	 * @param parnetId
	 * @return
	 */
	public List<SelectBean> findBusinessAreaByParentId(@Param("parentId") String parentId, @Param("groupCode") String groupCode);
	/**
	 * 根据父id查询营业区域信息
	 * @param parnetId
	 * @return
	 */
	public List<String> findAllByParentId(@Param("id") String id, @Param("groupCode") String groupCode);

	/**
	 * 根据父编号查询营业区域信息
	 * @param parnetId
	 * @return
	 */
	public List<String> findAllByParentNo(@Param("areaNo") String areaNo, @Param("groupCode") String groupCode);

	String getIdByAreaName(@Param("areaName") String areaName);

	BusinessAreaBean getSingle(BusinessAreaQueryBean businessAreaQueryBean);
}
