package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CostDetailBean;
import com.koron.zys.baseConfig.mapper.CostDetailMapper;
import com.koron.zys.baseConfig.queryBean.CostDetailQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 费用明细-编辑初始化
 * <AUTHOR>
 *
 */
public class CostDetailQuery implements ServerInterface {
	
private static Logger logger = LoggerFactory.getLogger(CostDetailQuery.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);

		try { 
			CostDetailQueryBean bean = JsonUtils.objectToPojo(req.getData(), CostDetailQueryBean.class);
			CostDetailMapper mapper = factory.getMapper(CostDetailMapper.class);
			List<CostDetailBean> ladderlist = mapper.selectCostDetailById(bean.getCostDetailId());
			info.setData( ladderlist);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}
