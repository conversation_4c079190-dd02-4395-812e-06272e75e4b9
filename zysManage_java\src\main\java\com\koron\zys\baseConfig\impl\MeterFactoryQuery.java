package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterFactoryBean;
import com.koron.zys.baseConfig.mapper.MeterFactoryMapper;
import com.koron.zys.baseConfig.queryBean.MeterFactoryQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 水表供应商-编辑初始化
 *
 * <AUTHOR>
 */
public class MeterFactoryQuery implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MeterFactoryQuery.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        @SuppressWarnings("rawtypes")
        MessageBean<MeterFactoryBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", MeterFactoryBean.class);

        try {
            MeterFactoryQueryBean bean = JsonUtils.objectToPojo(req.getData(), MeterFactoryQueryBean.class);
            MeterFactoryMapper mapper = factory.getMapper(MeterFactoryMapper.class);
            if (StringUtils.isEmpty(bean.getId())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
            }
            MeterFactoryBean meterFactoryBean = mapper.selectMeterFactoryById(bean.getId());
            info.setData(meterFactoryBean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }
}
