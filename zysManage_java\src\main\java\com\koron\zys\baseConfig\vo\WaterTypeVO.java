package com.koron.zys.baseConfig.vo;

import com.koron.zys.baseConfig.bean.BaseBean;

public class WaterTypeVO extends BaseBean{

	/**
	 *编号
	 */
    private String waterTypeNo;
    /**
     * 费用名称
     */
    private String waterTypeName;
   
    private Integer sortNo;
    
    private String status;
	private String parentId;
	
	/**
	 * 是否叶子节点 1是0否
	 */
	private Integer isLeaf;


	public String getWaterTypeNo() {
		return waterTypeNo;
	}

	public void setWaterTypeNo(String waterTypeNo) {
		this.waterTypeNo = waterTypeNo;
	}

	public String getWaterTypeName() {
		return waterTypeName;
	}

	public void setWaterTypeName(String waterTypeName) {
		this.waterTypeName = waterTypeName;
	}

	public Integer getSortNo() {
		return sortNo;
	}

	public void setSortNo(Integer sortNo) {
		this.sortNo = sortNo;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public Integer getIsLeaf() {
		return isLeaf;
	}

	public void setIsLeaf(Integer isLeaf) {
		this.isLeaf = isLeaf;
	}
	
	

}
