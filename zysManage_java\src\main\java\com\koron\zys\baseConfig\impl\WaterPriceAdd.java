package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.WaterPriceBean;
import com.koron.zys.baseConfig.bean.WaterPriceDetailBean;
import com.koron.zys.baseConfig.bean.WaterPriceLadderBean;
import com.koron.zys.baseConfig.mapper.BaseReceiptAccessoryMapper;
import com.koron.zys.baseConfig.mapper.WaterPriceMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;
/**
 * 用水价格-添加
 * <AUTHOR>
 *
 */
public class WaterPriceAdd implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(WaterPriceAdd.class);

	@Override
	@ValidationKey(clazz = WaterPriceBean.class,method = "insert")
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			WaterPriceMapper mapper = factory.getMapper(WaterPriceMapper.class);
			WaterPriceBean bean = JsonUtils.objectToPojo(req.getData(), WaterPriceBean.class);
			bean.setCreateInfo(userInfo);
			bean.setUpdateInfo(userInfo);

			MessageBean<?> retMsg = WaterPriceAdd.check(userInfo, bean);
			if(retMsg.getCode() != Constant.MESSAGE_INT_SUCCESS) {
				factory.close(false);
				return retMsg;
			}
			if(bean.getExpiryDate()!= null && bean.getExpiryDate().equals("")) {
				bean.setExpiryDate(null);
			}
			if(bean.getEffectiveDate()!= null && bean.getEffectiveDate().equals("")) {
				bean.setEffectiveDate(null);
			}
			mapper.insertWaterPrice(bean);
			for (WaterPriceDetailBean detailBean : bean.getDetails()) {
				if (StringUtils.isNotBlank(detailBean.getPenaltyStrategyId()) || detailBean.getFixedMoney() > 0 || detailBean.getFixedPrice() > 0 || detailBean.getLadders().size() > 0) {
					mapper.insertWaterPriceDetail(detailBean);
					for (int i = 0; i < detailBean.getLadders().size(); i++) {
						WaterPriceLadderBean ladderBean = detailBean.getLadders().get(i);
						mapper.insertWaterPriceLadder(ladderBean);
					}
				}
			}

			MessageBean<WaterPriceBean> ret = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", WaterPriceBean.class);
			ret.setData(bean);
			BaseReceiptAccessoryMapper accessoryMapper = factory.getMapper(BaseReceiptAccessoryMapper.class);
			if(!StringUtils.isBlank(bean.getTempId()))		//修改附件ID
				accessoryMapper.updateAccessoryReceiptId(bean.getId(), bean.getTempId());
			return ret;
		} catch (Exception e) {
			factory.close(false);
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
	}

	public static MessageBean<?> check(UserInfoBean userInfo, WaterPriceBean bean) {
		List<WaterPriceDetailBean> detailList = bean.getDetails();
		if (detailList.size() == 0) {
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "价格明细不能为空", void.class);
		}
		// 判断明细及阶梯数据是否正确
		for (WaterPriceDetailBean detailBean : detailList) {
			if(detailBean.getCostId()==null) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "明细内容费用类型不能为空！", void.class);
			}
			detailBean.setCreateInfo(userInfo);
			detailBean.setUpdateInfo(userInfo);
			detailBean.setWaterPriceId(bean.getId());
			if(detailBean.getLadderType().equals("1")) {//无阶梯
				detailBean.getLadders().clear();
			}
			String water = "0";
			for(int i =0;i<detailBean.getLadders().size();i++) {
				WaterPriceLadderBean ladderBean = detailBean.getLadders().get(i);
				ladderBean.setCreateInfo(userInfo);
				ladderBean.setUpdateInfo(userInfo);
				ladderBean.setWaterPriceDetailId(detailBean.getId());
				if(i==0) {
					if(!ladderBean.getBeginWater().equals("0")) {
						return MessageBean.create(Constant.ILLEGAL_PARAMETER, "配置错误："+detailBean.getCostName()+"第1阶梯起水量必须以0开始", void.class);
					}
//					if(ladderBean.getEndWater() <=0) {
//						return MessageBean.create(Constant.ILLEGAL_PARAMETER, "配置错误："+detailBean.getCostName()+"第1阶梯结束水量必须大于0", void.class);
//					}
					water = ladderBean.getEndWater();
				}else {
					if(ladderBean.getBeginWater().equals("0")) {
						return MessageBean.create(Constant.ILLEGAL_PARAMETER, "配置错误："+detailBean.getCostName()+"第"+(i+1)+"阶梯起水量不能等于0", void.class);
					}
					if(!ladderBean.getBeginWater().equals(water)) {
						return MessageBean.create(Constant.ILLEGAL_PARAMETER, "配置错误："+detailBean.getCostName()+"第"+(i+1)+"阶梯起水量必须是上一阶梯结束水量", void.class);
					}
					if(ladderBean.getEndWater().equals("0")) {
						//如果结束水量为0，检查他是不是最后一个阶梯
						if(i != detailBean.getLadders().size()-1) {
							return MessageBean.create(Constant.ILLEGAL_PARAMETER, "配置错误："+detailBean.getCostName()+"第"+(i+1)+"阶梯结束水量必须大于起水量", void.class);
						}
//					}else  if(ladderBean.getEndWater() <=water) {
//						return MessageBean.create(Constant.ILLEGAL_PARAMETER, "配置错误："+detailBean.getCostName()+"第"+(i+1)+"阶梯结束水量必须大于起水量", void.class);
					}
					water = ladderBean.getEndWater();
				}
			}
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "校验通过", void.class);
	}
}