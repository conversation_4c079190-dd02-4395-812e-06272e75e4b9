<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.PayMentElfMapper">
    <!--添加  -->
    <insert id="insertPayMentElf" parameterType="com.koron.zys.baseConfig.bean.BasePayMentElfBean">
		insert into base_payment_elf(id,notice_way,begin_time,end_time,tenant_id,create_time, create_name,create_account)
		values
		(
		#{id},
		#{noticeWay},
		#{beginTime},
		#{endTime},
		#{tenantId},
		#{createTime},
		#{createName},
		#{createAccount}
		)
	</insert>
</mapper>