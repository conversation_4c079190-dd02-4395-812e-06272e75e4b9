package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.koron.zys.baseConfig.bean.WaterTypeBean;
import com.koron.zys.baseConfig.queryBean.WaterTypeQueryBean;
import com.koron.zys.baseConfig.vo.WaterTypeVO;
import com.koron.zys.serviceManage.bean.SelectBean;

/**
 * 用水类型
 *
 * <AUTHOR>
 */
public interface WaterTypeMapper {

    /**
     * 新增
     *
     * @param bean
     * @return
     */
    Integer saveWaterType(WaterTypeBean bean);

    /**
     * 更新
     *
     * @param bean
     * @return
     */
    Integer updateWaterType(WaterTypeBean bean);

    /**
     * 查询
     *
     * @param bean
     * @return
     */
    List<WaterTypeVO> findWaterType(WaterTypeQueryBean bean);
    List<WaterTypeVO> findWaterTypeid(WaterTypeQueryBean bean);
    /**
     * 查询没有母节点的数据
     */
    List<WaterTypeVO> findParentWaterType();

    /**
     * 根据id查WaterType数据
     *
     * @param id
     * @return
     */
    @Select("select * from BASE_WATER_TYPE where id = #{id}")
    WaterTypeBean findWaterTypeById(@Param("id") String id);
    
    /**
     * 根据id查WaterType数据
     *
     * @param id
     * @return
     */
    @Select("select * from BASE_WATER_TYPE where water_type_name = #{name}")
    WaterTypeBean findWaterTypeByName(@Param("name") String name);

    /**
     * 根据编号查WaterType数据
     *
     * @param waterTypeNo
     * @return
     */
    SelectBean findWaterTypeByCode(@Param("waterTypeNo") String waterTypeNo);
    
    /**
     * 查询一级用水类型
     *
     * @param waterTypeNo
     * @return
     */
    List<SelectBean> findTopWaterType();

    /**
     * 根据某个类型编号下最大子级编号
     *
     * @param waterTypeNo
     * @return
     */
    String findMaxChild(@Param("waterTypeNo") String waterTypeNo);

    /**
     * 查询下级列表
     *
     * @param parentId
     * @return
     */
    List<SelectBean> findNextIdByParentId(@Param("parentId") String parentId);

    /**
     * 查询最下级的code列表
     *
     * @param waterTypeId
     * @return
     */
    List<String> findChildById(@Param("waterTypeId") String waterTypeId);

    /**
     * 校验字段内容重复
     */
    @Select("select count(1) from BASE_WATER_TYPE where ${key} = #{val} and is_leaf = 1")
    Integer check(@Param("key") String key, @Param("val") String val);

    /**
     * 校验字段内容重复-排除当前记录
     */
    @Select("select count(1) from BASE_WATER_TYPE where ${key} = #{val} and id <> #{id}")
    Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);

    @Select("select a.* from BASE_WATER_TYPE a left join BASE_WATER_TYPE b " +
            "on a.parent_id=b.id " +
            "where a.status =1 and  a.water_type_no LIKE CONCAT(#{waterTypeNo},'%')")
    List<WaterTypeBean> getChild(@Param("waterTypeNo") String waterTypeNo);
    @Select("select id from BASE_WATER_TYPE where water_type_no LIKE CONCAT(#{waterTypeNo},'%')")
    List<String> getChildId(@Param("waterTypeNo") String waterTypeNo);

    /**
     * 根据父id查询
     */
    public List<String> findAllByParentId(@Param("id") String id);

    List<SelectBean> findChildByParentId(@Param("id") String id);
}
