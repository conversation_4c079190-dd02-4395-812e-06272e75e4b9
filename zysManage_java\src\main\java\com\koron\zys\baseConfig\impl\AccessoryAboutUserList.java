package com.koron.zys.baseConfig.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.util.StringUtil;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BaseAccessoryMetadataBean;
import com.koron.zys.baseConfig.bean.BaseReceiptAccessoryBean;
import com.koron.zys.baseConfig.bean.PubAccessoryTypeBean;
import com.koron.zys.baseConfig.bean.PubReceiptBean;
import com.koron.zys.baseConfig.mapper.BaseAccessoryMetadataMapper;
import com.koron.zys.baseConfig.mapper.BaseReceiptAccessoryMapper;
import com.koron.zys.baseConfig.mapper.PubAccessoryTypeMapper;
import com.koron.zys.baseConfig.mapper.PubReceiptMapper;
import com.koron.zys.baseConfig.queryBean.BaseReceiptAccessoryQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

public class AccessoryAboutUserList implements ServerInterface{
	private static Logger log = Logger.getLogger(AccessoryAboutUserList.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			BaseReceiptAccessoryQueryBean query = JsonUtils.objectToPojo(req.getData(), BaseReceiptAccessoryQueryBean.class);
			BaseReceiptAccessoryMapper mapper = factory.getMapper(BaseReceiptAccessoryMapper.class);
			BaseAccessoryMetadataMapper accessoryMetadataMapper = factory.getMapper(BaseAccessoryMetadataMapper.class);
			PubAccessoryTypeMapper accessoryTypeMapper = factory.getMapper(PubAccessoryTypeMapper.class);
			PubReceiptMapper pmapper = factory.getMapper(PubReceiptMapper.class);
			
			if (StringUtils.isAllBlank(query.getUserNo(), query.getCtmNo(),query.getMeterNo())) {
				info.setCode(Constant.MESSAGE_INT_FAIL);
				info.setDescription("未获取到用户编号或客户编号或水表编号！");
				return info;
			}
			List<BaseReceiptAccessoryBean> accessorysList = mapper.selectListByCtmNo(query);
			List<BaseReceiptAccessoryBean> resultList = new ArrayList<>();
			
			for(BaseReceiptAccessoryBean accessory:accessorysList) {
				//获取单据类型转换
				PubReceiptBean receipt = pmapper.selectPubReceipt(accessory.getReceiptType());
				if(receipt!=null && StringUtil.isNotEmpty(receipt.getReceiptName())) {
					accessory.setReceiptName(receipt.getReceiptName());
				}
				
				BaseAccessoryMetadataBean accessoryMetadata = accessoryMetadataMapper.selectById(accessory.getMetadataId());
				String fileName = accessoryMetadata.getAccessoryName();
				if (StringUtils.isBlank(fileName)) {
					fileName = accessoryMetadata.getAccessoryPath().substring(accessoryMetadata.getAccessoryPath().lastIndexOf("/")+1);
				}
				// accessoryMetadata.setAccessoryPath(ApplicationConfig.getAccessoryDownloadUrl() + "fileDownload?path=" + accessoryMetadata.getAccessoryPath() + "&fileName=" + fileName);
				PubAccessoryTypeBean accessoryType = accessoryTypeMapper.selectByAccessNo(accessory.getAccessoryNo());
				accessory.setAccessoryMetadata(accessoryMetadata);
				if (accessoryType != null) {
					accessory.setAccessoryType(accessoryType.getAccessoryName());
				}
				if(StringUtils.isNotBlank(accessory.getAccessoryType())) {
					accessory.setAccessoryName(accessory.getAccessoryType());
				}
				resultList.add(accessory);
			}
			
			info.setData(resultList);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			log.error(e.getMessage(), e);
		}
		return info;
	}

}
