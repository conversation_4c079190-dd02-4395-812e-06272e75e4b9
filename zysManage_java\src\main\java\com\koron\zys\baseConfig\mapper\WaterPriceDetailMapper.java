package com.koron.zys.baseConfig.mapper;

import org.apache.ibatis.annotations.Param;

import com.koron.zys.baseConfig.bean.WaterPriceDetailBean;

public interface WaterPriceDetailMapper {
	
	WaterPriceDetailBean selectByPriceIdAndCostId(@Param("priceId") String priceId, @Param("costId") String costId);

	WaterPriceDetailBean selectByPriceIdAndCostNo(@Param("priceId") String priceId, @Param("costNo") String costNo);

}
