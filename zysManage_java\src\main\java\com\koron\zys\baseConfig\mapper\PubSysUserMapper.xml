<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.PubSysUserMapper">

	<select id="getUserByLoginName" resultType="com.koron.zys.baseConfig.bean.PubSysUserBean">
		SELECT
			t.id,
			t.USERNAME AS userName,
			t.LOGINNAME AS userAccount,
			t.`PASSWORD` AS `password`
		FROM
			pub_user t
		WHERE t.LOGINNAME = #{loginName}
	</select>
	
	<select id="getUserByUserName" resultType="com.koron.zys.baseConfig.bean.PubSysUserBean">
		SELECT
			t.id,
			t.USERNAME AS userName,
			t.LOGINNAME AS userAccount,
			t.`PASSWORD` AS `password`
		FROM
			pub_user t
		WHERE t.USERNAME = #{userName}
	</select>

	<select id="selectPubSysUserList" parameterType="com.koron.zys.baseConfig.queryBean.PubSysUserQueryBean" resultType="com.koron.zys.baseConfig.bean.PubSysUserBean">
		SELECT
			t.id,
			t.create_account,
			t.create_name,
			t.create_time,
			t.user_name,
			t.user_account,
			t.email,
			t.mobile,
			t.org_id,
			t.`password`,
			t.salt,
			t.`status`
		FROM
			pub_user t WHERE t.status = 1
		<if test="userAccount != null and userAccount != ''">
    		and t.user_account = #{userAccount, jdbcType=VARCHAR}
    	</if>
    	<if test="userName != null and userName != ''">
    		and t.user_name = #{userName, jdbcType=VARCHAR}
    	</if>
	</select>
	
	<select id="selectPubSysUserByPost" resultType="com.koron.zys.baseConfig.bean.PubSysUserBean">
		SELECT
			t.id,
			t.USERNAME AS userName,
			t.LOGINNAME AS userAccount,
			t.`PASSWORD` AS `password`
		FROM
			pub_user t, pub_sys_user_post a 
		WHERE t.LOGINNAME = a.user_account
		AND a.user_post = #{post, jdbcType=VARCHAR}
	</select>
	
	<select id="selectPubSysUserByOrgId" resultType="com.koron.zys.baseConfig.bean.PubSysUserBean">
		SELECT
			t.id,
			t.create_account,
			t.create_name,
			t.create_time,
			t.user_name,
			t.user_account,
			t.email,
			t.mobile,
			t.org_id,
			t.`password`,
			t.salt,
			t.`status`
		FROM
			pub_sys_user t
		WHERE t.status = 1 AND t.org_id = #{orgId,jdbcType=VARCHAR}
	</select>
	
</mapper>