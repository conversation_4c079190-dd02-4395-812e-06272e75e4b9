<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.common.web.mapper.DepartmentMapper">

	<update id="updateDepartment" parameterType="com.koron.common.web.bean.DepartmentBean">
		UPDATE tbldepartment SET
								 name = #{name},
								 shortname = #{shortname},
								 description = #{description},
								 tel = #{tel}
		WHERE code = #{code}
	</update>

	<!--批量新增组织信息-->
	<insert id="batchInsertDepartment" parameterType="com.koron.common.web.bean.DepartmentBean">
		INSERT INTO tbldepartment	(
		name,
		code,
		description,
		sn,
		tel,
		state,
		flag,
		datacenterkey,
		shortname,
		parentCode,
		class_code,
		org_code,
		org_name
		) VALUES
		<foreach collection="list" item="dept" separator=",">
			(
			#{dept.name,jdbcType=VARCHAR},
			#{dept.code,jdbcType=VARCHAR},
			#{dept.description,jdbcType=VARCHAR},
			#{dept.sn,jdbcType=INTEGER},
			#{dept.tel,jdbcType=VARCHAR},
			#{dept.state,jdbcType=INTEGER},
			#{dept.flag,jdbcType=INTEGER},
			#{dept.datacenterkey,jdbcType=VARCHAR},
			#{dept.shortname,jdbcType=VARCHAR},
			#{dept.parentcode,jdbcType=VARCHAR},
			#{dept.classCode,jdbcType=VARCHAR},
			#{dept.orgCode,jdbcType=VARCHAR},
			#{dept.orgName,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--批量新增组织信息-->
	<insert id="insertDepartment" keyProperty="id" useGeneratedKeys="true" parameterType="com.koron.common.web.bean.DepartmentBean">
		INSERT INTO tbldepartment	(
			name,
			code,
			sn,
			tel,
			state,
			flag,
			shortname,
			parentCode,
			description
		) VALUES
			(
				#{name,jdbcType=VARCHAR},
				#{code,jdbcType=VARCHAR},
				#{sn,jdbcType=INTEGER},
				#{tel,jdbcType=VARCHAR},
				#{state,jdbcType=INTEGER},
				#{flag,jdbcType=INTEGER},
				#{shortname,jdbcType=VARCHAR},
				#{parentcode,jdbcType=VARCHAR},
			 	#{description,jdbcType=VARCHAR}
			)
	</insert>

</mapper>