package com.koron.zys.baseConfig.queryBean;

import com.koron.common.bean.query.BaseQueryBean;

public class ConfigVacationQueryBean  extends BaseQueryBean{
	
	private String id;

    private String vacationName;

    private String vacationBeginDate;
    
    private String vacationEndDate;

    private String vacationComments;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getVacationName() {
		return vacationName;
	}

	public void setVacationName(String vacationName) {
		this.vacationName = vacationName;
	}

	public String getVacationBeginDate() {
		return vacationBeginDate;
	}

	public void setVacationBeginDate(String vacationBeginDate) {
		this.vacationBeginDate = vacationBeginDate;
	}

	public String getVacationEndDate() {
		return vacationEndDate;
	}

	public void setVacationEndDate(String vacationEndDate) {
		this.vacationEndDate = vacationEndDate;
	}

	public String getVacationComments() {
		return vacationComments;
	}

	public void setVacationComments(String vacationComments) {
		this.vacationComments = vacationComments;
	}
}
