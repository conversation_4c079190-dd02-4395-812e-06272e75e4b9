package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;

/**
 * 用水类型实体类
 * 
 * <AUTHOR>
 *
 */
public class WaterTypeBean extends BaseBean {
	/**
	 * 编号
	 */
	private String waterTypeNo;
	/**
	 * 水表类型名称
	 */
	@Check(name = "类型名称", notEmpty = true)
	private String waterTypeName;
	/**
	 * 排序号
	 */
	@Check(name = "排序号", notNull = true)
	private Integer sortNo;
	/**
	 * 状态
	 */
	@Check(name = "状态", notNull = true)
	private Integer status;
	/**
	 * 水司编号
	 */
	private String groupCode;

	/**
	 * 父节点
	 */
	private String parentId;

	/**
	 * 是否叶子节点 1是0否
	 */
	private Integer isLeaf;

	public String getWaterTypeNo() {
		return waterTypeNo;
	}

	public void setWaterTypeNo(String waterTypeNo) {
		this.waterTypeNo = waterTypeNo;
	}

	public String getWaterTypeName() {
		return waterTypeName;
	}

	public void setWaterTypeName(String waterTypeName) {
		this.waterTypeName = waterTypeName;
	}

	public Integer getSortNo() {
		return sortNo;
	}

	public void setSortNo(Integer sortNo) {
		this.sortNo = sortNo;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public Integer getIsLeaf() {
		return isLeaf;
	}

	public void setIsLeaf(Integer isLeaf) {
		this.isLeaf = isLeaf;
	}
}
