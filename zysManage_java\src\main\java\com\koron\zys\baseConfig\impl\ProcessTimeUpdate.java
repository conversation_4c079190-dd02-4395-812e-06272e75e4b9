package com.koron.zys.baseConfig.impl;

import org.apache.log4j.Logger;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ProcessTimeBean;
import com.koron.zys.baseConfig.mapper.ProcessTimeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;

public class ProcessTimeUpdate implements ServerInterface{
	private Logger log = Logger.getLogger(ProcessTimeUpdate.class);
	@Override
	@ValidationKey(clazz = ProcessTimeBean.class,method = "update")
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			ProcessTimeBean bean = JsonUtils.objectToPojo(req.getData(),ProcessTimeBean.class);
			ProcessTimeMapper mapper = factory.getMapper(ProcessTimeMapper.class);
			bean.setUpdateName(userInfo.getUserInfo().getName());
			bean.setUpdateTime(CommonUtils.getCurrentTime());
			mapper.update(bean);
		}catch(Exception e){
			logger.error("操作失败", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
