package com.koron.zys.baseConfig.queryBean;

import com.koron.common.bean.query.BaseQueryBean;

public class AlipayConfigQueryBean extends BaseQueryBean {

	private String id;

	private String companyNo;

	private String orgName;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCompanyNo() {
		return companyNo;
	}

	public void setCompanyNo(String companyNo) {
		this.companyNo = companyNo;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
}
