<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.PubCodeRuleMapper">
	
 	<select id="selectCodeRule" resultType="com.koron.zys.baseConfig.bean.PubCodeRuleBean" >
		SELECT
			t.id,
			t.rule_code,
			t.rule_name,
			t.rule_exp,
			t.reset_cycle,
			t.serial_number_increment,
			t.start_serial_number
		FROM
			pub_code_rule t 
		WHERE
			t.rule_code = #{ruleNo, jdbcType=VARCHAR}
	</select>
	
	<select id="selectCompanySample" resultType="java.lang.String" >
		SELECT
			t.SIMPLIFY_NO
		FROM
			pub_company t 
		WHERE
			t.COMPANY_NO = #{groupCode, jdbcType=VARCHAR}
	</select>
	
</mapper>