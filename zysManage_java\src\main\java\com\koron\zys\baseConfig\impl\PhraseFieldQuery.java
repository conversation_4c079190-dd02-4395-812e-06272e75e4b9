package com.koron.zys.baseConfig.impl;

import com.google.common.collect.Maps;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.PhraseFieldBean;
import com.koron.zys.baseConfig.mapper.MsgPhraseMapper;
import com.koron.zys.baseConfig.queryBean.PhraseFieldQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 短信模板字段查询
 *
 * <AUTHOR>
 * @date 2021/11/15
 */
public class PhraseFieldQuery implements ServerInterface {
    private static Logger logger = LoggerFactory.getLogger(PhraseFieldQuery.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        @SuppressWarnings("rawtypes")
        MessageBean<Map> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", Map.class);
        try {
            PhraseFieldQueryBean bean = JsonUtils.objectToPojo(req.getData(), PhraseFieldQueryBean.class);
            MsgPhraseMapper mapper = factory.getMapper(MsgPhraseMapper.class);
            List<PhraseFieldBean> phraseFieldList = mapper.phraseFieldQuery(bean.getPhraseNo());
            phraseFieldList.stream().forEach(phraseField->phraseField.setMergeStr(phraseField.getFieldName()+":"+phraseField.getField()));
            Map<String,List<PhraseFieldBean>> phraseFieldMap = phraseFieldList.stream().collect(Collectors.groupingBy(PhraseFieldBean::getPhraseNo));
            Map<String,List<String>> resultMap = Maps.newHashMap();
            phraseFieldMap.forEach((k,v)->resultMap.put(k,v.stream().map(phraseField->phraseField.getFieldName()+":"+phraseField.getField()).collect(Collectors.toList())));
            info.setCode(Constant.MESSAGE_INT_SUCCESS);
            info.setData(resultMap);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription("查询失败");
            logger.error("短信模板字段查询失败", e);
        }
        return info;
    }
}
