webpackJsonp([2],{"1H6C":function(e,t,a){var r=function(){return this}()||Function("return this")(),n=r.regeneratorRuntime&&Object.getOwnPropertyNames(r).indexOf("regeneratorRuntime")>=0,i=n&&r.regeneratorRuntime;if(r.regeneratorRuntime=void 0,e.exports=a("HhN8"),n)r.regeneratorRuntime=i;else try{delete r.regeneratorRuntime}catch(e){r.regeneratorRuntime=void 0}},"7bC4":function(e,t){},AhnW:function(e,t){},HhN8:function(e,t){!function(t){"use strict";var a,r=Object.prototype,n=r.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag",c="object"==typeof e,u=t.regeneratorRuntime;if(u)c&&(e.exports=u);else{(u=t.regeneratorRuntime=c?e.exports:{}).wrap=w;var h="suspendedStart",d="suspendedYield",p="executing",f="completed",m={},v={};v[o]=function(){return this};var b=Object.getPrototypeOf,y=b&&b(b(O([])));y&&y!==r&&n.call(y,o)&&(v=y);var g=_.prototype=k.prototype=Object.create(v);x.prototype=g.constructor=_,_.constructor=x,_[l]=x.displayName="GeneratorFunction",u.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===x||"GeneratorFunction"===(t.displayName||t.name))},u.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,l in e||(e[l]="GeneratorFunction")),e.prototype=Object.create(g),e},u.awrap=function(e){return{__await:e}},A(S.prototype),S.prototype[s]=function(){return this},u.AsyncIterator=S,u.async=function(e,t,a,r){var n=new S(w(e,t,a,r));return u.isGeneratorFunction(t)?n:n.next().then(function(e){return e.done?e.value:n.next()})},A(g),g[l]="Generator",g[o]=function(){return this},g.toString=function(){return"[object Generator]"},u.keys=function(e){var t=[];for(var a in e)t.push(a);return t.reverse(),function a(){for(;t.length;){var r=t.pop();if(r in e)return a.value=r,a.done=!1,a}return a.done=!0,a}},u.values=O,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=a,this.done=!1,this.delegate=null,this.method="next",this.arg=a,this.tryEntries.forEach(D),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=a)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,n){return s.type="throw",s.arg=e,t.next=r,n&&(t.method="next",t.arg=a),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var l=n.call(o,"catchLoc"),c=n.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),D(a),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var n=r.arg;D(a)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:O(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=a),m}}}function w(e,t,a,r){var n=t&&t.prototype instanceof k?t:k,i=Object.create(n.prototype),o=new T(r||[]);return i._invoke=function(e,t,a){var r=h;return function(n,i){if(r===p)throw new Error("Generator is already running");if(r===f){if("throw"===n)throw i;return z()}for(a.method=n,a.arg=i;;){var o=a.delegate;if(o){var s=N(o,a);if(s){if(s===m)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(r===h)throw r=f,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r=p;var l=C(e,t,a);if("normal"===l.type){if(r=a.done?f:d,l.arg===m)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(r=f,a.method="throw",a.arg=l.arg)}}}(e,a,o),i}function C(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}function k(){}function x(){}function _(){}function A(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function S(e){var t;this._invoke=function(a,r){function i(){return new Promise(function(t,i){!function t(a,r,i,o){var s=C(e[a],e,r);if("throw"!==s.type){var l=s.arg,c=l.value;return c&&"object"==typeof c&&n.call(c,"__await")?Promise.resolve(c.__await).then(function(e){t("next",e,i,o)},function(e){t("throw",e,i,o)}):Promise.resolve(c).then(function(e){l.value=e,i(l)},o)}o(s.arg)}(a,r,t,i)})}return t=t?t.then(i,i):i()}}function N(e,t){var r=e.iterator[t.method];if(r===a){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=a,N(e,t),"throw"===t.method))return m;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return m}var n=C(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,m;var i=n.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=a),t.delegate=null,m):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,m)}function $(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach($,this),this.reset(!0)}function O(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=a,t.done=!0,t};return i.next=i}}return{next:z}}function z(){return{value:a,done:!0}}}(function(){return this}()||Function("return this")())},KmCY:function(e,t){},X6Bd:function(e,t){},Xxa5:function(e,t,a){e.exports=a("1H6C")},cGki:function(e,t){},dzb1:function(e,t){},exGp:function(e,t,a){"use strict";t.__esModule=!0;var r,n=a("//Fk"),i=(r=n)&&r.__esModule?r:{default:r};t.default=function(e){return function(){var t=e.apply(this,arguments);return new i.default(function(e,a){return function r(n,o){try{var s=t[n](o),l=s.value}catch(e){return void a(e)}if(!s.done)return i.default.resolve(l).then(function(e){r("next",e)},function(e){r("throw",e)});e(l)}("next")})}}},ks3H:function(e,t){},"quG+":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("pFYg"),n=a.n(r),i=a("mvHQ"),o=a.n(i),s=a("Xxa5"),l=a.n(s),c=a("exGp"),u=a.n(c),h=a("BO1k"),d=a.n(h),p=a("mtWM"),f=a.n(p),m={name:"auth-tree",data:function(){return{filterText:"",data:[],defaultProps:{children:"children",label:"shortname"},data2:[],defaultProps2:{children:"children",label:"name"},department:"组织人员"}},mounted:function(){this.randerTree()},methods:{filterNode:function(e,t){return!e||-1!==t.name.indexOf(e)},handleNodeClick:function(e){this.department=e.name},distinct:function(e){var t,a={},r=[];e.length;for(t=0;t<e.length;t++)a[e[t]]||(a[e[t]]=1,r.push(e[t]));return r},dealData:function(e){var t=[],a=[],r=!0,n=!1,i=void 0;try{for(var o,s=d()(e);!(r=(o=s.next()).done);r=!0){var l=o.value;a.push(l.parentId)}}catch(e){n=!0,i=e}finally{try{!r&&s.return&&s.return()}finally{if(n)throw i}}a=this.distinct(a);var c=!0,u=!1,h=void 0;try{for(var p,f=d()(a);!(c=(p=f.next()).done);c=!0){var m=p.value;for(var v in e)e[v].id===m&&(void 0===e[v].children&&(e[v].children=[]),t.push(e.splice(v,1)[0]))}}catch(e){u=!0,h=e}finally{try{!c&&f.return&&f.return()}finally{if(u)throw h}}for(var b in t)for(var y=0;y<e.length;y++)t[b].id===e[y].parentId&&(t[b].children.push(e.splice(y,1)[0]),y--);return t},randerTree:function(){},getStaff:function(e){},getCheckedNodes:function(){this.$refs.tree.getCheckedNodes(),this.$emit("treeClick",this.$refs.tree.getCheckedNodes()),this.$refs.tree.setCheckedKeys([])},getCheckedKeys:function(){},setCheckedNodes:function(){this.$refs.tree.setCheckedNodes([{id:5,label:"二级 2-1"},{id:9,label:"三级 1-1-1"}])},setCheckedKeys:function(){this.$refs.tree.setCheckedKeys([3])},resetChecked:function(){this.$refs.tree.setCheckedKeys([]),this.$parent.activeBlur&&this.$parent.activeBlur(),this.$parent.showTreeClick&&this.$parent.showTreeClick()}},watch:{filterText:function(e){this.$refs.tree2.filter(e)}}},v={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"auth-tree"},[a("div",{staticClass:"top"},[a("h2",[e._v("组织结构树")]),e._v(" "),a("el-input",{attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),e._v(" "),a("el-tree",{ref:"tree2",staticClass:"filter-tree department",attrs:{data:e.data,props:e.defaultProps,"filter-node-method":e.filterNode},on:{"node-click":e.handleNodeClick}})],1),e._v(" "),a("div",{staticClass:"bottom"},[a("h2",[e._v(e._s(e.department))]),e._v(" "),a("el-tree",{ref:"tree",attrs:{data:e.data2,"show-checkbox":"","default-expand-all":"","node-key":"id","highlight-current":"",props:e.defaultProps2}})],1),e._v(" "),a("div",{staticClass:"buttons"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.getCheckedNodes}},[e._v("添加")]),e._v(" "),a("el-button",{attrs:{size:"small",type:"info",plain:""},on:{click:e.resetChecked}},[e._v("退出")])],1)])},staticRenderFns:[]};var b={name:"water-dialog",components:{authTree:a("VU/8")(m,v,!1,function(e){a("ks3H")},"data-v-081c7b13",null).exports},props:["isEdit"],data:function(){var e,t=this;return{databaseData:[],props:{value:"id",label:"name",checkStrictly:!0,lazy:!0,lazyLoad:(e=u()(l.a.mark(function e(a,r){var n,i,o;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log(a),n=t,!(a.level=0)){e.next=9;break}return e.next=5,n.adminAreaDataOptions(0);case 5:return i=e.sent,e.abrupt("return",r(i));case 9:if(!a.data){e.next=15;break}return e.next=12,n.adminAreaDataOptions(a.data.id);case 12:return o=e.sent,console.log(o),e.abrupt("return",r(o));case 15:return e.abrupt("return",r([]));case 16:case"end":return e.stop()}},e,t)})),function(t,a){return e.apply(this,arguments)})},formData:{companyId:"",companyNo:"",simplifyNo:"",shortName:"",adminAreaDivi:"",adminArea:"",companyArea:"",databaseAccount:"",databasePassword:"",maxIdle:"",maxTotal:"",workflowApp:"",comments:"",sourceCompany:"",dbId:"",appVersion:"",status:1},ruleForm:{},rules:{companyNo:[{required:!0,message:"请输入水司编号",trigger:"blur"}],simplifyNo:[{required:!0,message:"请输入简化编号",trigger:"blur"}],companyShortName:[{required:!0,message:"请输入水司简称",trigger:"blur"}],oaName:[{required:!0,message:"请选择水司OA名称",trigger:"blur"}],oaCode:[{required:!0,message:"请选择水司OA编号",trigger:"blur"}],adminArea:[{required:!0,message:"请选择省份",trigger:"blur"}],adminAreaDivi:[{required:!0,message:"请选择结构地址管理",trigger:"blur"}],companyArea:[{required:!0,message:"请选择片区",trigger:"blur"}],databaseAccount:[{required:!0,message:"请输入数据库名称",trigger:"blur"}],databasePassword:[{message:"请输入数据库密码",trigger:"blur",required:!1}],workflowApp:[{message:"请输入模板组编号",trigger:"blur",required:!0}],maxIdle:[{message:"请输入最大空闲连接",trigger:"blur",required:!0},{type:"number",message:"最大空闲连接必须为数字值"}],maxTotal:[{message:"请输入最大连接数",trigger:"blur",required:!0},{type:"number",message:"最大连接数必须为数字值"}],sourceCompany:[{message:"请选择模板水司",trigger:"blur",required:!0}]},authTreeShow:!1,activeShow:!1,RepastMember:[],amend:!0,disa:!1,selectAmend:!0,oaNameOptions:[],oaCodeOptions:[],adminAreaOptions:[],companyAreaOptions:[],sourceCompanyOptions:[],dbIdOptions:[],appVersionOptions:[],adminAreaData:[]}},mounted:function(){this.selectTDbServer(),this.adminAreaDataOptions(0)},methods:{getArr:function(e){return function e(t){t.map(function(t){!1===t.isParent?delete t.children:e(t.children)})}(e.children),e},adminAreaDataOptions:function(e){var t=this;return u()(l.a.mark(function a(){var r,n,i;return l.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return r={busicode:"DistrictTree",data:{parentId:e}},n=[],a.prev=2,a.next=5,t.$api.fetch({params:r});case 5:i=a.sent,console.log(e),0===e?(n=i,t.adminAreaData=i.children):n=i.children,a.next=13;break;case 10:a.prev=10,a.t0=a.catch(2),n=[];case 13:return a.abrupt("return",n);case 14:case"end":return a.stop()}},a,t,[[2,10]])}))()},selectTDbServer:function(){var e=this,t=this;this.$api.fetch({params:{busicode:"DbServerSelect",data:{}}}).then(function(e){t.dbIdOptions=e});this.$api.fetch({params:{busicode:"AppVersionList",data:{}}}).then(function(e){t.appVersionOptions=e});this.$api.fetch({params:{busicode:"CompanySelect",data:{}}}).then(function(e){t.sourceCompanyOptions=e}),this.$api.fetch({params:{busicode:"CompanyAreaSelect",data:{}}}).then(function(a){t.companyAreaOptions=e.handledata(a)}),this.$api.fetch({params:{busicode:"ProvinceSelect",data:{}}}).then(function(a){t.adminAreaOptions=e.handledata(a)})},handledata:function(e){var t=[];return e.forEach(function(e){null!==e&&t.push(e)}),t},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","waterManAdd",this.activeBlur),this.authTreeShow=!1,this.$refs.authTree.department="组织人员",this.$refs.authTree.data2=[]},activeBlur:function(){this.$parent.closeDialog(),this.activeShow=!1,this.authTreeShow=!1},treeToggle:function(){this.activeShow=!0,this.authTreeShow=!0},delMember:function(e,t){this.RepastMember.splice(t,1)},getAddData:function(e){var t=this.RepastMember,a=e,r="",n="";for(var i in a){var s={},l=0;for(var c in s=o()(a[i]),s=JSON.parse(s),t)a[i].id===t[c].id&&(r+=s.name+",",l++);var u=[];for(var h in s)u.push(s[h]);0===l&&(t.push(s),n+=s.name+",")}""===n&&""===r?this.$message({showClose:!0,message:"请选择添加人员"}):""===n&&""!==r?this.$message({showClose:!0,message:r+" 重复添加！",type:"error"}):""!==n&&""===r?(this.$message({showClose:!0,message:n+" 已经添加！",type:"success"}),this.RepastMember=t):this.$message({showClose:!0,message:n+" 已经添加！，"+r+" 重复添加！",type:"warning"}),this.$refs.member.validate()},editData:function(e){this.ruleForm=e},submitForm:function(e,t){var a=this,r=this,i={};this.$refs[e].validate(function(e){if(!e)return!1;a.ruleForm=a.common.handleData(a.ruleForm,a.formData),"object"===n()(r.ruleForm.adminAreaDivi)&&(r.ruleForm.adminAreaDivi=r.ruleForm.adminAreaDivi.pop()),i="添加"===t?{busicode:"CompanyAdd",data:a.ruleForm}:{busicode:"CompanyUpdate",data:a.ruleForm},a.$api.fetch({params:i}).then(function(e){r.$message({showClose:!0,message:"保存成功",type:"success"}),r.$parent.selectTSubSystem(),r.$parent.closeDialog()})})}},watch:{RepastMember:function(e,t){for(var a in this.ruleForm.managerStaff=[],this.RepastMember)this.ruleForm.managerStaff.push(this.RepastMember[a].loginid)}}},y={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"water-dialog"},[a("el-form",{ref:"waterRuleForm",staticClass:"demo-form",attrs:{model:e.ruleForm,rules:e.rules,"label-width":"120px",inline:!0}},[a("el-form-item",{attrs:{label:"水司编号：",prop:"companyNo"}},[a("el-input",{attrs:{maxlength:"50",clearable:"",disabled:e.isEdit},model:{value:e.ruleForm.companyNo,callback:function(t){e.$set(e.ruleForm,"companyNo",t)},expression:"ruleForm.companyNo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"简化编号：",prop:"simplifyNo"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.simplifyNo,callback:function(t){e.$set(e.ruleForm,"simplifyNo",t)},expression:"ruleForm.simplifyNo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"水司简称：",prop:"shortName"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.shortName,callback:function(t){e.$set(e.ruleForm,"shortName",t)},expression:"ruleForm.shortName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"模板水司：",prop:"sourceCompany"}},[a("el-select",{attrs:{disabled:e.isEdit},model:{value:e.ruleForm.sourceCompany,callback:function(t){e.$set(e.ruleForm,"sourceCompany",t)},expression:"ruleForm.sourceCompany"}},e._l(e.sourceCompanyOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"省份：",prop:"adminArea"}},[a("el-select",{model:{value:e.ruleForm.adminArea,callback:function(t){e.$set(e.ruleForm,"adminArea",t)},expression:"ruleForm.adminArea"}},e._l(e.adminAreaOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.code}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"片区：",prop:"companyArea"}},[a("el-select",{model:{value:e.ruleForm.companyArea,callback:function(t){e.$set(e.ruleForm,"companyArea",t)},expression:"ruleForm.companyArea"}},e._l(e.companyAreaOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.code}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用程序版本：",prop:"appVersion"}},[a("el-select",{model:{value:e.ruleForm.appVersion,callback:function(t){e.$set(e.ruleForm,"appVersion",t)},expression:"ruleForm.appVersion"}},e._l(e.appVersionOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.versionName,value:e.versionId}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"数据库服务器：",prop:"dbId"}},[a("el-select",{model:{value:e.ruleForm.dbId,callback:function(t){e.$set(e.ruleForm,"dbId",t)},expression:"ruleForm.dbId"}},e._l(e.dbIdOptions,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"数据库名称：",prop:"databaseAccount"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.databaseAccount,callback:function(t){e.$set(e.ruleForm,"databaseAccount",t)},expression:"ruleForm.databaseAccount"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"最大空闲连接：",prop:"maxIdle"}},[a("el-input",{attrs:{maxlength:"2",clearable:""},model:{value:e.ruleForm.maxIdle,callback:function(t){e.$set(e.ruleForm,"maxIdle",e._n(t))},expression:"ruleForm.maxIdle"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"最大连接数:",prop:"maxTotal"}},[a("el-input",{attrs:{maxlength:"4",clearable:""},model:{value:e.ruleForm.maxTotal,callback:function(t){e.$set(e.ruleForm,"maxTotal",e._n(t))},expression:"ruleForm.maxTotal"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"模板组编号:",prop:"workflowApp"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:e.ruleForm.workflowApp,callback:function(t){e.$set(e.ruleForm,"workflowApp",t)},expression:"ruleForm.workflowApp"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态：",prop:"status"}},[a("el-select",{attrs:{placeholder:"选择状态"},model:{value:e.ruleForm.status,callback:function(t){e.$set(e.ruleForm,"status",t)},expression:"ruleForm.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),e._v(" "),a("el-option",{attrs:{label:"停用",value:2}})],1)],1),e._v(" "),a("el-form-item",{staticClass:"remark",attrs:{label:"备注:"}},[a("el-input",{attrs:{type:"textarea",maxlength:"150",clearable:"","show-word-limit":""},model:{value:e.ruleForm.comments,callback:function(t){e.$set(e.ruleForm,"comments",t)},expression:"ruleForm.comments"}})],1)],1),e._v(" "),a("div",{class:{"select-tree":!0,"hide-tree":!e.authTreeShow,"show-tree":e.authTreeShow}},[a("authTree",{ref:"authTree",on:{treeClick:e.getAddData}})],1)],1)},staticRenderFns:[]};var g=a("VU/8")(b,y,!1,function(e){a("7bC4")},null,null).exports,w=a("//Fk"),C=a.n(w),k={name:"water-authorize",props:["appDialog","waterCom"],data:function(){return{waterCompony:"",appDialogVisible:!1,url:"/zysManage/",loading:!1,getedAuth:!0,clickedData:{},data1:[],transfTitles:["组织","已授权"],value4:[],staffActive:!0,inputActive:!0,outsidersData:[],filterText:"",data0:[],defaultProps:{children:"children",label:"name"},activeName2:"first"}},mounted:function(){this.randerTree()},methods:{reload:function(){var e=this;this.inputActive=!1,this.$nextTick(function(){return e.inputActive=!0})},saveCanteenAuth:function(e){var t=this,a=this,r={busicode:"TAuthorizationAdd",data:e};this.$api.fetch({params:r}).then(function(e){a.$message({showClose:!0,message:"添加授权成功！",type:"success"}),a.handleNodeClick(a.clickedData),a.staffActive=!0,t.loading=!1}).catch(function(e){})},delCanteenAuth:function(e){var t=this,a=this,r={busicode:"TAuthorizationDelete",data:e};this.$api.fetch({params:r}).then(function(e){a.$message({showClose:!0,message:"删除授权成功！",type:"success"}),a.handleNodeClick(a.clickedData),a.staffActive=!0,t.loading=!1})},handleChange:function(e,t,a){this.getedAuth=!1,this.loading=!1;var r={companyId:this.waterCom.companyId};if(this.staffActive=!1,"right"===t){for(var n in r.tAuthorizationBeans=[],a)for(var i in r.tAuthorizationBeans[n]={},this.data1)a[n]===this.data1[i].key&&(1===this.data1[i].type?(r.tAuthorizationBeans[n].department=this.data1[i].parentDepart,r.tAuthorizationBeans[n].userName=this.data1[i].info.name,r.tAuthorizationBeans[n].authorizationValue=this.data1[i].info.code,r.tAuthorizationBeans[n].authorizationType=this.data1[i].type):2===this.data1[i].type&&(r.tAuthorizationBeans[n].department=this.data1[i].parentDepart,r.tAuthorizationBeans[n].authorizationType=this.data1[i].type,r.tAuthorizationBeans[n].authorizationValue=this.data1[i].info.code,r.tAuthorizationBeans[n].userName=this.data1[i].info.name));this.saveCanteenAuth(r)}else if("left"===t){for(var o in r.authorizationValue=[],a)for(var s in this.data1)a[o]===this.data1[s].key&&(r.authorizationValue[o]=this.data1[s].key);this.delCanteenAuth(r)}},filterNode:function(e,t){return!e||-1!==t.name.indexOf(e)},handleNodeClick:function(e){var t=this;this.getedAuth=!1;var a=this;this.data1=[],this.value4=[],this.clickedData=e,this.transfTitles=[e.name,e.name+"已授权"],this.getStaff({code:e.code}).then(function(r){var n=JSON.parse(r.data.depOfDepartment),i=JSON.parse(r.data.staffOfDepartment);a.data1=[];for(var o=0;o<n.length;o++)a.data1.push({key:n[o].id,label:n[o].name,info:n[o],type:2,parentDepart:e.code});for(var s=0;s<i.length;s++)a.data1.push({key:i[s].id,label:i[s].name,info:i[s],type:1,parentId:e.id,parentDepart:e.code});a.getAutorized({department:e.id,companyCode:t.waterCom.companyId})})},handleSizeChange:function(e){this.outsidersQuery.pageSize=e},handleCurrentChange:function(e){this.outsidersQuery.currentPage=e},distinct:function(e){var t,a={},r=[];e.length;for(t=0;t<e.length;t++)a[e[t]]||(a[e[t]]=1,r.push(e[t]));return r},dealData:function(e){var t=[],a=[],r=!0,n=!1,i=void 0;try{for(var o,s=d()(e);!(r=(o=s.next()).done);r=!0){var l=o.value;a.push(l.parentId)}}catch(e){n=!0,i=e}finally{try{!r&&s.return&&s.return()}finally{if(n)throw i}}a=this.distinct(a);var c=!0,u=!1,h=void 0;try{for(var p,f=d()(a);!(c=(p=f.next()).done);c=!0){var m=p.value;for(var v in e)e[v].id===m&&(void 0===e[v].children&&(e[v].children=[]),t.push(e.splice(v,1)[0]))}}catch(e){u=!0,h=e}finally{try{!c&&f.return&&f.return()}finally{if(u)throw h}}for(var b in t)for(var y=0;y<e.length;y++)t[b].id===e[y].parentId&&(t[b].children.push(e.splice(y,1)[0]),y--);return t},randerTree:function(){var e=this;this.$axios({url:"/dep/json.htm",method:"post",data:{type:"all"}}).then(function(t){e.data0=t.data,e.data0[0].name="粤海水资源"})},getStaff:function(e){var t=this,a=e.code,r=e.name;return new C.a(function(e){t.$axios({url:"/dep/staff.htm",method:"post",data:{departmentCode:a,name:r},headers:{returntype:"ajax/json"}}).then(function(t){e(t.data)})})},getAutorized:function(e){var t=this;e={busicode:"TAuthorizationList",data:{department:1}},this.$api.fetch({params:e}).then(function(e){var a=e;if(t.value4=[],null!==a.length)for(var r=0;r<a.length;r++)t.data1.push({key:a[r].authorizationValue,label:a[r].userName,info:a[r]}),t.value4.push(a[r].authorizationValue);t.getedAuth=!0,t.loading=!1})},dataInit:function(e){var t=this;this.$axios({url:"/dep/json.htm",method:"post",data:{type:"all"}}).then(function(a){var r=a.data;t.data0=a.data,t.data0[0].name="粤海水资源";var n=t;t.getStaff({code:r[0].code}).then(function(t){var a=JSON.parse(t.data.depOfDepartment),i=JSON.parse(t.data.staffOfDepartment);n.data1=[];for(var o=0;o<a.length;o++)n.data1.push({key:a[o].id,label:a[o].name,info:a[o],type:2,parentDepart:r[0].code});for(var s=0;s<i.length;s++)n.data1.push({key:i[s].id,label:i[s].name,info:i[s],type:1,parentId:r[0].id,parentDepart:r[0].code});n.getAutorized({department:r[0].id,companyCode:e})})})}},watch:{appDialog:function(){this.appDialogVisible=this.appDialog},filterText:function(e){this.getStaff({name:e})},waterCom:function(e,t){this.dataInit(e.companyId),this.waterCompony=this.waterCom.shortName}}},x={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"auth-dialog"},[a("div",{staticClass:"water-authorize"},[a("h2",{staticClass:"tab-title"},[e._v("选择授权水司："+e._s(e.waterCompony)+"\n      ")]),e._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"auth-box clearfix"},[a("div",{staticClass:"left-side"},[a("el-input",{attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),e._v(" "),a("el-tree",{ref:"tree2",staticClass:"filter-tree",attrs:{data:e.data0,props:e.defaultProps,"filter-node-method":e.filterNode},on:{"node-click":e.handleNodeClick}})],1),e._v(" "),a("div",{staticClass:"right-side"},[a("div",[e.getedAuth?a("el-transfer",{staticStyle:{"text-align":"left",display:"inline-block"},attrs:{filterable:"","left-default-checked":[],"right-default-checked":[],titles:e.transfTitles,"button-texts":["删除","授权"],format:{noChecked:"${total}",hasChecked:"${checked}/${total}"},data:e.data1},on:{change:e.handleChange},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.option;return a("span",{},[e._v(e._s(r.label))])}}],null,!1,2285797998),model:{value:e.value4,callback:function(t){e.value4=t},expression:"value4"}}):e._e()],1)])])])])},staticRenderFns:[]};var _=a("VU/8")(k,x,!1,function(e){a("X6Bd")},null,null).exports,A={name:"staffView",components:{},data:function(){return{tableShow:!0,tableQuery:{page:1,pageCount:50,companyNo:"",fuzzyQuery:""},appServerData:[],maxHeight:0}},methods:{formatStatus:function(e){return 1==e.status?"启用":"禁用"},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(e){this.tableQuery.page=e,this.selectTSubSystem()},selectTSubSystem:function(){var e=this,t=this,a={busicode:"OrgUserList",data:this.tableQuery};this.$api.fetch({params:a}).then(function(a){t.appServerData=a,t.common.changeTable(t,".water-manage .kl-table",[".water-manage .toolbar",".water-manage .block"]),console.log(e.maxHeight)})},handleClose:function(){this.staffViewShow=!1},hanldeClick:function(e){var t=this;this.$confirm("是否确认将该账号设置为管理员?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var a={busicode:"SetupAdmin",data:e.account};t.$api.fetch({params:a}).then(function(e){t.$notify({title:"成功",message:"设置成功",type:"success"})})}).catch(function(){t.$message({type:"info",message:"已取消"})})}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},S={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"water-manage"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"toolbar"},[a("el-input",{staticStyle:{width:"300px"},attrs:{size:"mini",placeholder:"搜索姓名或账号",clearable:""},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.selectTSubSystem.apply(null,arguments)}},model:{value:e.tableQuery.fuzzyQuery,callback:function(t){e.$set(e.tableQuery,"fuzzyQuery",t)},expression:"tableQuery.fuzzyQuery"}}),e._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.selectTSubSystem}},[e._v("搜索")])],1),e._v(" "),a("div",{staticClass:"kl-table"},[e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.appServerData.list,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"userName",label:"姓名","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"account",label:"帐号","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"departmentName","min-width":"100",label:"部门"}}),e._v(" "),a("el-table-column",{attrs:{prop:"status","min-width":"80",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n            "+e._s(t.row.status?"未启用":"启用")+"\n          ")]}}],null,!1,*********)}),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.hanldeClick(t.row)}}},[e._v("设置为管理员")])]}}],null,!1,*********)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.appServerData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)])])},staticRenderFns:[]};var N=a("VU/8")(A,S,!1,function(e){a("cGki")},null,null).exports,$=a("Dd8w"),D=a.n($),T={name:"waterOperation",components:{},data:function(){return{waterOperationForm:{},treeArr:{},treeData:[],defaultProps:{children:"children",label:"opName"},firstOpCode:"",checkTreeData:[],allBtnList:[],rules:{},companyNo:null}},methods:{getTreeData:function(e){var t=this,a=this;this.companyNo=e;var r={busicode:"OrgOperationList",data:e};this.$api.fetch({params:r}).then(function(e){var r=e;a.treeData=r,a.firstOpCode=a.treeData[0].opCode,a.checkTreeData=[],t.initChecked(r)})},initChecked:function(e){var t=this;e.forEach(function(e){e.checked&&t.checkTreeData.push(e.opCode),""!==e.btnList&&e.btnList.forEach(function(e){e.checked&&t.allBtnList.push(e.opCode)}),e.children&&e.children.length>0&&t.initChecked(e.children)})},treeNodeClick:function(e,t){var a=this,r=t.checkedNodes,n=(t.checkedKeys,this.$refs.tree.getNode(e.opCode)),i=this.$refs.tree.getCheckedKeys();if(n.checked)for(var o=n.level;o>1;o--)n.parent.checked||(n=n.parent,i.push(n.data.opCode));this.$refs.tree.setCheckedKeys(i),this.treeData.forEach(function(t){a.checkNodeClick(t,e,r.some(function(t){return t.opCode===e.opCode}))})},handleNodeClick:function(e){this.treeArr=e;var t=D()({},e);""==t.children&&(t.children=[]),""==t.btnList&&(t.btnList=[]),t.children=t.children.concat(t.btnList),this.$emit("setTableData",t.children||[])},checkNodeClick:function(e,t,a){var r=this;e.children&&0!==e.children.length&&e.children.forEach(function(n){n.opCode!==t.opCode&&e.opCode!==t.opCode?r.checkNodeClick(n,t,a):r.toggleChildrenClick(n,a)})},toggleChildrenClick:function(e,t){var a=this;e.btnList&&e.btnList.length>0&&this.toggleBtnClick(e,t),this.$refs.tree.setChecked(e.opCode,t),e.children&&0!==e.children.length&&e.children.forEach(function(e){a.$refs.tree.setChecked(e.opCode,t),a.toggleChildrenClick(e,t)})},toggleBtnClick:function(e,t){var a=this;t?e.btnList.forEach(function(e){a.allBtnList.push(e.opCode)}):this.allBtnList=this.allBtnList.filter(function(t){return e.btnList.every(function(e){return e.opCode!==t})})},handleSave:function(){var e=this,t=[];this.$refs.tree.getCheckedNodes().forEach(function(e){t.push(e.opCode)});var a={busicode:"AddOrgOperation",data:{orgCode:this.companyNo,operationCode:t.concat(this.allBtnList)}};this.$api.fetch({params:a}).then(function(t){e.$message({message:"保存成功！",type:"success"}),e.$parent.closeDialog()})}}},O={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"waterOperation"},[a("div",{staticClass:"backfillSubmitForm"},[a("el-form",{ref:"ruleForm",staticClass:" ",attrs:{inline:!0,size:"mini",model:e.waterOperationForm,"label-width":"150px",rules:e.rules}},[a("el-tree",{ref:"tree",staticClass:"filter-tree department",attrs:{"check-strictly":!0,data:e.treeData,props:e.defaultProps,"node-key":"opCode","default-checked-keys":e.checkTreeData,"default-expanded-keys":[e.firstOpCode],accordion:"","show-checkbox":""},on:{check:e.treeNodeClick},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.node,n=t.data;return a("span",{staticClass:"custom-tree-node"},[a("span",{staticStyle:{display:"inline-block"}},[e._v(e._s(r.label))]),e._v(" "),a("span",{staticStyle:{display:"inline-block","margin-left":"30px"}},[a("el-checkbox-group",{model:{value:e.allBtnList,callback:function(t){e.allBtnList=t},expression:"allBtnList"}},e._l(n.btnList,function(t){return a("el-checkbox",{key:t.opCode,attrs:{label:t.opCode}},[e._v(e._s(t.opName))])}),1)],1)])}}])})],1)],1)])},staticRenderFns:[]};var z={name:"auth-tree",data:function(){return{checkAllShow:!0,filterText:"",data:[],defaultProps:{children:"children",label:"name"},data2:[],defaultProps2:{children:"children",label:"name"},department:"组织人员",isIndeterminate:!0,checkAll:!1,userData:[],userId:"",userList:[]}},mounted:function(){this.randerTree()},methods:{filterNode:function(e,t){return!e||-1!==t.name.indexOf(e)},handleCheckAllChange:function(e){var t=this;t.isIndeterminate=!1,e?this.userData.forEach(function(e,a){a>=0&&t.userList.push(e.loginid)}):this.userList=[]},handleNodeClick:function(e){this.department=e.name;this.getStaff({code:e.code}).then(function(e){})},distinct:function(e){var t,a={},r=[];e.length;for(t=0;t<e.length;t++)a[e[t]]||(a[e[t]]=1,r.push(e[t]));return r},dealData:function(e){var t=[],a=[],r=!0,n=!1,i=void 0;try{for(var o,s=d()(e);!(r=(o=s.next()).done);r=!0){var l=o.value;a.push(l.parentcode)}}catch(e){n=!0,i=e}finally{try{!r&&s.return&&s.return()}finally{if(n)throw i}}a=this.distinct(a);var c=!0,u=!1,h=void 0;try{for(var p,f=d()(a);!(c=(p=f.next()).done);c=!0){var m=p.value;for(var v in e)e[v].code===m&&(void 0===e[v].children&&(e[v].children=[]),t.push(e.splice(v,1)[0]))}}catch(e){u=!0,h=e}finally{try{!c&&f.return&&f.return()}finally{if(u)throw h}}for(var b in t)for(var y=0;y<e.length;y++)t[b].code===e[y].parentcode&&(t[b].children.push(e.splice(y,1)[0]),y--);return t},randerTree:function(){var e=this;sessionStorage.getItem("token");console.log(window.location.href,Object({NODE_ENV:"production",baseUrl:"/zysManage/"})),f()({method:"post",url:"dep/json.htm",data:{type:"all"},headers:{returntype:"ajax/json"}}).then(function(t){e.data=t.data})},getStaff:function(e){var t=e.code,a=e.name,r=this;sessionStorage.getItem("token");return new C.a(function(e){f()({method:"post",url:"/dep/staff.htm",data:{departmentCode:t,name:a},headers:{returntype:"ajax/json"}}).then(function(t){e(t.data),r.userData=t.data,r.checkAllShow=!(r.userData.length>0)})})},getCheckedNodes:function(){var e=this;if(0===this.userList.length)return this.$message({message:"请先选择至少一名职员",type:"warning"}),!1;var t={busicode:"PubOrgUserAdd",data:{companyNo:this.$parent.choosedCompony.companyNo,account:this.userList}};this.$api.fetch({params:t}).then(function(t){e.$notify({title:"成功",message:"添加成功，已过滤调重复账号",type:"success"}),e.off(),e.$parent.$refs.staffView.selectTSubSystem()})},getCheckedKeys:function(){console.log(this.$refs.tree.getCheckedKeys())},setCheckedNodes:function(){this.$refs.tree.setCheckedNodes([{id:5,label:"二级 2-1"},{id:9,label:"三级 1-1-1"}])},setCheckedKeys:function(){this.$refs.tree.setCheckedKeys([3])},resetChecked:function(){this.$refs.tree.setCheckedKeys([]),this.$parent.activeBlur&&this.$parent.activeBlur(),this.$parent.showTreeClick&&this.$parent.showTreeClick(),this.data2=[],this.department="组织人员",this.randerTree()},off:function(){this.userId="",this.userList=[],this.userData=[],this.data2=[],this.department="组织人员",this.randerTree(),this.$parent.authTreeShow=!1}},watch:{filterText:function(e){this.getStaff({name:e})}}},F={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"auth-tree"},[a("div",{staticClass:"top"},[a("h2",[e._v("组织结构树")]),e._v(" "),a("el-input",{attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),e._v(" "),a("el-tree",{ref:"tree2",staticClass:"filter-tree department",attrs:{data:e.data,props:e.defaultProps,"filter-node-method":e.filterNode},on:{"node-click":e.handleNodeClick}})],1),e._v(" "),a("div",{staticClass:"bottom"},[a("h2",[e._v(e._s(e.department))]),e._v(" "),a("el-checkbox",{attrs:{disabled:e.checkAllShow,indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选")]),e._v(" "),a("el-checkbox-group",{model:{value:e.userList,callback:function(t){e.userList=t},expression:"userList"}},e._l(e.userData,function(t){return a("el-checkbox",{key:t.loginid,attrs:{label:t.loginid}},[e._v(e._s(t.name))])}),1)],1),e._v(" "),a("div",{staticClass:"buttons"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.getCheckedNodes}},[e._v("添加")]),e._v(" "),a("el-button",{attrs:{size:"small",type:"info",plain:""},on:{click:e.off}},[e._v("退出")])],1)])},staticRenderFns:[]};var L={name:"UserAdd",components:{},data:function(){return{account:"",userName:"",roleCodeCheck:[],postNoCheck:[],status:"0",postData:{},roleData:[]}},mounted:function(){this.init(),this.roleInit()},methods:{init:function(){var e=this;this.$api.fetch({params:{busicode:"PostList",data:{page:1,pageCount:9999,status:1}}}).then(function(t){e.postData=t})},roleInit:function(){var e=this;this.$api.fetch({params:{busicode:"PubRoleList",data:{}}}).then(function(t){e.roleData=t})},editData:function(e){this.account="",this.userName="",this.roleCodeCheck=[],this.postNoCheck=[],this.status="0"},submitUpload:function(){this.$refs.upload.submit()},handleRemove:function(e,t){console.log(e,t)},handlePreview:function(e){console.log(e)}}},V={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"UserAdd common-bdiv"},[a("div",{staticClass:"backfillSubmitForm"},[a("el-form",{attrs:{inline:!0,size:"mini","label-width":"150px"}},[a("el-form-item",{staticStyle:{width:"30%"},attrs:{label:"用户账号：",prop:"account"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.account,callback:function(t){e.account=t},expression:"account"}})],1),e._v(" "),a("el-form-item",{staticStyle:{width:"30%"},attrs:{label:"用户名称：",prop:"userName"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.userName,callback:function(t){e.userName=t},expression:"userName"}})],1),e._v(" "),a("el-form-item",{staticStyle:{width:"30%"},attrs:{label:"岗位：",prop:"postNo"}},[a("el-select",{attrs:{multiple:"",placeholder:"请选择岗位"},model:{value:e.postNoCheck,callback:function(t){e.postNoCheck=t},expression:"postNoCheck"}},e._l(e.postData.list,function(e){return a("el-option",{key:e.postNo,attrs:{label:e.postName,value:e.postNo}})}),1)],1),e._v(" "),a("el-form-item",{staticStyle:{width:"30%"},attrs:{label:"状态：",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态"},model:{value:e.status,callback:function(t){e.status=t},expression:"status"}},[a("el-option",{key:"0",attrs:{label:"启用",value:"0"}}),e._v(" "),a("el-option",{key:"1",attrs:{label:"停用",value:"1"}})],1)],1),e._v(" "),a("legend",{staticClass:"common-legend"},[e._v("所属角色")]),e._v(" "),a("div",{staticClass:"fixCheckbox"},[a("el-checkbox-group",{model:{value:e.roleCodeCheck,callback:function(t){e.roleCodeCheck=t},expression:"roleCodeCheck"}},e._l(e.roleData,function(t){return a("el-checkbox",{key:t.roleCode,attrs:{name:"roleCode",label:t.roleCode}},[e._v(e._s(t.roleName))])}),1)],1)],1)],1)])},staticRenderFns:[]};var E={name:"water-manage",components:{waterManAdd:g,waterAuthorize:_,staffView:N,waterOperation:a("VU/8")(T,O,!1,function(e){a("KmCY")},null,null).exports,staffAuthTree:a("VU/8")(z,F,!1,function(e){a("xB7I")},"data-v-47e98684",null).exports,userAdd:a("VU/8")(L,V,!1,function(e){a("AhnW")},null,null).exports},data:function(){return{tableShow:!0,staffViewShow:!1,companyNo:"",waterOperationShow:!1,authTreeShow:!1,userAddVisible:!1,tableQuery:{page:1,pageCount:50},maxHeight:0,choosedCompony:"",appServerData:[],formData:{companyNo:"",simplifyNo:"",shortName:"",adminArea:"",adminAreaDivi:"",companyArea:"",databaseAccount:"",databasePassword:"",maxIdle:"",maxTotal:"",workflowApp:"",comments:"",sourceCompany:"",dbId:"",appVersion:"",status:1},crumbsData:{titleList:[{title:"租户管理",path:"/ChangeTables"},{title:"租户信息",method:function(){window.histroy.back()}}]},waterManShow:!0,waterManAddVisible:!1,waterAuthorize:!1,currentType:!1,userLoading:!1,deptLoading:!1}},mounted:function(){eventBus.$emit("secondMenuShow","secondMenuShow3"),this.selectTSubSystem()},methods:{formatStatus:function(e){return 1==e.status?"启用":"禁用"},appAdd:function(){this.waterManAddVisible=!0,this.waterManShow=!1,this.staffViewShow=!1,this.waterOperationShow=!1,this.currentType=!1,this.$set(this.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),this.$refs.waterManAdd.editData({companyNo:"",simplifyNo:"",shortName:"",adminArea:"",adminAreaDivi:"",companyArea:"",databaseAccount:"",databasePassword:"",maxIdle:"",maxTotal:"",workflowApp:"",comments:"",sourceCompany:"",dbId:"",appVersion:"",status:1}),this.common.chargeObjectEqual(this,this.formData,"set","waterManAdd")},staffViewAdd:function(){this.authTreeShow=!0},OtherStaffAdd:function(){this.staffViewShow=!1,this.userAddVisible=!0},userAddSubmit:function(){var e=this,t=this.$refs.userAdd.postNoCheck,a=this.$refs.userAdd.roleCodeCheck,r=this.$refs.userAdd.status,n={busicode:"OtherUserAdd",data:{companyNo:this.companyNo,userName:this.$refs.userAdd.userName,account:this.$refs.userAdd.account,postNo:t.join(","),status:r,roleCode:a.join(",")}};this.$api.fetch({params:n}).then(function(t){e.$message({type:"success",message:"保存成功!"}),e.userAddVisible=!1,e.staffViewShow=!0,e.$refs.staffView.selectTSubSystem()})},userAddClose:function(){this.$refs.userAdd.editData(""),this.userAddVisible=!1,this.staffViewShow=!0},editor:function(e){var t=this;this.currentType=!0;var a={busicode:"CompanyQuery",data:{companyId:e.row.companyId}};this.$api.fetch({params:a}).then(function(e){t.common.chargeObjectEqual(t,e,"set","waterManAdd"),t.$refs.waterManAdd.editData(e)}),this.waterManAddVisible=!0,this.waterManShow=!1,this.staffViewShow=!1,this.waterOperationShow=!1,this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}})},auth:function(e){this.choosedCompony=e.row,this.waterAuthorize=!0,this.waterManShow=!1,this.staffViewShow=!1,this.waterOperationShow=!1,this.$set(this.crumbsData.titleList,"2",{title:"授权范围",method:function(){window.histroy.back()}})},staffView:function(e){this.choosedCompony=e.row,this.waterAuthorize=!1,this.waterManShow=!1,this.staffViewShow=!0,this.waterOperationShow=!1,this.companyNo=e.row.companyNo,this.$refs.staffView.tableQuery.companyNo=e.row.companyNo,this.$refs.staffView.selectTSubSystem(),this.$set(this.crumbsData.titleList,"2",{title:"授权员工",method:function(){window.histroy.back()}})},waterOperation:function(e){var t=this;this.choosedCompony=e.row,this.waterAuthorize=!1,this.waterManShow=!1,this.staffViewShow=!1,this.waterOperationShow=!0,this.$nextTick(function(){t.$refs.waterOperation.getTreeData(e.row.companyNo),t.$set(t.crumbsData.titleList,"2",{title:"授权模块",method:function(){window.histroy.back()}})})},indexMethod:function(e){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(e+1)},handleSizeChange:function(e){this.tableQuery.pageCount=e,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(e){this.tableQuery.page=e,this.selectTSubSystem()},selectTSubSystem:function(){var e=this,t={busicode:"CompanyList",data:this.tableQuery};this.$api.fetch({params:t}).then(function(t){e.appServerData=t,e.common.changeTable(e,".water-manage .kl-table",[".water-manage .block"])})},closeDialog:function(){this.waterManShow=!0,this.staffViewShow=!1,this.waterOperationShow=!1,this.waterManAddVisible=!1,this.waterAuthorize=!1,this.authTreeShow=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.waterManAdd.handleClose()},authorClose:function(){this.closeDialog()},staffViewClose:function(){this.closeDialog()},waterOperationClose:function(){this.closeDialog()},submit:function(e){var t=this.crumbsData.titleList[2].title;this.$refs.waterManAdd.submitForm(e,t)},handleSave:function(){this.$refs.waterOperation.handleSave()},syncUser:function(){var e=this;this.userLoading=!0,f()({method:"post",url:"/sys/synchronization.htm",data:{},headers:{returntype:"ajax/json"}}).then(function(){e.$message({type:"success",message:"同步成功"}),e.userLoading=!1}).catch(function(){e.userLoading=!1})},syncDept:function(){var e=this;this.deptLoading=!0,f()({method:"post",url:"/sys/synchronization.htm",data:{},headers:{returntype:"ajax/json"}}).then(function(){e.$message({type:"success",message:"同步成功"}),e.deptLoading=!1}).catch(function(){e.deptLoading=!1})}},watch:{maxHeight:function(){var e=this;this.tableShow=!1,this.$nextTick(function(){e.tableShow=!0})}}},I={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"water-manage"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:e.crumbsData}}),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.waterManShow,expression:"waterManShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:e.appAdd}},[e._v("添加")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.waterManAddVisible,expression:"waterManAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.submit("waterRuleForm")}}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.closeDialog}},[e._v("返回")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.staffViewShow,expression:"staffViewShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:e.OtherStaffAdd}},[e._v("添加外部人员")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.staffViewClose}},[e._v("返回")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.waterOperationShow,expression:"waterOperationShow"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.handleSave}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.waterOperationClose}},[e._v("返回")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.waterAuthorize,expression:"waterAuthorize"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.authorClose}},[e._v("返回")])],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.userAddVisible,expression:"userAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:e.userAddSubmit}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:e.userAddClose}},[e._v("返回")])],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.waterManShow,expression:"waterManShow"}],staticClass:"kl-table"},[e.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:e.appServerData.list,"max-height":e.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:e.indexMethod}}),e._v(" "),a("el-table-column",{attrs:{prop:"companyNo",label:"水司编号","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"simplifyNo",label:"简化编号","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"shortName","min-width":"100",label:"水司简称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"adminArea","min-width":"80",label:"省份"}}),e._v(" "),a("el-table-column",{attrs:{prop:"companyArea","min-width":"100",label:"片区"}}),e._v(" "),a("el-table-column",{attrs:{prop:"versionName","min-width":"100",label:"应用程序版本"}}),e._v(" "),a("el-table-column",{attrs:{prop:"serverName","min-width":"100",label:"数据库服务器"}}),e._v(" "),a("el-table-column",{attrs:{prop:"status",label:"状态",formatter:e.formatStatus,"min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"180px",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.editor(t)}}},[e._v("编辑")]),e._v(" "),a("span",{staticStyle:{color:"#e6e6e6"}},[e._v("|")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.waterOperation(t)}}},[e._v("授权模块")])]}}],null,!1,2006179874)})],1):e._e(),e._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":e.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:e.appServerData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),e.userAddVisible?a("userAdd",{ref:"userAdd"}):e._e(),e._v(" "),e.waterManAddVisible?a("waterManAdd",{ref:"waterManAdd",attrs:{isEdit:e.currentType}}):e._e(),e._v(" "),e.waterAuthorize?a("waterAuthorize",{ref:"waterAuthorize",attrs:{waterCom:e.choosedCompony},on:{"update:waterCom":function(t){e.choosedCompony=t},"update:water-com":function(t){e.choosedCompony=t}}}):e._e(),e._v(" "),e.staffViewShow?a("staffView",{ref:"staffView"}):e._e(),e._v(" "),e.waterOperationShow?a("waterOperation",{ref:"waterOperation"}):e._e(),e._v(" "),e.authTreeShow?a("staffAuthTree",{ref:"authTree"}):e._e()],1)])},staticRenderFns:[]};var M=a("VU/8")(E,I,!1,function(e){a("dzb1")},null,null);t.default=M.exports},xB7I:function(e,t){}});