package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterStatusBean;
import com.koron.zys.baseConfig.mapper.MeterStatusMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;
/**
 * 抄表状态-编辑
 * <AUTHOR>
 *
 */
public class MeterStatusUpdate implements ServerInterface{
	
private static Logger logger = LoggerFactory.getLogger(MeterStatusUpdate.class);
	
@Override
public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
	MeterStatusMapper mapper = factory.getMapper(MeterStatusMapper.class);
	MeterStatusBean bean = null;
	try {
		bean = JsonUtils.objectToPojo(req.getData(), MeterStatusBean.class);
		// 校验字段重复
		if (mapper.check2("status_name", bean.getStatusName(),bean.getId()) > 0) {
			return MessageBean.create(Constant.ILLEGAL_PARAMETER,"抄表状态名称：" + bean.getStatusName() + "的信息已存在。", void.class);
		}
		bean.setUpdateName(userInfo.getUserInfo().getName());
		bean.setUpdateTime(CommonUtils.getCurrentTime());
		if (StringUtils.isNullOrEmpty(bean.getId())) {
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
		}

		mapper.updateMeterStatus(bean);
	} catch (Exception e) {
		logger.error("非法参数",e);
		return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
	}
	return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
}
}