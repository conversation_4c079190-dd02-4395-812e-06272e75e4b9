package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;
import com.koron.util.Check.Repeat;

/*
 * 流程超时配置表
 */
public class ProcessTimeBean extends BaseBean{

	
	@Check(name = "单据编号", notEmpty = true,repeat = @Repeat(tableName = "base_water_process_config"))
	private String receiptNo;
	
	@Check(name = "单据类型", notEmpty = true)
	private String receiptName;
	

	private int hour;


	public String getReceiptNo() {
		return receiptNo;
	}


	public void setReceiptNo(String receiptNo) {
		this.receiptNo = receiptNo;
	}


	public String getReceiptName() {
		return receiptName;
	}


	public void setReceiptName(String receiptName) {
		this.receiptName = receiptName;
	}


	public int getHour() {
		return hour;
	}


	public void setHour(int hour) {
		this.hour = hour;
	}


	
	
}
