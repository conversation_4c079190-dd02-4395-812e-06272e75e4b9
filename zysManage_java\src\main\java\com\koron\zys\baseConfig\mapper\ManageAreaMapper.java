package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.koron.zys.baseConfig.bean.ManageAreaBean;
import com.koron.zys.baseConfig.queryBean.ManageAreaQueryBean;
import com.koron.zys.baseConfig.vo.ManageAreaVO;
import com.koron.zys.serviceManage.bean.SelectBean;
import com.koron.zys.serviceManage.bean.TreeBean;

/**
 * 管理区域
 * <AUTHOR>
 *
 */
public interface ManageAreaMapper {
	
	/**
	 * 新增
	 * @param bean
	 * @return
	 */
	public Integer saveManageArea(ManageAreaBean bean);
	
	/**
	 * 更新
	 * @param bean
	 * @return
	 */
	public Integer updateManageArea(ManageAreaBean bean);
	
	/**
	 * 查询
	 * @param bean
	 * @return
	 */
	public List<ManageAreaVO> findManageArea(ManageAreaQueryBean bean);
	
	/**
	 * 查询省份
	 * @param bean
	 * @return
	 */
	public List<SelectBean> SelectProvince();

	/**
	 * 树结构
	 * @param
	 * @return
	 */
	public List<TreeBean> findManageAreaByTree();
	
	/**
	 * 下拉框
	 * @return
	 */
	public List<SelectBean> findManageAreaBySelect();
	
	/**
	 * 按ID查询
	 * @param bean
	 * @return
	 */
	public ManageAreaBean findManageAreaById(ManageAreaBean bean);
	
	/**
	 * 根据id查ManageArea数据
	 * @param id
	 * @return
	 */
	public ManageAreaBean findManageAreaById(@Param("manageAreaId") String manageAreaId);
	
	/**
	 * 根据id查ManageArea数据，包括返回父级信息
	 * @param id
	 * @return
	 */
	public ManageAreaBean findManageAreaInfoById(@Param("manageAreaId") String manageAreaId);
	
	/**
	 * 根据code查ManageArea数据
	 * @param id
	 * @return
	 */
	public SelectBean findManageAreaByCode(@Param("areaNo") String areaNo);
	
	/**
	 * 根据某个code下最大子级代号
	 * @param id
	 * @return
	 */
	public String finManageAreaMAXChild(@Param("areaNo") String areaNo);
	
	/**
	 * 根据某个code下最大子级代号
	 * @param id
	 * @return
	 */
	public String findMaxChild(@Param("areaNo") String areaNo);

}
