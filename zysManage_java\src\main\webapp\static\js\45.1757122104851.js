webpackJsonp([45],{"75fv":function(t,e){},OCti:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=s("mvHQ"),o=s.n(a),i={render:function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"pubCostAdd"},[s("el-form",{ref:"pubCostForm",staticClass:"formBill-Two",attrs:{model:t.pubCostForm,"label-width":"120px",inline:!0}},[s("el-form-item",{attrs:{label:"编号："}},[s("el-input",{attrs:{clearable:""},model:{value:t.pubCostForm.costNo,callback:function(e){t.$set(t.pubCostForm,"costNo",e)},expression:"pubCostForm.costNo"}})],1),t._v(" "),s("el-form-item",{attrs:{label:"名称："}},[s("el-input",{attrs:{clearable:""},model:{value:t.pubCostForm.costName,callback:function(e){t.$set(t.pubCostForm,"costName",e)},expression:"pubCostForm.costName"}})],1),t._v(" "),s("el-form-item",{attrs:{label:"英文名称："}},[s("el-input",{attrs:{clearable:""},model:{value:t.pubCostForm.costNameEn,callback:function(e){t.$set(t.pubCostForm,"costNameEn",e)},expression:"pubCostForm.costNameEn"}})],1),t._v(" "),s("el-form-item",{attrs:{label:"费用单位"}},[s("el-select",{attrs:{clearable:"",placeholder:""},model:{value:t.pubCostForm.costUnit,callback:function(e){t.$set(t.pubCostForm,"costUnit",e)},expression:"pubCostForm.costUnit"}},t._l(t.dictionaryData.COU,function(t,e){return s("el-option",{key:e,attrs:{label:t.name,value:t.value}})}),1)],1),t._v(" "),s("el-form-item",{attrs:{label:"排序："}},[s("el-input",{attrs:{clearable:""},model:{value:t.pubCostForm.sortNo,callback:function(e){t.$set(t.pubCostForm,"sortNo",e)},expression:"pubCostForm.sortNo"}})],1),t._v(" "),s("el-form-item",{attrs:{label:"状态："}},[s("el-select",{attrs:{placeholder:"请选择"},model:{value:t.pubCostForm.status,callback:function(e){t.$set(t.pubCostForm,"status",e)},expression:"pubCostForm.status"}},[s("el-option",{attrs:{label:"启用",value:1}}),t._v(" "),s("el-option",{attrs:{label:"停用",value:2}})],1)],1),t._v(" "),s("el-form-item",{staticClass:"f4",attrs:{label:"备注:"}},[s("el-input",{attrs:{type:"textarea",maxlength:"50","show-word-limit":""},model:{value:t.pubCostForm.comments,callback:function(e){t.$set(t.pubCostForm,"comments",e)},expression:"pubCostForm.comments"}})],1)],1)],1)},staticRenderFns:[]},l={name:"pubCost",components:{pubCostAdd:s("VU/8")({name:"pubCostAdd",data:function(){return{databaseData:[],pubCostForm:{costNo:"",costName:"",costNameEn:"",costUnit:"",sortNo:"",status:1,comments:""},dictionaryData:[]}},mounted:function(){this.getDictionary()},methods:{resetForm:function(){this.$refs.pubCostForm.resetFields()},getDictionary:function(){var t=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"COU"}}).then(function(e){t.dictionaryData=e})},submitForm:function(t,e){var s=this,a=this,o="",i={};this.$refs[t].validate(function(t){if(!t)return!1;"新建"===e?(i={busicode:"PubCostAdd",data:s.pubCostForm},o="新建成功"):(i={busicode:"PubCostUpdate",data:s.pubCostForm},o="保存成功"),s.$api.fetch({params:i}).then(function(t){a.$message({showClose:!0,message:o,type:"success"}),a.$parent.selectTSubSystem(),a.resetForm(),a.$parent.pubCostAddVisible=!1,a.$parent.pubCostShow=!0,a.$parent.crumbsData.titleList.pop()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.pubCostForm,"get","childpubCost",this.$parent.closeDialog)},editData:function(t){this.pubCostForm=t}}},i,!1,null,null,null).exports},data:function(){return{total:0,tableShow:!0,tableQuery:{page:1,pageCount:50},maxHeight:0,appServerData:[],formData:{costNo:"",costName:"",status:"",sortNo:"",comments:""},crumbsData:{titleList:[{title:"基础配置",path:"/ChangeTables"},{title:"费用类型",method:function(){window.histroy.back()}}]},pubCostShow:!0,pubCostAddVisible:!1}},mounted:function(){this.selectTSubSystem()},methods:{submitForm:function(t){var e=this.crumbsData.titleList[2].title;this.$refs.child.submitForm(t,e)},clear:function(){this.formData.costNo="",this.formData.costName="",this.formData.sortNo="",this.formData.status=1,this.formData.comments=""},formatStatus:function(t){return 1===t.status?"启用":"停用"},appAdd:function(t){var e=this;if(this.clear(),"add"===t)this.$set(this.crumbsData.titleList,"2",{title:"新建",method:function(){window.histroy.back()}}),this.$refs.child.editData(this.formData),this.common.chargeObjectEqual(this,this.formData,"set","childpubCost");else{this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var s={busicode:"PubCostQuery",data:{id:t.row.id}};this.$api.fetch({params:s}).then(function(t){e.$refs.child.editData(t),sessionStorage.setItem("formData",o()(e.formData)),e.common.chargeObjectEqual(e,t,"set","childpubCost")})}this.pubCostShow=!1,this.pubCostAddVisible=!0},indexMethod:function(t){return(this.tableQuery.page-1)*this.tableQuery.pageCount+t+1},handleSizeChange:function(t){this.tableQuery.pageCount=t,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(t){this.tableQuery.page=t,this.selectTSubSystem()},selectTSubSystem:function(){var t=this,e=this,s={busicode:"PubCostList",data:e.tableQuery};this.$api.fetch({params:s}).then(function(s){e.$set(e.appServerData,"list",s.list),e.total=s.total,e.common.changeTable(t,".pubCost .kl-table",[".pubCost .block"])})},closeDialog:function(){this.pubCostShow=!0,this.pubCostAddVisible=!1,this.crumbsData.titleList.pop(),this.$refs.child.resetForm()},handleClose:function(){this.$refs.child.handleClose()}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},r={render:function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"pubCost"},[s("div",{staticClass:"main-content"},[s("div",{staticClass:"bread-contain"},[s("publicCrumbs",{attrs:{crumbsData:t.crumbsData}}),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:t.pubCostShow,expression:"pubCostShow"}],staticClass:"bread-contain-right"},[s("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.appAdd("add")}}},[t._v("新建")])],1),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:t.pubCostAddVisible,expression:"pubCostAddVisible"}],staticClass:"bread-contain-right"},[s("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.submitForm("pubCostForm")}}},[t._v("保存")]),t._v(" "),s("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:t.handleClose}},[t._v("返回")])],1)],1),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:t.pubCostShow,expression:"pubCostShow"}],staticClass:"kl-table"},[t.tableShow?s("el-table",{attrs:{stripe:"",border:"",data:t.appServerData.list,"max-height":t.maxHeight}},[s("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:t.indexMethod}}),t._v(" "),s("el-table-column",{attrs:{prop:"costNo",label:"编号","min-width":"100"}}),t._v(" "),s("el-table-column",{attrs:{prop:"costName",label:"名称","min-width":"100"}}),t._v(" "),s("el-table-column",{attrs:{prop:"costNameEn",label:"英文名称","min-width":"100"}}),t._v(" "),s("el-table-column",{attrs:{prop:"costUnit",label:"费用单位","min-width":"100"}}),t._v(" "),s("el-table-column",{attrs:{prop:"sortNo",label:"排序","min-width":"100"}}),t._v(" "),s("el-table-column",{attrs:{prop:"status",formatter:t.formatStatus,"min-width":"80",label:"状态"}}),t._v(" "),s("el-table-column",{attrs:{prop:"comments",label:"备注","min-width":"150"}}),t._v(" "),s("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-button",{attrs:{type:"text"},on:{click:function(s){return t.appAdd(e)}}},[t._v("编辑")])]}}],null,!1,1191321586)})],1):t._e(),t._v(" "),s("div",{staticClass:"block"},[s("el-pagination",{attrs:{"current-page":t.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":t.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),t._v(" "),s("div",{directives:[{name:"show",rawName:"v-show",value:t.pubCostAddVisible,expression:"pubCostAddVisible"}]},[s("pubCostAdd",{ref:"child"})],1)])])},staticRenderFns:[]};var n=s("VU/8")(l,r,!1,function(t){s("75fv")},null,null);e.default=n.exports}});