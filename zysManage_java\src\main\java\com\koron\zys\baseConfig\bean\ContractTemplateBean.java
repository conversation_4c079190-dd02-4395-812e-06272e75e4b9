package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;

public class ContractTemplateBean extends BaseBean{
	@Check(name = "模板名称", notEmpty = true)
	private String templateName;
	
	@Check(name = "合同类型", notEmpty = true)
	private int type;
	
	private String fjName;
	
	private String tempId;
	
	public String getFjName() {
		return fjName;
	}
	public void setFjName(String fjName) {
		this.fjName = fjName;
	}
	public String getTempId() {
		return tempId;
	}
	public void setTempId(String tempId) {
		this.tempId = tempId;
	}
	public String getTemplateName() {
		return templateName;
	}
	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}

	
}
