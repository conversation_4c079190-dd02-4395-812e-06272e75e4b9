package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.AlipayConfigBean;
import com.koron.zys.baseConfig.mapper.AlipayConfigMapper;
import com.koron.zys.baseConfig.queryBean.AlipayConfigQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

/**
 * 支付宝配置-查询
 *
 * <AUTHOR>
 */
public class AlipayConfigQuery implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(AlipayConfigQuery.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        MessageBean<AlipayConfigBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", AlipayConfigBean.class);
        try {
            AlipayConfigQueryBean bean = JsonUtils.objectToPojo(req.getData(), AlipayConfigQueryBean.class);
            AlipayConfigMapper mapper = factory.getMapper(AlipayConfigMapper.class);
            if (StringUtils.isEmpty(bean.getId())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
            }
            AlipayConfigBean query = mapper.selectAlipayConfigById(bean.getId());
            info.setData(query);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }
}
