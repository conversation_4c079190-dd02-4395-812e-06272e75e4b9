package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.TradeClassifyBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.baseConfig.mapper.TradeClassifyMapper;
import com.koron.zys.baseConfig.queryBean.TradeClassifyQueryBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 行业分类-编辑初始化
 * 
 * <AUTHOR>
 *
 */
public class TradeClassifyQuery implements ServerInterface {
	
private static Logger logger = LoggerFactory.getLogger(TradeClassifyQuery.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<TradeClassifyBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", TradeClassifyBean.class);

		try {
			TradeClassifyQueryBean bean = JsonUtils.objectToPojo(req.getData(), TradeClassifyQueryBean.class);
			TradeClassifyMapper mapper = factory.getMapper(TradeClassifyMapper.class);
			TradeClassifyBean TradeClassifybean = mapper.selectTradeClassifyById(bean.getId());
			info.setData( TradeClassifybean);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
	
	
}
