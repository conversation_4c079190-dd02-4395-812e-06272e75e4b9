package com.koron.zys.baseConfig.queryBean;

import com.koron.common.bean.query.BaseQueryBean;

/**
 * 
 * <AUTHOR>
 *
 */
public class BaseAccessoryConfigQueryBean extends BaseQueryBean{
	
	/**
	 * 主键
	 */
	private String id;
	
	/**
	 * 单据类型 
	 */
	private String receiptType;
	
	/**
	 * 流程节点
	 */
	private String processNode;
	
	/**
	 * 附件编码   UFI
	 */
	private String accessoryNo;
	
	/**
	 * 是否必须 0否，1是
	 */
	private Integer requiredFlag;

	public String getId() {
		return id;
	}

	public String getReceiptType() {
		return receiptType;
	}

	public String getProcessNode() {
		return processNode;
	}

	public String getAccessoryNo() {
		return accessoryNo;
	}

	public Integer getRequiredFlag() {
		return requiredFlag;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setReceiptType(String receiptType) {
		this.receiptType = receiptType;
	}

	public void setProcessNode(String processNode) {
		this.processNode = processNode;
	}

	public void setAccessoryNo(String accessoryNo) {
		this.accessoryNo = accessoryNo;
	}

	public void setRequiredFlag(Integer requiredFlag) {
		this.requiredFlag = requiredFlag;
	}
	
}
