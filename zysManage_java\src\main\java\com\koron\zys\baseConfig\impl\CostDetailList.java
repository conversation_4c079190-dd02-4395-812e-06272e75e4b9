package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.CostDetailMapper;
import com.koron.zys.baseConfig.queryBean.CostDetailQueryBean;
import com.koron.zys.baseConfig.vo.CostDetailVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.DictionaryMapper;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.zys.serviceManage.vo.DictionaryVO;
import com.koron.util.Constant;

/**
 * 费用信息-列表初始化
 * <AUTHOR>
 */
public class CostDetailList implements ServerInterface  {
	
	private static Logger logger = LoggerFactory.getLogger(CostDetailList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
		try {
			CostDetailQueryBean bean = JsonUtils.objectToPojo(req.getData(), CostDetailQueryBean.class);
			CostDetailMapper mapper = factory.getMapper(CostDetailMapper.class);	
			DictionaryMapper dmapper = factory.getMapper(DictionaryMapper.class);
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			List<CostDetailVO> list = mapper.selectCostDetailList(bean);		
			List<DictionaryVO> ladderlist = dmapper.findDictionaryParamName("UWJ");	//阶梯类型，数据字典
			//数据列表数据中的数据字典为中文名称
			for(CostDetailVO vo:list) {
				for(DictionaryVO dvo:ladderlist) {
					if((dvo.getValue()+"").equals(vo.getLadderType())) {
						vo.setLadderType(dvo.getName());
						break;
					}
				}
			}
			info.setData(new PageInfo<>(list));
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}