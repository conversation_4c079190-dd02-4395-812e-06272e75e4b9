package com.koron.zys.baseConfig.mapper;

import com.koron.zys.baseConfig.bean.BasePayMentElfBean;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

public interface PayMentElfMapper {
    @Delete("delete from base_payment_elf")
    Integer deletePayMentElf();

    Integer insertPayMentElf(BasePayMentElfBean payMentElfBean);

    @Select("select * from base_payment_elf")
    BasePayMentElfBean selectPayMentElf();

}
