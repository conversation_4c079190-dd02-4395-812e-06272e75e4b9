package com.koron.zys.baseConfig.bean;

/**
 * 费用票据关系
 * 
 * <AUTHOR> 2020年1月17日
 */
public class CostInvoiceBean extends BaseBean {

	/*
	 * 主键
	 */
	private String id;
	/*
	 * 费用主键
	 */
	private String costId;
	/*
	 * 票据类型
	 */
	private String invoiceType;
	/*
	 * 商品编号
	 */
	private String commodityNo;
	/*
	 * 商品名称
	 */
	private String commodityName;
	/*
	 * 商品单位
	 */
	private String commodityUnit;
	/*
	 * 税率
	 */
	private Double taxRate;
	/*
	 * 备注
	 */
	private String comments;
	/*
	 * 状态
	 */
	private String status;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCostId() {
		return costId;
	}

	public void setCostId(String costId) {
		this.costId = costId;
	}

	public String getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(String invoiceType) {
		this.invoiceType = invoiceType;
	}

	public String getCommodityNo() {
		return commodityNo;
	}

	public void setCommodityNo(String commodityNo) {
		this.commodityNo = commodityNo;
	}

	public String getCommodityName() {
		return commodityName;
	}

	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}

	public String getCommodityUnit() {
		return commodityUnit;
	}

	public void setCommodityUnit(String commodityUnit) {
		this.commodityUnit = commodityUnit;
	}

	public Double getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(Double taxRate) {
		this.taxRate = taxRate;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}
