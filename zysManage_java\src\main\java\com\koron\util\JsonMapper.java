package com.koron.util;

import java.io.IOException;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

public class JsonMapper extends ObjectMapper{


	private static final long serialVersionUID = 1L;

	public JsonMapper() {
		this(Include.NON_EMPTY);
		SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Double.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Double.TYPE, ToStringSerializer.instance);
        registerModule(simpleModule);
	}
	
	public JsonMapper(Include include) {
		this.getSerializerProvider().setNullValueSerializer(new JsonSerializer<Object>(){
			@Override
			public void serialize(Object object, JsonGenerator generator,SerializerProvider provider) throws IOException,JsonProcessingException {
				generator.writeString("");
			}
		});
	}
}
