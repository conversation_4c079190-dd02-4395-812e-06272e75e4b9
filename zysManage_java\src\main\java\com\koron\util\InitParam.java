package com.koron.util;

import java.util.Objects;
import java.util.Set;

import org.apache.ibatis.io.ResolverUtil;
import org.koron.ebs.businessLog.LogInfo;
import org.koron.ebs.module.ModuleService;
import org.koron.ebs.mybatis.ADOSession;
import org.koron.ebs.mybatis.EnvSource;
import org.koron.ebs.mybatis.MybatisInfo;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.koron.zys.serviceManage.utils.PushBlockQueue;

public class InitParam {
	
	private static Logger log = LoggerFactory.getLogger(InitParam.class);
	
	public static ADOSession session;
	public static boolean isDev=true;

	public InitParam(ModuleService service, String env) {
		log.info("当前环境：{}", env);
		PushBlockQueue.getInstance().start(); // 开启线程
		service.registe(new MybatisInfo());
		service.registe(new LogInfo());
		if(session == null)
			session = service.invokeObject("org.koron.mybatis", ADOSession.class);
		SessionFactory.setMapUnderscoreToCamelCase(true);
		session.addInterceptor("_default", "com.koron.util.PageInterceptor");
		session.registeJndiMap("_default", "java:comp/env/jndi/mysql/" + env);
		session.registeJndiMap("_mg", "java:comp/env/jndi/mg/" + env);
		ResolverUtil<Class<?>> resolverUtil = new ResolverUtil<Class<?>>();
        resolverUtil.find(new ResolverUtil.IsA(Object.class), "com.koron");
        Set<Class<? extends Class<?>>> mapperSet = resolverUtil.getClasses();
        for (Class<?> mapperClass : mapperSet) {
        	if(!mapperClass.isInterface()) {
    		   continue;
    	    }
           EnvSource envSource = mapperClass.getAnnotation(EnvSource.class);
           if(envSource != null){
        	   if(Objects.equals(envSource.value(), "_default")){
                   session.registeClazz("_default", mapperClass);
               }
           }
        }
		if(!"Windows 10".equals(System.getProperty("os.name")) && env.equals("prod")) {
			isDev=false;
		}
	}
}
