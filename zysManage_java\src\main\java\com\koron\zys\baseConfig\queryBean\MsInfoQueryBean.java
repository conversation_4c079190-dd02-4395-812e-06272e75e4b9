package com.koron.zys.baseConfig.queryBean;

import com.koron.zys.baseConfig.bean.BaseBean;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class MsInfoQueryBean extends BaseBean {

    /**
     * 主键
     */
    private String meterId;

    /**
     * 用户编号
     */
    private String userNo;
    /**
     * 用户名称
     */
    private String userName;

    /**
     * 客户名称
     */
    private String ctmName;

    /**
     * 水表编号
     */
    private String meterNo;
    /**
     * 出厂编号
     */
    private String factoryNo;

    /**
     * imei号
     */
    private String imei;
    /**
     * imsi号
     */
    private String imsi;
    /**
     * 水表口径
     */
    private String meterBore;
    /**
     * 水表厂家
     */
    private String meterFactory;
    /**
     * 水表形态
     */
    private Integer meterForm;
    /**
     * 水表类型
     */
    private Integer meterType;
    /**
     * 水表型号
     */
    private String meterModel;
    /**
     * 合格证号
     */
    private String meterCert;
    /**
     * 水表状态
     */
    private Integer meterStatus;
    /**
     * 所属部门
     */
    private String departBelong;
    /**
     * 入库日期
     */
    private String billDate;

    /**
     * 查询开始日期
     */
    private String beginDate;

    /**
     * 查询结束日期
     */
    private String endDate;

    /**
     * 册本号
     */
    private String bookNo;

    /**
     * 用户地址
     */
    private String userAddr;

    /**
     * 客户地址
     */
    private String ctmAddr;

    /**
     * 安装日期开始
     */
    private String setupDate1;

    /**
     * 安装日期结束
     */
    private String setupDate2;

    /**
     * 上期抄码
     */
    private Integer upperNum;
    /**
     * 使用周期
     */
    private Integer usageCycle;
    /**
     * 模糊查询
     */
    private String fuzzyQuery;

    /**
     * 是否超期
     */
    private Integer overdue;

    private int isZeroWaterPay;

    /**
     * 是否换表流程查询水表
     */
    private Integer isMsChg;
    /**
     * 请求类型 1：水表编号、2：水表表身号、3：用户编号
     */
    private Integer queryType;

    /**
     * 传输方式（通讯类型）
     */
    private String transmittalModel;


    private List<String> modelIds;

    private List<String> meterNos;

    private List<String> newMeterNos;

    private String businessArea;

    private List<String> businessAreaList;

    private String groupCode;

    private String reportQuery;

    /**
     * 上传标识  1：为失败
     */
    private Integer uploadFlag;

    private String exactQuery;

    private String queryTypeEn;

    private List<String> modelNos;

    private List<String> factoryNoList;

    @ApiModelProperty("id列表")
    private List<String> idList;

    private Integer statisticsWay;

}
