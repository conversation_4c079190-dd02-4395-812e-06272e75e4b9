package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.common.bean.query.BaseQueryBean;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ProcessTimeBean;
import com.koron.zys.baseConfig.mapper.ProcessTimeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

public class ProcessTimeList implements ServerInterface{

	private static Logger logger = LoggerFactory.getLogger(ProcessTimeList.class);	
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);

		try {
			BaseQueryBean bean = JsonUtils.objectToPojo(req.getData(), BaseQueryBean.class);
			ProcessTimeMapper mapper = factory.getMapper(ProcessTimeMapper.class);
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			List<ProcessTimeBean> list = mapper.selectList();			
			info.setData(new PageInfo<>(list));
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}
