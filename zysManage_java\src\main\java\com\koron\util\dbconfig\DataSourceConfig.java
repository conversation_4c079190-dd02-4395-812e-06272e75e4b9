package com.koron.util.dbconfig;

import java.sql.SQLException;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.zaxxer.hikari.HikariConfig;

@Configuration
public class DataSourceConfig {
	
	@Bean
	public HikariConfig hikariConfig(DataSourceProperties config) throws SQLException {
		HikariConfig dataSource = new HikariConfig();
		dataSource.setReadOnly(config.getReadOnly());
		dataSource.setConnectionTimeout(config.getConnectionTimeout());
		dataSource.setIdleTimeout(config.getIdleTimeout());
		dataSource.setValidationTimeout(config.getValidationTimeout());
		dataSource.setMaxLifetime(config.getMaxLifetime());
		dataSource.setMaximumPoolSize(config.getMaximumPoolSize());
		dataSource.setMinimumIdle(config.getMinimumIdle());
		dataSource.setAllowPoolSuspension(config.getAllowPoolSuspension());
		return dataSource;
	}

}
