package com.koron.zys.baseConfig.mapper;

import com.koron.zys.baseConfig.bean.AlipayConfigBean;
import com.koron.zys.baseConfig.queryBean.AlipayConfigQueryBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.koron.ebs.mybatis.EnvSource;

import java.util.List;
import java.util.Map;

@EnvSource("_default")
public interface AlipayConfigMapper {
	
	
	/**
	 * 查询列表
	 * 
	 * @return
	 */
	List<AlipayConfigBean> selectAlipayConfigList(AlipayConfigQueryBean alipayConfigQueryBean);
	
	/**
	 * 添加
	 * 
	 * @param alipayConfigBean
	 * @return
	 */
	void insertAlipayConfig(AlipayConfigBean alipayConfigBean);
	
	/**
	 * 修改
	 * 
	 * @param alipayConfigBean
	 * @return
	 */
	Integer updateAlipayConfig(AlipayConfigBean alipayConfigBean);

	/**
	 * 根据id查询
	 * @param id
	 * @return
	 */
	@Select("select * from pub_alipay_config where id =#{id}")
	AlipayConfigBean selectAlipayConfigById(@Param("id") String id);

	/**
	 * 查询水司编号列表
	 * @return
	 */
	@Select("select company_no companyNo from pub_company")
	List<Map<String,String>> selectCompanyNo();
	
}
