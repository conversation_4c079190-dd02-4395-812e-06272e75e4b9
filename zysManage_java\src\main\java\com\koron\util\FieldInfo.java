package com.koron.util;

public class FieldInfo {
	
	private String name;
	
	private String type;
	
	private String remark;
	
	private Integer accuracy;
	
	private Integer length;

	private boolean pk;
	
	private boolean required;
	
	public Integer getAccuracy() {
		return accuracy;
	}

	public void setAccuracy(Integer accuracy) {
		this.accuracy = accuracy;
	}

	@Override
	public String toString() {
		return "FieldInfo [name=" + name + ", type=" + type + ", remark=" + remark + ", length=" + length + ", pk=" + pk
				+ ", required=" + required + "]";
	}

	public boolean isRequired() {
		return required;
	}

	public void setRequired(boolean required) {
		this.required = required;
	}

	public String getName() {
		return name;
	}

	public String getType() {
		return type;
	}

	public String getRemark() {
		return remark;
	}

	public Integer getLength() {
		return length;
	}

	public boolean isPk() {
		return pk;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setType(String type) {
		this.type = type;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public void setLength(Integer length) {
		this.length = length;
	}

	public void setPk(boolean pk) {
		this.pk = pk;
	}
	
}
