package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BaseArrearageElfBean;
import com.koron.zys.baseConfig.mapper.ArrearageElfMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;


/**
 * 欠费催缴精灵-列表
 *
 * <AUTHOR>
 */
public class ArrearageElfList implements ServerInterface {
    private static Logger logger = LoggerFactory.getLogger(ArrearageElfList.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        @SuppressWarnings("rawtypes")
        MessageBean<BaseArrearageElfBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", BaseArrearageElfBean.class);
        try {
            ArrearageElfMapper mapper = factory.getMapper(ArrearageElfMapper.class);
            BaseArrearageElfBean baseArrearageElfBean = mapper.selectArrearageElf();
            info.setCode(Constant.MESSAGE_INT_SUCCESS);
            info.setDescription("success");
            info.setData(baseArrearageElfBean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription("操作失败");
            logger.error("操作失败", e);
        }
        return info;
    }
}
