package com.koron.zys.baseConfig.queryBean;

import com.koron.common.bean.query.BaseQueryBean;

public class PubSysUserQueryBean extends BaseQueryBean{
	
	/**
	 * 主键
	 */
	private String id;
	
	/**
	 * 用户账号
	 */
	private String userAccount;
	
	/**
	 * 用户名称
	 */
	private String userName;
	
	/**
	 * 组织id
	 */
	private String orgId;
	
	/**
	 * 组织名称
	 */
	private String orgName;
	
	/**
	 * 邮件
	 */
	private String email;
	
	/**
	 * 岗位
	 */
	private String post;

	public String getId() {
		return id;
	}

	public String getUserAccount() {
		return userAccount;
	}

	public String getUserName() {
		return userName;
	}

	public String getOrgId() {
		return orgId;
	}

	public String getOrgName() {
		return orgName;
	}

	public String getEmail() {
		return email;
	}

	public String getPost() {
		return post;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setUserAccount(String userAccount) {
		this.userAccount = userAccount;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public void setPost(String post) {
		this.post = post;
	}

}
