package com.koron.zys.baseConfig.impl;

import java.util.ArrayList;
import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.google.common.base.Joiner;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BaseOtherElfBean;
import com.koron.zys.baseConfig.mapper.BaseOtherElfMapper;
import com.koron.zys.baseConfig.vo.BaseOtherElfVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

public class BaseOtherQuery implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub
	    MessageBean<BaseOtherElfVO> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", BaseOtherElfVO.class);
		
		BaseOtherElfMapper mapper = factory.getMapper(BaseOtherElfMapper.class);
		List<BaseOtherElfBean> list = mapper.selectList();
		List<String> staff1 = new ArrayList<String>();
		List<String> staff2 = new ArrayList<String>();
		List<String> staff3 = new ArrayList<String>();
		List<String> staff4 = new ArrayList<String>();
		BaseOtherElfVO vo = new BaseOtherElfVO();
		for (BaseOtherElfBean bean : list) {
			if("1".equals(bean.getNoticeType())) {
				staff1.add(bean.getNoticeStaff());
			}else if("2".equals(bean.getNoticeType())) {
				staff2.add(bean.getNoticeStaff());
			}else if("3".equals(bean.getNoticeType())) {
				staff3.add(bean.getNoticeStaff());
			}else if("4".equals(bean.getNoticeType())) {
				staff4.add(bean.getNoticeStaff());
			}
		}
		vo.setNoticeStaff1(Joiner.on(",").join(staff1));
		vo.setNoticeStaff2(Joiner.on(",").join(staff2));
		vo.setNoticeStaff3(Joiner.on(",").join(staff3));
		vo.setNoticeStaff4(Joiner.on(",").join(staff4));
		info.setData(vo);

		return info;
	}

}
