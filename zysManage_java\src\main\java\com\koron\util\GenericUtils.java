package com.koron.util;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class GenericUtils {

	public static Object getValue(Field field, Object object) throws Exception {
        String fieldName = field.getName();
		String str1 = fieldName.substring(0, 1);
		String str2 = fieldName.substring(1, fieldName.length());
		String methodName = "get" + str1.toUpperCase() + str2;
		Method method = object.getClass().getMethod(methodName, new Class[] {});
        return method.invoke(object, new Object[] {});
	}

}
