<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.MeterBoreMapper">

		
 	<select id="selectMeterBoreList" parameterType="com.koron.zys.baseConfig.queryBean.MeterBoreQueryBean" resultType="com.koron.zys.baseConfig.vo.MeterBoreVO" >
 	select xx.id, xx.bore_name, xx.bore_value,xx.max_flux, xx.meter_places,xx.old_change_cycle,xx.new_change_cycle,xx.comments,xx.sort_no,case when xx.status=1 then '启用' else '停用' end status
		from PUB_METER_BORE xx
	    <where>
	    	<if test="boreValue != null and boreValue != ''">
	    		and xx.bore_value  LIKE  concat('%' , #{boreValue} , '%')
	    	</if>
	    </where>
	    order by xx.sort_no asc  
	</select>
	 
	<select id="selectMeterBoreById" resultType="com.koron.zys.baseConfig.bean.MeterBoreBean">
		select *
		from PUB_METER_BORE
		where id = #{id}
	</select>
	
	 <insert id="insertMeterBore" parameterType="com.koron.zys.baseConfig.bean.MeterBoreBean">
		insert into PUB_METER_BORE (id,bore_name,bore_value, max_flux, meter_places,old_change_cycle,new_change_cycle, 
		comments,status, sort_no, create_time, create_name,create_account)
		values
		(
		#{id,},
		#{boreName},
		#{boreValue},
		#{maxFlux},
		#{meterPlaces},
		#{oldChangeCycle},
		#{newChangeCycle},
		#{comments},
		#{status},
		#{sortNo},		
		now(),
		#{createName},
		#{createAccount}
		)
	</insert>
	
		<update id="updateMeterBore" parameterType="com.koron.zys.baseConfig.bean.MeterBoreBean">
		update PUB_METER_BORE
		set bore_name = #{boreName},	
		    bore_value = #{boreValue},	 
			max_flux = #{maxFlux},
			meter_places= #{meterPlaces},
			old_change_cycle = #{oldChangeCycle},	
			new_change_cycle = #{newChangeCycle},		
			comments = #{comments},
			status = #{status},
			sort_no = #{sortNo},		
			update_time=now(),
			update_name = #{updateName},
			update_account=#{updateAccount}
		    where id = #{id}
	</update>
	<select id="getBoreByValue" resultType="String">
		select id
		from PUB_METER_BORE
		where 
		bore_value
		<if test="compare !=null and compare !='' and compare =='more'.toString()">
		  &gt;= #{value}
		</if>
		<if test="compare !=null and compare !='' and compare =='less'.toString()">
		  &lt;= #{value}
		</if>
		<if test="compare !=null and compare !='' and compare =='equal'.toString()">
		  = #{value}
		</if>
	</select>
	
	<select id="getModelByValue" resultType="String">
		select b.id 
		from PUB_METER_BORE a join pub_meter_model b
		where a.id = b.METER_BORE and 
		bore_value
		<if test="compare !=null and compare !='' and compare =='more'.toString()">
		  &gt;= #{value}
		</if>
		<if test="compare !=null and compare !='' and compare =='less'.toString()">
		  &lt;= #{value}
		</if>
		<if test="compare !=null and compare !='' and compare =='equal'.toString()">
		  = #{value}
		</if>
	</select>
	<select id="getBoreIdByValue" resultType="String">
		select a.id
		from PUB_METER_BORE a
		where bore_value
		<if test="compare !=null and compare !='' and compare =='more'.toString()">
			&gt;= #{value}
		</if>
		<if test="compare !=null and compare !='' and compare =='less'.toString()">
			&lt;= #{value}
		</if>
		<if test="compare !=null and compare !='' and compare =='equal'.toString()">
			= #{value}
		</if>
	</select>
	<select id="boresSelect" resultType="java.util.List">
		select
		id
		from
		PUB_METER_BORE
		where
		bore_value&lt;40
		and status=1
	</select> 
	
	<select id="boreSelectList" resultType="com.koron.zys.baseConfig.bean.BoreSelectBean">
		select
		id,bore_value 
		from
		PUB_METER_BORE
	</select>
</mapper>