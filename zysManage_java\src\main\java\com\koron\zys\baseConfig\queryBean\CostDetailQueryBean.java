package com.koron.zys.baseConfig.queryBean;

import com.koron.common.bean.query.BaseQueryBean;

public class CostDetailQueryBean extends BaseQueryBean{
	
	private String costDetailId;

	/**
	 * 明细名称
	 */
    private String detailName;
    /**
              * 费用名称
     */
    private String costName;
 /**
  * 状态
  */
    private Integer status;
    /**
              * 模糊查询字段
     */
    private String searchContent;
    
public String getCostDetailId() {
	return costDetailId;
}
public void setCostDetailId(String costDetailId) {
	this.costDetailId = costDetailId;
}
public String getDetailName() {
	return detailName;
}
public void setDetailName(String detailName) {
	this.detailName = detailName;
}
public String getCostName() {
	return costName;
}
public void setCostName(String costName) {
	this.costName = costName;
}
public Integer getStatus() {
	return status;
}
public void setStatus(Integer status) {
	this.status = status;
}
public String getSearchContent() {
	return searchContent;
}
public void setSearchContent(String searchContent) {
	this.searchContent = searchContent;
}
    
    

}
