package com.koron.zys.baseConfig.queryBean;
import com.koron.common.bean.query.BaseQueryBean;

import java.util.List;

public class BaseBookQueryBean extends BaseQueryBean{
	private String businessAbode;
	private String meterReadingStaff;
	private String meterReadingStaffAccount;
	/*
	 * 营业区域
	 */
	private String businessArea;
	private List<String> businessAreaList;//营业区域列表
	private String bookNo;

	/**
	 * 排序字段
	 */
	private String sortField;

	/**
	 * 排序顺序，1升序2倒序
	 */
	private Integer sortOrder;
	private String sortStr;//判断排序

    private Integer status;

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public String getSortField() {
		return sortField;
	}

	public void setSortField(String sortField) {
		this.sortField = sortField;
	}

	public Integer getSortOrder() {
		return sortOrder;
	}

	public void setSortOrder(Integer sortOrder) {
		this.sortOrder = sortOrder;
	}

	public String getSortStr() {
		return sortStr;
	}

	public void setSortStr(String sortStr) {
		this.sortStr = sortStr;
	}

	public String getMeterReadingStaffAccount() {
		return meterReadingStaffAccount;
	}
	public void setMeterReadingStaffAccount(String meterReadingStaffAccount) {
		this.meterReadingStaffAccount = meterReadingStaffAccount;
	}
	public String getBusinessAbode() {
		return businessAbode;
	}
	public void setBusinessAbode(String businessAbode) {
		this.businessAbode = businessAbode;
	}
	public String getMeterReadingStaff() {
		return meterReadingStaff;
	}
	public void setMeterReadingStaff(String meterReadingStaff) {
		this.meterReadingStaff = meterReadingStaff;
	}
	public String getBusinessArea() {
		return businessArea;
	}
	public void setBusinessArea(String businessArea) {
		this.businessArea = businessArea;
	}
	public String getBookNo() {
		return bookNo;
	}
	public void setBookNo(String bookNo) {
		this.bookNo = bookNo;
	}

	public List<String> getBusinessAreaList() {
		return businessAreaList;
	}

	public void setBusinessAreaList(List<String> businessAreaList) {
		this.businessAreaList = businessAreaList;
	}
}
