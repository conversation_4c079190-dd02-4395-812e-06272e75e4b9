<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BaseReceiptAccessoryMapper">

	<select id="selectListByCtmNo" parameterType="com.koron.zys.baseConfig.queryBean.BaseReceiptAccessoryQueryBean" resultType="com.koron.zys.baseConfig.bean.BaseReceiptAccessoryBean" >
 		SELECT
			t.accessory_name,
			t.create_account,
			t.create_name,
			t.create_time,
			t.ctm_no,
			t.id,
			t.metadata_id,
			t.process_node,
			t.receipt_id,
			t.receipt_type,
			t.user_no,
			t.accessory_no,
			t.meter_no 
		FROM
			base_receipt_accessory t
		<where>
			<if test="ctmNo != null and ctmNo != ''">
				and t.ctm_no = #{ctmNo, jdbcType=VARCHAR}
			</if>
			<if test="userNo != null and userNo != ''">
				and t.user_no = #{userNo, jdbcType=VARCHAR}
			</if>
			<if test="meterNo != null and meterNo != ''">
				and t.meter_no = #{meterNo, jdbcType=VARCHAR}
			</if>
			<if test="receiptId != null and receiptId != ''">
				and t.receipt_id = #{receiptId, jdbcType=VARCHAR}
			</if>
			<if test="receiptType != null and receiptType != ''">
				and t.receipt_type = #{receiptType, jdbcType=VARCHAR}
			</if>
		</where>
		 order by t.create_time desc
	</select>
		
 	<select id="selectList" parameterType="com.koron.zys.baseConfig.queryBean.BaseReceiptAccessoryQueryBean" resultType="com.koron.zys.baseConfig.bean.BaseReceiptAccessoryBean" >
 		SELECT
			t.accessory_name,
			t.create_account,
			t.create_name,
			t.create_time,
			t.ctm_no,
			t.id,
			t.metadata_id,
			t.process_node,
			t.receipt_id,
			t.receipt_type,
			t.user_no,
			t.accessory_no,
			t.meter_no 
		FROM
			base_receipt_accessory t
		<where>
			<if test="id != null and id != ''">
				and id = #{id, jdbcType=VARCHAR}
			</if>
			<if test="accessoryName != null and accessoryName != ''">
				and accessory_name = #{accessoryName, jdbcType=VARCHAR}
			</if>
			<if test="receiptId != null and receiptId != ''">
				and receipt_id = #{receiptId, jdbcType=VARCHAR}
			</if>
			<if test="receiptType != null and receiptType != ''">
				and receipt_type = #{receiptType, jdbcType=VARCHAR}
			</if> 
			<if test="meterNo != null and meterNo != ''">
				and meter_no = #{meterNo, jdbcType=VARCHAR}
			</if>
			<if test="metadataId != null and metadataId != ''">
				and metadata_id = #{metadataId, jdbcType=VARCHAR}
			</if>
			<if test="accessoryNo != null and accessoryNo != ''">
				and accessory_no = #{accessoryNo, jdbcType=VARCHAR}
			</if>
		</where>
		order by t.create_time asc 
	</select>
	
	<select id="selectById" parameterType="java.lang.String" resultType="com.koron.zys.baseConfig.bean.BaseReceiptAccessoryBean">
		SELECT
			t.accessory_name,
			t.create_account,
			t.create_name,
			t.create_time,
			t.ctm_no,
			t.id,
			t.metadata_id,
			t.process_node,
			t.receipt_id,
			t.receipt_type,
			t.user_no,
			t.accessory_no,
			t.meter_no 
		FROM
			base_receipt_accessory t
		WHERE t.id = #{id, jdbcType=VARCHAR}
	</select>
	
	<insert id="insert" parameterType="com.koron.zys.baseConfig.bean.BaseReceiptAccessoryBean">
		insert into base_receipt_accessory (
			id,
			accessory_name,
			create_account,
			create_name,
			create_time,
			ctm_no,
			metadata_id,
			process_node,
			receipt_id,
			receipt_type,
			user_no,
			accessory_no,
			meter_no
		)values(
			#{id,jdbcType=VARCHAR},
			#{accessoryName,jdbcType=VARCHAR},
			#{createAccount,jdbcType=VARCHAR},
			#{createName,jdbcType=VARCHAR},
			now(),
			#{ctmNo,jdbcType=VARCHAR},
			#{metadataId,jdbcType=VARCHAR},
			#{processNode,jdbcType=VARCHAR},
			#{receiptId,jdbcType=VARCHAR},
			#{receiptType,jdbcType=VARCHAR},
			#{userNo,jdbcType=VARCHAR},
			#{accessoryNo,jdbcType=VARCHAR},
			#{meterNo,jdbcType=VARCHAR}
		)
	</insert>
	
	<delete id="deleteById" parameterType="java.lang.String">
		delete from base_receipt_accessory where id = #{id}
	</delete>
	<delete id="deleteByAccessoryNo" parameterType="com.koron.zys.baseConfig.bean.BaseReceiptAccessoryBean">
		delete from base_receipt_accessory where receipt_id=#{receiptId} and receipt_type=#{receiptType} 
			and accessory_no=#{accessoryNo}
	</delete>
	<update id="updateAccessoryReceiptId">
		update base_receipt_accessory set receipt_id = #{id} where receipt_id = #{tempId} 
	</update>
	
	<select id="selectReceiptByUserNo" parameterType="com.koron.zys.baseConfig.queryBean.BaseReceiptAccessoryQueryBean" resultType="java.lang.Integer">
		select count(*) from ${receiptType} where user_no=#{userNo} and id = #{receiptId}
	</select>
	
	<select id="isHaveUserNo" parameterType="string"  resultType="java.lang.Integer">
		select count(*) from information_schema.COLUMNS  where table_name =  #{_parameter} and (column_name = 'user_no') 
	</select>
	
</mapper>