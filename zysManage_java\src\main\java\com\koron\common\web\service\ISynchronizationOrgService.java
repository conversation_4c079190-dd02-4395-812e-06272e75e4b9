package com.koron.common.web.service;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

public interface ISynchronizationOrgService {
	
	int BATCH_COUNT = 500;
	
	MessageBean<?> synchronization(SessionFactory factory);
	
	MessageBean<?> synDepartment(SessionFactory factory);
	
	MessageBean<?> synOrg(SessionFactory factory);
	
	MessageBean<?> synStaff(SessionFactory factory);
	
	MessageBean<?> synStaffDepartmentRelation(SessionFactory factory);

}
