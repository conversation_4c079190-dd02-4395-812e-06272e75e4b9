package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.MeterBoreMapper;
import com.koron.zys.baseConfig.vo.MeterBoreVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.baseConfig.queryBean.MeterBoreQueryBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 水表口径-列表初始化
 * <AUTHOR>
 *
 */
public class MeterBoreList implements ServerInterface  {
	
	private static Logger logger = LoggerFactory.getLogger(MeterBoreList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);

		try {
			MeterBoreQueryBean bean = JsonUtils.objectToPojo(req.getData(), MeterBoreQueryBean.class);
			MeterBoreMapper mapper = factory.getMapper(MeterBoreMapper.class);
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			List<MeterBoreVO> list = mapper.selectMeterBoreList(bean);			

			info.setData(new PageInfo<>(list));
			return info;
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}
