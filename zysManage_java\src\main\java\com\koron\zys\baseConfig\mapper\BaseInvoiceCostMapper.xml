<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BaseInvoiceCostMapper">
	<select id="select"
		resultType="com.koron.zys.baseConfig.vo.BaseInvoiceCostVO">
		select 
			id,
			commodity_no,
			commodity_name,
			commodity_unit,
			invoice_type as invoiceNo,
			tax_rate,
			cost_id,
			comments,
			status,
			is_main,
			is_special_tax
		from BASE_INVOICE_COST
		order by create_time asc
	</select>
	<insert id="insert"
		parameterType="com.koron.zys.baseConfig.bean.BaseInvoiceCostBean">
		insert into BASE_INVOICE_COST(id,invoice_type,commodity_no,commodity_name,commodity_unit,
		tax_rate,comments,status,tenant_id,create_time,create_account,create_name,cost_id,is_main,is_special_tax)
		values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.id},#{item.invoiceNo},#{item.commodityNo},#{item.commodityName},#{item.commodityUnit},
			#{item.taxRate},#{item.comments},#{item.status},#{item.tenantId},#{item.createTime},
			#{item.createAccount},#{item.createName},#{item.costId},#{item.isMain},#{item.isSpecialTax})
		</foreach>
	</insert>
	<delete id="delete">
		delete from BASE_INVOICE_COST
	</delete>
	<select id="selectMainCost"
			resultType="com.koron.zys.baseConfig.vo.BaseInvoiceCostVO">
		select
			id,
			commodity_no,
			commodity_name,
			commodity_unit,
			invoice_type as invoiceNo,
			tax_rate,
			cost_id,
			comments,
			status,
			is_main,
			is_special_tax
		from BASE_INVOICE_COST
		where is_main = 1
		order by create_time asc
	</select>
</mapper>
