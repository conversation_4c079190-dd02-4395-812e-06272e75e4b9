package com.koron.util;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;


public class JsonUtils {

    // 定义jackson对象
    private static final ObjectMapper MAPPER = new ObjectMapper();
    private static final Gson gson = new Gson();

    @SuppressWarnings("unchecked")
	public static Map<String, String> stringToMap(String data){
    	Map<String, String> map = new HashMap<String, String>();
        map = gson.fromJson(data, map.getClass());
        return map;
    }
    
    @SuppressWarnings("unchecked")
	public static Map<String, Long> stringToLMap(String data){
    	Map<String, Long> map = new HashMap<String, Long>();
        map = gson.fromJson(data, new TypeToken<HashMap<String, Long>>(){}.getType());
        return map;
    }
    
    public static JsonNode stringToJsonNode(String data) {
    	try {
    		JsonNode jsonNode = MAPPER.readTree(data);
			return jsonNode;
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
    	return null;
    }

    /**
     * 将对象转换成json字符串。
     * <p>Title: pojoToJson</p>
     * <p>Description: </p>
     * @param data
     * @return
     */
    public static String objectToJson(Object data) {
    	try {
			String string = MAPPER.writeValueAsString(data);
			return string;
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
    	return null;
    }
    
    /**
     * 将json结果集转化为对象
     * 
     * @param jsonData json数据
     * @param clazz 对象中的object类型
     * @return
     * @throws Exception 
     */
    public static <T> T jsonToPojo(String jsonData, Class<T> beanType) {
    	try {
           return MAPPER.readValue(jsonData, beanType);
    	}catch(Exception ex) {
    		throw new RuntimeException("数据转换异常!!!", ex);
    	}
    }
    
    /**
     * 将json结果集转化为对象
     * 
     * @param jsonData json数据
     * @param clazz 对象中的object类型
     * @return
     * @throws Exception 
     */
    public static <T> T objectToPojo(Object jsonData, Class<T> beanType) {
    	try {
			 T t = MAPPER.readValue(MAPPER.writeValueAsString(jsonData), beanType);
	         return t;
    	}catch(Exception ex) {
    		throw new RuntimeException("数据转换异常!!!", ex);
    	}
    }
    
    /**
     * 将json数据转换成pojo对象list
     * <p>Title: jsonToList</p>
     * <p>Description: </p>
     * @param jsonData
     * @param beanType
     * @return
     */
    public static <T>List<T> jsonToList(String jsonData, Class<T> beanType) {
    	JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, beanType);
    	try {
    		List<T> list = MAPPER.readValue(jsonData, javaType);
    		return list;
		} catch (Exception e) {
			e.printStackTrace();
		}
    	
    	return null;
    }
    
}
