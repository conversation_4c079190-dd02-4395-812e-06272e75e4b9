package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.koron.zys.baseConfig.bean.ConfigVacationBean;
import com.koron.zys.baseConfig.queryBean.ConfigVacationQueryBean;
import com.koron.zys.baseConfig.vo.ConfigVacationVO;

//@EnvSource("_default")
public interface ConfigVacationMapper {


    /**
     * 查询列表
     *
     * @return
     */
    List<ConfigVacationVO> selectConfigVacationList(ConfigVacationQueryBean configVacationQueryBean);

    /**
     * 校验假期配置是否重复
     */
    Integer checkConfigVacation(ConfigVacationQueryBean configVacationQueryBean);
    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @Select("select VACATION_ID id,VACATION_NAME,VACATION_BEGIN_DATE,VACATION_END_DATE,VACATION_COMMENTS, CREATE_NAME,CREATE_TIME,UPDATE_NAME,UPDATE_TIME "
    		+ " from PUB_CONFIG_VACATION where VACATION_ID = #{id}")
    ConfigVacationBean selectConfigVacationById(@Param("id") String id);

    /**
     * 添加
     *
     * @param configVacationBean
     * @return
     */
    void insertConfigVacation(ConfigVacationBean configVacationBean);

    /**
     * 校验字段内容重复
     */
    @Select("select count(1) from PUB_CONFIG_VACATION where ${key} = #{val}")
    Integer check(@Param("key") String key, @Param("val") String val);

    /**
     * 校验字段内容重复-排除当前记录
     */
    @Select("select count(1) from PUB_CONFIG_VACATION where ${key} = #{val} and VACATION_ID <> #{id}")
    Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);

    /**
     * 修改
     *
     * @param configVacationBean
     * @return
     */
    Integer updateConfigVacation(ConfigVacationBean configVacationBean);
}
