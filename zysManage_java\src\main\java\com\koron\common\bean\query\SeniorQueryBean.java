package com.koron.common.bean.query;

/**
 * 高级查询类
 * 
 * <AUTHOR>
 *
 */
public class SeniorQueryBean {
	private String fieldName;// 字段名
	private String operator;// 操作符
	private String logicalValue;// 逻辑值
	private String queryValue;// 查询值

	public String getFieldName() {
		return fieldName;
	}

	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getLogicalValue() {
		return logicalValue;
	}

	public void setLogicalValue(String logicalValue) {
		this.logicalValue = logicalValue;
	}

	public String getQueryValue() {
		return queryValue;
	}

	public void setQueryValue(String queryValue) {
		this.queryValue = queryValue;
	}

}
