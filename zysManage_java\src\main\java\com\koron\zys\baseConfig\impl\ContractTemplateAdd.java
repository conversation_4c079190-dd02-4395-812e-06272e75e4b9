package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ContractTemplateBean;
import com.koron.zys.baseConfig.mapper.BaseReceiptAccessoryMapper;
import com.koron.zys.baseConfig.mapper.ContractTemplateMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;

public class ContractTemplateAdd implements ServerInterface{

	@Override
	@ValidationKey(clazz = ContractTemplateBean.class,method = "insert")
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			ContractTemplateBean bean = JsonUtils.objectToPojo(req.getData(),ContractTemplateBean.class);
			ContractTemplateMapper mapper = factory.getMapper(ContractTemplateMapper.class);
			bean.setId(new ObjectId().toHexString());
			bean.setCreateName(userInfo.getUserInfo().getName());
			bean.setCreateTime(CommonUtils.getCurrentTime());
			mapper.templateAdd(bean);
			BaseReceiptAccessoryMapper accessoryMapper = factory.getMapper(BaseReceiptAccessoryMapper.class);
			if(!StringUtils.isBlank(bean.getTempId()))
				accessoryMapper.updateAccessoryReceiptId(bean.getId(), bean.getTempId());
		}catch(Exception e){
			logger.error("操作失败", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
