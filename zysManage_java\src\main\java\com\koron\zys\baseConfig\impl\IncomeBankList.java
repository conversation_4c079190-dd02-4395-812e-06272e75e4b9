package com.koron.zys.baseConfig.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.IncomeBankBean;
import com.koron.zys.baseConfig.mapper.IncomeBankMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import java.util.List;

/**
 * <AUTHOR>
 * 查询进账银行信息
 */
public class IncomeBankList implements ServerInterface {

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            IncomeBankMapper mapper = factory.getMapper(IncomeBankMapper.class);
            IncomeBankBean bean = JsonUtils.objectToPojo(req.getData(), IncomeBankBean.class);
            if(bean.getPage()!=null&&bean.getPageCount()!=null){
                PageHelper.startPage(bean.getPage(), bean.getPageCount());
            }
            List<IncomeBankBean> list = mapper.select(bean);
            MessageBean<PageInfo> msg = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
            msg.setData(new PageInfo(list));
            return msg;
        } catch (Exception e) {
            logger.error("查询进账银行信息失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "查询进账银行信息失败", void.class);
        }
    }
}
