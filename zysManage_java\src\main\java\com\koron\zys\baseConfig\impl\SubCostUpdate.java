package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.SubCostBean;
import com.koron.zys.baseConfig.mapper.SubCostMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

public class SubCostUpdate implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub
		try {
			SubCostBean bean = JsonUtils.objectToPojo(req.getData(), SubCostBean.class);
			SubCostMapper mapper = factory.getMapper(SubCostMapper.class);
			bean.setUpdateInfo(userInfo);
			mapper.update(bean);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "操作成功", void.class);
	}

}
