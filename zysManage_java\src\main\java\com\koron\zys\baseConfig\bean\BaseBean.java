package com.koron.zys.baseConfig.bean;

import java.util.Date;

import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.DateUtils;
import com.koron.util.Tools;

/*
 * 公共属性
 */
public class BaseBean {
	/**
	 * 主键ID
	 */
	private String id;
	
	/*
	 * 预留字段
	 */
	private String tenantId;
	
	/*
	 * 建立时间
	 */
	private String createTime;
	
	/*
	 * 建立人
	 */
	private String createName;
	
	/*
	 * 建立人ID
	 */
	private String createAccount;
	
	/*
	 * 最后修改时间
	 */
	private String updateTime;
	
	/*
	 * 最后修改人
	 */
	private String updateName;
	
	/*
	 * 最后修改人ID
	 */
	private String updateAccount;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public String getCreateAccount() {
		return createAccount;
	}

	public void setCreateAccount(String createAccount) {
		this.createAccount = createAccount;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public String getUpdateAccount() {
		return updateAccount;
	}

	public void setUpdateAccount(String updateAccount) {
		this.updateAccount = updateAccount;
	}

	public void setCreateInfo(UserInfoBean userInfo) {
		setId(Tools.getObjectId());
		setCreateAccount(userInfo.getUserInfo().getAcount());
		setCreateName(userInfo.getUserInfo().getName());
		setTenantId(userInfo.getCurWaterCode());
		setCreateTime(DateUtils.parseDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
	}
	
	public void setUpdateInfo(UserInfoBean userInfo) {
		setUpdateAccount(userInfo.getUserInfo().getAcount());
		setUpdateName(userInfo.getUserInfo().getName());
		setUpdateTime(DateUtils.parseDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
	}
	
	
}
