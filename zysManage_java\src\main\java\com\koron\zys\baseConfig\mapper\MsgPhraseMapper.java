package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.baseConfig.bean.MsgPhraseBean;
import com.koron.zys.baseConfig.bean.PhraseFieldBean;
import org.apache.ibatis.annotations.Param;

public interface MsgPhraseMapper {

	List<MsgPhraseBean> selectMsgPhrase();
	
	int insertMsgPhraseList(@Param("list") List<MsgPhraseBean> list);
	
	int deleteAllMsgPhrase();

	List<PhraseFieldBean> phraseFieldQuery(@Param("phraseNo") String phraseNo);
}
