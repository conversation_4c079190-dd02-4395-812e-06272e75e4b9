package com.koron.common.web.mapper;

import com.koron.common.bean.PubDepartmentBean;
import com.koron.common.bean.StaffBean;
import com.koron.common.bean.query.StaffQueryBean;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PubDepartmentNewMapper {

    PubDepartmentBean selectById(@Param("id") String id);

    int insert(PubDepartmentBean record);

    int updateByPrimaryKeySelective(PubDepartmentBean record);

    PubDepartmentBean selectSupId(@Param("code") String parentCode);

    void updateParentId(@Param("parentId") String parentId,@Param("id") String id);

    @Delete("TRUNCATE TABLE tblstaff")
    void deleteAllStaff();

    Map getDeptByCode(@Param("code") String code);

    void insertStaffByMap(Map<String, Object> item);

    List<PubDepartmentBean> list(@Param("id") String id,@Param("type") String type);

    List<Map> listStaff(@Param("departmentCode") String departmentCode,@Param("name") String name);

    List<StaffBean> selectList(StaffQueryBean query);

    StaffBean selectByCode(@Param("code") String code);
}
