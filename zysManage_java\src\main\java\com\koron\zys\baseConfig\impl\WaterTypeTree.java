package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.WaterTypeMapper;
import com.koron.zys.baseConfig.queryBean.WaterTypeQueryBean;
import com.koron.zys.baseConfig.vo.WaterTypeVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.TreeBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

/**
 * 用水类型树
 *
 * <AUTHOR>
 */
public class WaterTypeTree implements ServerInterface {
    private static Logger logger = LoggerFactory.getLogger(WaterTypeTree.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        MessageBean<TreeBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", TreeBean.class);
        try {
            WaterTypeMapper mapper = factory.getMapper(WaterTypeMapper.class);

            WaterTypeQueryBean selectBean = new WaterTypeQueryBean();
            selectBean.setStatus(1);//只查询启用的
            selectBean.setWaterTypeNo("all");
            List<WaterTypeVO> list = mapper.findWaterType(selectBean);
            TreeBean treeBean = new TreeBean();
            //创建根目录
            treeBean.setId("0");
            treeBean.setCode("");
            treeBean.setName("根目录");
            treeBean.setParent("");
            treeBean.setIsParent(true);
            //递归下级目录
            recTree(list, treeBean);

            info.setCode(Constant.MESSAGE_INT_SUCCESS);
            info.setDescription("success");
            info.setData(treeBean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_DBFAIL);
            info.setDescription("数据库异常");
            logger.error("数据库异常", e);
            factory.close(false);
        }
        return info;
    }

    /**
     * 递归查询下级目录树
     *
     * @param list
     * @param treeBean
     */
    private void recTree(List<WaterTypeVO> list, TreeBean treeBean) {
        for (WaterTypeVO bean : list) {
            //如果code是以父级开头，且长度多2位，说明这是他的下级
            if (bean.getWaterTypeNo().startsWith(treeBean.getCode()) && bean.getWaterTypeNo().length() == treeBean.getCode().length() + 2) {
                TreeBean b = new TreeBean();
                b.setId(bean.getId() + "");
                b.setCode(bean.getWaterTypeNo());
                b.setName(bean.getWaterTypeName());
                b.setParent(treeBean.getId() + "");
                b.setIsParent(false);
                treeBean.setIsParent(true);
                treeBean.getChildren().add(b);
                recTree(list, b); //递归循环下级目录
            }
        }
    }
}
