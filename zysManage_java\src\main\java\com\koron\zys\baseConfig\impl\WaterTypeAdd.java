package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.WaterTypeBean;
import com.koron.zys.baseConfig.mapper.WaterTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;

/**
 * 用水类型-添加
 *
 * <AUTHOR>
 */
public class WaterTypeAdd implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(WaterTypeAdd.class);

    @Override
    @ValidationKey(clazz = WaterTypeBean.class,method = "insert")
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            WaterTypeMapper mapper = factory.getMapper(WaterTypeMapper.class);
            WaterTypeBean bean = JsonUtils.objectToPojo(req.getData(), WaterTypeBean.class);
            if (("0").equals(bean.getParentId())){
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "不能在根目录下添加节点", void.class);
            }
            // 校验字段重复
//            if (mapper.check("water_type_name", bean.getWaterTypeName()) > 0) {
//                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "用水类型名：" + bean.getWaterTypeName() + "的信息已存在。", void.class);
//            }
            bean.setCreateInfo(userInfo);
            bean.setGroupCode(userInfo.getCurWaterCode());
            handCode(mapper, userInfo, bean);
            mapper.saveWaterType(bean);
        } catch (Exception e) {
            logger.error("用水类型添加失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "用水类型添加失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }

    /**
     * 自动生成分级code
     *
     * @param mapper
     * @param bean
     */
    public static void handCode(WaterTypeMapper mapper, UserInfoBean userInfo, WaterTypeBean bean) {
        //生成code, 规则2位一级，下级自动继承上级
        String parentCode = "";
        WaterTypeBean parentBean = null;
        if (StringUtils.isNotBlank(bean.getParentId()) && !bean.getParentId().equals("0")) {
            parentBean = mapper.findWaterTypeById(bean.getParentId()); //通过id查询数据
            parentCode = parentBean.getWaterTypeNo();
        }
        //取父级目录下最大的下级目录
        String maxCode = mapper.findMaxChild(parentCode);//通过行政区域编号查询
        if (StringUtils.isBlank(maxCode)) {//如果未找到，说明下面没有子级，给初始值
            maxCode = parentCode + "00";
        }
        //未位加1
        long last5 = Long.parseLong("1" + maxCode.substring(maxCode.length() - 2)) + 1;
        maxCode = maxCode.substring(0, maxCode.length() - 2) + (last5 + "").substring(1);
        //重设code
        bean.setWaterTypeNo(maxCode);

//		SelectBean selectBean = mapper.findWaterTypeByCode(parentCode+"00001");
//		if(selectBean!=null){
//			//bean.setIsLeaf(selectBean.getIsLeaf());
//		}else{
//			bean.setIsLeaf(1);
//		}

        bean.setIsLeaf(1);//新增加的数据，都是叶结点
        //自动更新其上级为目录
        if (parentBean != null) {
            parentBean.setIsLeaf(0);
            parentBean.setUpdateInfo(userInfo);
            mapper.updateWaterType(parentBean);
        }
    }

}
