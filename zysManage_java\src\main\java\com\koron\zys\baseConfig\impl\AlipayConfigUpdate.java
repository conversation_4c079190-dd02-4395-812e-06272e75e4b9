package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.AlipayConfigBean;
import com.koron.zys.baseConfig.mapper.AlipayConfigMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

/**
 * 支付宝配置-编辑
 *
 * <AUTHOR>
 */
public class AlipayConfigUpdate implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(AlipayConfigUpdate.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            AlipayConfigMapper mapper = factory.getMapper(AlipayConfigMapper.class);
            AlipayConfigBean bean = JsonUtils.objectToPojo(req.getData(), AlipayConfigBean.class);
            bean.setUpdateInfo(userInfo);
            mapper.updateAlipayConfig(bean);
        } catch (Exception e) {
            logger.error("修改支付宝配置失败", e);
            return MessageBean.create(Constant.ILLEGAL_PARAMETER, "修改支付宝配置失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}