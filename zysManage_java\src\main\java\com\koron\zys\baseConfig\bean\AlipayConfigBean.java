package com.koron.zys.baseConfig.bean;

/**
 * 支付宝配置
 *
 * <AUTHOR>
 */
public class AlipayConfigBean extends BaseBean {
    /**
     * 水司编号
     */
    private String companyNo;
    /**
     * 机构清算单位编号
     */
    private String orgNo;
    /**
     * 机构英文名
     */
    private String orgName;
    /**
     * 机构英文简称
     */
    private String orgEnShortName;
    /**
     * 是否自由缴
     */
    private Integer freePay;
    /**
     * SFTP地址
     */
    private String sftpIp;
    /**
     * SFTP端口
     */
    private String sftpPort;
    /**
     * SFTP用户
     */
    private String sftpUserName;
    /**
     * SFTP密码
     */
    private String sftpUserPsw;
    /**
     * 出账最后日期
     */
    private String lastDate;
    /**
     * 出账规则
     */
    private Integer ruleType;
    /**
     * 出账值
     */
    private Integer ruleValue;


	public String getCompanyNo() {
		return companyNo;
	}

	public void setCompanyNo(String companyNo) {
		this.companyNo = companyNo;
	}

	public String getOrgNo() {
		return orgNo;
	}

	public void setOrgNo(String orgNo) {
		this.orgNo = orgNo;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgEnShortName() {
		return orgEnShortName;
	}

	public void setOrgEnShortName(String orgEnShortName) {
		this.orgEnShortName = orgEnShortName;
	}

	public Integer getFreePay() {
		return freePay;
	}

	public void setFreePay(Integer freePay) {
		this.freePay = freePay;
	}

	public String getSftpIp() {
		return sftpIp;
	}

	public void setSftpIp(String sftpIp) {
		this.sftpIp = sftpIp;
	}

	public String getSftpPort() {
		return sftpPort;
	}

	public void setSftpPort(String sftpPort) {
		this.sftpPort = sftpPort;
	}

	public String getSftpUserName() {
		return sftpUserName;
	}

	public void setSftpUserName(String sftpUserName) {
		this.sftpUserName = sftpUserName;
	}

	public String getSftpUserPsw() {
		return sftpUserPsw;
	}

	public void setSftpUserPsw(String sftpUserPsw) {
		this.sftpUserPsw = sftpUserPsw;
	}

	public String getLastDate() {
		return lastDate;
	}

	public void setLastDate(String lastDate) {
		this.lastDate = lastDate;
	}

	public Integer getRuleType() {
		return ruleType;
	}

	public void setRuleType(Integer ruleType) {
		this.ruleType = ruleType;
	}

	public Integer getRuleValue() {
		return ruleValue;
	}

	public void setRuleValue(Integer ruleValue) {
		this.ruleValue = ruleValue;
	}
}
