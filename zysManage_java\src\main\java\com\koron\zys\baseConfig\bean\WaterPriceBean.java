package com.koron.zys.baseConfig.bean;

import java.util.List;

import com.koron.util.Check;

/**
 * 用水价格表
 * <AUTHOR>
 *
 */
public class WaterPriceBean extends BaseBean {
	

	/**
	 * 用水类型
	 */
	@Check(name = "用水类型", notEmpty = true)
    private String waterTypeId;
    /**
     * 生效日期
     */
	@Check(name = "生效日期", notEmpty = true)
    private String effectiveDate;
    /**
     * 截止日期
     */
    private String expiryDate;
    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态
     */
    private String remark;
    
    private String processInstanceId;
    private String processState;
    private String processName;
    private String processHandleMan;
    /**
     * 明细
     */
    private List<WaterPriceDetailBean> details;
    

    private String tempId;
    
    
	public String getTempId() {
		return tempId;
	}

	public void setTempId(String tempId) {
		this.tempId = tempId;
	}

	public String getProcessInstanceId() {
		return processInstanceId;
	}

	public void setProcessInstanceId(String processInstanceId) {
		this.processInstanceId = processInstanceId;
	}

	public String getProcessState() {
		return processState;
	}

	public void setProcessState(String processState) {
		this.processState = processState;
	}

	public String getProcessName() {
		return processName;
	}

	public void setProcessName(String processName) {
		this.processName = processName;
	}

	public String getProcessHandleMan() {
		return processHandleMan;
	}

	public void setProcessHandleMan(String processHandleMan) {
		this.processHandleMan = processHandleMan;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}


	public String getWaterTypeId() {
		return waterTypeId;
	}

	public void setWaterTypeId(String waterTypeId) {
		this.waterTypeId = waterTypeId;
	}

	public String getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public String getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(String expiryDate) {
		this.expiryDate = expiryDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public List<WaterPriceDetailBean> getDetails() {
		return details;
	}

	public void setDetails(List<WaterPriceDetailBean> details) {
		this.details = details;
	}

}
