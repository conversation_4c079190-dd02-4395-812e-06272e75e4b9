package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterBoreBean;
import com.koron.zys.baseConfig.mapper.MeterBoreMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

/**
 * 水表口径-添加
 * <AUTHOR>
 *
 */
public class MeterBoreAdd implements ServerInterface{

	private static Logger logger = LoggerFactory.getLogger(MeterBoreAdd.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		MeterBoreMapper mapper = factory.getMapper(MeterBoreMapper.class);
		MeterBoreBean bean = new MeterBoreBean();
		try {
			bean = JsonUtils.objectToPojo(req.getData(), MeterBoreBean.class);
		} catch (Exception e) {
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		// 校验字段重复
		if (mapper.check("bore_name", bean.getBoreName()) > 0) {
			return MessageBean.create(Constant.ILLEGAL_PARAMETER,"水表口径名称：" + bean.getBoreName() + "的信息已存在。", void.class);
		}
		bean.setId(mapper.getBoreId().toString());;
		bean.setCreateName(userInfo.getUserInfo().getName());
		bean.setCreateTime(CommonUtils.getCurrentTime());
		bean.setCreateAccount(userInfo.getUserInfo().getAcount());
		
		mapper.insertMeterBore(bean);
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
}