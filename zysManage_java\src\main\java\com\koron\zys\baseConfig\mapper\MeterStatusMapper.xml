<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.MeterStatusMapper">
	
 		<select id="selectMeterStatusList" parameterType="com.koron.zys.baseConfig.queryBean.MeterStatusQueryBean"
 		resultType="com.koron.zys.baseConfig.vo.MeterStatusVO" >
		select xx.id,xx.status_name,xx.status_value,xx.comments,xx.sort_no,case when xx.status=1 then '启用' else '停用' end status,case when xx.abnormal_flag=1 then '是' else '否' end abnormalFlag
		from PUB_METER_STATUS xx
		  where 1=1
	    	<if test="statusName != null and statusName != ''">
	    		and xx.status_name  LIKE  '%' || #{statusName} || '%'
	    	</if>
	    order by xx.sort_no asc  
	</select>
	
	<select id="selectComboBox" resultType="com.koron.zys.baseConfig.vo.SelectVO">
	SELECT STATUS_VALUE ID, STATUS_NAME NAME
		FROM PUB_METER_STATUS XX
		  WHERE XX.STATUS = 1
		  <if test="abnormalFlag != null and abnormalFlag!= 0">
		  	AND XX.ABNORMAL_FLAG = 1
		  </if>
	    ORDER BY XX.SORT_NO,XX.STATUS_VALUE
	</select>
	 
	<select id="selectMeterStatusById" resultType="com.koron.zys.baseConfig.bean.MeterStatusBean">
		select *
		from PUB_METER_STATUS
		where status_id = #{statusId}
	</select>
	
	 	<insert id="insertMeterStatus" parameterType="com.koron.zys.baseConfig.bean.MeterStatusBean">
		insert into PUB_METER_STATUS (status_id,status_name,abnormal_flag, 
		comments,status, sort_no, create_time, create_name)
		values
		(
		#{statusId,jdbcType=VARCHAR},
		#{statusName,jdbcType=VARCHAR},
		#{abnormalFlag,jdbcType=INTEGER},
		#{comments,jdbcType=VARCHAR},
		#{status,jdbcType=INTEGER},
		#{sortNo,jdbcType=INTEGER},		
		date_format(#{createTime},'%Y-%m-%d %T'),
		#{createName,jdbcType=VARCHAR}
		)
	</insert>
	
		<update id="updateMeterStatus" parameterType="com.koron.zys.baseConfig.bean.MeterStatusBean">
		update PUB_METER_STATUS
		set status_name = #{statusName,jdbcType=VARCHAR},	
		    abnormal_flag = #{abnormalFlag,jdbcType=INTEGER},	 			
			comments = #{comments,jdbcType=VARCHAR},
			status = #{status,jdbcType=INTEGER},
			sort_no = #{sortNo,jdbcType=INTEGER},		
			update_time=date_format(#{updateTime},'%Y-%m-%d %T'),
			update_name = #{updateName,jdbcType=VARCHAR}
		    where status_id = #{statusId}
	</update>
	
	
</mapper>