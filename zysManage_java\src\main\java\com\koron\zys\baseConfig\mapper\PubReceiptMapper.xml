<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.PubReceiptMapper">
  
	<resultMap id="resultMap" type="com.koron.zys.baseConfig.bean.PubReceiptBean">
		<id column="RECEIPT_ID" jdbcType="VARCHAR" property="receiptId" />
		<result column="RECEIPT_NO" jdbcType="VARCHAR" property="receiptNo" />
		<result column="RECEIPT_NAME" jdbcType="VARCHAR" property="receiptName" />
		<result column="CREATE_TIME" jdbcType="DATE" property="createTime" />
		<result column="CREATE_NAME" jdbcType="VARCHAR" property="createName" />
		<result column="UPDATE_TIME" jdbcType="DATE" property="updateTime" />
		<result column="UPDATE_NAME" jdbcType="VARCHAR" property="updateName" />
		<result column="PROCESS_CODE" jdbcType="VARCHAR" property="processCode" />
		<result column="WORKFLOW_INTERFACE" jdbcType="VARCHAR" property="workflowInterface" />		
		<result column="URL" jdbcType="INTEGER" property="url" />		
	</resultMap>
	
	<delete id="deleteProcessNode" parameterType="java.lang.String">
		DELETE FROM BASE_PROCESS_NODE WHERE RECEIPT_TYPE = #{receiptType,jdbcType=VARCHAR}
	</delete>
	
	<insert id="insertProcessNode" parameterType="com.koron.zys.serviceManage.bean.ProcessNodeBean">
		INSERT INTO BASE_PROCESS_NODE (PROCESS_NODE_ID, PROCESS_ID, NODE_NAME,RECEIPT_TYPE,NODE_CODE,NODE_ID)
		VALUES (#{processNodeId,jdbcType=VARCHAR}, #{processId,jdbcType=VARCHAR}, #{nodeName,jdbcType=VARCHAR}, 
		#{receiptType,jdbcType=VARCHAR}, #{nodeCode,jdbcType=VARCHAR}, #{nodeId,jdbcType=VARCHAR})  
	</insert>
	
	<sql id="resultSql">
		t.CREATE_NAME,
		t.CREATE_TIME,
		t.RECEIPT_ID,
		t.RECEIPT_NAME,
		t.RECEIPT_NO,
		t.PROCESS_CODE,
		t.UPDATE_NAME,
		t.UPDATE_TIME,
		t.URL,
		t.WORKFLOW_INTERFACE 
	</sql>
	<sql id="resultSql1">
		t.CREATE_NAME,
		t.CREATE_TIME,
		t.RECEIPT_ID,
		t.RECEIPT_NAME,
		t.RECEIPT_NO,
		t.PROCESS_CODE,
		t.UPDATE_NAME,
		t.UPDATE_TIME,
		t.URL,
		t.WORKFLOW_INTERFACE,
		t.start_access
	</sql>
	
	<select id="listProcessNodeByBillType" parameterType="java.util.Map" resultType="com.koron.zys.serviceManage.bean.ProcessNodeBean">
		SELECT * FROM BASE_PROCESS_NODE
		WHERE RECEIPT_TYPE = #{receiptType,jdbcType=VARCHAR}
		<if test="nodeCode != null and nodeCode != ''">
			AND NODE_CODE = #{nodeCode}
		</if>
		ORDER BY sort_no
	</select>
	
	<select id="selectProcessInstanceId" resultType="string">
		SELECT process_instance_id FROM ${billType} WHERE id = #{billId}
	</select>
	
	<insert id="saveReceipt" parameterType="com.koron.zys.serviceManage.bean.ReceiptBean">
		insert into pub_receipt
		(receipt_id,receipt_no,receipt_name,workflow_Interface,url,create_time,create_name,process_code,start_access)
		values
		(#{receiptId,jdbcType=VARCHAR},#{receiptNo,jdbcType=VARCHAR},#{receiptName,jdbcType=VARCHAR},
		#{workflowInterface,jdbcType=VARCHAR},#{url,jdbcType=VARCHAR},
		date_format(#{createTime,jdbcType=VARCHAR},'%Y-%m-%d %T'),#{createName,jdbcType=VARCHAR}, #{processCode,jdbcType=VARCHAR},#{startAccess,jdbcType=VARCHAR})
	</insert>
	
	<update id="updateReceipt" parameterType="com.koron.zys.serviceManage.bean.ReceiptBean">
		update pub_receipt
		<trim prefix="SET" suffixOverrides=",">
			
			workflow_Interface=#{workflowInterface},
			
			<if test="receiptNo != null and receiptNo != ''">
				receipt_no=#{receiptNo},
			</if>
			<if test="receiptName != null and receiptName != ''">
				receipt_name=#{receiptName},
			</if>
			
			<if test="url != null and url != ''">
				url=#{url},
			</if>
			<if test="updateTime != null and updateTime != ''">
				update_time=date_format(#{updateTime},'%Y-%m-%d %T'),
			</if>
			<if test="updateName != null and updateName != ''">
				update_name=#{updateName},
			</if>
			<if test="processCode != null and processCode != ''">
				process_code = #{processCode},
			</if>
			<if test="startAccess != null and startAccess != ''">
				start_access = #{startAccess},
			</if>
		</trim>
		where receipt_id = #{receiptId}
	</update>
	
	<select id="getBillWorkflowInfo" parameterType="string" resultType="java.util.HashMap">
		select 
		ID,
		BILL_NO AS billNo,
		PROCESS_INSTANCE_ID as processInstanceId,
		PROCESS_STATE as processState
		FROM ${billType}
		WHERE ID = #{billId,jdbcType=VARCHAR}
	</select>
	<update id="updateBillWorkflowInfo">
		UPDATE ${billType}
		SET 
		PROCESS_INSTANCE_ID = #{processInstanceId,jdbcType=VARCHAR},
		PROCESS_STATE = #{processState,jdbcType=VARCHAR},
		PROCESS_NAME = #{nextNodeName,jdbcType=VARCHAR},
		PROCESS_HANDLE_MAN = #{nextCandidateUsers,jdbcType=VARCHAR}
		WHERE ID = #{billId,jdbcType=VARCHAR}
	</update>
 	<select id="selectPubReceiptList" resultMap="resultMap" parameterType="com.koron.zys.baseConfig.queryBean.PubReceiptQueryBean">
		select <include refid="resultSql"/> from pub_receipt t
		<where>
			<if test="receiptId != null">
				AND RECEIPT_ID = #{receiptId}
			</if>
			<if test="receiptNo != null">
				AND RECEIPT_NO = #{receiptNo}
			</if>
			<if test="receiptName != null and receiptName != ''">
				AND RECEIPT_NAME = #{receiptName}
			</if>
			<if test="processCode != null and processCode != ''">
				AND PROCESS_CODE = #{processCode}
			</if>
			<if test="startAccess != null and startAccess != ''">
				AND start_access = #{startAccess}
			</if>
			<if test="prefix != null and prefix != ''">
				AND left(PROCESS_CODE, LENGTH(#{prefix})) = #{prefix}
			</if>
			<if test="fuzzyQuery != null and fuzzyQuery != ''">
				AND (RECEIPT_NAME LIKE CONCAT ('%', #{fuzzyQuery}, '%') OR RECEIPT_NO LIKE CONCAT('%', #{fuzzyQuery}, '%'))
			</if>
		</where>
		order by create_time desc
	</select>
	
	<select id="selectByProcessCode" resultMap="resultMap">
		select <include refid="resultSql"/> from pub_receipt t where process_code = #{processCode}
	</select>
	
	<select id="selectPubReceipt" resultMap="resultMap" >
		select <include refid="resultSql"/> from pub_receipt t where RECEIPT_NO = #{receipt}
	</select>
	
	<select id="selectById" resultMap="resultMap" >
		select <include refid="resultSql1"/> from pub_receipt t where RECEIPT_ID = #{receiptId}
	</select>
	
	<select id="getWorkflowApp" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT WORKFLOW_APP FROM PUB_COMPANY WHERE COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
	</select>
</mapper>