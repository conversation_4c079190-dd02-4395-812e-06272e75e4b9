package com.koron.zys.baseConfig.bean;

/**
 * 水表口径
 * 
 * <AUTHOR>
 *
 */
public class MeterBoreBean {

	private String id;

	private String boreName;

	private Integer boreValue;

	private Integer maxFlux;

	private Integer meterPlaces;

	private Integer oldChangeCycle;

	private Integer newChangeCycle;

	private String comments;

	private Integer status;

	private Integer sortNo;

	/**
	 * 创建时间
	 */
	private String createTime;

	/**
	 * 创建人
	 */
	private String createName;
	/**
	 * 修改时间
	 */
	private String updateTime;
	/**
	 * 修改人
	 */
	private String updateName;
	private String updateAccount;
	private String createAccount;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUpdateAccount() {
		return updateAccount;
	}

	public void setUpdateAccount(String updateAccount) {
		this.updateAccount = updateAccount;
	}

	public String getCreateAccount() {
		return createAccount;
	}

	public void setCreateAccount(String createAccount) {
		this.createAccount = createAccount;
	}

	public String getBoreName() {
		return boreName;
	}

	public Integer getBoreValue() {
		return boreValue;
	}

	public Integer getMaxFlux() {
		return maxFlux;
	}

	public Integer getMeterPlaces() {
		return meterPlaces;
	}

	public Integer getOldChangeCycle() {
		return oldChangeCycle;
	}

	public Integer getNewChangeCycle() {
		return newChangeCycle;
	}

	public String getComments() {
		return comments;
	}

	public Integer getStatus() {
		return status;
	}

	public Integer getSortNo() {
		return sortNo;
	}

	public String getCreateTime() {
		return createTime;
	}

	public String getCreateName() {
		return createName;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setBoreName(String boreName) {
		this.boreName = boreName;
	}

	public void setBoreValue(Integer boreValue) {
		this.boreValue = boreValue;
	}

	public void setMaxFlux(Integer maxFlux) {
		this.maxFlux = maxFlux;
	}

	public void setMeterPlaces(Integer meterPlaces) {
		this.meterPlaces = meterPlaces;
	}

	public void setOldChangeCycle(Integer oldChangeCycle) {
		this.oldChangeCycle = oldChangeCycle;
	}

	public void setNewChangeCycle(Integer newChangeCycle) {
		this.newChangeCycle = newChangeCycle;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public void setSortNo(Integer sortNo) {
		this.sortNo = sortNo;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

}
