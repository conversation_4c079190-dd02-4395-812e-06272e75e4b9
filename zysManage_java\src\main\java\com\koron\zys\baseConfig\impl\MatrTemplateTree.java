package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MatrBean;
import com.koron.zys.baseConfig.bean.MatrCodeBean;
import com.koron.zys.baseConfig.mapper.MatrMapper;
import com.koron.zys.baseConfig.vo.MatrCodeTreeVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.MatrCodeMapper;
import com.koron.zys.serviceManage.queryBean.matrCodeQueryBean;
import com.koron.util.Constant;

/**
 * 物料编码树-用于材料定价左侧树结构
 *
 * <AUTHOR>
 * 2020年4月2日
 */
public class MatrTemplateTree implements ServerInterface {
    private static Logger logger = LoggerFactory.getLogger(MatrTemplateTree.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        MessageBean<MatrCodeTreeVO> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", MatrCodeTreeVO.class);
        try {
            MatrCodeMapper mapper = factory.getMapper(MatrCodeMapper.class, "_default");
            matrCodeQueryBean selectBean = new matrCodeQueryBean();
            selectBean.setClassCode("");
            // 只查询目录
            selectBean.setIsLeaf(0);
            List<MatrCodeBean> list = mapper.selectMatrCodeList(selectBean);
            MatrMapper matrMapper = factory.getMapper(MatrMapper.class);
            List<MatrBean> matrPrices = matrMapper.selectMatrPrice();
            for (MatrBean p : matrPrices) {
                for (MatrCodeBean matr : list) {
                    if (p.getMatrNo().equals(matr.getMatrNo())) {
                        matr.setPrice(p.getMatrPrice());
                        matr.setMatrPriceId(p.getId());
                        break;
                    }
                }
            }
            MatrCodeTreeVO treeBean = new MatrCodeTreeVO();
            //创建根目录
            treeBean.setId("0");
            treeBean.setClassCode("");
            treeBean.setMatrName("所有目录");
            treeBean.setParent("");
            treeBean.setIsParent(true);
            //递归下级目录
            recTree(list, treeBean);
            info.setCode(Constant.MESSAGE_INT_SUCCESS);
            info.setDescription("success");
            info.setData(treeBean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_DBFAIL);
            info.setDescription("数据库异常");
            logger.error("数据库异常", e);
            factory.close(false);
        }
        return info;
    }

    /**
     * 递归查询下级目录树
     *
     * @param list
     * @param treeBean
     */
    private void recTree(List<MatrCodeBean> list, MatrCodeTreeVO treeBean) {
        for (MatrCodeBean bean : list) {
            //如果code是以父级开头，且长度多5位，说明这是他的下级
            if ("".equals(treeBean.getClassCode()) || bean.getClassCode().startsWith(treeBean.getClassCode()) && bean.getClassCode().length() == treeBean.getClassCode().length() + 5) {
                MatrCodeTreeVO b = new MatrCodeTreeVO();
                b.setId(bean.getId() + "");
                b.setMatrPriceId(bean.getMatrPriceId());
                b.setClassCode(bean.getClassCode());
                b.setMatrNo(bean.getMatrNo());
                b.setMatrName(bean.getMatrName());
                b.setMatrMode(bean.getMatrMode());
                b.setMatrUnit(bean.getMatrUnit());
                b.setMatrPrice(bean.getMatrPrice());
                b.setParent(treeBean.getId() + "");
                b.setIsParent(false);
                treeBean.setIsParent(true);
                treeBean.getChildren().add(b);
                recTree(list, b); //递归循环下级目录
            }
        }
    }
}
