package com.koron.common.bean;

import java.util.List;

import edu.emory.mathcs.backport.java.util.Arrays;

/**
 * 各表头标题列表
 * 
 * <AUTHOR>
 *
 */
public class CheckTitleListBean {
	@SuppressWarnings("unchecked")
	public static final List<String> wmeterList = Arrays.asList(new String[] { "水表编号", "厂家水表编号", "水表名称", "用户类型",
			"设备供应商", "协议名称", "表身码", "供电方式", "水司编码", "小区名称", "集中器", "采集器", "水表型号", "RTU", "通讯方式", "传感方式",
			"IWaterGateway", "IWaterGatewayPort", "精度等级","口径（mm）", "等级", "最小流量", "分界流量", "常用流量", "过载流量", "最大读数", "报警电压",
			"安装时间", "安装人员", "安装地点", "安装位置", "报废时间","报废原因", "水表用户号", "IMEI号", "IMSI号", "新表底码", "经纬度 (x,y)",
			"可用状态", "排序" });
	@SuppressWarnings("unchecked")
	public static final List<String> concentratorList = Arrays.asList(new String[] { "集中器编号", "集中器名称", "水司编号",
			"小区名称","设备供应商","传输协议","IWaterGateway","IWaterGatewayPort", "集中器型号", "序列号", "安装时间", "安装人员", "安装位置", "经纬度坐标", "可用状态", "排序" });

	@SuppressWarnings("unchecked")
	public static final List<String> collectorList = Arrays.asList(new String[] { "采集器编号", "采集器名称", "集中器编号", "水司编号"
			,"容量", "供电方式", "安装时间", "安装人员", "安装地址", "经纬度坐标", "可用状态", "排序" });

	@SuppressWarnings("unchecked")
	public static final List<String> rtuList = Arrays
			.asList(new String[] { "终端编号", "终端类型", "出厂编号", "行政区划", "组织架构","管理区域","制造商", "名称", "终端卡号", 
					"采集频率", "上报频率","终端型号", "供电类型", "安装时间", "安装人员", "安装位置", "可用状态", "备注", "排序字段" });

	@SuppressWarnings("unchecked")
	public static final List<String> simList = Arrays.asList(new String[] { "通讯卡号", "下次缴费日期", "设备类型", "设备编号" });

	public static final List<String> getList(int importType) {
		switch (importType) {
		case 1:
			return wmeterList;
		case 2:
			return concentratorList;
		case 3:
			return collectorList;
		case 4:
			return rtuList;
		case 5:
			return simList;
		default:
			return null;
		}
	}
}
