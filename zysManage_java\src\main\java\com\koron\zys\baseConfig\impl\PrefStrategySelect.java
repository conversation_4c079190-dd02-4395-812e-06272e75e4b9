package com.koron.zys.baseConfig.impl;

import java.util.List;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.PrefStrategyMapper;
import com.koron.zys.baseConfig.vo.SelectVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;

/**
 * 优惠策略下拉框
 * 
 * <AUTHOR>
 *
 */
public class PrefStrategySelect implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {
			PrefStrategyMapper mapper = factory.getMapper(PrefStrategyMapper.class);
			// 获取下拉框
			List<SelectVO> list = mapper.prefStrategySelect();
			info.setData(list);
			return info;
		} catch (Exception e) {
			logger.error("优惠策略查询失败" + e.getMessage(), e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "优惠策略查询失败", null);
		}

	}
}
