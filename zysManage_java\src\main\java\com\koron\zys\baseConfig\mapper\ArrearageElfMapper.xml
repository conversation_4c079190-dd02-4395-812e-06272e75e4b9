<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.ArrearageElfMapper">
    <!--添加  -->
    <insert id="insertArrearageElf" parameterType="com.koron.zys.baseConfig.bean.BaseArrearageElfBean">
        insert into base_arrearage_elf(id, rule_way, rule_value, penalty_before, penalty_later, notice_way, begin_time,
                                       end_time, tenant_id, create_time, create_name, create_account)
        values (#{id},
                #{ruleWay},
                #{ruleValue},
                #{penaltyBefore},
                #{penaltyLater},
                #{noticeWay},
                #{beginTime},
                #{endTime},
                #{tenantId},
                #{createTime},
                #{createName},
                #{createAccount})
    </insert>
</mapper>