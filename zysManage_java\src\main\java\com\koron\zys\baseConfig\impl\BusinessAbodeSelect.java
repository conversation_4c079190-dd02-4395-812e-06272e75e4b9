package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.BusinessAbodeMapper;
import com.koron.zys.baseConfig.queryBean.BankQueryBean;
import com.koron.zys.baseConfig.vo.SelectVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

/**
 * 营业所下拉框
 * 
 * <AUTHOR>
 *
 */
public class BusinessAbodeSelect implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
		try {		
			BusinessAbodeMapper mapper = factory.getMapper(BusinessAbodeMapper.class,"_default");
			
			String companyNo=userInfo.getCurWaterCode();
			if(StringUtils.isEmpty(companyNo)) {
				BankQueryBean selectBean = JsonUtils.objectToPojo(req.getData(), BankQueryBean.class);
            	companyNo=selectBean.getCompanyNo();
            }
			//获取下拉框
			List<SelectVO> list = mapper.businessAbodeSelect(companyNo);
			info.setData(list);
			return info;
		} catch (Exception e) {
			logger.error("营业所查询失败" + e.getMessage(), e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "营业所查询失败", null);
		}

	}
}
