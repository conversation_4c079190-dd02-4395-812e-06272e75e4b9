package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ProcessTimeBean;
import com.koron.zys.baseConfig.mapper.ProcessTimeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

public class ProcessTimeDelete implements ServerInterface{

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub
		try {
			ProcessTimeBean bean = JsonUtils.objectToPojo(req.getData(),ProcessTimeBean.class);
			ProcessTimeMapper mapper = factory.getMapper(ProcessTimeMapper.class);
			ProcessTimeBean timeBean = mapper.query(bean.getId());
			mapper.delete(bean);
			mapper.deleteRecord(timeBean);
		}catch(Exception e){
			logger.error("操作失败", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "操作失败", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
