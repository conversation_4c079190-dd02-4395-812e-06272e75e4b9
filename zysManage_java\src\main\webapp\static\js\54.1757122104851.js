webpackJsonp([54],{"+9io":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"engMaterManEdit"},[a("el-form",{ref:"engMaterManEditform",staticClass:"formBill-One",attrs:{model:t.formData,rules:t.rules,"label-width":"100px",inline:!0}},[a("el-form-item",{attrs:{label:"材料编号：",prop:"matrNo"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:t.formData.matrNo,callback:function(e){t.$set(t.formData,"matrNo",e)},expression:"formData.matrNo"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"材料名称：",prop:"matrName"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:t.formData.matrName,callback:function(e){t.$set(t.formData,"matrName",e)},expression:"formData.matrName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"材料规格：",prop:"matrMode"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:t.formData.matrMode,callback:function(e){t.$set(t.formData,"matrMode",e)},expression:"formData.matrMode"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"材料数量：",prop:"matrNum"}},[a("el-input",{attrs:{maxlength:"10",clearable:""},model:{value:t.formData.matrNum,callback:function(e){t.$set(t.formData,"matrNum",t._n(e))},expression:"formData.matrNum"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"材料单位：",prop:"matrUnit"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:t.formData.matrUnit,callback:function(e){t.$set(t.formData,"matrUnit",e)},expression:"formData.matrUnit"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"材料单价：",prop:"matrPrice"}},[a("el-input",{attrs:{type:"number",maxlength:"10",clearable:""},model:{value:t.formData.matrPrice,callback:function(e){t.$set(t.formData,"matrPrice",e)},expression:"formData.matrPrice"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"排序号：",prop:"sortNo"}},[a("el-input",{attrs:{placeholder:"排序号",maxlength:"6",clearable:""},model:{value:t.formData.sortNo,callback:function(e){t.$set(t.formData,"sortNo",t._n(e))},expression:"formData.sortNo"}})],1)],1)],1)},staticRenderFns:[]},i={name:"engMaterMan",components:{engMaterManEdit:a("VU/8")({name:"engMaterManEdit",data:function(){return{formData:{id:"",matrNo:"",matrName:"",matrMode:"",matrNum:"",matrUnit:"",matrPrice:"",parentId:"",classCode:"",sortNo:""},rules:{matrNo:[{required:!0,message:"请输入材料编号",trigger:"blur"}],matrName:[{required:!0,message:"请输入材料名称",trigger:"blur"}],matrMode:[{required:!0,message:"请输入材料规格",trigger:"blur"}],matrNum:[{required:!0,message:"请输入材料数量"},{type:"number",message:"材料数量必须为数字值"}],matrUnit:[{message:"请输入材料单位",trigger:"blur",required:!0}],matrPrice:[{message:"请输入材料单价",trigger:"blur",required:!0}],sortNo:[{message:"请输入排序号",trigger:"blur",required:!0},{type:"number",message:"排序号必须为数字值"}]}}},methods:{resetForm:function(){this.$refs.engMaterManEditform.resetFields()},submitForm:function(t,e){var a=this,r={};"MatrCodeAdd"===t?(this.formData.parentId=e,r={busicode:"MatrCodeAdd",data:this.formData}):r={busicode:"MatrCodeUpdate",data:this.formData},this.$api.fetch({params:r}).then(function(e){a.$message({showClose:!0,message:"保存成功",type:"success"}),"MatrCodeAdd"===t?a.$parent.getTreeDatas():a.$parent.init(),a.$parent.closeDialog()})},handleClose:function(){this.common.chargeObjectEqual(this,this.formData,"get","engMaterManEdit",this.$parent.closeDialog())},editData:function(t){this.formData=t}}},r,!1,null,null,null).exports,autoTree:a("yJVD").a},data:function(){return{EditVisible:!1,tableShow:!1,tableQuery:{page:1,pageCount:10,matrName:"",classCode:"",parentId:""},maxHeight:0,tableData:[],formData:{id:"",matrNo:"",matrName:"",matrMode:"",matrNum:"",matrUnit:"",classCode:"",matrPrice:"",parentId:""},crumbsData:{titleList:[{title:"基础配置",path:"/ChangeTables"},{title:"物料编码",method:function(){window.histroy.back()}}]},treeDatas:{tree:[{foreignkey:"全部",id:"222",_child:[]}],defaultProps:{label:"matrName",children:"children"},inputProp:{showSearch:!1,Inp_placeholder:"请输入节点"},sendTreeProp:["id","matrNo","matrName","matrMode","matrUnit","matrPrice","parent","idLeaf","isParent","classCode"],rootName:"总部",defaultOpen:{nodeKey:"id"}},classCode:"",parentId:"",formType:""}},mounted:function(){var t=this;this.getTreeDatas(),this.$nextTick(function(){t.common.changeTable(t,".engMaterMan .company-content",[])})},methods:{add:function(t){var e=this;if(this.EditVisible=!0,"add"===t)this.formType="MatrCodeAdd";else{this.formType="MatrCodeUpdate";var a={busicode:"MatrCodeQuery",data:{matrId:t.id}};this.$api.fetch({params:a}).then(function(t){e.$refs.engMaterManEdit.editData(t)})}},init:function(){var t=this,e={busicode:"MatrCodeList",data:this.tableQuery};this.$api.fetch({params:e}).then(function(e){console.log(e),t.tableData=e})},search:function(){this.tableQuery.page=1,this.init()},getTreeDatas:function(){var t=this;this.$api.fetch({params:{busicode:"MatrCodeTree",data:{}}}).then(function(e){t.treeDatas.tree=e,t.classCode=e.classCode,t.parentId=e.id,t.init()})},backTreeData:function(t){var e=this;this.tableQuery.classCode=t.classCode,this.parentId=t.id,this.tableQuery.parentId=t.id,this.tableQuery.page=1;var a={busicode:"MatrCodeList",data:this.tableQuery};this.$api.fetch({params:a}).then(function(t){e.tableData=t})},indexMethod:function(t){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(t+1)},handleSizeChange:function(t){this.tableQuery.pageCount=t,this.tableQuery.page=1,this.init()},handleCurrentChange:function(t){this.tableQuery.page=t,this.init()},closeDialog:function(){this.EditVisible=!1},handleClose:function(){this.$refs.engMaterManEdit.handleClose()},submitForm:function(){this.$refs.engMaterManEdit.submitForm(this.formType,this.parentId)}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},n={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"engMaterMan"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:t.crumbsData}}),t._v(" "),t.EditVisible?a("div",{staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.submitForm("engMaterManEditRuleForm")}}},[t._v("保存")]),t._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:t.handleClose}},[t._v("返回")])],1):a("div",{staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.add("add")}}},[t._v("添加")])],1)],1),t._v(" "),t.EditVisible?a("engMaterManEdit",{ref:"engMaterManEdit"}):t._e(),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.EditVisible,expression:"!EditVisible"}],staticClass:"company-content"},[a("div",{staticClass:"company-left"},[a("auto-tree",{attrs:{treeData:t.treeDatas},on:{sendTreeData:t.backTreeData}})],1),t._v(" "),a("div",{staticClass:"company-right engMaterManIndex"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,size:"mini",model:t.formData}},[a("el-form-item",{staticClass:"form-left"},[a("el-form-item",{attrs:{label:"材料名称："}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入材料名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search.apply(null,arguments)}},model:{value:t.tableQuery.matrName,callback:function(e){t.$set(t.tableQuery,"matrName",e)},expression:"tableQuery.matrName"}})],1),t._v(" "),a("el-form-item",[a("el-button",{staticClass:"searchBtn",attrs:{icon:"el-icon-search"},on:{click:t.search}})],1)],1)],1)],1),t._v(" "),a("div",{staticClass:"kl-table"},[t.tableShow?a("el-table",{staticClass:"wuserInfo-table",attrs:{"max-height":t.maxHeight,stripe:"",border:"",data:t.tableData.list}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:t.indexMethod}}),t._v(" "),a("el-table-column",{attrs:{prop:"matrNo",label:"材料编号","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"matrName",label:"材料名称","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"matrMode",label:"材料规格","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"matrNum",label:"材料数量","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"matrUnit",label:"材料单位","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"matrPrice",label:"材料单价","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"80px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return t.add(e.row)}}},[t._v("编辑")])]}}],null,!1,1819888711)})],1):t._e(),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":t.tableQuery.page,"page-sizes":[10,20,50,100,500],"page-size":t.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)])])],1)},staticRenderFns:[]};var s=a("VU/8")(i,n,!1,function(t){a("jCLg")},null,null);e.default=s.exports},jCLg:function(t,e){}});