package com.koron.zys.baseConfig.queryBean;

public class BaseAccessoryMetadataQueryBean {
	
	/**
	 * 主键
	 */
	private String id;
	
	/**
	 * 附件名称
	 */
	private String accessoryName;
	
	/**
	 * 附件类型
	 */
	private String accessoryType;
	
	/**
	 * 附件大小
	 */
	private Integer accessorySize;
	
	/**
	 * 附件来源
	 */
	private String sourceFlag;

	public String getId() {
		return id;
	}

	public String getAccessoryName() {
		return accessoryName;
	}

	public String getAccessoryType() {
		return accessoryType;
	}

	public Integer getAccessorySize() {
		return accessorySize;
	}

	public String getSourceFlag() {
		return sourceFlag;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setAccessoryName(String accessoryName) {
		this.accessoryName = accessoryName;
	}

	public void setAccessoryType(String accessoryType) {
		this.accessoryType = accessoryType;
	}

	public void setAccessorySize(Integer accessorySize) {
		this.accessorySize = accessorySize;
	}

	public void setSourceFlag(String sourceFlag) {
		this.sourceFlag = sourceFlag;
	}

}
