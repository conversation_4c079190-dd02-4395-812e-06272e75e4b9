webpackJsonp([19],{"+FSM":function(t,e){},NUM3:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=a("//Fk"),s=a.n(r),i=a("BO1k"),n=a.n(i),o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"staffMgtAdd"},[a("el-form",{ref:"staffMgtAddRuleForm",staticClass:"formBill-One",attrs:{model:t.ruleForm,rules:t.rules,"label-width":"100px",inline:!0}},[a("el-form-item",{attrs:{label:"姓名：",prop:"matrNo"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:t.ruleForm.matrNo,callback:function(e){t.$set(t.ruleForm,"matrNo",e)},expression:"ruleForm.matrNo"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"帐号：",prop:"matrNo"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:t.ruleForm.matrNo,callback:function(e){t.$set(t.ruleForm,"matrNo",e)},expression:"ruleForm.matrNo"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"姓别：",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.ruleForm.status,callback:function(e){t.$set(t.ruleForm,"status",e)},expression:"ruleForm.status"}},[a("el-option",{attrs:{label:"男",value:1}}),t._v(" "),a("el-option",{attrs:{label:"女",value:0}})],1)],1)],1)],1)},staticRenderFns:[]};var l={name:"staffMgt",components:{staffMgtAdd:a("VU/8")({name:"staffMgtAdd",data:function(){return{databaseData:[],ruleForm:{matrNo:"",matrName:"",matrMode:"",matrNum:"",matrUnit:"",matrPrice:""},rules:{matrNo:[{required:!0,message:"请输入材料编号",trigger:"blur"}],matrName:[{required:!0,message:"请输入材料名称",trigger:"blur"}],matrMode:[{required:!0,message:"请输入材料规格",trigger:"blur"}],matrNum:[{required:!0,message:"请输入材料数量"},{type:"number",message:"材料数量必须为数字值"}],matrUnit:[{message:"请输入材料单位",trigger:"blur",required:!0}],matrPrice:[{message:"请输入材料单价",trigger:"blur",required:!0},{type:"number",message:"材料单价必须为数字值"}]}}},mounted:function(){},methods:{resetForm:function(){this.$refs.staffMgtAddRuleForm.resetFields()},submitForm:function(t,e){var a=this,r=this,s={};this.$refs[t].validate(function(t){if(!t)return!1;s="添加"===e?{busicode:"MatrTemplateAdd",data:a.ruleForm}:{busicode:"MatrTemplateUpdate",data:a.ruleForm},a.$api.fetch({params:s}).then(function(t){r.$message({showClose:!0,message:"保存成功",type:"success"}),r.$parent.selectTSubSystem(),r.$parent.closeDialog(),a.resetForm()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.ruleForm,"get","staffMgtAdd",this.$parent.closeDialog)},editData:function(t){this.ruleForm=t}}},o,!1,function(t){a("+FSM")},null,null).exports,autoTree:a("yJVD").a},data:function(){return{tableShow:!0,tableQuery:{page:1,pageCount:20},maxHeight:0,appServerData:{list:[],totalCount:0},formData:{matrNo:"",matrName:"",matrMode:"",matrNum:"",matrUnit:"",matrPrice:""},crumbsData:{titleList:[{title:"系统设置",path:"/ChangeTables"},{title:"职员管理",method:function(){window.histroy.back()}}]},treeDatas:{tree:[{name:"根目录",id:"2",children:[]}],defaultProps:{label:"name",children:"children"},inputProp:{showSearch:!0,Inp_placeholder:"请输入节点"},sendTreeProp:["code","description","flag","id","name","parentcode","parentmask","seq","shortname","sn","state","tel"],rootName:"根目录",defaultOpen:{nodeKey:"id"}},staffMgtShow:!0,staffMgtAddVisible:!1,selectedSubCode:"",userLoading:!1,deptLoading:!1}},mounted:function(){this.selectTSubSystemTree(),this.getStaff()},methods:{syncUser:function(){var t=this;this.userLoading=!0,this.$axios({method:"post",url:"/sys/synchronization.htm",data:{},headers:{returntype:"ajax/json"}}).then(function(){t.$message({type:"success",message:"同步成功"}),t.userLoading=!1}).catch(function(){t.userLoading=!1})},syncDept:function(){var t=this;this.deptLoading=!0,this.$axios({method:"post",url:"/sys/synchronization.htm",data:{},headers:{returntype:"ajax/json"}}).then(function(){t.$message({type:"success",message:"同步成功"}),t.deptLoading=!1}).catch(function(){t.deptLoading=!1})},selectTSubSystemTree:function(){var t=this;this.$axios({url:"/dep/json.htm?id=-1",method:"post"}).then(function(e){t.treeDatas.tree[0].children=e.data[0].children,t.common.changeTable(t,".staffMgt .kl-table",[".staffMgt .toolbar",".staffMgt .block"])})},dealData:function(t){var e=[],a=[],r=!0,s=!1,i=void 0;try{for(var o,l=n()(t);!(r=(o=l.next()).done);r=!0){var u=o.value;a.push(u.parentcode)}}catch(t){s=!0,i=t}finally{try{!r&&l.return&&l.return()}finally{if(s)throw i}}a=this.distinct(a);var d=!0,c=!1,m=void 0;try{for(var f,h=n()(a);!(d=(f=h.next()).done);d=!0){var p=f.value;for(var g in t)t[g].code===p&&(void 0===t[g].children&&(t[g].children=[]),e.push(t.splice(g,1)[0]))}}catch(t){c=!0,m=t}finally{try{!d&&h.return&&h.return()}finally{if(c)throw m}}for(var b in e)for(var v=0;v<t.length;v++)e[b].code===t[v].parentcode&&(e[b].children.push(t.splice(v,1)[0]),v--);return e},distinct:function(t){var e,a={},r=[];t.length;for(e=0;e<t.length;e++)a[t[e]]||(a[t[e]]=1,r.push(t[e]));return r},formatStatus:function(t){return 1===t.status?"启用":"禁用"},appAdd:function(t){var e=this;if(this.staffMgtShow=!1,this.staffMgtAddVisible=!0,"add"===t)this.$refs.staffMgtAdd.editData({matrNo:"",matrName:"",matrMode:"",matrNum:"",matrUnit:"",matrPrice:""}),this.$set(this.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),this.common.chargeObjectEqual(this,this.formData,"set","staffMgtAdd");else{this.$set(this.crumbsData.titleList,"2",{title:"编辑",method:function(){window.histroy.back()}});var a={busicode:"MatrTemplateQuery",data:{matrId:t.row.matrId}};this.$api.fetch({params:a}).then(function(t){e.$refs.staffMgtAdd.editData(t),e.common.chargeObjectEqual(e,t,"set","staffMgtAdd")})}},indexMethod:function(t){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(t+1)},handleSizeChange:function(t){this.tableQuery.pageCount=t,this.tableQuery.page=1,this.getStaff()},handleCurrentChange:function(t){this.tableQuery.page=t,this.getStaff()},selectTSubSystem:function(t){this.selectedSubCode=t.code,this.getStaff()},getStaff:function(t){var e=this;return new s.a(function(t){e.$axios({url:"/dep/staffOfPage.htm",method:"post",params:{departmentCode:e.selectedSubCode,page:e.tableQuery.page,pageCount:e.tableQuery.pageCount},headers:{returntype:"ajax/json"}}).then(function(t){console.log(t),e.appServerData.list=t.data.data,e.appServerData.totalCount=Number(t.data.totalCount)})})},closeDialog:function(){this.staffMgtShow=!0,this.staffMgtAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.staffMgtAdd.handleClose()},submitForm:function(t){var e=this.crumbsData.titleList[2].title;this.$refs.staffMgtAdd.submitForm(t,e)},backTreeData:function(t){this.selectTSubSystem(t)}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},u={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"staffMgt"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:t.crumbsData}}),t._v(" "),a("div",{staticClass:"bread-contain-right"}),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.staffMgtAddVisible,expression:"staffMgtAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.submitForm("staffMgtAddRuleForm")}}},[t._v("保存")]),t._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:t.handleClose}},[t._v("返回")])],1)],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.staffMgtShow,expression:"staffMgtShow"}],staticClass:"company-content"},[a("div",{staticClass:"company-left"},[a("auto-tree",{attrs:{treeData:t.treeDatas},on:{sendTreeData:t.backTreeData}})],1),t._v(" "),a("div",{staticClass:"company-right kl-table"},[t.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:t.appServerData.list,"max-height":t.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:t.indexMethod}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",label:"姓名","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"loginid",label:"帐号","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"departmentName",label:"部门","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"phone",label:"手机号码","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"email",label:"email","min-width":"100"}})],1):t._e(),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":t.tableQuery.page,"page-sizes":[20,100,500,1e3],"page-size":t.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:t.appServerData.totalCount},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)]),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.staffMgtAddVisible,expression:"staffMgtAddVisible"}]},[a("staffMgtAdd",{ref:"staffMgtAdd"})],1)])])},staticRenderFns:[]};var d=a("VU/8")(l,u,!1,function(t){a("WN+N")},null,null);e.default=d.exports},"WN+N":function(t,e){}});