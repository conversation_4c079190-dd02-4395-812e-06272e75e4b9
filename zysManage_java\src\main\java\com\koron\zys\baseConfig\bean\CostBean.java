package com.koron.zys.baseConfig.bean;

/**
 * 费用名称表
 * <AUTHOR>
 *
 */
public class CostBean {
	
	private String id;

	/**
	 * 费用名称
	 */
    private String costName;
    /**
              * 费用编号
     */
    private String costNo;
    
    private String costNameEn;
    
    private String costUnit;
 
    private String comments;
    
    private Integer status;
    
    private Integer sortNo;
    /**
                *  预留字段
     */
    private String tenantId;

    private String isMust;

    private Integer allowRushRed;

    private Integer isComprehensive;

	public Integer getIsComprehensive() {
		return isComprehensive;
	}

	public void setIsComprehensive(Integer isComprehensive) {
		this.isComprehensive = isComprehensive;
	}

	public String getIsMust() {
		return isMust;
	}

	public void setIsMust(String isMust) {
		this.isMust = isMust;
	}

	public String getCostUnit() {
		return costUnit;
	}
	public void setCostUnit(String costUnit) {
		this.costUnit = costUnit;
	}
	public String getCostNameEn() {
		return costNameEn;
	}
	public void setCostNameEn(String costNameEn) {
		this.costNameEn = costNameEn;
	}
	/**
	 * 创建时间
	 */
	private String createTime;
	
	/**
	 * 创建人
	 */
	private String createName;
	/**
	 * 修改时间
	 */
	private String updateTime;
	/**
	 * 修改人
	 */
	private String updateName;
	
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getCostName() {
		return costName;
	}
	public void setCostName(String costName) {
		this.costName = costName;
	}
	public String getCostNo() {
		return costNo;
	}
	public void setCostNo(String costNo) {
		this.costNo = costNo;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Integer getSortNo() {
		return sortNo;
	}
	public void setSortNo(Integer sortNo) {
		this.sortNo = sortNo;
	}
	public String getTenantId() {
		return tenantId;
	}
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getCreateName() {
		return createName;
	}
	public void setCreateName(String createName) {
		this.createName = createName;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public String getUpdateName() {
		return updateName;
	}
	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Integer getAllowRushRed() {
		return allowRushRed;
	}

	public void setAllowRushRed(Integer allowRushRed) {
		this.allowRushRed = allowRushRed;
	}
}
