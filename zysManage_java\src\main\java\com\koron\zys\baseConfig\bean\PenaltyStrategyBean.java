package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;
import com.koron.util.Check.Repeat;

/**
 * 违约金策略表
 * <AUTHOR>
 *
 */
public class PenaltyStrategyBean extends BaseBean{

	private String id;

	private String penaltyId;

	private String costId;

	/**
	 * 策略名称
	 */
	@Check(name = "策略名称", notNull=true, notEmpty = true, repeat = @Repeat(tableName = "BASE_PENALTY_STRATEGY",columnName = "strategy_name"))
    private String strategyName;
    /**
              * 最低计算金额
     */
	@Check(name = "最低计算金额", notEmpty = true, notNull=true)
    private Double minCalculateMoney;
   /**
             *保留小数位数
    */
	@Check(name = "保留小数位数", notEmpty = true, notNull=true,max=8,number=true)
    private Integer decimalPlacse;
    /**
               * 最低违约金额
     */
	@Check(name = "最低违约金额", notEmpty = true, notNull=true)
    private Double minPenaltyMoney;
    /**
                *  最高违约金额
     */
	@Check(name = "最高违约金额", notEmpty = true, notNull=true)
    private Double maxPenaltyMoney;
    /**
                *每日收取比例 
     */
	@Check(name = "每日收取比例 ", notEmpty = true, notNull=true,number=true, min=0,max=1000)
    private Double dailyScaling;
    /**
                * 最高收取比例 
     */
	@Check(name = "最高收取比例 ", notEmpty = true, notNull=true,number=true, min=0,max=1000)
    private Double maxScaling;
    /**
                * 是否计算余额
     */
    private Integer calculateBalance;
    /**
                * 计算方式
     */
    private Integer calculateWay;
    /**
                * 计算值
     */
    private Integer calculateValue;
    /**
                * 假期除外
     */
    private Integer vacationFlag;
    
    private String comments;
    @Check(name = "状态", notEmpty = true, notNull=true)
    private Integer status;
    
    /**
                *  预留字段
     */
    private String tenantId;
    

    /**
	 * 创建时间
	 */
	private String createTime;
	
	/**
	 * 创建人
	 */
	private String createName;
	/**
	 * 修改时间
	 */
	private String updateTime;
	/**
	 * 修改人
	 */
	private String updateName;
	/**
	 * 收取方式（0每天，1每月）
	 */
	private int chargeWay;
	/**
	 * 计算值2（如开账2月后第N天）
	 */
	private Integer calculateValue2;

	@Override
	public String getId() {
		return id;
	}

	@Override
	public void setId(String id) {
		this.id = id;
	}

	public String getPenaltyId() {
		return penaltyId;
	}

	public void setPenaltyId(String penaltyId) {
		this.penaltyId = penaltyId;
	}

	public String getCostId() {
		return costId;
	}

	public void setCostId(String costId) {
		this.costId = costId;
	}

	public String getStrategyName() {
		return strategyName;
	}

	public void setStrategyName(String strategyName) {
		this.strategyName = strategyName;
	}

	public Double getMinCalculateMoney() {
		return minCalculateMoney;
	}

	public void setMinCalculateMoney(Double minCalculateMoney) {
		this.minCalculateMoney = minCalculateMoney;
	}

	public Integer getDecimalPlacse() {
		return decimalPlacse;
	}

	public void setDecimalPlacse(Integer decimalPlacse) {
		this.decimalPlacse = decimalPlacse;
	}

	public Double getMinPenaltyMoney() {
		return minPenaltyMoney;
	}

	public void setMinPenaltyMoney(Double minPenaltyMoney) {
		this.minPenaltyMoney = minPenaltyMoney;
	}

	public Double getMaxPenaltyMoney() {
		return maxPenaltyMoney;
	}

	public void setMaxPenaltyMoney(Double maxPenaltyMoney) {
		this.maxPenaltyMoney = maxPenaltyMoney;
	}

	public Double getDailyScaling() {
		return dailyScaling;
	}

	public void setDailyScaling(Double dailyScaling) {
		this.dailyScaling = dailyScaling;
	}

	public Double getMaxScaling() {
		return maxScaling;
	}

	public void setMaxScaling(Double maxScaling) {
		this.maxScaling = maxScaling;
	}

	public Integer getCalculateBalance() {
		return calculateBalance;
	}

	public void setCalculateBalance(Integer calculateBalance) {
		this.calculateBalance = calculateBalance;
	}

	public Integer getCalculateWay() {
		return calculateWay;
	}

	public void setCalculateWay(Integer calculateWay) {
		this.calculateWay = calculateWay;
	}

	public Integer getCalculateValue() {
		return calculateValue;
	}

	public void setCalculateValue(Integer calculateValue) {
		this.calculateValue = calculateValue;
	}

	public Integer getVacationFlag() {
		return vacationFlag;
	}

	public void setVacationFlag(Integer vacationFlag) {
		this.vacationFlag = vacationFlag;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Override
	public String getTenantId() {
		return tenantId;
	}

	@Override
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	@Override
	public String getCreateTime() {
		return createTime;
	}

	@Override
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	@Override
	public String getCreateName() {
		return createName;
	}

	@Override
	public void setCreateName(String createName) {
		this.createName = createName;
	}

	@Override
	public String getUpdateTime() {
		return updateTime;
	}

	@Override
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public String getUpdateName() {
		return updateName;
	}

	@Override
	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public int getChargeWay() {
		return chargeWay;
	}

	public void setChargeWay(int chargeWay) {
		this.chargeWay = chargeWay;
	}

	public Integer getCalculateValue2() {
		return calculateValue2;
	}

	public void setCalculateValue2(Integer calculateValue2) {
		this.calculateValue2 = calculateValue2;
	}
}
