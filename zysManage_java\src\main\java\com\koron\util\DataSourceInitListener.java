package com.koron.util;

import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * 数据源初始化
 * <AUTHOR>
 *
 */
@Component
public class DataSourceInitListener implements ApplicationListener<ContextRefreshedEvent>{

	@Override
	public void onApplicationEvent(ContextRefreshedEvent event) {
		DBSourceUtils.initDataSources();
	}

}
