package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.BookBean;
import com.koron.zys.baseConfig.mapper.BookMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
/**
 * 册本信息-编辑初始化
 * <AUTHOR>
 */
public class BookQuery implements ServerInterface {
	private static Logger logger = LoggerFactory.getLogger(BookQuery.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		MessageBean<BookBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", BookBean.class);
		try {
			BookBean bean = JsonUtils.objectToPojo(req.getData(), BookBean.class);
			if(StringUtils.isBlank(bean.getId())){
				return MessageBean.create(Constant.MESSAGE_INT_FAIL, "主键不能为空。", void.class);
			}
			BookMapper mapper = factory.getMapper(BookMapper.class);
			bean = mapper.query(bean.getId());
			
			info.setCode(Constant.MESSAGE_INT_SUCCESS);
			info.setDescription("success");
			info.setData(bean);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription("操作失败");
			logger.error("操作失败", e);
		}
		return info;
	}

}
