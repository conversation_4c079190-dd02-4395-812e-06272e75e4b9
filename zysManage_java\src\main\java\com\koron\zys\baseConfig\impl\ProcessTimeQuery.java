package com.koron.zys.baseConfig.impl;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ProcessTimeBean;
import com.koron.zys.baseConfig.mapper.ProcessTimeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

public class ProcessTimeQuery implements ServerInterface{
	private Logger log = Logger.getLogger(ProcessTimeQuery.class);
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
        MessageBean<ProcessTimeBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", ProcessTimeBean.class);

        try {
            Map bean = JsonUtils.objectToPojo(req.getData(), Map.class);
            ProcessTimeMapper mapper = factory.getMapper(ProcessTimeMapper.class);
            if (StringUtils.isEmpty(bean.get("id").toString())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
            }
            ProcessTimeBean processTimeBean = mapper.query(bean.get("id").toString());
            info.setData(processTimeBean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
	}

}
