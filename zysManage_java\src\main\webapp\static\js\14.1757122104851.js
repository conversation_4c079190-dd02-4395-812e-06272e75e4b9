webpackJsonp([14],{"5Z1r":function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var i={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"HolidayConfigurationEdit"},[e("el-form",{ref:"HolidayConfigurationEditForm",staticClass:"formBill-Two",attrs:{inline:!0,size:"mini",model:t.formData,rules:t.formRules,"label-width":"100px"}},[e("el-form-item",{staticClass:"f4",attrs:{label:"名称：",prop:"vacationName"}},[e("el-input",{attrs:{clearable:"",placeholder:"请输入假期名称"},model:{value:t.formData.vacationName,callback:function(a){t.$set(t.formData,"vacationName",a)},expression:"formData.vacationName"}})],1),t._v(" "),e("el-form-item",{staticClass:"f4",attrs:{label:"日期："}},[e("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:"",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.monthDate,callback:function(a){t.monthDate=a},expression:"monthDate"}})],1),t._v(" "),e("el-form-item",{staticClass:"f4",attrs:{label:"说明："}},[e("el-input",{attrs:{"show-word-limit":"",maxlength:"50",clearable:"",placeholder:"请输入假期说明",type:"textarea"},model:{value:t.formData.vacationComments,callback:function(a){t.$set(t.formData,"vacationComments",a)},expression:"formData.vacationComments"}})],1)],1)],1)},staticRenderFns:[]};var o={components:{HolidayConfigurationEdit:e("VU/8")({name:"HolidayConfigurationEdit",data:function(){return{monthDate:[],formData:{id:"",vacationBeginDate:"",vacationEndDate:"",vacationName:"",vacationComments:""},formRules:{vacationName:{required:!0,trigger:"blur",message:"请输入名称"}}}},mounted:function(){},methods:{resetForm:function(){this.$refs.HolidayConfigurationEditForm.resetFields()},submitForm:function(t){var a=this,e=this;null===e.monthDate&&(e.monthDate=["",""]),e.formData.vacationBeginDate=e.monthDate[0],e.formData.vacationEndDate=e.monthDate[1],e.$refs.HolidayConfigurationEditForm.validate(function(i){if(i){var o={};o="PubConfigVacationAdd"===t?{busicode:"PubConfigVacationAdd",data:a.formData}:{busicode:"PubConfigVacationUpdate",data:a.formData},a.$api.fetch({params:o}).then(function(t){e.$message({showClose:!0,message:"保存成功",type:"success"}),e.$parent.init(),e.$parent.closeDialog()})}else e.$message({showClose:!0,message:"必填项未填写",type:"error"})})},editData:function(t){this.monthDate=[t.vacationBeginDate,t.vacationEndDate],this.formData=t}}},i,!1,function(t){e("YPD1")},"data-v-ca782ea8",null).exports},name:"HolidayConfiguration",data:function(){return{EditVisible:!1,monthDate:[],formData:{vacationBeginDate:"",vacationEndDate:"",vacationName:"",page:1,pageCount:10},crumbsData:{titleList:[{title:"基础配置",path:"/baseInfo"},{title:"假期配置",method:function(){window.histroy.back()}}]},maxHeight:0,tableShow:!1,tableData:[],formType:""}},mounted:function(){var t=this;this.init(),this.$nextTick(function(){t.common.changeTable(t,".HolidayConfigurationHolidayConfiguration .wHolidayConfigurationIndex",[".HolidayConfiguration .block",".HolidayConfiguration .toolbar"])})},methods:{init:function(){var t=this;null===t.monthDate&&(t.monthDate=["",""]),t.formData.vacationBeginDate=t.monthDate[0],t.formData.vacationEndDate=t.monthDate[1];var a={busicode:"PubConfigVacationList",data:this.formData};this.$api.fetch({params:a}).then(function(a){t.tableData=a})},search:function(){this.formData.page=1,this.init()},add:function(t){var a=this;if(this.EditVisible=!0,"add"===t)this.formType="PubConfigVacationAdd";else{this.formType="PubConfigVacationUpdate";var e={busicode:"PubConfigVacationQuery",data:{id:t.id}};this.$api.fetch({params:e}).then(function(t){a.$refs.HolidayConfigurationEdit.editData(t)})}},indexMethod:function(t){return(this.formData.page-1)*this.formData.pageCount+(t+1)},handleSizeChange:function(t){this.formData.pageCount=t,this.formData.page=1,this.init()},handleCurrentChange:function(t){this.formData.page=t,this.init()},closeDialog:function(){this.EditVisible=!1},submitForm:function(){this.$refs.HolidayConfigurationEdit.submitForm(this.formType)}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},n={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"HolidayConfiguration"},[e("div",{staticClass:"bread-contain"},[e("publicCrumbs",{attrs:{crumbsData:t.crumbsData}}),t._v(" "),t.EditVisible?e("div",{staticClass:"bread-contain-right"},[e("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return t.submitForm()}}},[t._v("保存")]),t._v(" "),e("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:t.closeDialog}},[t._v("返回")])],1):e("div",{staticClass:"bread-contain-right"},[e("el-button",{attrs:{size:"mini",icon:"el-icon-plus",type:"primary"},on:{click:function(a){return t.add("add")}}},[t._v("添加")])],1)],1),t._v(" "),t.EditVisible?e("HolidayConfigurationEdit",{ref:"HolidayConfigurationEdit"}):e("div",{staticClass:"HolidayConfigurationIndex"},[e("div",{staticClass:"toolbar"},[e("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,size:"mini",model:t.formData}},[e("el-form-item",{staticClass:"form-left"},[e("el-form-item",{attrs:{label:"名称："}},[e("el-input",{attrs:{placeholder:"名称",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.search.apply(null,arguments)}},model:{value:t.formData.vacationName,callback:function(a){t.$set(t.formData,"vacationName",a)},expression:"formData.vacationName"}})],1),t._v(" "),e("el-form-item",{attrs:{label:"日期：",prop:"meterType"}},[e("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:"",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.monthDate,callback:function(a){t.monthDate=a},expression:"monthDate"}})],1),t._v(" "),e("el-form-item",[e("el-button",{staticClass:"searchBtn",attrs:{icon:"el-icon-search"},on:{click:t.search}})],1)],1)],1)],1),t._v(" "),e("div",{staticClass:"kl-table"},[t.tableShow?e("el-table",{staticClass:"wuserInfo-table",attrs:{"max-height":t.maxHeight,stripe:"",border:"",data:t.tableData.list}},[e("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:t.indexMethod}}),t._v(" "),e("el-table-column",{attrs:{prop:"vacationName","min-width":"100",label:"名称","show-overflow-tooltip":""}}),t._v(" "),e("el-table-column",{attrs:{prop:"vacationBeginDate","min-width":"100",label:"开始日期","show-overflow-tooltip":""}}),t._v(" "),e("el-table-column",{attrs:{prop:"vacationEndDate","min-width":"100",label:"结束日期","show-overflow-tooltip":""}}),t._v(" "),e("el-table-column",{attrs:{prop:"vacationComments","min-width":"200",label:"说明","show-overflow-tooltip":""}}),t._v(" "),e("el-table-column",{attrs:{label:"操作",width:"70",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"mini"},nativeOn:{click:function(e){return t.add(a.row)}}},[t._v("编辑")])]}}],null,!1,669204806)})],1):t._e(),t._v(" "),e("div",{staticClass:"block"},[e("el-pagination",{attrs:{"current-page":t.formData.page,"page-sizes":[10,50,100,500,1e3],"page-size":t.formData.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:t.tableData.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)])],1)},staticRenderFns:[]};var r=e("VU/8")(o,n,!1,function(t){e("DSaI")},null,null);a.default=r.exports},DSaI:function(t,a){},YPD1:function(t,a){}});