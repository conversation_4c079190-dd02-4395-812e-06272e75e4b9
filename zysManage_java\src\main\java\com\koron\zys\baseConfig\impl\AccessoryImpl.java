package com.koron.zys.baseConfig.impl;

import java.util.*;

import com.koron.zys.serviceManage.bean.ToolAccessoryBean;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.web.multipart.MultipartFile;
import org.swan.bean.MessageBean;

import com.koron.zys.AccessoryInterface;
import com.koron.zys.ApplicationConfig;
import com.koron.zys.baseConfig.bean.BaseAccessoryMetadataBean;
import com.koron.zys.baseConfig.bean.BaseReceiptAccessoryBean;
import com.koron.zys.baseConfig.mapper.BaseAccessoryMetadataMapper;
import com.koron.zys.baseConfig.mapper.BaseReceiptAccessoryMapper;
import com.koron.zys.common.bean.FileProBean;
import com.koron.zys.common.bean.MultipartContentBean;
import com.koron.zys.common.utils.HttpUtils;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.zys.serviceManage.utils.RedisUtils;
import com.koron.util.Constant;
import com.koron.util.Tools;

public class AccessoryImpl implements AccessoryInterface{

	private static Logger log = LoggerFactory.getLogger(AccessoryImpl.class);

	// private static final Map<String, String> FTOKEN = new ConcurrentHashMap<String, String>();

	/***
	 * 附件上传
	 */
	public MessageBean<?> upload(SessionFactory factory, UserInfoBean userInfo, RequestBean req, List<MultipartFile> files) {
		try {
			BaseReceiptAccessoryBean bean = JsonUtils.objectToPojo(req.getData(), BaseReceiptAccessoryBean.class);
			BaseAccessoryMetadataMapper metadataMapper = factory.getMapper(BaseAccessoryMetadataMapper.class);
			BaseReceiptAccessoryMapper mapper = factory.getMapper(BaseReceiptAccessoryMapper.class);

			if(StringUtils.isBlank(bean.getReceiptId())) {
				return MessageBean.create(Constant.MESSAGE_DBFAIL, "单据主键不能为空", Void.class);
			}
			if(StringUtils.isBlank(bean.getReceiptType())) {
				return MessageBean.create(Constant.MESSAGE_DBFAIL, "单据类型不能为空", Void.class);
			}
			if(StringUtils.isBlank(bean.getAccessoryNo())) {
				return MessageBean.create(Constant.MESSAGE_DBFAIL, "附件类型不能为空", Void.class);
			}
			if(!"000".equals(bean.getAccessoryNo())) {
				mapper.deleteByAccessoryNo(bean);
			}
			List<String> paths = new ArrayList<String>();
			for(MultipartFile file : files) {
				String path = upload(file);
				if(StringUtils.isNotBlank(path)) {

					BaseAccessoryMetadataBean metadata = new BaseAccessoryMetadataBean();
					String metadataId = Tools.getObjectId();
					metadata.setAccessoryPath(path);
					metadata.setAccessoryName(FilenameUtils.getName(file.getOriginalFilename()));
					metadata.setId(metadataId);
					metadata.setAccessorySize(file.getSize());
					metadata.setAccessoryType(file.getContentType());
					metadata.setCreateAccount(userInfo.getUserInfo().getAcount());
					metadata.setCreateName(userInfo.getUserInfo().getName());
					metadata.setSourceFlag("1");
					metadataMapper.insert(metadata);
					bean.setId(Tools.getObjectId());
					bean.setMetadataId(metadataId);
					bean.setCreateAccount(userInfo.getUserInfo().getAcount());
					bean.setCreateName(userInfo.getUserInfo().getName());
					bean.setAccessoryName(FilenameUtils.getName(file.getOriginalFilename()));
					mapper.insert(bean);
					paths.add(ApplicationConfig.getAccessoryUploadUrl() + "fileDownload?path="+path);
				}
			}
			MessageBean<List> data = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "上传附件成功", List.class);
			data.setData(paths);
			return data;
		}catch (Exception e) {
			log.error("附件上传失败：", e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "附件上传失败", null);
		}
	}

	/**
	 * 工具页附件上传
	 */
	@Override
	public MessageBean<?> toolUpload(SessionFactory factory, UserInfoBean userInfo, RequestBean req, List<MultipartFile> files) {
		List<ToolAccessoryBean> list = new ArrayList<>();
		try {
			for (MultipartFile file : files) {
				ToolAccessoryBean bean = new ToolAccessoryBean();
				//文件大小
				bean.setAccessorySize(getFileSizeString(file.getSize()));
				//文件类型
				bean.setAccessoryType(file.getContentType());
				//文件名字
				bean.setAccessoryName(file.getOriginalFilename());
				//文件路径
				bean.setAccessoryPath("http://wx.guangdongwater.com/BCFileService/fileService/" + "fileDownload?path=" + toolUpload(file));
				list.add(bean);
			}
			MessageBean<List> data = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "工具页附件上传成功", List.class);
			data.setData(list);
			return data;
		} catch (Exception e) {
			log.error("工具页附件上传失败：", e.getMessage(), e);
			return MessageBean.create(Constant.MESSAGE_INT_FAIL, "工具页附件上传失败", null);
		}
	}

	/**
	 * 附件下载 待实现.....
	 */
	@Override
	public HttpEntity<?> down(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		return null;
	}

	private String upload(MultipartFile file) throws Exception {
		Map<String, String> data = new HashMap<String, String>();
		FileProBean filePro = new FileProBean();
		List<MultipartContentBean> multipartContents = new ArrayList<MultipartContentBean>();
		MultipartContentBean multipartContent = new MultipartContentBean();
		multipartContent.setBytes(file.getBytes());
		multipartContent.setContentType(file.getContentType());
		multipartContent.setFileName(FilenameUtils.getName(file.getOriginalFilename()));
		multipartContents.add(multipartContent);
		String ftoken = getFToken();
		data.put("ftoken", ftoken);
		filePro.setData(data);
		filePro.setMultipart(multipartContents);
		String result = HttpUtils.upload(ApplicationConfig.getAccessoryUploadUrl() + "fileUpload", filePro);
		log.info("文件上传结果返回：{}", result);
		MessageBean<?> message = JsonUtils.jsonToPojo(result, MessageBean.class);
		if(message.getCode() != Constant.MESSAGE_INT_SUCCESS) {
			throw new RuntimeException("文件上传失败：" + message.getDescription());
		}
		Map<String, String> map = JsonUtils.objectToPojo(message.getData(), Map.class);
		return map.get("path");
	}

	private String getFToken() throws Exception {
		String ftoken = RedisUtils.get("FJftoken");
		if(StringUtils.isNotBlank(ftoken)) {
			return ftoken;
		}
		Map<String, String> data = new HashMap<String, String>();
		data.put("appid", ApplicationConfig.getAccessoryAppId());
		data.put("secret", ApplicationConfig.getAccessorySecret());
		String result = HttpUtils.sendPostJson(ApplicationConfig.getAccessoryUploadUrl() + "fileAuthorize", JsonUtils.objectToJson(data));
		log.info("文件上传获取token结果返回：{}", result);
		MessageBean<?> message = JsonUtils.jsonToPojo(result, MessageBean.class);
		if(message.getCode() != Constant.MESSAGE_INT_SUCCESS) {
			throw new RuntimeException("获取 FTOKEN失败:" + message.getDescription());
		}
		Map<String, String> map = JsonUtils.objectToPojo(message.getData(), Map.class);
		RedisUtils.setEx("FJftoken", (30 * 60) - 10, map.get("ftoken"));
		return map.get("ftoken");
	}

	public static String getFileSizeString(Long size) {
		double length = Double.valueOf(String.valueOf(size));
		//如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
		if (length < 1024) {
			return length + "B";
		} else {
			length = length / 1024.0;
		}
		//如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
		//因为还没有到达要使用另一个单位的时候
		//接下去以此类推
		if (length < 1024) {
			return Math.round(length * 100) / 100.0 + "KB";
		} else {
			length = length / 1024.0;
		}
		if (length < 1024) {
			//因为如果以MB为单位的话，要保留最后1位小数，
			//因此，把此数乘以100之后再取余
			return Math.round(length * 100) / 100.0 + "MB";
		} else {
			//否则如果要以GB为单位的，先除于1024再作同样的处理
			return Math.round(length / 1024 * 100) / 100.0 + "GB";
		}
	}

    private String toolUpload(MultipartFile file) throws Exception {
        Map<String, String> data = new HashMap<String, String>();
        FileProBean filePro = new FileProBean();
        List<MultipartContentBean> multipartContents = new ArrayList<MultipartContentBean>();
        MultipartContentBean multipartContent = new MultipartContentBean();
        multipartContent.setBytes(file.getBytes());
        multipartContent.setContentType(file.getContentType());
        multipartContent.setFileName(FilenameUtils.getName(file.getOriginalFilename()));
        multipartContents.add(multipartContent);
        String ftoken = getToolFToken();
        data.put("ftoken", ftoken);
        filePro.setData(data);
        filePro.setMultipart(multipartContents);
        String result = HttpUtils.upload("http://wx.guangdongwater.com/BCFileService/fileService/" + "fileUpload", filePro);
        log.info("工具附件文件上传结果返回：{}", result);
        MessageBean<?> message = com.koron.util.JsonUtils.jsonToPojo(result, MessageBean.class);
        if(message.getCode() != Constant.MESSAGE_INT_SUCCESS) {
            log.error("工具附件文件上传失败:  "+message.getDescription());
            throw new RuntimeException("文件上传失败：" + message.getDescription());
        }
        Map<String, String> map = com.koron.util.JsonUtils.objectToPojo(message.getData(), Map.class);
        return map.get("path");
    }

    private String getToolFToken() throws Exception {
        String ftoken = RedisUtils.get("ftoken");
        if(StringUtils.isNotBlank(ftoken)) {
            return ftoken;
        }
        Map<String, String> data = new HashMap<String, String>();
        data.put("appid", ApplicationConfig.getAccessoryAppId());
        data.put("secret", ApplicationConfig.getAccessorySecret());
        String result = HttpUtils.sendPostJson("http://wx.guangdongwater.com/BCFileService/fileService/" + "fileAuthorize", JsonUtils.objectToJson(data));
        log.info("文件上传获取token结果返回：{}", result);
        MessageBean<?> message = JsonUtils.jsonToPojo(result, MessageBean.class);
        if(message.getCode() != Constant.MESSAGE_INT_SUCCESS) {
            throw new RuntimeException("获取 FTOKEN失败:" + message.getDescription());
        }
        Map<String, String> map = JsonUtils.objectToPojo(message.getData(), Map.class);
        RedisUtils.setEx("ftoken", (30 * 60) - 10, map.get("ftoken"));
        return map.get("ftoken");
    }

}

