webpackJsonp([7],{"6vn2":function(t,e){},GMeR:function(t,e){},SsIX:function(t,e){},gyly:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"printTemplateAdd"},[a("el-form",{ref:"printTemplateAddForm",staticClass:"formBill",attrs:{model:t.modelData,rules:t.rules,"label-width":"100px",inline:!0}},[a("el-form-item",{attrs:{label:"打印业务：",prop:"businessId"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.modelData.businessId,callback:function(e){t.$set(t.modelData,"businessId",e)},expression:"modelData.businessId"}},t._l(t.businessData,function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"样式名称：",prop:"name"}},[a("el-input",{attrs:{maxlength:"50",clearable:""},model:{value:t.modelData.name,callback:function(e){t.$set(t.modelData,"name",e)},expression:"modelData.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"是否默认样式：",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.modelData.defaultFlag,callback:function(e){t.$set(t.modelData,"defaultFlag",e)},expression:"modelData.defaultFlag"}},[a("el-option",{attrs:{label:"默认",value:1}}),t._v(" "),a("el-option",{attrs:{label:"否",value:0}})],1)],1),t._v(" "),a("el-form-item",{staticClass:"scope f3",attrs:{label:"权限范围：",prop:"scope"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:5,maxRows:8},rows:"15",maxlength:"3000",clearable:"","show-word-limit":""},model:{value:t.modelData.scope,callback:function(e){t.$set(t.modelData,"scope",e)},expression:"modelData.scope"}}),t._v(" "),a("font",{staticStyle:{color:"#bdbdc5"}},[t._v("如果要限定此样式为少数用户使用，请将用户登录账号填在这里，以英文逗号分隔")])],1),t._v(" "),a("el-form-item",{staticClass:"remark f3",attrs:{label:"描述：",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:2,maxRows:4},maxlength:"500",clearable:"","show-word-limit":""},model:{value:t.modelData.remark,callback:function(e){t.$set(t.modelData,"remark",e)},expression:"modelData.remark"}})],1)],1)],1)},staticRenderFns:[]};var n=a("VU/8")({name:"printTemplateAdd",data:function(){return{businessData:[],modelData:{id:"",name:"",remark:"",businessId:"",defaultFlag:0,scope:"",companyNo:""},formData:{id:"",name:"",remark:"",businessId:"",defaultFlag:0,scope:"",companyNo:""},rules:{name:[{required:!0,message:"请输入样式名称",trigger:"blur"}]}}},mounted:function(){this.getBusinessData()},methods:{resetForm:function(){this.$refs.printTemplateAddForm.resetFields()},getBusinessData:function(){var t=this;this.$api.fetch({params:{busicode:"PrintBusinessList",data:{name:"",code:"",page:1,pageCount:5e4}}}).then(function(e){t.businessData=e.list})},submitForm:function(t,e,a){var i=this,n=this,s={};this.$refs[t].validate(function(t){if(!t)return!1;n.modelData=n.common.handleData(n.modelData,n.formData),s="添加"===e?{busicode:"PrintTemplateAdd",data:i.modelData}:{busicode:"PrintTemplateUpdate",data:i.modelData},i.$api.fetch({params:s}).then(function(t){n.$message({showClose:!0,message:"保存成功",type:"success"}),n.$parent.getData(),n.$parent.closeDialog()})})},handleClose:function(){this.common.chargeObjectEqual(this,this.modelData,"get","printTemplateAdd",this.$parent.closeDialog)},editData:function(t){this.modelData=t}}},i,!1,function(t){a("GMeR")},null,null).exports,s={name:"printStyleDialog",data:function(){return{dialogVisible:!0,selServicesData:[],printStyle:"",currentRow:{}}},props:{companyNo:{type:String},printStyleData:{}},mounted:function(){this.getData();var t=this;document.onkeydown=function(e){if(38==(e=e||window.event).keyCode){var a=t.handleTableData(t.currentRow);if(0===a)return;a-=1,t.$refs.printStyleTable.setCurrentRow(t.selServicesData[a]),t.currentRow=t.selServicesData[a],t.printStyle=t.selServicesData[a].id}else if(40==e.keyCode){var i=t.handleTableData(t.currentRow);if(i===t.selServicesData.length-1)return;i+=1,t.$refs.printStyleTable.setCurrentRow(t.selServicesData[i]),t.currentRow=t.selServicesData[i],t.printStyle=t.selServicesData[i].id}e.stopPropagation()}},destroyed:function(){document.onkeydown=void 0},methods:{getData:function(){var t=this,e=this,a={busicode:"PrintTemplateUserList",data:{code:this.printStyleData.printBusinessCode,companyNo:this.companyNo}};this.$api.fetch({params:a}).then(function(a){e.selServicesData=a,t.printStyle=a[0].id,t.$refs.printStyleTable.setCurrentRow(a[0]),t.currentRow=a[0]})},handleClose:function(){this.$parent.close()},indexMethod:function(t){return t+1},print:function(t){var e=this,a=this;this.$api.fetch({params:this.printStyleData.printDataParams}).then(function(i){a.common.print(t,a.companyNo,a.printStyleData.printBusinessCode,a.printStyle,i,a),"print"===t&&e.$parent.close()})},view:function(t,e,a){this.printStyle=t.id,this.print("view")},cellClick:function(t,e,a){this.printStyle=t.id},currentChange:function(t,e){this.currentRow=t},handleTableData:function(t){var e="";return this.selServicesData.forEach(function(a,i){a.id===t.id&&(e=i)}),e}}},r={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"printStyle"},[a("el-dialog",{attrs:{title:"选择打印样式",visible:t.dialogVisible,width:"45%","before-close":t.handleClose,"close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("div",{staticClass:"kl-table"},[a("el-table",{ref:"printStyleTable",attrs:{border:"",data:t.selServicesData,"highlight-current-row":""},on:{"row-dblclick":t.view,"row-click":t.cellClick,"current-change":t.currentChange}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:t.indexMethod}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",label:"模板名称","min-width":"150"}})],1)],1),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini"},on:{click:t.handleClose}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.print("print")}}},[t._v("打印")]),t._v(" "),a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.print("view")}}},[t._v("预览")])],1)])],1)},staticRenderFns:[]};var l={name:"printTemplate",components:{printTemplateAdd:n,printStyle:a("VU/8")(s,r,!1,function(t){a("6vn2")},null,null).exports,autoTree:a("yJVD").a},data:function(){return{printStyleData:{printDataParams:{},printBusinessCode:"base_print_template"},printStyleShow:!1,tableShow:!0,maxHeight:0,selServicesData:{},tableQuery:{name:"",code:"",companyNo:"",page:1,pageCount:50},formData:{id:"",name:"",remark:"",businessId:"",defaultFlag:0,scope:""},crumbsData:{titleList:[{title:"水司配置",path:"/waterSet"},{title:"打印配置",method:function(){window.histroy.back()}},{title:"根目录",method:function(){window.histroy.back()}}]},printTemplateShow:!0,printTemplateAddVisible:!1,treeDatas:{tree:[{shortName:"根目录",id:"2",children:[]}],defaultProps:{label:"shortName",children:"children"},inputProp:{showSearch:!1,Inp_placeholder:"请输入节点"},sendTreeProp:["code","shortName","districtArr","children","companyNo","group","isLeaf","isParent","parent","sonData"],rootName:"根目录",defaultOpen:{nodeKey:"id"}},treeParantId:"",ruleForm:{printStyle:""}}},mounted:function(){this.getTreeDatas()},methods:{appAdd:function(t){var e=this;if(this.printTemplateShow=!1,this.printTemplateAddVisible=!0,"add"===t)this.$refs.printTemplateAdd.editData({id:"",name:"",remark:"",businessId:"",defaultFlag:0,scope:"",companyNo:this.tableQuery.companyNo}),this.$set(this.crumbsData.titleList,"3",{title:"添加",method:function(){window.histroy.back()}}),this.common.chargeObjectEqual(this,this.formData,"set","printTemplateAdd");else{this.$set(this.crumbsData.titleList,"3",{title:"编辑",method:function(){window.histroy.back()}});var a=this,i={busicode:"PrintTemplateQuery",data:{id:t.row.tempId,companyNo:a.tableQuery.companyNo}};this.$api.fetch({params:i}).then(function(t){t.companyNo=a.tableQuery.companyNo,e.$refs.printTemplateAdd.editData(t),e.common.chargeObjectEqual(e,t,"set","printTemplateAdd")})}},design:function(t){console.log(t.row),this.common.print("formatSet",this.tableQuery.companyNo,t.row.busiCode,t.row.tempId,"",this)},printSyleSelect:function(){this.printStyleData.printDataParams={busicode:"PrintTemplateListPrint",data:this.tableQuery.companyNo},this.printStyleShow=!0},print:function(t){var e=this,a={busicode:"PrintTemplateListPrint",data:e.tableQuery.companyNo};this.$api.fetch({params:a}).then(function(a){e.common.print(t,e.tableQuery.companyNo,"base_print_template","",a,e)})},search:function(){this.tableQuery.page=1,this.getData()},indexMethod:function(t){return(this.tableQuery.page-1)*this.tableQuery.pageCount+(t+1)},handleSizeChange:function(t){this.tableQuery.pageCount=t,this.tableQuery.page=1,this.getData()},handleCurrentChange:function(t){this.tableQuery.page=t,this.getData()},getData:function(){var t=this,e={busicode:"PrintTempBusinessList",data:this.tableQuery};this.$api.fetch({params:e}).then(function(e){t.selServicesData=e,t.common.changeTable(t,".printTemplate .kl-table",[])}).catch(function(e){t.common.changeTable(t,".printTemplate .kl-table",[])})},getTreeDatas:function(){var t=this,e=this;this.$api.fetch({params:{busicode:"CompanyNameEnableList",data:{}}}).then(function(a){e.treeDatas.tree[0].children=a,e.tableQuery.companyNo=a[0].companyNo,t.$set(t.crumbsData.titleList,"2",{title:a[0].shortName,method:function(){window.histroy.back()}}),e.getData()})},closeDialog:function(){this.printTemplateShow=!0,this.printTemplateAddVisible=!1,this.crumbsData.titleList.pop()},handleClose:function(){this.$refs.printTemplateAdd.handleClose()},close:function(){this.printStyleShow=!1},backTreeData:function(t){if("根目录"!==t.shortName){this.tableQuery.companyNo=t.companyNo,this.$set(this.crumbsData.titleList,"2",{title:t.shortName,method:function(){window.histroy.back()}}),this.getData()}},submitForm:function(t){var e=this.crumbsData.titleList[3].title;this.$refs.printTemplateAdd.submitForm(t,e,this.tableQuery.companyNo)}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"printTemplate"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:t.crumbsData}}),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.printTemplateShow,expression:"printTemplateShow"}],staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.appAdd("add")}}},[t._v("添加")]),t._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.print("print")}}},[t._v("直接打印")]),t._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.print("view")}}},[t._v("打印预览")]),t._v(" "),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.printSyleSelect()}}},[t._v("选打印样式")])],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.printTemplateAddVisible,expression:"printTemplateAddVisible"}],staticClass:"bread-contain-right"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.submitForm("printTemplateAddForm")}}},[t._v("保存")]),t._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:t.handleClose}},[t._v("返回")])],1)],1),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.printTemplateShow,expression:"printTemplateShow"}],staticClass:"company-content"},[a("div",{staticClass:"company-left"},[a("auto-tree",{attrs:{treeData:t.treeDatas},on:{sendTreeData:t.backTreeData}})],1),t._v(" "),a("div",{staticClass:"kl-table company-right"},[a("div",{staticClass:"toolbar"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.tableQuery,size:"mini"}},[a("div",{staticClass:"toolbar-left"},[a("el-form-item",{attrs:{label:"打印业务编号："}},[a("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search.apply(null,arguments)}},model:{value:t.tableQuery.code,callback:function(e){t.$set(t.tableQuery,"code",e)},expression:"tableQuery.code"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"打印业务名称："}},[a("el-input",{staticClass:"default_class",attrs:{size:"mini",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search.apply(null,arguments)}},model:{value:t.tableQuery.name,callback:function(e){t.$set(t.tableQuery,"name",e)},expression:"tableQuery.name"}})],1),t._v(" "),a("el-form-item",{staticClass:"button-group"},[a("el-button",{staticClass:"compile",attrs:{type:"primary el-icon-search",size:"mini"},on:{click:t.search}})],1)],1),t._v(" "),a("div",{staticClass:"toolbar-right"})])],1),t._v(" "),t.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:t.selServicesData.list,"max-height":t.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:t.indexMethod}}),t._v(" "),a("el-table-column",{attrs:{prop:"busiCode",label:"业务编码",width:"200"}}),t._v(" "),a("el-table-column",{attrs:{prop:"busiName",label:"业务名称","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{prop:"tempName",label:"样式名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"hasDesign",label:"是否设计"}}),t._v(" "),a("el-table-column",{attrs:{prop:"tempRemark",label:"样式描述"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.appAdd(e)}}},[t._v("编辑")]),t._v(" "),a("span",{staticStyle:{color:"#e6e6e6"}},[t._v("|")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.design(e)}}},[t._v("设计")])]}}],null,!1,1255019731)})],1):t._e(),t._v(" "),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":t.tableQuery.page,"page-sizes":[50,100,500,1e3],"page-size":t.tableQuery.pageCount,layout:"total, sizes, prev, pager, next, jumper",total:t.selServicesData.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)]),t._v(" "),t.printStyleShow?a("printStyle",{attrs:{companyNo:t.tableQuery.companyNo,printStyleData:t.printStyleData}}):t._e(),t._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:t.printTemplateAddVisible,expression:"printTemplateAddVisible"}]},[a("printTemplateAdd",{ref:"printTemplateAdd"})],1)],1)])},staticRenderFns:[]};var c=a("VU/8")(l,o,!1,function(t){a("SsIX")},null,null);e.default=c.exports}});