package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.WaterPriceMapper;
import com.koron.zys.baseConfig.queryBean.WaterPriceQueryBean;
import com.koron.zys.baseConfig.vo.WaterPriceVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 用水价格-列表初始化
 * <AUTHOR>
 *
 */
public class WaterPriceList implements ServerInterface  {
	
	private static Logger logger = LoggerFactory.getLogger(WaterPriceList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
		try {
			WaterPriceQueryBean bean = JsonUtils.objectToPojo(req.getData(), WaterPriceQueryBean.class);
			WaterPriceMapper mapper = factory.getMapper(WaterPriceMapper.class);	
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			if(bean.getStatus().equals("")) {
				bean.setStatus(null);	
			}
			List<WaterPriceVO> list = mapper.selectWaterPriceList(bean);			
			info.setData(new PageInfo<>(list));
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}