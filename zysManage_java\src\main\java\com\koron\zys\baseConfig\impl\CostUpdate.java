package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CostBean;
import com.koron.zys.baseConfig.mapper.CostMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;

/**
 * 费用名称-编辑
 * <AUTHOR>
 */
public class CostUpdate implements ServerInterface {

	private static Logger logger = LoggerFactory.getLogger(CostUpdate.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		try {
			CostMapper mapper = factory.getMapper(CostMapper.class);
			CostMapper pubmapper = factory.getMapper(CostMapper.class,"_default");
			CostBean bean = JsonUtils.objectToPojo(req.getData(), CostBean.class);
			if (StringUtils.isNullOrEmpty(bean.getId())) {
				return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
			}
			//如果是启用
			if(bean.getStatus()==1) {
				if(mapper.selectBaseCostById(bean.getId()) > 0) {		//查询水司库是否有该条记录
					mapper.updateCostStatus(bean);
				}else {		//没有,则从公共库复制一条新记录过来
					CostBean cb = pubmapper.selectCostById(bean.getId());
					//mapper.deleteCostById(bean.getId());
					cb.setIsMust(bean.getIsMust());
					cb.setAllowRushRed(bean.getAllowRushRed());
					cb.setIsComprehensive(bean.getIsComprehensive());
					mapper.insertCost(cb);
				}
				
			}else {
				//如果是停用，直接修改状态
				//mapper.stopCostById(bean.getId());
				mapper.updateCostStatus(bean);
			}
		} catch (Exception e) {
			factory.close(false);
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}

}
