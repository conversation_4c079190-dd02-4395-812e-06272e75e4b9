<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.CouponExpirationReminderMapper">
    <!--添加  -->
    <insert id="insertCouponExpirationReminder" parameterType="com.koron.zys.baseConfig.bean.CouponExpirationReminderBean">
		insert into coupon_expiration_reminder(id,days_before_expiration,notice_way,tenant_id,create_time, create_name,create_account)
		values
		(
		#{id},
		#{daysBeforeExpiration},
		#{noticeWay},

		#{tenantId},
		#{createTime},
		#{createName},
		#{createAccount}
		)
	</insert>

	<select id="getCouponExpirationReminder" resultType="com.koron.zys.baseConfig.bean.CouponExpirationReminderBean">
		select days_before_expiration,notice_way,begin_time,end_time
		FROM coupon_expiration_reminder
	</select>
</mapper>