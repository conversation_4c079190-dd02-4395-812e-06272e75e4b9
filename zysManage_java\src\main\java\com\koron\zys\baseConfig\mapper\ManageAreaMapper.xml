<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.ManageAreaMapper">
	<insert id="saveManageArea"
		parameterType="com.koron.zys.baseConfig.bean.ManageAreaBean">
		insert into
		BASE_MANAGE_AREA
		(manage_area_id,area_no,area_name,area_comments,parentId,isLeaf,status,sort_no,create_name,create_time)
		values
		(#{manageAreaId,jdbcType=VARCHAR},#{areaNo,jdbcType=VARCHAR},#{areaName,jdbcType=VARCHAR},#{areaComments,jdbcType=VARCHAR},#{parentId,jdbcType=VARCHAR},
		#{isLeaf,jdbcType=INTEGER},#{status,jdbcType=INTEGER},#{sortNo,jdbcType=INTEGER},#{createName},date_format(#{createTime,jdbcType=VARCHAR},'%Y-%m-%d %T'))
	</insert>
	<update id="updateManageArea"
		parameterType="com.koron.zys.baseConfig.bean.ManageAreaBean">
		update BASE_MANAGE_AREA
		<trim prefix="SET" suffixOverrides=",">
			<if test="areaName != null and areaName != ''">
				area_name=#{areaName},
			</if>
			<if test="areaComments != null">
				area_comments=#{areaComments},
			</if>
			<if test="isLeaf != null">
				isLeaf=#{isLeaf},
			</if>
			<if test="status != null">
				status=#{status},
			</if>
			<if test="sortNo != null">
				sort_no=#{sortNo},
			</if>
			<if test="updateName != null and updateName != ''">
				update_name=#{updateName},
			</if>
			<if test="updateTime != null and updateTime != ''">
				update_time=date_format(#{updateTime},'%Y-%m-%d %T'),
			</if>
		</trim>
		where manage_area_id = #{manageAreaId}
	</update>

	<select id="findManageArea" parameterType="com.koron.zys.baseConfig.queryBean.ManageAreaQueryBean"
		   resultType="com.koron.zys.baseConfig.vo.ManageAreaVO">
		select a.sort_no ,a.area_name,case when a.status=1 then '启用' else '停用' end status,a.area_comments ,a.area_no as areaNo,a.manage_area_id as manageAreaId
		from BASE_MANAGE_AREA a left join BASE_MANAGE_AREA
		b
		on a.parentId=b.manage_area_id
		<where>
			<if test="areaNo == null or areaNo !='all'">
				and a.area_no LIKE CONCAT(#{areaNo},'_____')
			</if>
			<if test="status != null and status ==1">
				and a.status = 1
			</if>
			<if test="isLeaf != null and isLeaf ==0">
				and a.isLeaf = 0
			</if>
		</where>
		order by a.sort_no asc
	</select>

	<select id="findManageAreaByTree"
		resultType="com.koron.zys.serviceManage.bean.TreeBean">
		select area_no as code,area_name as name
		case 
		WHEN substr(area_no,length(area_no)-2) &lt;&gt; '000' THEN
		CONCAT (substr(area_no, 1, 6), '000')
		WHEN substr(area_no,length(area_no)-5) &lt;&gt; '000000' THEN
		CONCAT (substr(area_no, 1, 3), '000000') 
		END PARENT
		from BASE_MANAGE_AREA
		where isLeaf = 0 and status = 1 order by area_no
	</select>

	<select id="findManageAreaBySelect"
		resultType="com.koron.zys.serviceManage.bean.SelectBean">
		SELECT
		area_no as code,
		CASE
		WHEN RIGHT (area_no, 6) = '000000' THEN
		area_name
		WHEN RIGHT (area_no, 3) = '000' THEN
		CONCAT(' ', area_name)
		ELSE
		CONCAT(' ', area_name)
		END name
		FROM
		BASE_MANAGE_AREA
		WHERE
		status = 1
		ORDER BY
		area_no
	</select>
	<!-- 通过id查询记录 -->
	<select id="findManageAreaById"
		resultType="com.koron.zys.baseConfig.bean.ManageAreaBean">
		SELECT
		x.*
		FROM
		BASE_MANAGE_AREA x
		WHERE
		manage_area_id = #{manageAreaId}
	</select>
		<!-- 编辑初始化查询 -->
	<select id="findManageAreaInfoById"
		resultType="com.koron.zys.baseConfig.bean.ManageAreaBean">
		select a.area_name as areaName, a.status as status,a.sort_no as sortNo,a.area_comments as areaComments,a.manage_area_id as manageAreaId
		from BASE_MANAGE_AREA a left join BASE_MANAGE_AREA
		b
		on a.parentId=b.manage_area_id
		where
			a.manage_area_id = #{manageAreaId}		
	</select>
	<!-- 通过Code查询记录 -->
	<select id="findManageAreaByCode"
		resultType="com.koron.zys.serviceManage.bean.SelectBean">
		SELECT
		x.*, area_no code, area_name name, PARENTID parentId ,manage_area_id id
		FROM
		BASE_MANAGE_AREA x
		WHERE
		area_no = #{areaNo}
	</select>
	<!-- 查询最大的子级code编号 -->
	<select id="findMaxChild" resultType="java.lang.String">
		SELECT
		max(area_no) maxCode 
		FROM
		BASE_MANAGE_AREA
		WHERE
		area_no like CONCAT(#{areaNo},'_____')
	</select>
	
	<select id="SelectProvince"
		resultType="com.koron.zys.serviceManage.bean.SelectBean">
		select area_no as code,area_name as name
		from BASE_MANAGE_AREA
		<where>
			and area_no LIKE '_____'
		</where>
		order by area_no 
	</select>
</mapper>