package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.koron.ebs.mybatis.EnvSource;

import com.koron.zys.baseConfig.bean.BusinessAbodeBean;
import com.koron.zys.baseConfig.vo.SelectVO;
@EnvSource("_default")
public interface BusinessAbodeMapper {
	
	/**
	 * 下拉框
	 * 
	 * @return
	 */
	List<SelectVO> businessAbodeSelect(String groupCode);
	/**
	 * 根据id查询营业所
	 * @param bean
	 * @return
	 */
	BusinessAbodeBean findBusinessAbodeById(String id);

	/**
	 * 根据name查询营业所
	 * @param bean
	 * @return
	 */
	@Select("select * from pub_business_abode where status=1 and abode_name=#{name} and group_code=#{code}")
	BusinessAbodeBean findBusinessAbodeByName(@Param("name")String name,@Param("code")String code);
}
