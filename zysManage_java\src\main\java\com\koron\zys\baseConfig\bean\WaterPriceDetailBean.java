package com.koron.zys.baseConfig.bean;

import java.util.List;

/**
 * 用水价格明细
 * <AUTHOR>
 * 2020年5月12日
 */
public class WaterPriceDetailBean extends BaseBean {

	/**
	 * 用水价格ID
	 */
	private String waterPriceId;
	/**
	 * 费用类型ID
	 */
	private String costId;
	/**
	 * 费用类型名称，不实际存储
	 */
	private String costName;
	/**
	 * 费用类型编号，不实际存储
	 */
	private String costNo;
	/**
	 * 违约金策略ID
	 */
	private String penaltyStrategyId;
	/**
	 * 固定单价
	 */
	private double fixedPrice;
	/**
	 * 固定单价单位
	 */
	private String fixedPriceUnit;
	/**
	 * 固定金额
	 */
	private double fixedMoney;
	/**
	 * 固定金额单位
	 */
	private String fixedMoneyUnit;
	/**
	 * 阶梯类型
	 */
	private String ladderType;
	/**
	 * 阶梯计算方式
	 */
	private String ladderCalculateWay;
	/**
	 * 人口基数
	 */
	private Integer personBase;
	/**
	 * 水量基数
	 */
	private double waterBase;

	/**
	 * 阶梯
	 */
	private List<WaterPriceLadderBean> ladders;

	/**
	 *	阶梯动态列表
	 */
	private List<LadderListBean> ladderList;

	private double minMoney;

	private double maxMoney;

	public double getMinMoney() {
		return minMoney;
	}

	public void setMinMoney(double minMoney) {
		this.minMoney = minMoney;
	}

	public double getMaxMoney() {
		return maxMoney;
	}

	public void setMaxMoney(double maxMoney) {
		this.maxMoney = maxMoney;
	}

	public List<LadderListBean> getLadderList() {
		return ladderList;
	}

	public void setLadderList(List<LadderListBean> ladderList) {
		this.ladderList = ladderList;
	}

	public String getCostNo() {
		return costNo;
	}
	public void setCostNo(String costNo) {
		this.costNo = costNo;
	}
	public String getCostName() {
		return costName;
	}
	public void setCostName(String costName) {
		this.costName = costName;
	}
	public String getWaterPriceId() {
		return waterPriceId;
	}
	public void setWaterPriceId(String waterPriceId) {
		this.waterPriceId = waterPriceId;
	}
	public String getCostId() {
		return costId;
	}
	public void setCostId(String costId) {
		this.costId = costId;
	}
	public String getPenaltyStrategyId() {
		return penaltyStrategyId;
	}
	public void setPenaltyStrategyId(String penaltyStrategyId) {
		this.penaltyStrategyId = penaltyStrategyId;
	}
	public String getFixedPriceUnit() {
		return fixedPriceUnit;
	}
	public void setFixedPriceUnit(String fixedPriceUnit) {
		this.fixedPriceUnit = fixedPriceUnit;
	}
	public String getFixedMoneyUnit() {
		return fixedMoneyUnit;
	}
	public void setFixedMoneyUnit(String fixedMoneyUnit) {
		this.fixedMoneyUnit = fixedMoneyUnit;
	}
	public String getLadderType() {
		return ladderType;
	}
	public void setLadderType(String ladderType) {
		this.ladderType = ladderType;
	}
	public String getLadderCalculateWay() {
		return ladderCalculateWay;
	}
	public void setLadderCalculateWay(String ladderCalculateWay) {
		this.ladderCalculateWay = ladderCalculateWay;
	}

	public List<WaterPriceLadderBean> getLadders() {
		return ladders;
	}
	public void setLadders(List<WaterPriceLadderBean> ladders) {
		this.ladders = ladders;
	}
	public double getFixedPrice() {
		return fixedPrice;
	}
	public void setFixedPrice(double fixedPrice) {
		this.fixedPrice = fixedPrice;
	}
	public double getFixedMoney() {
		return fixedMoney;
	}
	public void setFixedMoney(double fixedMoney) {
		this.fixedMoney = fixedMoney;
	}
	public Integer getPersonBase() {
		return personBase;
	}
	public void setPersonBase(Integer personBase) {
		this.personBase = personBase;
	}
	public double getWaterBase() {
		return waterBase;
	}
	public void setWaterBase(double waterBase) {
		this.waterBase = waterBase;
	}

}
