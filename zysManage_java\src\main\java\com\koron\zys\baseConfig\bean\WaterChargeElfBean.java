package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;

/**
 * 水费账单精灵规则实体类
 */
public class WaterChargeElfBean extends BaseBean{
    /**
     * 规则方式 1开账后，2下个月
     */
	@Check(name="规则方式",number=true,notNull = true)
    private Integer ruleWay;
    /**
     * 规则值
     */
	@Check(name="规则值",number=true,notNull = true)
    private Integer ruleValue;
    /**
     * 通知方式
     */
	@Check(name="通知方式",number=true,notNull = true)
    private Integer noticeWay;
    /**
     * 通知起始时间
     */
    private String beginTime;
    /**
     * 通知结束时间
     */
    private String endTime;

    public Integer getRuleWay() {
        return ruleWay;
    }

    public void setRuleWay(Integer ruleWay) {
        this.ruleWay = ruleWay;
    }

    public Integer getRuleValue() {
        return ruleValue;
    }

    public void setRuleValue(Integer ruleValue) {
        this.ruleValue = ruleValue;
    }

    public Integer getNoticeWay() {
		return noticeWay;
	}

	public void setNoticeWay(int noticeWay) {
		this.noticeWay = noticeWay;
	}

	public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public WaterChargeElfBean(Integer ruleWay, Integer ruleValue, Integer noticeWay, String beginTime, String endTime) {
        this.ruleWay = ruleWay;
        this.ruleValue = ruleValue;
        this.noticeWay = noticeWay;
        this.beginTime = beginTime;
        this.endTime = endTime;
    }

    public WaterChargeElfBean() {
    }
}
