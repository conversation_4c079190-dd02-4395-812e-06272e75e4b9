package com.koron.zys.baseConfig.impl;

import org.apache.log4j.Logger;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ProjectPayeeBean;
import com.koron.zys.baseConfig.mapper.ProjectPayeeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 工程收款主体 根据ID查找
 * <AUTHOR>
 *
 */
public class ProjectPayeeQuery implements ServerInterface{
	private static Logger log = Logger.getLogger(ProjectPayeeSave.class);
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		MessageBean<ProjectPayeeBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", ProjectPayeeBean.class);

		try { 
			ProjectPayeeBean bean = JsonUtils.objectToPojo(req.getData(), ProjectPayeeBean.class);
			ProjectPayeeMapper mapper = factory.getMapper(ProjectPayeeMapper.class);
			ProjectPayeeBean rojectPayeeBean = mapper.query(bean);
			info.setData( rojectPayeeBean);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			log.error(e.getMessage(), e);
		}
		return info;
	}

}
