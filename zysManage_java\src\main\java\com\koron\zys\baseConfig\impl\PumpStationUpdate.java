package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.PumpStationBean;
import com.koron.zys.baseConfig.mapper.PumpStationMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;
/**
 * 泵站信息-编辑
 * <AUTHOR>
 *
 */
public class PumpStationUpdate implements ServerInterface{
	
private static Logger logger = LoggerFactory.getLogger(PumpStationUpdate.class);
	
@Override
public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
	PumpStationMapper mapper = factory.getMapper(PumpStationMapper.class);
	PumpStationBean bean = null;
	try {
		bean = JsonUtils.objectToPojo(req.getData(), PumpStationBean.class);
		// 校验字段重复
		if (mapper.check2("pump_station_name", bean.getPumpStationName(),bean.getPumpStationId()) > 0) {
			return MessageBean.create(Constant.ILLEGAL_PARAMETER,"泵站名称：" + bean.getPumpStationName() + "的信息已存在。", void.class);
		}
		if (StringUtils.isNullOrEmpty(bean.getPumpStationId())) {
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
		}
		bean.setUpdateName(userInfo.getUserInfo().getName());
		bean.setUpdateTime(CommonUtils.getCurrentTime());
		mapper.updatePumpStation(bean);
	} catch (Exception e) {
		logger.error("非法参数",e);
		return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
	}
	return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
}
}