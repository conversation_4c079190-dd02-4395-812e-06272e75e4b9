package com.koron.zys.baseConfig.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.CostNameBean;
import com.koron.zys.baseConfig.bean.WaterPriceBean;
import com.koron.zys.baseConfig.bean.WaterPriceDetailBean;
import com.koron.zys.baseConfig.bean.WaterPriceLadderBean;
import com.koron.zys.baseConfig.mapper.CostMapper;
import com.koron.zys.baseConfig.mapper.WaterPriceMapper;
import com.koron.zys.baseConfig.queryBean.WaterPriceQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
/**
 * 用水价格-编辑初始化
 * <AUTHOR>
 *
 */
public class WaterPriceQuery implements ServerInterface {
	
private static Logger logger = LoggerFactory.getLogger(WaterPriceQuery.class);
	
	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<HashMap> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", HashMap.class);

		try { 
			WaterPriceQueryBean bean = JsonUtils.objectToPojo(req.getData(), WaterPriceQueryBean.class);
			WaterPriceMapper mapper = factory.getMapper(WaterPriceMapper.class);
			//查询用水价格信息
			WaterPriceBean waterPricebean = mapper.selectWaterPriceById(bean.getId());
			//查询用水价格明细
			List<WaterPriceDetailBean> waterPricDetailList = mapper.selectWaterPriceBetailById(bean.getId());
			
			//根据最新的费用类型对明细进行重排
			List<WaterPriceDetailBean> newWaterPricDetailList = new ArrayList<>();
			CostMapper cmapper = factory.getMapper(CostMapper.class);		
			List<CostNameBean>  costList = cmapper.selectCostNameList();	
			for(CostNameBean cb :costList) {
				boolean found = false;
				for(WaterPriceDetailBean db:waterPricDetailList) {
					if(cb.getId().equals(db.getCostId())) {
						db.setCostName(cb.getName());
						newWaterPricDetailList.add(db);
						List<WaterPriceLadderBean> ladderList = new ArrayList<WaterPriceLadderBean>();
						ladderList = mapper.selectWaterPriceLadderById(db.getId());
						db.setLadders(ladderList);
						db.setLadderList(new ArrayList<>());
						found = true;
						break;
					}
				}
				if(!found) {
					WaterPriceDetailBean db = new WaterPriceDetailBean();
					db.setCostId(cb.getId());
					db.setCostName(cb.getName());
					db.setFixedMoneyUnit("2");
					db.setLadderType("1");
					db.setFixedPriceUnit("1");
					db.setLadderCalculateWay("1");
					db.setLadderList(new ArrayList<>());
					db.setLadders(new ArrayList<>());
					newWaterPricDetailList.add(db);
				}
			}
			waterPricebean.setDetails(newWaterPricDetailList);
			
			HashMap<String,Object> map = new HashMap<>();
			map.put("WaterPricebean", waterPricebean);
			map.put("costlist", costList);
			info.setData( map);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}
