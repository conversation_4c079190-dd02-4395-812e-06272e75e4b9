package com.koron.zys.baseConfig.queryBean;

public class ManageAreaQueryBean {
	
	/**
	 * 主键ID
	 */
	private String manageAreaId;
	
	private String parentId;
	/**
	 * 行政区划代码（国标）code
	 */
	private String areaNo;
	/**
	 * 名称name
	 */
	private String areaName;

	/**
	 * 是否叶子节点 1是0否
	 */
	private Integer isLeaf;
	
	/**
	 * 状态启用状态 [ 0 停用 1 启用 ]
	 */
	private Integer status;
	
	/**
	 * 排序号
	 */
	private Integer sortNo;
	
	/**
	 * 区域描述
	 */
	private String areaComments;

	public String getManageAreaId() {
		return manageAreaId;
	}

	public void setManageAreaId(String manageAreaId) {
		this.manageAreaId = manageAreaId;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getAreaNo() {
		return areaNo;
	}

	public void setAreaNo(String areaNo) {
		this.areaNo = areaNo;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public Integer getIsLeaf() {
		return isLeaf;
	}

	public void setIsLeaf(Integer isLeaf) {
		this.isLeaf = isLeaf;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getSortNo() {
		return sortNo;
	}

	public void setSortNo(Integer sortNo) {
		this.sortNo = sortNo;
	}

	public String getAreaComments() {
		return areaComments;
	}

	public void setAreaComments(String areaComments) {
		this.areaComments = areaComments;
	}
	
	
	
	

}
