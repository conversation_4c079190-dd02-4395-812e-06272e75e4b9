<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.WaterPriceMapper">
 	<!--列表初始化  -->
	<select id="selectWaterPriceList" parameterType="com.koron.zys.baseConfig.queryBean.WaterPriceQueryBean" resultType="com.koron.zys.baseConfig.vo.WaterPriceVO" >
		SELECT XX.*,YY.WATER_TYPE_NO,YY.WATER_TYPE_NAME
		FROM BASE_WATER_PRICE XX JOIN BASE_WATER_TYPE YY ON XX.WATER_TYPE_ID=YY.ID
		WHERE 1=1
	    <if test="status != null">
	    	AND XX.STATUS = #{status} 
	    </if>
	    <if test="waterTypeName !=null and waterTypeName !=''">     
        	AND YY.WATER_TYPE_NAME  LIKE concat('%',#{waterTypeName},'%')
        </if>
        <if test="waterTypeId !=null and waterTypeId !=''">     
        	AND XX.WATER_TYPE_ID =#{waterTypeId}
        </if>
        <if test="processState != null and processState != ''">
        	AND XX.PROCESS_STATE = #{processState}
        </if>
        <if test="code != null and code != ''">
        	AND YY.water_type_no like concat(#{code},'%')
        </if>
        <if test="waterTypeIdList != null">
			AND YY.ID in
			<foreach collection="waterTypeIdList" item="waterTypeId" open="(" close=")" separator=",">
				#{waterTypeId}
			</foreach>
		</if>
        ORDER BY XX.effective_date DESC
	</select>
	<!--添加用水价格  -->
	<insert id="insertWaterPrice" parameterType="com.koron.zys.baseConfig.bean.WaterPriceBean">
		insert into BASE_WATER_PRICE(id,water_type_id,remark, effective_date,status,tenant_id,
		create_time, create_name,create_account,update_time, update_name,update_account)
		values
		(
		#{id,jdbcType=VARCHAR},
		#{waterTypeId,jdbcType=VARCHAR},
		#{remark,jdbcType=VARCHAR},
		#{effectiveDate},
		#{status,jdbcType=INTEGER},
		#{tenantId},
		#{createTime},
		#{createName,jdbcType=VARCHAR},
		#{createAccount,jdbcType=VARCHAR},
		#{updateTime},
		#{updateName,jdbcType=VARCHAR},
		#{updateAccount,jdbcType=VARCHAR}
		)
	</insert>
	<!--添加价格明细  -->
	<insert id="insertWaterPriceDetail" parameterType="com.koron.zys.baseConfig.bean.WaterPriceDetailBean">
		insert into BASE_WATER_PRICE_DETAIL(  
		   id,water_price_id,PENALTY_STRATEGY_ID,cost_id,fixed_price,fixed_price_unit,fixed_money,fixed_money_unit,ladder_type,ladder_calculate_way,
		   person_base,water_base,tenant_id,create_time, create_name,create_account,update_time, update_name,update_account,min_money,max_money
		)
		values
		(
		 #{id,jdbcType=VARCHAR},
         #{waterPriceId,jdbcType=VARCHAR},
         #{penaltyStrategyId,jdbcType=VARCHAR},
         #{costId,jdbcType=VARCHAR}  ,
         #{fixedPrice}  ,
         #{fixedPriceUnit}  ,
         #{fixedMoney}  ,
         #{fixedMoneyUnit}  ,
         #{ladderType}  ,
         #{ladderCalculateWay}  ,
         #{personBase}  ,
         #{waterBase}  ,
		 #{tenantId},
		 #{createTime},
		 #{createName,jdbcType=VARCHAR},
		 #{createAccount,jdbcType=VARCHAR},
		 #{updateTime},
		 #{updateName,jdbcType=VARCHAR},
		 #{updateAccount,jdbcType=VARCHAR},
		 #{minMoney},
		 #{maxMoney}
		)
	</insert>
	<!--添加阶梯明细  -->
	<insert id="insertWaterPriceLadder" parameterType="com.koron.zys.baseConfig.bean.WaterPriceLadderBean">
		insert into BASE_WATER_PRICE_LADDER(  
		   id,water_price_detail_id,begin_water,end_water,price,ladder_begin_money,tenant_id,create_time, create_name,create_account,update_time, update_name,update_account
		)
		values
		(
		 #{id,jdbcType=VARCHAR},
         #{waterPriceDetailId,jdbcType=VARCHAR},
         #{beginWater}  ,
         #{endWater}  ,
         #{price}  ,
         #{ladderBeginMoney}  ,
		 #{tenantId},
		 #{createTime},
		 #{createName,jdbcType=VARCHAR},
		 #{createAccount,jdbcType=VARCHAR},
		 #{updateTime},
		 #{updateName,jdbcType=VARCHAR},
		 #{updateAccount,jdbcType=VARCHAR} 
		)
	</insert>
	<!--修改用水价格  -->
	<update id="updateWaterPrice" parameterType="com.koron.zys.baseConfig.bean.WaterPriceBean">
		update BASE_WATER_PRICE
		set water_type_id = #{waterTypeId,jdbcType=VARCHAR},	
		remark = #{remark,jdbcType=VARCHAR},	
		effective_date = #{effectiveDate},
		status = #{status,jdbcType=INTEGER},		
		update_time = date_format(#{updateTime}, '%Y-%m-%d %T'),
		update_name = #{updateName,jdbcType=VARCHAR},
		update_account = #{updateAccount,jdbcType=VARCHAR}
		where id = #{id}
	</update>
	<update id="updateWaterPriceStatus" parameterType="com.koron.zys.baseConfig.bean.WaterPriceBean">
		UPDATE BASE_WATER_PRICE
		SET STATUS = #{status,jdbcType=INTEGER},		
			UPDATE_TIME = DATE_FORMAT(#{updateTime}, '%Y-%m-%d %T'),
			UPDATE_NAME = #{updateName,jdbcType=VARCHAR},
			UPDATE_ACCOUNT = #{updateAccount,jdbcType=VARCHAR}
		WHERE WATER_TYPE_ID = #{waterTypeId}
	</update>
	<!--编辑初始化用水价格信息-->
	<select id="selectWaterPriceById"
		resultType="com.koron.zys.baseConfig.bean.WaterPriceBean">
		select *
		from BASE_WATER_PRICE xx 	   
	    where xx.id = #{id}
	</select>
	<select id="selectWaterPriceByWaterTypeId"
		resultType="com.koron.zys.baseConfig.bean.WaterPriceBean">
		select * from BASE_WATER_PRICE 
		where water_type_id = #{waterTypeId}
		  and process_state = 'END'
		  and effective_date = (select MAX(effective_date)
			from BASE_WATER_PRICE xx 	   
		    where xx.water_type_id = #{waterTypeId}
		      and status = 1
		      and process_state = 'END')
		order by id desc
		limit 1 
	</select>
	<!--编辑初始化价格明细信息-->
	<select id="selectWaterPriceBetailById"
		resultType="com.koron.zys.baseConfig.bean.WaterPriceDetailBean">
	    select * 
	    from BASE_WATER_PRICE_DETAIL 
	    where 
	    water_price_id = #{waterPriceId}
	</select>
	<!--编辑初始化价格明细信息-->
	<select id="selectWaterPriceBetailByIdWithCostNo"
			resultType="com.koron.zys.baseConfig.bean.WaterPriceDetailBean">
		select a.*,b.cost_no
		from BASE_WATER_PRICE_DETAIL a,base_cost b
		where a.cost_id= b.id
			and water_price_id = #{waterPriceId}
	</select>
	<!--编辑初始化价格明细信息-->
	<select id="selectWaterPriceBetailByIdForRedRush"
		resultType="com.koron.zys.baseConfig.bean.WaterPriceDetailBean">
	    select a.*,b.cost_no,b.cost_name
	    from BASE_WATER_PRICE_DETAIL a,base_cost b 
	    where a.cost_id=b.id
	    and water_price_id = #{id}
	</select>
	<!--编辑初始化价格阶梯明细信息-->
	<select id="selectWaterPriceLadderById"
		resultType="com.koron.zys.baseConfig.bean.WaterPriceLadderBean">
	    select * 
	    from BASE_WATER_PRICE_LADDER 
	    where 
	    water_price_detail_id = #{waterPriceDetailId}
	</select>
	<!--编辑初始化价格阶梯明细信息并排序-->
	<select id="selectWaterPriceLadderByIdWithOrder"
			resultType="com.koron.zys.baseConfig.bean.WaterPriceLadderBean">
		select *
		from BASE_WATER_PRICE_LADDER
		where
			water_price_detail_id = #{waterPriceDetailId}
		order by price
	</select>
	<delete id="deleteWaterPriceLadderByPriceId"> 	    
	    delete
	    from BASE_WATER_PRICE_LADDER 
	    where 
	    water_price_detail_id in ( select id from BASE_WATER_PRICE_DETAIL where water_price_id  = #{id} ) 
	</delete>
	
	<delete id="deleteWaterPriceDetailByPriceId"> 	    
	    delete
	    from BASE_WATER_PRICE_DETAIL 
	    where water_price_id  = #{id} 
	</delete>
	<select id="getWaterPriceIdByType" parameterType="String" resultType="String">
		select id from BASE_WATER_PRICE 
		where water_type_id = #{useWaterType}
		  and process_state = 'END'
		  and effective_date = (select MAX(effective_date)
			from BASE_WATER_PRICE xx 	   
		    where xx.water_type_id = #{useWaterType}
		      and status = 1
		      and process_state = 'END')
		order by id desc
		limit 1 
	</select>
	<update id="updateWaterPriceRemark" parameterType="com.koron.zys.baseConfig.bean.WaterPriceBean">
		UPDATE BASE_WATER_PRICE
			SET remark = #{remark,jdbcType=VARCHAR}
		WHERE id = #{id}
	</update>

	<select id="findNextIdByParentId" resultType="com.koron.zys.serviceManage.bean.SelectBean">
		select y.id code, water_type_name name, parent_Id, y.id
		from base_water_type x,base_water_price y
		where x.id = y.water_type_id
		<if test="useWaterType != null and useWaterType !=''">
			and x.id = #{useWaterType}
		</if>
		<if test="parentId != null and parentId != ''">
			<choose>
				<when test="parentId == null or parentId == '' or parentId == '0'.toString()">
					and (x.parent_id is null or x.parent_id = '0' or x.parent_id = '' )
				</when>
				<otherwise>
					and x.parent_id=#{parentId}
				</otherwise>
			</choose>
		</if>
	</select>
	<select id="selectAllWaterPrice" resultType="java.lang.String">

		select id from base_water_price where water_type_id in (
			select id from base_water_type where id = #{useWaterType}
			union all
			select id from base_water_type where parent_Id = #{useWaterType}
			union all
			select id from base_water_type where parent_Id in (select id from base_water_type where parent_Id = #{useWaterType})
		)
	</select>

	<select id="queryList" parameterType="com.koron.zys.baseConfig.queryBean.WaterPriceQueryBean" resultType="com.koron.zys.baseConfig.vo.WaterPriceVO" >
		SELECT XX.*
		FROM BASE_WATER_PRICE XX
		WHERE 1=1
		<if test="status != null">
			AND XX.STATUS = #{status}
		</if>
<!--		<if test="waterTypeName !=null and waterTypeName !=''">-->
<!--			AND YY.WATER_TYPE_NAME  LIKE concat('%',#{waterTypeName},'%')-->
<!--		</if>-->
		<if test="waterTypeId !=null and waterTypeId !=''">
			AND XX.WATER_TYPE_ID =#{waterTypeId}
		</if>
		<if test="processState != null and processState != ''">
			AND XX.PROCESS_STATE = #{processState}
		</if>
		<!--<if test="code != null and code != ''">
			AND YY.water_type_no like concat(#{code},'%')
		</if>
		<if test="waterTypeIdList != null">
			AND YY.ID in
			<foreach collection="waterTypeIdList" item="waterTypeId" open="(" close=")" separator=",">
				#{waterTypeId}
			</foreach>
		</if>-->
		ORDER BY XX.WATER_TYPE_ID
	</select>
</mapper>