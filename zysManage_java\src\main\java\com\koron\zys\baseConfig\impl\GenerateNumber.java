package com.koron.zys.baseConfig.impl;

import org.koron.ebs.mybatis.SessionFactory;

/**
 * 生成编号接口 根据 BaseNumberInfo 定义的规则生成单据编号
 * 每种类型的单据编号是唯一的
 * @see BaseNumberInfo
 * <AUTHOR>
 *
 */
public interface GenerateNumber {
	
	/**
	 * 
	 * @param factory
	 * @param moduleCode 
	 * @return
	 */
	String generate(SessionFactory factory, String ruleCode);
	
	/**
	 * 
	 * @param factory
	 * @param moduleCode
	 * @param waterCode
	 * @return
	 */
	String generate(SessionFactory factory, String ruleCode, String waterCode);
	
}
