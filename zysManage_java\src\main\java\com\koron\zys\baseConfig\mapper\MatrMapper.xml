<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.MatrMapper">

    <select id="selectMatrList" parameterType="com.koron.zys.baseConfig.queryBean.MatrQueryBean"
            resultType="com.koron.zys.baseConfig.vo.MatrVO">
 		select * from BASE_MATR
		where 1=1
		<if test="matrNo != null and matrNo != ''">
			and matr_no  LIKE CONCAT('%',#{matrNo},'%')
		</if>
	    order by create_time desc
	</select>

    <insert id="insertMatr" parameterType="com.koron.zys.baseConfig.bean.MatrBean">
		insert into BASE_MATR (id,matr_no,matr_price, tenant_id, create_time, create_account,create_name)
		values
		(
		#{id},
		#{matrNo},
		#{matrPrice},
		#{tenantId},
		#{createTime},
		#{createAccount},
		#{createName}
		)
	</insert>

    <update id="updateMatr" parameterType="com.koron.zys.baseConfig.bean.MatrBean">
		update BASE_MATR
		set matr_no = #{matrNo},
		    matr_price = #{matrPrice},
			update_time=#{updateTime},
			update_account=#{updateAccount},
			update_name = #{updateName}
		    where id = #{id}
	</update>


</mapper>