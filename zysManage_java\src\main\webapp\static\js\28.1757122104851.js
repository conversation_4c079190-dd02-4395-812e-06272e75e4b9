webpackJsonp([28],{"6V8r":function(t,e){},"Oo+T":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r={name:"bankInterfaceManAdd",data:function(){return{databaseData:[],ruleForm:{id:"",name:"",jarFile:{},communicationType:"",bankDriverClassName:"",bankFtpIp:"",bankFtpPort:"",bankFtpPassword:"",bankClientIp:"",bankClientPort:"",bankServerPort:"",configParam:"",status:""},formData:{id:"",name:"",jarFile:{},communicationType:"",bankDriverClassName:"",bankFtpIp:"",bankFtpPort:"",bankFtpPassword:"",bankClientIp:"",bankClientPort:"",bankServerPort:"",configParam:"",status:""},dictionaryData:[],detail:!1}},mounted:function(){this.getDictionary()},methods:{getDictionary:function(){var t=this;this.$api.fetch({params:{busicode:"DictionarySelect",data:"PLUGIN_STATUS"}}).then(function(e){t.dictionaryData=e.PLUGIN_STATUS})},getFile:function(t){this.ruleForm.jarFile=t.target.files[0]},resetForm:function(){this.detail=!1,this.$refs.ruleForm.resetFields()},submitForm:function(t,e,a){var r=this;this.ruleForm=this.common.handleData(this.ruleForm,this.formData),this.$refs[t].validate(function(t){t&&("添加"===e?r.uploadAttachment("add"):r.uploadAttachment("update"))})},uploadAttachment:function(t){var e=this,a={busicode:"add"==t?"BankPluginAdd":"BankPluginUpdate",type:"signUpload",data:{id:e.ruleForm.id,name:e.ruleForm.name,communicationType:e.ruleForm.communicationType,bankDriverClassName:e.ruleForm.bankDriverClassName,bankFtpIp:e.ruleForm.bankFtpIp,bankFtpPort:e.ruleForm.bankFtpPort,bankFtpPassword:e.ruleForm.bankFtpPassword,bankClientIp:e.ruleForm.bankClientIp,bankClientPort:e.ruleForm.bankClientPort,bankServerPort:e.ruleForm.bankServerPort,configParam:e.ruleForm.configParam,file:e.ruleForm.jarFile,status:e.ruleForm.status},token:"krrjdev123",sysType:"002"};this.$api.fetch({params:a}).then(function(t){e.$message({type:"success",message:"操作成功"}),e.$parent.selectTSubSystem(),e.$parent.closeDialog(),e.resetForm()})},handleClose:function(){this.detail?this.$parent.closeDialog():this.common.chargeObjectEqual(this,this.ruleForm,"get","bankInterfaceManAdd",this.$parent.closeDialog)},editData:function(t,e){this.detail=!!e;var a=this.ruleForm;a.name=t.name,a.bankClientIp=t.bankClientIp,a.bankClientPort=t.bankClientPort,a.bankDriverClassName=t.bankDriverClassName,a.bankFtpIp=t.bankFtpIp,a.bankFtpPassword=t.bankFtpPassword,a.bankFtpPort=t.bankFtpPort,a.bankServerPort=t.bankServerPort,a.communicationType=t.communicationType,a.communicationTypeName=t.communicationTypeName,a.configParam=t.configParam,a.status="number"==typeof t.status?String(t.status):t.status,a.statusName=t.statusName,a.id=t.id}}},n={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bankInterfaceManAdd"},[a("el-form",{ref:"ruleForm",staticClass:"formBill-Two",attrs:{model:t.ruleForm,"label-width":"120px",inline:!0}},[a("el-form-item",{attrs:{label:"驱动名称："}},[a("el-input",{attrs:{disabled:t.detail},model:{value:t.ruleForm.name,callback:function(e){t.$set(t.ruleForm,"name",e)},expression:"ruleForm.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"通讯类型：",prop:"communicationType"}},[a("el-select",{attrs:{disabled:t.detail,placeholder:"",clearable:"",size:"mini"},model:{value:t.ruleForm.communicationType,callback:function(e){t.$set(t.ruleForm,"communicationType",e)},expression:"ruleForm.communicationType"}},[a("el-option",{attrs:{label:"socket",value:1}}),t._v(" "),a("el-option",{attrs:{label:"http",value:2}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"驱动类路径：",prop:"bankDriverClassName"}},[a("el-input",{attrs:{disabled:t.detail,clearable:""},model:{value:t.ruleForm.bankDriverClassName,callback:function(e){t.$set(t.ruleForm,"bankDriverClassName",e)},expression:"ruleForm.bankDriverClassName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"银行FTP地址：",prop:"bankFtpIp"}},[a("el-input",{attrs:{disabled:t.detail,placeholder:"",clearable:""},model:{value:t.ruleForm.bankFtpIp,callback:function(e){t.$set(t.ruleForm,"bankFtpIp",e)},expression:"ruleForm.bankFtpIp"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"银行FTP端口：",prop:"bankFtpPort"}},[a("el-input",{attrs:{disabled:t.detail,oninput:"this.value = this.value.replace(/[^0-9]/g, '');",placeholder:"",clearable:""},model:{value:t.ruleForm.bankFtpPort,callback:function(e){t.$set(t.ruleForm,"bankFtpPort",e)},expression:"ruleForm.bankFtpPort"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"银行FTP密码：",prop:"bankFtpPassword"}},[a("el-input",{attrs:{disabled:t.detail,placeholder:"",clearable:""},model:{value:t.ruleForm.bankFtpPassword,callback:function(e){t.$set(t.ruleForm,"bankFtpPassword",e)},expression:"ruleForm.bankFtpPassword"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"客户端IP：",prop:"bankClientIp"}},[a("el-input",{attrs:{disabled:t.detail,placeholder:"",clearable:""},model:{value:t.ruleForm.bankClientIp,callback:function(e){t.$set(t.ruleForm,"bankClientIp",e)},expression:"ruleForm.bankClientIp"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"客户端端口：",prop:"bankClientPort"}},[a("el-input",{attrs:{disabled:t.detail,oninput:"this.value = this.value.replace(/[^0-9]/g, '');",placeholder:"",clearable:""},model:{value:t.ruleForm.bankClientPort,callback:function(e){t.$set(t.ruleForm,"bankClientPort",e)},expression:"ruleForm.bankClientPort"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"服务端端口：",prop:"bankServerPort"}},[a("el-input",{attrs:{disabled:t.detail,oninput:"this.value = this.value.replace(/[^0-9]/g, '');",placeholder:"",clearable:""},model:{value:t.ruleForm.bankServerPort,callback:function(e){t.$set(t.ruleForm,"bankServerPort",e)},expression:"ruleForm.bankServerPort"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"状态："}},[a("el-select",{attrs:{disabled:t.detail,placeholder:"请选择"},model:{value:t.ruleForm.status,callback:function(e){t.$set(t.ruleForm,"status",e)},expression:"ruleForm.status"}},t._l(t.dictionaryData,function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.value}})}),1)],1),t._v(" "),t.detail?t._e():a("el-form-item",{attrs:{label:"驱动文件："}},[a("input",{staticStyle:{"font-size":"12px"},attrs:{type:"file"},on:{change:function(e){return t.getFile(e)}}})]),t._v(" "),a("el-form-item",{staticClass:"remark f3",staticStyle:{"min-width":"400px"},attrs:{label:"配置参数：",prop:"configParam"}},[a("el-input",{staticClass:"expSql",attrs:{disabled:t.detail,type:"textarea","show-word-limit":"",maxlength:"2000",clearable:""},model:{value:t.ruleForm.configParam,callback:function(e){t.$set(t.ruleForm,"configParam",e)},expression:"ruleForm.configParam"}})],1)],1)],1)},staticRenderFns:[]};var i={name:"bankInterfaceMan",components:{bankInterfaceManAdd:a("VU/8")(r,n,!1,function(t){a("6V8r")},"data-v-233cf730",null).exports},data:function(){return{total:0,tableShow:!0,tableQuery:{page:1,pageCount:50},formData:{name:"",jarFile:{},communicationType:"",bankDriverClassName:"",bankFtpIp:"",bankFtpPort:"",bankFtpPassword:"",bankClientIp:"",bankClientPort:"",bankServerPort:"",configParam:"",status:""},maxHeight:0,appServerData:[],showSelectIndex:-1,crumbsData:{titleList:[{title:"基础配置",path:"/ChangeTables"},{title:"银行驱动",method:function(){window.histroy.back()}}]},bankInterfaceManShow:!0,bankInterfaceManAddVisible:!1,detail:""}},mounted:function(){var t=this;this.$nextTick(function(){t.common.changeTable(t,".bankInterfaceMan .kl-table",[])}),this.selectTSubSystem()},methods:{deleteOperate:function(t){var e=this,a=this;this.$confirm("确认删除？").then(function(){var r={busicode:"BankPluginDelete",data:{id:t.row.id}};e.$api.fetch({params:r}).then(function(t){a.$message({showClose:!0,message:"删除成功",type:"success"}),a.selectTSubSystem()})}).catch(function(){a.$message({type:"info",message:"已取消删除"})})},submitForm:function(t){var e=this.crumbsData.titleList[2].title;this.$refs.child.submitForm(t,e)},formatStatus:function(t){return 0===t.status?"启用":"停用"},clear:function(){this.formData.name="",this.formData.communicationType="",this.formData.bankDriverClassName="",this.formData.bankFtpIp="",this.formData.bankFtpPort="",this.formData.bankFtpPassword="",this.formData.bankClientIp="",this.formData.bankClientPort="",this.formData.bankServerPort="",this.formData.configParam="",this.formData.bankInterfaceNo="",this.formData.bankInterfaceName="",this.formData.driverClass="",this.formData.jarFile={},this.formData.status="",this.formData.bankInterfaceId="",this.detail=""},appAdd:function(t,e){var a=this;if(this.bankInterfaceManAddVisible=!0,this.bankInterfaceManShow=!1,this.clear(),"add"===t)this.$set(this.crumbsData.titleList,"2",{title:"添加",method:function(){window.histroy.back()}}),this.common.chargeObjectEqual(this,this.formData,"set","bankInterfaceManAdd");else{this.detail=e,this.$set(this.crumbsData.titleList,"2",{title:e?"详情":"编辑",method:function(){window.histroy.back()}});var r={busicode:"BankPluginQuery",data:{id:t.row.id}};this.$api.fetch({params:r}).then(function(t){var r=a.formData;a.$refs.child.editData(t,e),a.common.chargeObjectEqual(a,r,"set","bankInterfaceManAdd")})}},showSelect:function(t){this.showSelectIndex=t.$index},editor:function(t){this.formData=t.row,this.parentAppDialogVisible=!0},indexMethod:function(t){return(this.tableQuery.page-1)*this.tableQuery.pageCount+t+1},handleSizeChange:function(t){this.tableQuery.pageCount=t,this.tableQuery.page=1,this.selectTSubSystem()},handleCurrentChange:function(t){this.tableQuery.page=t,this.selectTSubSystem()},selectTSubSystem:function(){var t=this;t.detail="";this.$api.fetch({params:{busicode:"BankPluginList",data:{}}}).then(function(e){t.$set(t.appServerData,"list",e)})},closeDialog:function(){this.bankInterfaceManShow=!0,this.bankInterfaceManAddVisible=!1,this.crumbsData.titleList.pop(),this.$refs.child.resetForm()},handleClose:function(){this.$refs.child.handleClose()}},watch:{maxHeight:function(){var t=this;this.tableShow=!1,this.$nextTick(function(){t.tableShow=!0})}}},l={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bankInterfaceMan"},[a("div",{staticClass:"main-content"},[a("div",{staticClass:"bread-contain"},[a("publicCrumbs",{attrs:{crumbsData:t.crumbsData}}),t._v(" "),t.bankInterfaceManAddVisible?a("div",{staticClass:"bread-contain-right"},[t.detail?t._e():a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.submitForm("ruleForm")}}},[t._v("保存")]),t._v(" "),a("el-button",{attrs:{icon:"el-icon-caret-left",size:"mini"},on:{click:t.handleClose}},[t._v("返回")])],1):a("div",{staticClass:"bread-contain-right"},[a("el-button",{staticClass:"el-icon-plus",attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.appAdd("add")}}},[t._v("添加")])],1)],1),t._v(" "),t.bankInterfaceManAddVisible?a("bankInterfaceManAdd",{ref:"child"}):a("div",{staticClass:"kl-table"},[t.tableShow?a("el-table",{attrs:{stripe:"",border:"",data:t.appServerData.list,"max-height":t.maxHeight}},[a("el-table-column",{attrs:{type:"index",label:"NO.",width:"50",fixed:"left",index:t.indexMethod}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",label:"驱动名称","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"classPath",label:"驱动类路径","show-overflow-tooltip":"","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"statusName","min-width":"80",label:"状态"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"150",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.appAdd(e,"detail")}}},[t._v("查看")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.appAdd(e)}}},[t._v("编辑")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.deleteOperate(e)}}},[t._v("删除")])]}}],null,!1,2735699332)})],1):t._e()],1)],1)])},staticRenderFns:[]};var o=a("VU/8")(i,l,!1,function(t){a("mLtd")},null,null);e.default=o.exports},mLtd:function(t,e){}});