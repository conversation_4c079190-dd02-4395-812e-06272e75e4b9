package com.koron.zys.baseConfig.impl;

import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.MeterStatusBean;
import com.koron.zys.baseConfig.mapper.MeterStatusMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
/**
 * 抄表状态-添加
 * <AUTHOR>
 *
 */
public class MeterStatusAdd implements ServerInterface{

	private static Logger logger = LoggerFactory.getLogger(MeterStatusAdd.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		MeterStatusMapper mapper = factory.getMapper(MeterStatusMapper.class);
		MeterStatusBean bean = new MeterStatusBean();
		try {
			bean = JsonUtils.objectToPojo(req.getData(), MeterStatusBean.class);
		} catch (Exception e) {
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		// 校验字段重复
		if (mapper.check("status_name", bean.getStatusName()) > 0) {
			return MessageBean.create(Constant.ILLEGAL_PARAMETER,"抄表状态名称：" + bean.getStatusName() + "的信息已存在。", void.class);
		}
		bean.setId(new ObjectId().toHexString());
		bean.setCreateName(userInfo.getUserInfo().getName());
		bean.setCreateTime(CommonUtils.getCurrentTime());
		
		mapper.insertMeterStatus(bean);
		return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
	}
}