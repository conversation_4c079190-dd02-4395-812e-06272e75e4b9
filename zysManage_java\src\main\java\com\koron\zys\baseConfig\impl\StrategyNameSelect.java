package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;

import com.koron.zys.baseConfig.mapper.PenaltyStrategyMapper;
import com.koron.zys.baseConfig.queryBean.PenaltyQueryBean;
import com.koron.zys.baseConfig.vo.SelectVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 *查询 违约金策略名称下拉框
 * <AUTHOR>
 *
 */
public class StrategyNameSelect implements ServerInterface{

	private static Logger logger = LoggerFactory.getLogger(StrategyNameSelect.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);

		try {
			PenaltyQueryBean bean = JsonUtils.objectToPojo(req.getData(), PenaltyQueryBean.class);
			PenaltyStrategyMapper mapper = factory.getMapper(PenaltyStrategyMapper.class);
			List<SelectVO>  list = mapper.selectStrategyNameList();
			info.setData(list);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}
}
