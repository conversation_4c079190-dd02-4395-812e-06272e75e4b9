package com.koron.zys.baseConfig.impl;

import com.koron.zys.baseConfig.queryBean.ConfigVacationQueryBean;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ConfigVacationBean;
import com.koron.zys.baseConfig.mapper.ConfigVacationMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.mysql.cj.util.StringUtils;

/**
 * 假期设置-编辑
 *
 * <AUTHOR>
 */
public class PubConfigVacationUpdate implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(PubConfigVacationUpdate.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            ConfigVacationMapper mapper = factory.getMapper(ConfigVacationMapper.class, "_default");
            ConfigVacationBean bean = JsonUtils.objectToPojo(req.getData(), ConfigVacationBean.class);
            // 校验字段重复
            if (mapper.check2("vacation_name", bean.getVacationName(), bean.getId()) > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "假期名称：" + bean.getVacationName() + "的信息已存在。", void.class);
            }
            if (StringUtils.isNullOrEmpty(bean.getId())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id为空", void.class);
            }
            if (StringUtils.isNullOrEmpty(bean.getVacationBeginDate()) || StringUtils.isNullOrEmpty(bean.getVacationEndDate())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "假期开始日期和结束日期不能为空。", void.class);
            }
            ConfigVacationQueryBean configVacationQueryBean = new ConfigVacationQueryBean();
            configVacationQueryBean.setVacationBeginDate(bean.getVacationBeginDate());
            configVacationQueryBean.setVacationEndDate(bean.getVacationEndDate());
            configVacationQueryBean.setId(bean.getId());
            Integer counts = mapper.checkConfigVacation(configVacationQueryBean);
            if (counts > 0) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "假期日期已存在。", void.class);
            }

            bean.setUpdateInfo(userInfo);
            mapper.updateConfigVacation(bean);
        } catch (Exception e) {
            logger.error("假期设置修改失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "假期设置修改失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}