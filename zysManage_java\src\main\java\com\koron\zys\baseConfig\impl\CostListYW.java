package com.koron.zys.baseConfig.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.CostMapper;
import com.koron.zys.baseConfig.vo.CostVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.Tools;
/**
 * 费用名称-列表初始化
 * <AUTHOR>
 */
public class CostListYW implements ServerInterface  {
	
	private static Logger logger = LoggerFactory.getLogger(CostListYW.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		
		@SuppressWarnings("rawtypes")
		MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);

		try {
			//先查询公共库的费用类型
 			CostMapper mapper = factory.getMapper(CostMapper.class, "_default");
 			List<CostVO> list = mapper.selectPubCostList(null);
 			//过滤掉停用费用
			List<CostVO> enabledCostList = list.stream().filter(dto -> {
				String status = dto.getStatus();
				return !status.equals("2");
			}).collect(Collectors.toList());
			//再查询水司库费用类型
 			mapper = factory.getMapper(CostMapper.class);
			List<CostVO> comlist = mapper.selectCostListIgnoreStatus();	
			Map<String,String> cou = Tools.mapDicByCode(factory, "COU");
 			//设置已经启用的费用类型
 			for(CostVO vo :enabledCostList) {					//运维同步到水司
 				vo.setUsed(false);
 				vo.setStatus("0");
 				for(CostVO vo1:comlist) {
 					if(vo.getId().equals(vo1.getId())){
						vo.setCostUnit(vo1.getCostUnit());
						vo.setIsMust(vo1.getIsMust());
						vo.setAllowRushRed(vo1.getAllowRushRed());
						vo.setIsComprehensive(vo1.getIsComprehensive());
					}
 					if(vo.getId().equals(vo1.getId()) && "1".equals(vo1.getStatus())) {
 						vo.setUsed(true);
 						vo.setStatus("1");
 						break;
 					} 					
 				}
 				vo.setCostUnit(cou.get(vo.getCostUnit()));
 			}
			info.setData(enabledCostList);
		} catch (Exception e) {
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

}
