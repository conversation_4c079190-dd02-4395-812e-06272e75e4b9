package com.koron.common.web.dto;

public class OrgNodeDto {
	
	private Integer id;
	
	private Integer treeType;
	
	private String parentCode;
	
	private String code;
	
	private Integer status;
	
	private Integer weight;
	
	private String name;
	
	private String shortname;
	
	private String description;
	
	private String phone;
	
	private Integer orgKind;
	
	private String standardCode;
	
	private Integer mask;
	
	private Integer parentmask;
	
	private Integer childmask;
	
	private Long seq;
	
	private Integer businessKind;
	
	private String createTime;
	
	private String creator;
	
	private String lastModificationTime;
	
	private String lastModifier;
	
	private Boolean link;
	
	private Integer syncFlag;
	
	public Integer getSyncFlag() {
		return syncFlag;
	}

	public void setSyncFlag(Integer syncFlag) {
		this.syncFlag = syncFlag;
	}

	public String getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public String getLastModificationTime() {
		return lastModificationTime;
	}

	public String getLastModifier() {
		return lastModifier;
	}

	public Boolean getLink() {
		return link;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setLastModificationTime(String lastModificationTime) {
		this.lastModificationTime = lastModificationTime;
	}

	public void setLastModifier(String lastModifier) {
		this.lastModifier = lastModifier;
	}

	public void setLink(Boolean link) {
		this.link = link;
	}

	public Integer getId() {
		return id;
	}

	public Integer getTreeType() {
		return treeType;
	}

	public String getParentCode() {
		return parentCode;
	}

	public String getCode() {
		return code;
	}

	public Integer getStatus() {
		return status;
	}

	public Integer getWeight() {
		return weight;
	}

	public String getName() {
		return name;
	}

	public String getShortname() {
		return shortname;
	}

	public String getDescription() {
		return description;
	}

	public String getPhone() {
		return phone;
	}

	public Integer getOrgKind() {
		return orgKind;
	}

	public String getStandardCode() {
		return standardCode;
	}

	public Integer getMask() {
		return mask;
	}

	public Integer getParentmask() {
		return parentmask;
	}

	public Integer getChildmask() {
		return childmask;
	}

	public Long getSeq() {
		return seq;
	}

	public Integer getBusinessKind() {
		return businessKind;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public void setTreeType(Integer treeType) {
		this.treeType = treeType;
	}

	public void setParentCode(String parentCode) {
		this.parentCode = parentCode;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public void setWeight(Integer weight) {
		this.weight = weight;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setShortname(String shortname) {
		this.shortname = shortname;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public void setOrgKind(Integer orgKind) {
		this.orgKind = orgKind;
	}

	public void setStandardCode(String standardCode) {
		this.standardCode = standardCode;
	}

	public void setMask(Integer mask) {
		this.mask = mask;
	}

	public void setParentmask(Integer parentmask) {
		this.parentmask = parentmask;
	}

	public void setChildmask(Integer childmask) {
		this.childmask = childmask;
	}

	public void setSeq(Long seq) {
		this.seq = seq;
	}

	public void setBusinessKind(Integer businessKind) {
		this.businessKind = businessKind;
	}
}
