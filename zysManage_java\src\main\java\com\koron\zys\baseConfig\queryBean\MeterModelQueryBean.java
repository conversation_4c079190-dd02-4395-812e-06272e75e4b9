package com.koron.zys.baseConfig.queryBean;

import com.koron.common.bean.query.BaseQueryBean;

public class MeterModelQueryBean extends BaseQueryBean {

    private String id;

    private String modelName;

    private String factoryId;//厂商id
    private String valveControl;//是否阀控
    private String tramsWay;//通讯类型
    private String meterType;//水表类型

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(String factoryId) {
        this.factoryId = factoryId;
    }

    public String getValveControl() {
        return valveControl;
    }

    public void setValveControl(String valveControl) {
        this.valveControl = valveControl;
    }

    public String getTramsWay() {
        return tramsWay;
    }

    public void setTramsWay(String tramsWay) {
        this.tramsWay = tramsWay;
    }

    public String getMeterType() {
        return meterType;
    }

    public void setMeterType(String meterType) {
        this.meterType = meterType;
    }
}
