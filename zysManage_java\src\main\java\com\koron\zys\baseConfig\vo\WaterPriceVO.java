package com.koron.zys.baseConfig.vo;

public class WaterPriceVO {
	
	private String id;

	/**
	 * 用水类型
	 */
    private String waterTypeId;
	/**
	 * 用水类型编号
	 */
    private String waterTypeNo;
	/**
	 * 用水类型名称
	 */
    private String waterTypeName;

	/**
	 * 描述
	 */
    private String remark;
    /**
     * 生效日期
     */
    private String effectiveDate;
    /**
     * 截止日期
     */
    private String expiryDate;
 
    private String status;
    
    private String processInstanceId;
    private String processState;
    private String processName;
    private String processHandleMan;
    
    
    

	public String getProcessInstanceId() {
		return processInstanceId;
	}

	public void setProcessInstanceId(String processInstanceId) {
		this.processInstanceId = processInstanceId;
	}

	public String getProcessState() {
		return processState;
	}

	public void setProcessState(String processState) {
		this.processState = processState;
	}

	public String getProcessName() {
		return processName;
	}

	public void setProcessName(String processName) {
		this.processName = processName;
	}

	public String getProcessHandleMan() {
		return processHandleMan;
	}

	public void setProcessHandleMan(String processHandleMan) {
		this.processHandleMan = processHandleMan;
	}

	public String getWaterTypeNo() {
		return waterTypeNo;
	}

	public void setWaterTypeNo(String waterTypeNo) {
		this.waterTypeNo = waterTypeNo;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getWaterTypeId() {
		return waterTypeId;
	}

	public void setWaterTypeId(String waterTypeId) {
		this.waterTypeId = waterTypeId;
	}

	public String getWaterTypeName() {
		return waterTypeName;
	}

	public void setWaterTypeName(String waterTypeName) {
		this.waterTypeName = waterTypeName;
	}

	public String getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public String getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(String expiryDate) {
		this.expiryDate = expiryDate;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}


}
