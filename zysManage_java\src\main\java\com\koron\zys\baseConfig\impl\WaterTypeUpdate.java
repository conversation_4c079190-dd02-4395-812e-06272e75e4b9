package com.koron.zys.baseConfig.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.WaterPriceBean;
import com.koron.zys.baseConfig.bean.WaterTypeBean;
import com.koron.zys.baseConfig.mapper.WaterPriceMapper;
import com.koron.zys.baseConfig.mapper.WaterTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.ValidationKey;

/**
 * 用水类型-编辑
 *
 * <AUTHOR>
 */
public class WaterTypeUpdate implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(WaterTypeUpdate.class);

    @Override
    @ValidationKey(clazz = WaterTypeBean.class,method = "update")
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            WaterTypeMapper mapper = factory.getMapper(WaterTypeMapper.class);
            WaterTypeBean bean = JsonUtils.objectToPojo(req.getData(), WaterTypeBean.class);
            if (StringUtils.isBlank(bean.getId())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "缺少必要参数", void.class);
            }
            //一级目录不让修改
            if (("01").equals(bean.getWaterTypeNo()) || ("02").equals(bean.getWaterTypeNo()) || ("03").equals(bean.getWaterTypeNo())
                    || ("04").equals(bean.getWaterTypeNo()) || ("05").equals(bean.getWaterTypeNo())) {
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "一级目录不能修改", void.class);
            }
//            if (mapper.check2("water_type_name", bean.getWaterTypeName(), bean.getId()) > 0) {
//                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "用水类型名：" + bean.getWaterTypeName() + "的信息已存在。", void.class);
//            }
            // TODO 修改状态从启用到停用，节点下存在子节点时，子节点是否停用

//            // 不停用子节点
//            mapper.updateWaterType(bean);
//            bean.setUpdateInfo(userInfo);

            // 停用子节点
            // 获取修改前的的节点数据
            WaterTypeBean oldInfo = mapper.findWaterTypeById(bean.getId());
            WaterPriceMapper waterPriceMapper = factory.getMapper(WaterPriceMapper.class);
            //从启用状态改为停用
            if (oldInfo.getStatus() == 1 && bean.getStatus() == 0) {
                // 查找节点下是否有启用状态的子节点
                List<WaterTypeBean> childs = mapper.getChild(bean.getWaterTypeNo());
                if (null != childs && childs.size() > 0) {
                    List<String> collect = childs.stream().map(child -> child.getId()).collect(Collectors.toList());
                    if (!collect.contains(bean.getId())) {
                        childs.add(bean);
                    }
                    // 停用节点及其子节点
                    for (WaterTypeBean waterTypeBean : childs) {
                        waterTypeBean.setUpdateInfo(userInfo);
                        waterTypeBean.setStatus(0);
                        mapper.updateWaterType(waterTypeBean);
                        //停用用水价格
                        WaterPriceBean waterPriceBean = new WaterPriceBean();
                        waterPriceBean.setWaterTypeId(waterTypeBean.getId());
                        waterPriceBean.setUpdateInfo(userInfo);
                        waterPriceBean.setStatus(0);
                        waterPriceMapper.updateWaterPriceStatus(waterPriceBean);
                    }
                }
            } else if (oldInfo.getStatus() == 0 && bean.getStatus() == 1) {// TODO 修改状态从停用到启用，只有节点的父节点为启用状态时方可启用
                //获取父节点数据
                WaterTypeBean parentInfo = mapper.findWaterTypeById(bean.getParentId());
                if (null != parentInfo && parentInfo.getStatus() == 0) {
                    return MessageBean.create(Constant.ILLEGAL_PARAMETER, "父节点停用，请先启用父节点", void.class);
                }
                bean.setUpdateInfo(userInfo);
                mapper.updateWaterType(bean);
                //启用用水价格
                WaterPriceBean waterPriceBean = new WaterPriceBean();
                waterPriceBean.setWaterTypeId(bean.getId());
                waterPriceBean.setUpdateInfo(userInfo);
                waterPriceBean.setStatus(1);
                waterPriceMapper.updateWaterPriceStatus(waterPriceBean);
            } else {
                bean.setUpdateInfo(userInfo);
                mapper.updateWaterType(bean);
            }
        } catch (Exception e) {
            logger.error("用水类型修改失败", e);
            factory.close(false);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "用水类型修改失败", void.class);
        }
        return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", void.class);
    }
}
