<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.ProcessTimeMapper">
	<select id="selectList"
		resultType="com.koron.zys.baseConfig.bean.ProcessTimeBean">
		select a.id,a.receipt_no,a.receipt_name,a.hour from base_water_process_config a 
	</select>
	
	<select id="selectRecordList"
		resultType="com.koron.zys.baseConfig.bean.ProcessRecordBean">
		select a.id,a.receipt_name as receiptName,a.bill_no as billNo,a.bill_date as billDate,a.delay_days,a.process_state  
		 from  base_water_process_record a where 1=1 
		 <if test="fuzzyQuery!=null and fuzzyQuery!=''">
		 	 and a.receipt_name like concat('%',#{fuzzyQuery},'%')
		 </if> 
		 order by a.delay_days desc
	</select>
	
	<select id="query"
		resultType="com.koron.zys.baseConfig.bean.ProcessTimeBean">
		select a.id,a.receipt_name as receiptName,a.receipt_no as receiptNo,a.hour 
		 from base_water_process_config a  where a.id =#{_parameter}
	</select>
	<insert id="insert"
		parameterType="com.koron.zys.baseConfig.bean.ProcessTimeBean">
		insert into base_water_process_config(id,receipt_no,receipt_name,hour,create_time,create_name)
		values(#{id},#{receiptNo},#{receiptName},#{hour},#{createTime},#{createName})
	</insert>
	<update id="update" parameterType="com.koron.zys.baseConfig.bean.ProcessTimeBean">
		update base_water_process_config set
		receipt_no = #{receiptNo},
		receipt_name = #{receiptName},
		hour=#{hour},
		update_time = #{updateTime},
		update_name = #{updateName}
		where id = #{id}
	</update>
	
	
	<delete id="delete" parameterType="com.koron.zys.baseConfig.bean.ProcessTimeBean">
		delete from base_water_process_config where id =#{id}
	</delete>
	
	
	
	<delete id="deleteRecord" parameterType="com.koron.zys.baseConfig.bean.ProcessTimeBean">
		delete from base_water_process_record where receipt_type =#{receiptNo}
	</delete>
</mapper>