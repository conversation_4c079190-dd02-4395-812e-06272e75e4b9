package com.koron.util;

import org.apache.ibatis.annotations.Select;
import org.koron.ebs.mybatis.ADOConnection;
import org.koron.ebs.mybatis.SessionFactory;
import org.koron.ebs.mybatis.SqlTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Controller
public class JkCheck {

    private static Logger logger = LoggerFactory.getLogger(JkCheck.class);

    @Value("${spring.profiles.active}")
    private String profiles;

    @ResponseBody
    @RequestMapping(value = "/JkCheck.api", method = RequestMethod.GET)
    public String testConnection(HttpServletRequest request, HttpServletResponse response){
        try {
            return ADOConnection.runTask("_default", new SqlTask() {
                @Override
                public Object run(SessionFactory factory) {
                    TestMapper mapper = factory.getMapper(TestMapper.class);
                    mapper.testSelect();
                    return "ok";
                }
            }, String.class);
        }catch (Exception ex){
            logger.error("数据库连接失败！！！！" + ex);
        }
        return "error";
    }


    public interface TestMapper{
        @Select("select 1 from dual")
        int testSelect();
    }
}
