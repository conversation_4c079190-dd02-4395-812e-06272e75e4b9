package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.koron.ebs.mybatis.EnvSource;

import com.koron.zys.baseConfig.bean.PubAccessoryTypeBean;

@EnvSource("_default")
public interface PubAccessoryTypeMapper {
	
	PubAccessoryTypeBean selectByAccessNo(String accessNo);
	
	List<PubAccessoryTypeBean> selectList();
	
	int insert(PubAccessoryTypeBean bean);
	
	int deleteById(String id);

}
