package com.koron.common.web.mapper;

import com.koron.common.web.bean.UserSatisfiedBean;
import com.koron.common.web.bean.UserSatisfiedQueryBean;

import java.util.List;

public interface InteractiveMapper {

    /**
    * @description: 保存用户满意度
    * @author: zhongxj
    * @date: 2020/7/23 16:13
    */
     int satisfiedAdd(UserSatisfiedBean userSatisfiedBean);

     /**
     * @description:  查询用户满意度列表
     * @author: zhongxj
     * @date: 2020/7/30 11:36
     */
     List<UserSatisfiedBean> satisfiedList(UserSatisfiedQueryBean bean);
}
