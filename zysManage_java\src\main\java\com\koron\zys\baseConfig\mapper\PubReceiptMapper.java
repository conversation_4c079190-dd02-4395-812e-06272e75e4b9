package com.koron.zys.baseConfig.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.koron.ebs.mybatis.EnvSource;

import com.koron.zys.baseConfig.bean.PubReceiptBean;
import com.koron.zys.baseConfig.queryBean.PubReceiptQueryBean;
import com.koron.zys.serviceManage.bean.ProcessNodeBean;

@EnvSource("_default")
public interface PubReceiptMapper {
	
	/**
	 * 通过条件查询单据列表
	 * @param query
	 * @return
	 */
	List<PubReceiptBean> selectPubReceiptList(PubReceiptQueryBean query);
	
	/**
     * 删除所有结点信息 
     * @param processId
     * @return
     */
    int deleteProcessNode(String receiptType);
    /**
     * 添加结点信息
     * @param bean
     * @return
     */
    int insertProcessNode(ProcessNodeBean bean);
	
    /**
     * 根据单据编号查询单据信息
     * @param receiptType
     * @return
     */
	PubReceiptBean selectPubReceipt(String receiptType);
	
	/**
	 * 根据主键查询单据信息
	 * @param receiptId
	 * @return
	 */
	PubReceiptBean selectById(String receiptId);
	
	/**
	 * 根据流程编号查询单据信息
	 * @param processCode
	 * @return
	 */
	PubReceiptBean selectByProcessCode(String processCode);
	
	/**
	 * 根据水司查询出流程模板组编号
	 * @param companyNo
	 * @return
	 */
	String getWorkflowApp(String companyNo);
	
	/**
	 * 查询出流程节点列表根据单据编号
	 * @param receiptType
	 * @return
	 */
	List<ProcessNodeBean> listProcessNodeByBillType(Map<String, String> param);
	
	/**
	 * 通过单据类型查询业务表的流程实例ID
	 * @param billType
	 * @return
	 */
	String selectProcessInstanceId(@Param("billType") String billType, @Param("billId") String billId);
	
	/**
	 * 修改具体单据信息
	 * @param billType
	 * @param billId
	 * @param process_instance_id
	 * @param process_state
	 * @param nextNodeName
	 * @param nextCandidateUsers
	 * @return
	 */
	int updateBillWorkflowInfo(@Param("billType") String billType,
							   @Param("billId") String billId,
    		                   @Param("processInstanceId") String process_instance_id,
    		                   @Param("processState") String process_state,
    		                   @Param("nextNodeName") String nextNodeName,
    		                   @Param("nextCandidateUsers") String nextCandidateUsers);
	
	/**
	 * 查询单据字段信息
	 * @param billType
	 * @param billId
	 * @param titleField
	 * @return
	 */
	Map<String, String> getBillWorkflowInfo(@Param("billType") String billType, @Param("billId") String billId);
	
	
	/**
	 * 新增
	 * @param bean
	 * @return
	 */
	int saveReceipt(PubReceiptBean bean);
	
	/**
	 * 更新
	 * @param bean
	 * @return
	 */
	int updateReceipt(PubReceiptBean bean);
}
