package com.koron.zys.baseConfig.queryBean;

import com.koron.common.bean.query.BaseQueryBean;

public class WaterTypeQueryBean extends BaseQueryBean {

    private String waterTypeId;

    /**
     * 用水类型编号
     */
    private String waterTypeNo;
    /**
     * 用水类型名称
     */
    private String waterTypeName;

    private Integer sortNo;

    private Integer status;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String createName;
    /**
     * 修改时间
     */
    private String updateTime;
    /**
     * 修改人
     */
    private String updateName;

    private String parentId;

    /**
     * 是否叶子节点 1是0否
     */
    private Integer isLeaf;

    public String getWaterTypeId() {
        return waterTypeId;
    }

    public void setWaterTypeId(String waterTypeId) {
        this.waterTypeId = waterTypeId;
    }

    public String getWaterTypeNo() {
        return waterTypeNo;
    }

    public void setWaterTypeNo(String waterTypeNo) {
        this.waterTypeNo = waterTypeNo;
    }

    public String getWaterTypeName() {
        return waterTypeName;
    }

    public void setWaterTypeName(String waterTypeName) {
        this.waterTypeName = waterTypeName;
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Integer getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }


}
