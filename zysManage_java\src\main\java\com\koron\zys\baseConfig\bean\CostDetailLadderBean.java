package com.koron.zys.baseConfig.bean;

/**
 * 费用明细阶梯表
 * <AUTHOR>
 *
 */
public class CostDetailLadderBean {
	
	private String costDetailLadderId;

	/**
	 * 费用明细ID
	 */
    private String costDetailId;
    /**
              *开始水量
     */
    private Integer beginWater;
    /**
               * 结束水量
     */
    private Integer endWater;
    /**
                * 单价
     */
    private Double price;
    
    /**
               * 系数
     */
	private Double coefficient;
   
    /**
              阶梯起始金额
    */   
	private Double ladderBeginMoney;

	public String getCostDetailLadderId() {
		return costDetailLadderId;
	}

	public void setCostDetailLadderId(String costDetailLadderId) {
		this.costDetailLadderId = costDetailLadderId;
	}

	public String getCostDetailId() {
		return costDetailId;
	}

	public void setCostDetailId(String costDetailId) {
		this.costDetailId = costDetailId;
	}

	public Integer getBeginWater() {
		return beginWater;
	}

	public void setBeginWater(Integer beginWater) {
		this.beginWater = beginWater;
	}

	public Integer getEndWater() {
		return endWater;
	}

	public void setEndWater(Integer endWater) {
		this.endWater = endWater;
	}

	public Double getPrice() {
		return price;
	}

	public void setPrice(Double price) {
		this.price = price;
	}

	public Double getCoefficient() {
		return coefficient;
	}

	public void setCoefficient(Double coefficient) {
		this.coefficient = coefficient;
	}

	public Double getLadderBeginMoney() {
		return ladderBeginMoney;
	}

	public void setLadderBeginMoney(Double ladderBeginMoney) {
		this.ladderBeginMoney = ladderBeginMoney;
	}
	
}
