package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.apache.log4j.Logger;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.ProjectPayeeBean;
import com.koron.zys.baseConfig.mapper.ProjectPayeeMapper;

import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 工程收款主体 列表
 * <AUTHOR>
 *
 */
public class ProjectPayeeList implements ServerInterface{
	
	private static Logger log = Logger.getLogger(ProjectPayeeList.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info =MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "查询成功", PageInfo.class);
		try {
			ProjectPayeeBean bean = JsonUtils.objectToPojo(req.getData(), ProjectPayeeBean.class);
			ProjectPayeeMapper mapper = factory.getMapper(ProjectPayeeMapper.class);
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			List<ProjectPayeeBean> cdList = mapper.selectList(bean);
			info.setData(new PageInfo<>(cdList));
		}catch(Exception e) {
			log.error("工程收款主体列表查询失败",e);
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription("工程收款主体列表查询失败");
		}
		return info;
	}

}
