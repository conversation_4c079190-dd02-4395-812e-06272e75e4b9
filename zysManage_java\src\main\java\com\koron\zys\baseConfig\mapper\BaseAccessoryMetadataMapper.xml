<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BaseAccessoryMetadataMapper">

		
 	<select id="selectList" parameterType="com.koron.zys.baseConfig.queryBean.BaseAccessoryMetadataQueryBean" resultType="com.koron.zys.baseConfig.bean.BaseAccessoryMetadataBean" >
 		SELECT
			t.accessory_name,
			t.accessory_path,
			t.accessory_size,
			t.accessory_type,
			t.create_account,
			t.create_name,
			t.create_time,
			t.id
		FROM
			base_accessory_metamata t
		<where>
			<if test="id != null and id != ''">
				and id = #{id, jdbcType=VARCHAR}
			</if>
			<if test="accessoryName != null and accessoryName != ''">
				and accessory_name = #{accessoryName, jdbcType=VARCHAR}
			</if>
			<if test="accessoryType != null and accessoryType != ''">
				and accessory_type = #{accessoryType, jdbcType=VARCHAR}
			</if>
		</where>
	</select>
	
	<select id="selectById" parameterType="java.lang.String" resultType="com.koron.zys.baseConfig.bean.BaseAccessoryMetadataBean">
		SELECT
			t.accessory_name,
			t.accessory_path,
			t.accessory_size,
			t.accessory_type,
			t.create_account,
			t.create_name,
			t.create_time,
			t.id
		FROM
			base_accessory_metamata t
		WHERE t.id = #{id, jdbcType=VARCHAR}
	</select>
	
	<insert id="insert" parameterType="com.koron.zys.baseConfig.bean.BaseAccessoryMetadataBean">
		insert into base_accessory_metamata (
			accessory_name,
			accessory_path,
			accessory_size,
			accessory_type,
			create_account,
			create_name,
			create_time,
			id
		)values(
			#{accessoryName,jdbcType=VARCHAR},
			#{accessoryPath,jdbcType=VARCHAR},
			#{accessorySize,jdbcType=VARCHAR},
			#{accessoryType,jdbcType=VARCHAR},
			#{createAccount,jdbcType=VARCHAR},
			#{createName,jdbcType=VARCHAR},
			now(),
			#{id,jdbcType=VARCHAR}
		)
	</insert>
	
	<delete id="deleteById" parameterType="java.lang.String">
		delete from base_accessory_metamata where id = #{id}
	</delete>
	
	<select id="checkAccessoryRequired" resultType="java.lang.String">
		select accessory_no from base_accessory_config where receipt_type=#{receiptType} and required_flag = 1 
 and accessory_no not in (select accessory_no from base_receipt_accessory a 
		where a.receipt_type=#{receiptType} and a.receipt_id = #{id} and a.accessory_no is not null)
group by accessory_no 
	</select>
	
</mapper>