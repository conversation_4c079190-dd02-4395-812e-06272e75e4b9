package com.koron.zys.baseConfig.bean;

import com.koron.util.Check;

/**
 * 短信模板实体类
 */
public class MsgPhraseBean extends BaseBean {
	@Check(name = "模板编号", notEmpty = true)
    private String phraseNo;
	
	@Check(name = "模板名称", notEmpty = true)
    private String phraseName;
	
	@Check(name = "模板内容", notEmpty = true)
    private String phraseContent;

    private Integer status;

    public String getPhraseNo() {
        return phraseNo;
    }

    public void setPhraseNo(String phraseNo) {
        this.phraseNo = phraseNo;
    }

    public String getPhraseName() {
        return phraseName;
    }

    public void setPhraseName(String phraseName) {
        this.phraseName = phraseName;
    }

    public String getPhraseContent() {
        return phraseContent;
    }

    public void setPhraseContent(String phraseContent) {
        this.phraseContent = phraseContent;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

}
