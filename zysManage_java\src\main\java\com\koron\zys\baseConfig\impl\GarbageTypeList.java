package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.GarbageTypeBean;
import com.koron.zys.baseConfig.bean.GarbageTypeQueryBean;
import com.koron.zys.baseConfig.mapper.GarbageTypeMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;

public class GarbageTypeList implements ServerInterface {

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
		// TODO Auto-generated method stub
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
		try {
			GarbageTypeQueryBean bean = JsonUtils.objectToPojo(req.getData(), GarbageTypeQueryBean.class);
			GarbageTypeMapper mapper = factory.getMapper(GarbageTypeMapper.class);
			PageHelper.startPage(bean.getPage(), bean.getPageCount());
			List<GarbageTypeBean> list = mapper.list(bean);
			for (GarbageTypeBean garbageTypeBean : list) {
				garbageTypeBean.setStatusName("1".equals(garbageTypeBean.getStatus())?"启用":"停用");
			}
			info.setData(new PageInfo<>(list));
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.error("非法参数", e);
			return MessageBean.create(Constant.ILLEGAL_PARAMETER, "非法参数", void.class);
		}
		return info;
	}

}
