package com.koron.zys.baseConfig.impl;

import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.ToolAccessoryBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.ToolAccessoryMapper;
import com.koron.zys.serviceManage.queryBean.ToolAccessoryQueryBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import java.util.List;

public class ToolDownloadList implements ServerInterface {

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "查询成功", PageInfo.class);
            ToolAccessoryQueryBean bean = JsonUtils.objectToPojo(req.getData(), ToolAccessoryQueryBean.class);
            ToolAccessoryMapper mapper = factory.getMapper(ToolAccessoryMapper.class,"_default");
            List<ToolAccessoryBean> list = mapper.getToolAccessoryList(bean);
            info.setData(new PageInfo<>(list));
            return info;
        } catch (Exception e) {
            logger.error("获取工具下载列表查询失败" + e.getMessage(), e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "查询失败", null);
        }
    }
}
