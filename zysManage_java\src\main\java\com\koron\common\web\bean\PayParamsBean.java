package com.koron.common.web.bean;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * 互联网支付参数
 * <AUTHOR>
 * 2019年10月21日
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PayParamsBean {

	/**
	 * 水司编号
	 */
	private String groupCode;

	/**
	 * 账户编号（旧客服传用户编号）
	 */
	private String accountNo;

	/**
	 * 支付方式，1微信直接支付，2微信二维码支付，3支付宝生活缴费
	 */
	private Integer payType;

	/**
	 * 缴费渠道，1微厅，2网厅，3智慧营业厅，4柜台，5支付宝生活缴费
	 */
	private Integer payChannel;

	/**
	 * 是否预存
	 */
	private Integer isPrestore;

	/**
	 * 支付金额
	 */
	private Double payMoney;

	/**
	 * 费用类型，1水费，2工程费
	 */
	private Integer feeType;
	
	/**
	 * openId
	 */
	private String openId;
	
	/**
	 * 水司对应公众号openId
	 */
	private String groupOpenId;

	/**
	 * 核销数据（如果为空，则不指定核销用户和账单）
	 */
	private List<VerificationBean> verification;

	/**
	 * 付费类型，0后付费，1预付费
	 */
	private Integer paymentType;

	/**
	 * 预付费水量
	 */
	private Integer waterNum;

	/**
	 * 互联网订单创建人（用于IC卡预购水扫码支付）
	 */
	private String createAccount;

	/**
	 * 互联网订单创建人（用于IC卡预购水扫码支付）
	 */
	private String createName;
	
	//添加无参的构造器
    public PayParamsBean(){ 
    }
	
	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Integer getPayChannel() {
		return payChannel;
	}

	public void setPayChannel(Integer payChannel) {
		this.payChannel = payChannel;
	}

	public Double getPayMoney() {
		return payMoney;
	}

	public void setPayMoney(Double payMoney) {
		this.payMoney = payMoney;
	}

	public Integer getFeeType() {
		return feeType;
	}

	public void setFeeType(Integer feeType) {
		this.feeType = feeType;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getGroupOpenId() {
		return groupOpenId;
	}

	public void setGroupOpenId(String groupOpenId) {
		this.groupOpenId = groupOpenId;
	}

	public List<VerificationBean> getVerification() {
		return verification;
	}

	public void setVerification(List<VerificationBean> verification) {
		this.verification = verification;
	}

	public Integer getIsPrestore() {
		return isPrestore;
	}

	public void setIsPrestore(Integer isPrestore) {
		this.isPrestore = isPrestore;
	}

	public Integer getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(Integer paymentType) {
		this.paymentType = paymentType;
	}

	public Integer getWaterNum() {
		return waterNum;
	}

	public void setWaterNum(Integer waterNum) {
		this.waterNum = waterNum;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public String getCreateAccount() {
		return createAccount;
	}

	public void setCreateAccount(String createAccount) {
		this.createAccount = createAccount;
	}
}
