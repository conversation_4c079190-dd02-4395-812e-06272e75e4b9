package com.koron.util;

import java.io.Serializable;

/**
 * <p>
 * Title: 返回结果
 * </p>
 *
 * <p>
 * Description:
 * </p>
 *
 * <p>
 * Copyright: Copyright (c) 2008
 * </p>
 *
 * <p>
 * Company: 周新宇
 * </p>
 *
 * <AUTHOR>
 * @version 1.0h
 */
@SuppressWarnings("serial")
public class Result implements Serializable {

	public int retCode = 0;
	public Object retVal;

	public static Result OK() {
		Result rs = new Result();
		return rs;
	}

	public static Result OK(Object retVal) {
		Result rs = new Result();
		rs.retVal = retVal;
		return rs;
	}

	/**
	 * 报错，默认错误代码
	 * 
	 * @param msg
	 * @return
	 */
	public static Result ERROR(String msg) {
		Result rs = new Result();
		rs.setRetCode(-1);
		rs.setRetVal(msg);
		return rs;
	}

	public static Result ERROR(int retCode, String msg) {
		Result rs = new Result();
		rs.setRetCode(retCode);
		rs.setRetVal(msg);
		return rs;
	}

	public int getRetCode() {
		return retCode;
	}

	public Object getRetVal() {
		return retVal;
	}

	public void setRetVal(Object retVal) {
		this.retVal = retVal;
	}

	public void setRetCode(int retCode) {
		this.retCode = retCode;
	}

}

