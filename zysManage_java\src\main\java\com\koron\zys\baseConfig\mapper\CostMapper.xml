<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.CostMapper">
	<select id="selectPubCostList"  resultType="com.koron.zys.baseConfig.vo.CostVO" >
		select *
		from PUB_COST
	    <where>
	    	<if test="status != null">
	    		and status = #{status}
	    	</if>
	    </where>
	    order by sort_no
	</select>
	<select id="selectCostList"
		resultType="com.koron.zys.baseConfig.vo.CostVO">
		select * from BASE_COST where `STATUS` = 1
		order by sort_no asc
	</select>
	<select id="selectCostListNoLj"
		resultType="com.koron.zys.baseConfig.vo.CostVO">
		select * from BASE_COST where `STATUS` = 1 and cost_name_en not in ('ljclf','wsf','ljde','ljgc','ljsy','ljqy','ljqt')
		order by sort_no asc
	</select>

	<select id="selectCostListIgnoreStatus"
		resultType="com.koron.zys.baseConfig.vo.CostVO">
		select * from BASE_COST
		order by sort_no asc
	</select>

	<select id="selectCostNameList" resultType="com.koron.zys.baseConfig.bean.CostNameBean">
		select xx.cost_name name,xx.id id
		from BASE_COST xx
		where xx.status=1
	</select>

	<select id="selectBaseCostById" parameterType="string" resultType="java.lang.Integer">
		select count(*) from base_cost where id = #{id}
	</select>

	<select id="selectBaseCostByIds" parameterType="java.util.List" resultType="com.koron.zys.baseConfig.vo.CostVO">
		select * from base_cost
		 <where>
			 <if test="list != null and list.size > 0">
				 id in
				 <foreach collection="list" item="item" open="(" close=")" separator=",">
					 #{item}
				 </foreach>
			 </if>
		 </where>
	</select>

	<select id="selectCostById"
		resultType="com.koron.zys.baseConfig.bean.CostBean">
		select *
		from PUB_COST
		where id = #{id}
	</select>
	<select id="selectCostForAllowRushRed" resultType="String">
		select cost_no from base_cost where allowRushRed = 1
	</select>

	<select id="selectCostForNotCompreHensive" resultType="com.koron.zys.baseConfig.bean.CostBean">
		select cost_no,cost_name from base_cost where (isComprehensive is null or isComprehensive = 0) and status=1
	</select>

	<select id="selectCostForCompreHensive" resultType="com.koron.zys.baseConfig.bean.CostBean">
		select cost_no,cost_name from base_cost where isComprehensive = 1 and `STATUS` = 1
	</select>

	<select id="selectUserBillQuota" resultType="com.koron.zys.baseConfig.bean.BillQuotaBean">
		select y.cost_no, y.cost_name, z.sub_cost_no, z.sub_cost_name, x.quota_money, x.zero_water_calc,x.calc_way
		from bill_quota x, base_cost y, base_sub_cost z
		where x.cost_no = y.cost_no
		  and x.sub_cost_no = z.sub_cost_no
		  and x.user_no = #{userNo}
	</select>
    <delete id="deleteCostById" >
		delete from BASE_COST
		where id = #{id}
	</delete>

	<update id="stopCostById" >
		update BASE_COST set status=0 where id = #{id}
	</update>

	<update id="updateCostStatus" parameterType="com.koron.zys.baseConfig.bean.CostBean">
		update BASE_COST
		 <set>
			 <if test="status !=null">
				 status=#{status},
			 </if>
			 <if test="isMust !=null">
				 isMust = #{isMust},
			 </if>
			 <if test="allowRushRed !=null">
				 allowRushRed = #{allowRushRed},
			 </if>
			 <if test="isComprehensive !=null">
				 isComprehensive = #{isComprehensive},
			 </if>
		 </set>
		where id = #{id}
	</update>

	<update id="updateCostUnit" parameterType="com.koron.zys.baseConfig.bean.CostBean">
		update BASE_COST set cost_unit=#{costUnit} where id =#{id}
	</update>

	<insert id="insertCost"
		parameterType="com.koron.zys.baseConfig.bean.CostBean">
		insert into BASE_COST (id,cost_name,cost_no,isMust,allowRushRed,isComprehensive,
		cost_name_en,cost_unit,comments,status, sort_no, create_time, create_name,update_time,update_name)
		values
		(
		#{id},
		#{costName},
		#{costNo},
		#{isMust},
		#{allowRushRed},
		#{isComprehensive},
		#{costNameEn},
		#{costUnit},
		#{comments},
		#{status},
		#{sortNo},
		date_format(#{createTime},'%Y-%m-%d %T'),
		#{createName,jdbcType=VARCHAR},
		date_format(#{updateTime},'%Y-%m-%d %T'),
		#{updateName,jdbcType=VARCHAR}
		)
	</insert>
	<select id="selectUsingCostList"
			resultType="com.koron.zys.baseConfig.vo.CostVO">
		select * from BASE_COST
		where status = 1
		order by sort_no, cost_no
	</select>
</mapper>
