package com.koron.zys.baseConfig.mapper;

import java.util.List;

import com.koron.zys.baseConfig.bean.BaseInvoiceTypeBean;
import com.koron.zys.baseConfig.vo.BaseInvoiceTypeListVO;

public interface BaseInvoiceTypeMapper {
	//列表接口
	List<BaseInvoiceTypeListVO> selectList();
	//添加接口
	void insert(BaseInvoiceTypeBean bean);
	//修改接口
	void update(BaseInvoiceTypeBean bean);
	//带条件的查询接口
	List<BaseInvoiceTypeListVO> selectOnList(BaseInvoiceTypeBean bean);
}
