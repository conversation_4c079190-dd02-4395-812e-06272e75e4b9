<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.BookMapper">
    <select id="select" parameterType="com.koron.zys.baseConfig.queryBean.BookQueryBean"
            resultType="com.koron.zys.baseConfig.bean.BookBean">
        select id,book_no,book_name,meter_reading_cycle,meter_reading_staff,
        meter_reading_day,case when x.status = 1 then '启用' else '停用' end status,
        x.comments, y.branch_name business_branch
        from base_book x, base_business_branch y
        where x.business_branch = y.id
        <if test="businessBranch != null and businessBranch != ''">
            and x.business_branch = #{businessBranch}
        </if>
        <if test="meterReadingStaff != null and meterReadingStaff != ''">
            and x.meter_Reading_Staff = #{meterReadingStaff}
        </if>
        <if test="searchContent != null and searchContent != ''">
            and (x.book_no = #{searchContent}
            or x.book_name like concat('%',#{searchContent},'%')
            or x.comments like concat('%',#{searchContent},'%')
            )
        </if>
    </select>
    <select id="query" parameterType="String" resultType="com.koron.zys.baseConfig.bean.BookBean">
        select *
        from base_book
        where id = #{id}
    </select>
    <select id="queryByBookNo" parameterType="String" resultType="com.koron.zys.baseConfig.bean.BookBean">
        select *
        from base_book
        where book_no = #{bookNo}
    </select>
    <insert id="insert" parameterType="com.koron.zys.baseConfig.bean.BookBean">
        insert into base_book(id, book_no, book_name, business_branch, meter_reading_cycle, meter_reading_staff,
                              meter_reading_day, status, comments, open_account_lock, tenant_id, create_time,
                              create_account, create_name, update_time, update_account, update_name)
        values (#{id}, #{bookNo}, #{bookName}, #{businessBranch}, #{meterReadingCycle}, #{meterReadingStaff},
                #{meterReadingDay}, #{status}, #{comments}, #{openAccountLock}, #{tenantId}, #{createTime},
                #{createAccount}, #{createName}, #{updateTime}, #{updateAccount}, #{updateName})
    </insert>
    <update id="update" parameterType="com.koron.zys.baseConfig.bean.BookBean">
        update base_book
        set book_name           = #{bookName},
            business_branch     = #{businessBranch},
            meter_reading_cycle = #{meterReadingCycle},
            meter_reading_staff = #{meterReadingStaff},
            meter_reading_day   = #{meterReadingDay},
            status              = #{status},
            comments            = #{comments},
            update_time         = #{updateTime},
            update_account      = #{updateAccount},
            update_name         = #{updateName}
        where id = #{id}
    </update>
</mapper>