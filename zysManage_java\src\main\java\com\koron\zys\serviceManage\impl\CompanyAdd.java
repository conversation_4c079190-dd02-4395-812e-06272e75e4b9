package com.koron.zys.serviceManage.impl;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import com.koron.zys.serviceManage.mapper.PubScriptManageMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.bson.types.ObjectId;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageInfo;
import com.koron.zys.ApplicationConfig;
import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.DbServerBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.TWaterCompanyBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.DatabaseOracleMapper;
import com.koron.zys.serviceManage.mapper.DbServerMapper;
import com.koron.zys.serviceManage.mapper.TWaterCompanyMapper;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;
import com.koron.util.DBSourceUtils;

public class CompanyAdd implements ServerInterface {
	Logger log = Logger.getLogger(CompanyAdd.class);

	@Override
	public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

		@SuppressWarnings("rawtypes")
		MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);

		try {
			TWaterCompanyBean bean = JsonUtils.objectToPojo(req.getData(), TWaterCompanyBean.class);
			TWaterCompanyMapper mapper = factory.getMapper(TWaterCompanyMapper.class);
			PubScriptManageMapper pubScriptManageMapper = factory.getMapper(PubScriptManageMapper.class);
			// 校验字段重复
			if (mapper.check("COMPANY_NO", bean.getCompanyNo()) > 0) {
				info.setCode(Constant.MESSAGE_INT_FAIL);
				info.setDescription("水司编号：" + bean.getCompanyNo() + "的信息已存在。");
				return info;
			}
			if (mapper.check("SIMPLIFY_NO", bean.getSimplifyNo()) > 0) {
				info.setCode(Constant.MESSAGE_INT_FAIL);
				info.setDescription("简化编号：" + bean.getSimplifyNo() + "的信息已存在。");
				return info;
			}
			if (mapper.check("SHORT_NAME", bean.getShortName()) > 0) {
				info.setCode(Constant.MESSAGE_INT_FAIL);
				info.setDescription("水司简称：" + bean.getShortName() + "的信息已存在。");
				return info;
			}

			//mySql中DATABASE_ACCOUNT 存储schema信息
			if (mapper.check("DATABASE_ACCOUNT", bean.getDatabaseAccount()) > 0) {
				info.setCode(Constant.MESSAGE_INT_FAIL);
				info.setDescription("数据库：" + bean.getDatabaseAccount() + "的信息已存在。");
				return info;
			}
			// 保存水司信息
			bean.setCompanyId(new ObjectId().toHexString());
			bean.setCreateName(userInfo.getUserInfo().getName());
			bean.setCreateTime(CommonUtils.getCurrentTime());
			mapper.save(bean);
//			// TODO 水司管理员，暂时不做，等权限管理
//			if (bean.getManagerStaff() != null && bean.getManagerStaff().size() > 0) {
//
//			}
			// 数据库相关操作
			createMySqlDataBase(factory, bean.getCompanyId(), bean.getCompanyNo(), bean.getDatabaseAccount());
			/**
			 * 创建新水司时，继承模板水司未执行脚本
			 */
			//查询原表的公司信息
			TWaterCompanyBean sourceCompany = mapper.selectById(bean.getSourceCompany());
			pubScriptManageMapper.updateScriptManage(bean.getCompanyNo(),sourceCompany.getCompanyNo());

		} catch (Exception e) {
			factory.close(false);
			info.setCode(Constant.MESSAGE_INT_FAIL);
			info.setDescription(e.getMessage());
			logger.error(e.getMessage(), e);
		}
		return info;
	}

	/**
	 * TODO 创建Mysql库,
	 * @param factory
	 * @param templeteId
	 * @param companyNo
	 * @param dbSchema
	 */
	private void createMySqlDataBase(SessionFactory factory, String templeteId, String companyNo, String dbSchema) throws Exception{
		TWaterCompanyMapper publicMapper = factory.getMapper(TWaterCompanyMapper.class);
		DbServerMapper dbServerMapper = factory.getMapper(DbServerMapper.class);
		// 查询新水司信息
		TWaterCompanyBean newCompany = publicMapper.selectById(templeteId);
		// 获取新水司数据库服务器信息
		DbServerBean newDbServerBean = dbServerMapper.selectById(newCompany.getDbId());

		//查询原表的公司信息
		TWaterCompanyBean sourceCompany = publicMapper.selectById(newCompany.getSourceCompany());
		// 获取原水司数据库服务器信息
		DbServerBean sourceDbServerBean = dbServerMapper.selectById(sourceCompany.getDbId());

		//创建数据库，因为待创建的数据库可能不在主服务器上，因此这里直接采用jdbc连接来创建
		boolean success = createMySqlDataBase(newDbServerBean.getDbIp(), newDbServerBean.getDbPort(), newDbServerBean.getManagerAccount(),
				newDbServerBean.getManagerPassword(),newCompany.getDatabaseAccount());
		if(!success) {
			throw new RuntimeException("创建数据库失败");
		}
		//读取建表语句，主键、索引、外键
		log.info("开始从源数据库读取表结构，源数据库信息：ip=" + sourceDbServerBean.getDbIp() +
				",port=" + sourceDbServerBean.getDbPort() + ",schema=" + sourceCompany.getDatabaseAccount());
		ArrayList<String> sqls  = getMysqlTableSql(sourceDbServerBean.getDbIp(), sourceDbServerBean.getDbPort(),
				sourceDbServerBean.getManagerAccount(),sourceDbServerBean.getManagerPassword(), sourceCompany.getDatabaseAccount());
		log.info("成功读取到 " + sqls.size() + " 个建表语句");

		//新数据库建表
		log.info("开始在新数据库创建表结构，目标数据库信息：ip=" + newDbServerBean.getDbIp() +
				",port=" + newDbServerBean.getDbPort() + ",schema=" + newCompany.getDatabaseAccount());
		success = createMySqlDataBaseTables(newDbServerBean.getDbIp(), newDbServerBean.getDbPort(), newDbServerBean.getManagerAccount(),
				newDbServerBean.getManagerPassword(),newCompany.getDatabaseAccount(), sqls);
		if(!success) {
			throw new RuntimeException("创建数据库表失败");
		}

		// 查询需要复制数据的表名
		List<String> tableNames = ApplicationConfig.getCopyTableNames() == null ? null
				: Arrays.asList(ApplicationConfig.getCopyTableNames().split(","));
		sqls = getMysqlDataSql(sourceDbServerBean.getDbIp(), sourceDbServerBean.getDbPort(), sourceDbServerBean.getManagerAccount(),
				sourceDbServerBean.getManagerPassword(),sourceCompany.getDatabaseAccount(), tableNames);
		saveMysqlDataSql(newDbServerBean.getDbIp(), newDbServerBean.getDbPort(), newDbServerBean.getManagerAccount(),
				newDbServerBean.getManagerPassword(),newCompany.getDatabaseAccount(), sqls);
	}
	/**
	 * 把同步数据写入新水司
	 * @param ip
	 * @param port
	 * @param account
	 * @param password
	 * @param shcema
	 * @param sqls
	 * @throws Exception
	 */
	private void saveMysqlDataSql(String ip,String port,String account,String password,String shcema,List<String> sqls) throws Exception {
		String url = "jdbc:mysql://"+ip+":"+port+"/"+shcema+"?useUnicode=true&AutoReconnect=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai";  //mysql库，使用上海时区
		Connection conn=null;

		try {
			Class.forName("com.mysql.jdbc.Driver");  //加载JDBC驱动程序
			conn = DriverManager.getConnection(url, account, password);//建立与数据库的连接，后两个参数分别为账号和密码
			for(String sql:sqls) {
				PreparedStatement pst =conn.prepareStatement(sql);
				pst.executeUpdate();
			}
		} catch (Exception e) {
			log.error("创建水司复制数据保存失败:ip="+ip+",port="+port+",account="+account+",password="+password+",shcema="+shcema,e);
			throw e;
		}finally {
			try {
				if(conn != null)
					conn.close();
			} catch (Exception e) {
				log.error("创建水司复制数据保存关闭数据库连接失败:ip="+ip+",port="+port+",account="+account+",password="+password+",shcema="+shcema,e);
			}//关闭与数据库的连接
		}
	}

	/**
	 * 执行建表操作
	 * @param ip
	 * @param port
	 * @param account
	 * @param password
	 * @param shcema
	 * @param sqls
	 * @return
	 */
	private boolean createMySqlDataBaseTables(String ip,String port,String account,String password,String shcema,ArrayList<String> sqls) {
		String url = "jdbc:mysql://"+ip+":"+port+"/"+shcema+"?useUnicode=true&AutoReconnect=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai";  //mysql库，使用上海时区
		Connection con=null;
		Statement statement;

		try {
			Class.forName("com.mysql.jdbc.Driver");  //加载JDBC驱动程序
			con = DriverManager.getConnection(url, account, password);//建立与数据库的连接，后两个参数分别为账号和密码
			log.info("开始执行 " + sqls.size() + " 个建表SQL语句");
			for(int i = 0; i < sqls.size(); i++) {
				String sql = sqls.get(i);
				log.info("执行第 " + (i+1) + " 个SQL语句，长度：" + sql.length());
				// 如果SQL包含时间字段，记录完整SQL用于调试
				if (sql.toLowerCase().contains("datetime") || sql.toLowerCase().contains("timestamp")) {
					log.info("包含时间字段的建表SQL：" + sql);
				}
				statement = con.createStatement();//创建Statement对象
				statement.executeUpdate(sql);
				statement.close();//关闭Statement对象
				log.info("第 " + (i+1) + " 个SQL执行成功");
			}
			return true;
		} catch (Exception e) {
			log.error("创建数据库失败:ip="+ip+",port="+port+",account="+account+",password="+password+",shcema="+shcema,e);
		}finally {
			try {
				if(con != null)
					con.close();
			} catch (Exception e) {
				log.error("创建数据库关闭数据库连接失败:ip="+ip+",port="+port+",account="+account+",password="+password+",shcema="+shcema,e);
			}//关闭与数据库的连接
		}
		return false;
	}

	/**
	 * 采用jdbc直接创建数据库
	 * @param ip
	 * @param port
	 * @param account
	 * @param password
	 * @param shcema
	 * @return
	 */
	private boolean createMySqlDataBase(String ip,String port,String account,String password,String shcema) {
		String url = "jdbc:mysql://"+ip+":"+port+"/mysql?useUnicode=true&AutoReconnect=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai";  //mysql库，使用上海时区
		Connection con=null;
		Statement statement;

		try {
			Class.forName("com.mysql.jdbc.Driver");  //加载JDBC驱动程序
			con = DriverManager.getConnection(url, account, password);//建立与数据库的连接，后两个参数分别为账号和密码
			statement = con.createStatement();//创建Statement对象
			statement.executeUpdate("create database `"+shcema+"` character set utf8mb4 collate utf8mb4_general_ci;");
			statement.close();//关闭Statement对象
			return true;
		} catch (Exception e) {
			log.error("创建数据库失败:ip="+ip+",port="+port+",account="+account+",password="+password+",shcema="+shcema,e);
		}finally {
			try {
				if(con != null)
					con.close();
			} catch (Exception e) {
				log.error("创建数据库关闭数据库连接失败:ip="+ip+",port="+port+",account="+account+",password="+password+",shcema="+shcema,e);
			}//关闭与数据库的连接
		}
		return false;
	}

	public static void main(String[] args) {
		CompanyAdd add = new CompanyAdd();
		List<String> tableNames = new ArrayList<>();
		tableNames.add("base_print_template");
		try {
			ArrayList<String> sql =add.getMysqlDataSql("*************", "3306", "root", "water", "css_ns", tableNames);
			for(String s:sql) {
				System.out.println(s);
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}


	/**
	 * 读取原水司的待同步数据
	 * @param ip
	 * @param port
	 * @param account
	 * @param password
	 * @param shcema
	 * @param tableNames
	 * @return
	 * @throws Exception
	 */
	private ArrayList<String> getMysqlDataSql(String ip,String port,String account,String password,String shcema,List<String> tableNames) throws Exception {
		ArrayList<String> sqls = new ArrayList<>();
		String url = "jdbc:mysql://"+ip+":"+port+"/"+shcema+"?useUnicode=true&AutoReconnect=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai";  //mysql库，使用上海时区
		Connection conn=null;

		try {
			Class.forName("com.mysql.jdbc.Driver");  //加载JDBC驱动程序
			conn = DriverManager.getConnection(url, account, password);//建立与数据库的连接，后两个参数分别为账号和密码
			for(String tableName:tableNames) {
				String temp = "insert into `"+tableName+"`(";
				PreparedStatement pst =conn.prepareStatement("select * from "+tableName+" ");
				ResultSet rs = pst.executeQuery();
				ArrayList<String> fields = new ArrayList<>();
				for(int i=1;i<rs.getMetaData().getColumnCount()+1;i++) {
					fields.add(rs.getMetaData().getColumnName(i));
					temp +="`"+rs.getMetaData().getColumnName(i)+"`,";
				}
				temp =temp.substring(0,temp.length()-1)+ ") values(";
				while (rs.next()) {
					String line = temp;
					for(int i=0;i<fields.size();i++) {
						String s = rs.getString(fields.get(i));
						if(s==null) {
							line+="null,";
						}else {
//							System.out.println(fields.get(i)+"="+rs.getMetaData().getColumnTypeName(i+1));
							if("DECIMAL".equals(rs.getMetaData().getColumnTypeName(i+1))) {
								s = rs.getBigDecimal(fields.get(i)).stripTrailingZeros().toString();
							}
							line+="'"+s+"',";
						}
					}
					line =line.substring(0,line.length()-1)+ ")";
					sqls.add(line);
				}
			}
		} catch (Exception e) {
			log.error("创建水司复制数据读取失败:ip="+ip+",port="+port+",account="+account+",password="+password+",shcema="+shcema,e);
			throw e;
		}finally {
			try {
				if(conn != null)
					conn.close();
			} catch (Exception e) {
				log.error("创建水司复制数据读取关闭数据库连接失败:ip="+ip+",port="+port+",account="+account+",password="+password+",shcema="+shcema,e);
			}//关闭与数据库的连接
		}
		return sqls;
	}
	/**
	 * 读取原数据库的表结构信息
	 * @param ip
	 * @param port
	 * @param account
	 * @param password
	 * @param shcema
	 * @return
	 * @throws Exception
	 */
	private ArrayList<String> getMysqlTableSql(String ip,String port,String account,String password,String shcema) throws Exception {
		ArrayList<String> sqls = new ArrayList<>();
		String url = "jdbc:mysql://"+ip+":"+port+"/"+shcema+"?useUnicode=true&AutoReconnect=true&characterEncoding=utf-8&serverTimezone=Asia/Shanghai";  //mysql库，使用上海时区
		Connection conn=null;

		try {
			Class.forName("com.mysql.jdbc.Driver");  //加载JDBC驱动程序
			conn = DriverManager.getConnection(url, account, password);//建立与数据库的连接，后两个参数分别为账号和密码
			ArrayList<HashMap<String,String>> tables = new ArrayList<>();
			PreparedStatement pst =conn.prepareStatement("select table_name,table_comment from information_schema.tables where table_schema = \""+shcema+"\" order by table_name;");
			ResultSet rs = pst.executeQuery();
			while(rs.next()) {
				String tableName=rs.getString(1);
				String comment=rs.getString(2);
				HashMap<String,String> map = new HashMap<>();
				map.put("tableName", tableName);
				map.put("comment", comment);
				tables.add(map);
			}
			for(HashMap<String,String> table : tables) {
				StringBuffer sb=new StringBuffer();
				String tableName=table.get("tableName");
				String comment=table.get("comment");
				log.info("开始处理表：" + tableName + "，注释：" + comment);
				sb.append("CREATE TABLE `"+tableName+"` (\r\n");
				PreparedStatement pst1 =conn.prepareStatement("SELECT COLUMN_NAME,COLUMN_TYPE,COLUMN_KEY,IS_NULLABLE,COLUMN_DEFAULT,COLUMN_COMMENT,EXTRA FROM INFORMATION_SCHEMA.COLUMNS where table_schema ='"+shcema+"' and TABLE_NAME='"+tableName+"' ORDER BY ORDINAL_POSITION;");
				rs = pst1.executeQuery();
				String key ="";
				while(rs.next()) {
					String columnName = rs.getString("COLUMN_NAME");
					String columnType = rs.getString("COLUMN_TYPE");
					String isNullable = rs.getString("IS_NULLABLE");
					String extra = rs.getString("EXTRA");
					String columnDefault = rs.getString("COLUMN_DEFAULT");
					String columnComment = rs.getString("COLUMN_COMMENT");

					// 记录字段信息，特别是时间字段
					if (columnType != null &&
							(columnType.toLowerCase().startsWith("datetime") ||
									columnType.toLowerCase().startsWith("timestamp"))) {
						log.info("处理时间字段：" + tableName + "." + columnName +
								"，类型：" + columnType + "，默认值：" + columnDefault +
								"，是否可空：" + isNullable + "，额外属性：" + extra);
					}

					// 对时间字段的默认值进行特殊处理，避免Invalid default value错误
					if (columnType != null &&
							(columnType.toLowerCase().startsWith("datetime") ||
									columnType.toLowerCase().startsWith("timestamp"))) {
						if (columnDefault != null) {
							String defaultValue = columnDefault.trim();
							// 处理各种可能导致错误的默认值
							if ("0000-00-00 00:00:00".equals(defaultValue) ||
									"".equals(defaultValue) ||
									"CURRENT_TIMESTAMP".equals(defaultValue) ||
									defaultValue.startsWith("CURRENT_TIMESTAMP") ||
									"NULL".equalsIgnoreCase(defaultValue) ||
									defaultValue.contains("ON UPDATE")) {
								// 对于这些特殊情况，不设置默认值
								log.info("移除时间字段 " + tableName + "." + columnName + " 的无效默认值：" + columnDefault);
								columnDefault = null;
							}
						}
					}

					sb.append("  "+"`"+columnName+"` "+columnType+
							" "+(isNullable.equals("NO")?"NOT NULL ":"")+
							" "+((extra != null && !extra.trim().isEmpty() && extra.equalsIgnoreCase("auto_increment"))?" AUTO_INCREMENT ":"")+
							" "+((columnDefault != null && !columnDefault.trim().isEmpty())?"DEFAULT '"+columnDefault+"' ":"")+
							" "+((columnComment != null && !columnComment.trim().isEmpty())?"COMMENT '"+columnComment+"' ":"")+
							",\r\n");
					if("PRI".equals(rs.getString("COLUMN_KEY"))) {
						key = ((key == null || key.trim().isEmpty())?"":key+",")+"`"+columnName+"`";
					}
				}
				if(key != null && !key.trim().isEmpty()) {
					sb.append("  PRIMARY KEY ("+key+")");
				}else {
					sb.replace(sb.length()-3, sb.length(), "");
				}

				pst1 =conn.prepareStatement("show index from "+tableName+";");
				rs = pst1.executeQuery();
				HashMap<String,HashMap<String,String>> indexs = new HashMap<>();
				while(rs.next()) {
					String key_name = rs.getString("Key_name");
					if(!key_name.equals("PRIMARY")) {
						HashMap<String,String> index = indexs.get(key_name);
						if(index == null) {
							index = new HashMap<String,String>();
							indexs.put(key_name, index);
						}
						index.put("Key_name", rs.getString("Key_name"));
						index.put("Non_unique", rs.getInt("Non_unique")==0?"UNIQUE ":"");
						index.put("Index_type", rs.getString("Index_type"));
						String field = index.get("field");
						index.put("field",(field==null?"":field+",")+ "`"+rs.getString("Column_name")+ "`");
					}
				}
				for(HashMap<String,String> index:indexs.values()) {
					sb.append(",\r\n");
					sb.append("  "+index.get("Non_unique")+"KEY `"+index.get("Key_name")+"` ("+index.get("field")+") USING "+index.get("Index_type"));
				}

				pst1 =conn.prepareStatement("select * from INFORMATION_SCHEMA.KEY_COLUMN_USAGE  where TABLE_NAME='"+tableName+"' and TABLE_SCHEMA='"+shcema+"' and REFERENCED_TABLE_NAME is not null");
				rs = pst1.executeQuery();
				while(rs.next()) {
					sb.append(",\r\n");
					sb.append("  CONSTRAINT `"+rs.getString("CONSTRAINT_NAME")+
							"` FOREIGN KEY (`"+rs.getString("COLUMN_NAME")+
							"`) REFERENCES `"+rs.getString("REFERENCED_TABLE_NAME")+
							"` (`"+rs.getString("REFERENCED_COLUMN_NAME")+"`)");
				}

				sb.append("\r\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci "+((comment != null && !comment.trim().isEmpty())?" COMMENT='"+comment+"'":"")+";\r\n");
				log.info("生成建表SQL：" + tableName + "，SQL长度：" + sb.length());
				sqls.add(sb.toString());
			}
		} catch (Exception e) {
			log.error("创建数据库失败:ip="+ip+",port="+port+",account="+account+",password="+password+",shcema="+shcema,e);
			throw e;
		}finally {
			try {
				if(conn != null)
					conn.close();
			} catch (Exception e) {
				log.error("创建数据库关闭数据库连接失败:ip="+ip+",port="+port+",account="+account+",password="+password+",shcema="+shcema,e);
			}//关闭与数据库的连接
		}
		return sqls;
	}


	/**
	 * 创建水司数据库相关操作
	 *
	 * @param factory    数据库连接
	 * @param templeteId 模板水司ID
	 * @param companyNo  水司编号
	 * @param dbAccount  数据库账号
	 * @param dbPassWord 数据库密码
	 * @return
	 */
	private void createOracleDataBase(SessionFactory factory, String templeteId, String companyNo, String dbAccount,
									  String dbPassWord) {
		dbAccount = dbAccount.toUpperCase();
		String datTableSpace = dbAccount + "_DAT";
		String inxTableSpace = dbAccount + "_INX";
		String tmpTableSpace = dbAccount + "_TMP";
		// 查询模板水司信息（水司编号、数据库账号、数据库主键）
		TWaterCompanyMapper publicMapper = factory.getMapper(TWaterCompanyMapper.class);
		DatabaseOracleMapper databaseOracleMapper = factory.getMapper(DatabaseOracleMapper.class);
		TWaterCompanyBean tempWaterCompany = publicMapper.selectById(templeteId);

		// 获取表空间文件存储路径
		DbServerMapper dbServerMapper = factory.getMapper(DbServerMapper.class);
		DbServerBean dbServerBean = dbServerMapper.selectById(tempWaterCompany.getDbId());
		String tablespaceLocation = dbServerBean.getTableSpaceLocation();

		//创建数据表空间
		databaseOracleMapper.oralceCreateTableSpace(datTableSpace, tablespaceLocation);

		//创建索引表空间
		databaseOracleMapper.oralceCreateTableSpace(inxTableSpace, tablespaceLocation);

		//创建临时表空间
		databaseOracleMapper.oracleCreateTempTableSpace(tmpTableSpace, tablespaceLocation);

		//创建账户
		databaseOracleMapper.oracleCreateDbUser(dbAccount, dbPassWord, datTableSpace, tmpTableSpace);

		//授权账户
		databaseOracleMapper.oracleGrantDbUser(dbAccount);

		//单独修改数据库用户使用表空间的权限
		databaseOracleMapper.oracleAlertDbUser(dbAccount, datTableSpace);
		databaseOracleMapper.oracleAlertDbUser(dbAccount, inxTableSpace);

		//查询模板水司所有表名
		List<String> tableNames = databaseOracleMapper.oracleSelectAllTables(tempWaterCompany.getDatabaseAccount());

		//创建模板水司所有表并设置数据表空间
		for (String tableName : tableNames) {
			databaseOracleMapper.oracleCreateAllTables(tempWaterCompany.getDatabaseAccount(), dbAccount, tableName, datTableSpace);
		}

		// 添加新水司数据源
		DBSourceUtils.registeDataSource(companyNo);

		//使用模板水司数据源
		DatabaseOracleMapper templateMapper = factory.getMapper(DatabaseOracleMapper.class, "_DB" + tempWaterCompany.getCompanyNo());

		//使用新水司数据源
		DatabaseOracleMapper newCompanyMapper = factory.getMapper(DatabaseOracleMapper.class, "_DB" + companyNo);

		//查询表注释
		List<HashMap<String, String>> tableComments = templateMapper.oracleSelectTableComment(tempWaterCompany.getDatabaseAccount());

		//创建表注释
		for (HashMap<String, String> hashMap : tableComments) {
			newCompanyMapper.oracleCreateTableComment(hashMap.get("TABLE_NAME"), hashMap.get("COMMENTS"));
		}

		//查询表字段注释
		List<HashMap<String, String>> tableColumnComments = templateMapper.oracleSelectTableColumnComment(tempWaterCompany.getDatabaseAccount());

		//创建表字段注释
		for (HashMap<String, String> hashMap : tableColumnComments) {
			newCompanyMapper.oracleCreateTableColumnComment(hashMap.get("TABLE_NAME"), hashMap.get("COLUMN_NAME"), hashMap.get("COMMENTS"));
		}

		//查询主键
		List<HashMap<String, String>> tablePrimarys = templateMapper.oracleSelectTablePrimary();

		//创建主键
		for (HashMap<String, String> hashMap : tablePrimarys) {
			newCompanyMapper.oracleCreateTablePrimary(hashMap.get("TABLE_NAME"), hashMap.get("INDEX_NAME"), hashMap.get("COLUMN_NAME"), inxTableSpace);
		}

		//查询索引
		List<HashMap<String, String>> tableIndexs = templateMapper.oracleSelectTableIndex();

		//创建主键
		for (HashMap<String, String> hashMap : tableIndexs) {
			newCompanyMapper.oracleCreateTableIndex(hashMap.get("TABLE_NAME"), hashMap.get("INDEX_NAME"), hashMap.get("COLUMN_NAME"), inxTableSpace);
		}

		//查询需要复制数据的表名
		tableNames = ApplicationConfig.getCopyTableNames()==null ? null : Arrays.asList(ApplicationConfig.getCopyTableNames().split(","));

		// 复制表数据
		for (String tableName : tableNames) {
			databaseOracleMapper.oracleCopyTableData(tempWaterCompany.getDatabaseAccount(), dbAccount, tableName);
		}
	}
}
