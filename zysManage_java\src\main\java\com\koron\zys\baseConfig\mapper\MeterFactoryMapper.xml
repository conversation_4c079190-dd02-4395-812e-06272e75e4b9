<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.koron.zys.baseConfig.mapper.MeterFactoryMapper">

    <resultMap id="baseResultMap" type="com.koron.zys.baseConfig.bean.MeterFactoryBean">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="factory_name" jdbcType="VARCHAR" property="factoryName"/>
        <result column="factory_full_name" jdbcType="VARCHAR" property="factoryFullName"/>
        <result column="factory_addr" jdbcType="VARCHAR" property="factoryAddr"/>
        <result column="link_man" jdbcType="VARCHAR" property="linkMan"/>
        <result column="link_tel" jdbcType="VARCHAR" property="linkTel"/>
        <result column="comments" jdbcType="VARCHAR" property="comments"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="sort_no" jdbcType="INTEGER" property="sortNo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_name" jdbcType="VARCHAR" property="updateName"/>
    </resultMap>
    <select id="selectMeterFactoryList" parameterType="com.koron.zys.baseConfig.queryBean.MeterFactoryQueryBean"
            resultType="com.koron.zys.baseConfig.vo.MeterFactoryVO">
        select m.id, m.factory_name, m.factory_full_name,m.factory_addr,
        m.link_man,m.link_tel,m.comments,m.sort_no,m.tenant_id,case when m.status=1 then '启用' else '停用' end status
        from PUB_METER_FACTORY m
        where 1=1
        <if test="factoryName != null and factoryName != ''">
			and m.factory_name LIKE CONCAT('%',#{factoryName},'%')
        </if>
        order by m.sort_no asc
    </select>

    <insert id="insertMeterFactory" parameterType="com.koron.zys.baseConfig.bean.MeterFactoryBean">
		insert into PUB_METER_FACTORY (id,factory_name,factory_full_name, factory_addr, link_man, link_tel,
		comments,status, sort_no,tenant_id,create_time, create_name,create_account)
		values
		(
		#{id},
		#{factoryName},
		#{factoryFullName},
		#{factoryAddr},
		#{linkMan},
		#{linkTel},
		#{comments},
		#{status},
		#{sortNo},
		#{tenantId},
		now(),
		#{createName},
		#{createAccount}
		)
	</insert>

    <update id="updateMeterFactory" parameterType="com.koron.zys.baseConfig.bean.MeterFactoryBean">
		update PUB_METER_FACTORY
		set factory_name = #{factoryName},
		    factory_full_name = #{factoryFullName},
			factory_addr = #{factoryAddr},
			link_man= #{linkMan},
			link_tel = #{linkTel},
			comments = #{comments},
			status = #{status},
			sort_no = #{sortNo},
			update_time=now(),
			update_name = #{updateName},
			update_account=#{updateAccount}
		    where id = #{id}
	</update>

</mapper>