package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.ToolAccessoryBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.mapper.ToolAccessoryMapper;
import com.koron.zys.serviceManage.utils.CommonUtils;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import com.koron.util.Tools;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

/**
 * 工具下载页附件新增
 *
 * @author: lrk
 * @date: 2022-04-21 09:55
 * @description:
 */
public class ToolAccessorySave implements ServerInterface {
    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        try {
            ToolAccessoryBean bean = JsonUtils.objectToPojo(req.getData(), ToolAccessoryBean.class);
            bean.setId(Tools.getObjectId());
            //创建时间
            bean.setCreateTime(CommonUtils.getCurrentTime());
            //创建人账号
            bean.setCreateAccount(userInfo.getUserInfo().getAcount());
            //创建人名称
            bean.setCreateName(userInfo.getUserInfo().getName());
            bean.setDownloadCount(0);
            ToolAccessoryMapper mapper = factory.getMapper(ToolAccessoryMapper.class,"_default");
            mapper.addToolAccessory(bean);
            return MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "添加成功", null);
        } catch (Exception e) {
            logger.error("工具页附件新增失败", e.getMessage(), e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "添加失败", null);
        }
    }
}
