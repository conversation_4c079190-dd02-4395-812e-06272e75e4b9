package com.koron.zys.baseConfig.impl;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.bean.PrefStrategyBean;
import com.koron.zys.baseConfig.mapper.PrefStrategyMapper;
import com.koron.zys.baseConfig.queryBean.PrefStrategyQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 优惠策略编辑初始化
 *
 * <AUTHOR>
 */
public class PrefStrategyQuery implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(PrefStrategyQuery.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        MessageBean<PrefStrategyBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PrefStrategyBean.class);
        try {
            PrefStrategyQueryBean bean = JsonUtils.objectToPojo(req.getData(), PrefStrategyQueryBean.class);
            PrefStrategyMapper mapper = factory.getMapper(PrefStrategyMapper.class);
            if (StringUtils.isEmpty(bean.getId())){
                return MessageBean.create(Constant.ILLEGAL_PARAMETER, "id不能为空！", void.class);
            }
            PrefStrategyBean PrefStrategybean = mapper.selectPrefStrategyDetailById(bean.getId());
            info.setData(PrefStrategybean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }
}
