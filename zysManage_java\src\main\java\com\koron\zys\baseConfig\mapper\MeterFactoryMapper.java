package com.koron.zys.baseConfig.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.koron.ebs.mybatis.EnvSource;

import com.koron.zys.baseConfig.bean.MeterFactoryBean;
import com.koron.zys.baseConfig.queryBean.MeterFactoryQueryBean;
import com.koron.zys.baseConfig.vo.MeterFactoryVO;
@EnvSource("_default")
public interface MeterFactoryMapper {
	
	/**
	 * 查询列表
	 * 
	 * @return
	 */
	List<MeterFactoryVO> selectMeterFactoryList(MeterFactoryQueryBean meterFactoryQueryBean);
	
	/**
	 * 根据id查询
	 * @param id
	 * @return
	 */
	@Select("select * from pub_meter_factory where id = #{id}")
	MeterFactoryBean selectMeterFactoryById(@Param("id") String id);
	
	/**
	 * 根据id查询
	 * @param id
	 * @return
	 */
	@Select("select * from pub_meter_factory where factory_name = #{name} limit 1")
	MeterFactoryBean selectMeterFactoryByName(@Param("name") String name);

	/**
	 * 添加
	 * 
	 * @param meterFactoryBean
	 * @return
	 */
	void insertMeterFactory(MeterFactoryBean meterFactoryBean);
	
	/**
	 * 校验字段内容重复
	 */
	@Select("select count(1) from pub_meter_factory where ${key} = #{val}")
	Integer check(@Param("key") String key, @Param("val") String val);
	
	/**
	 * 校验字段内容重复-排除当前记录
	 */
	@Select("select count(1) from pub_meter_factory where ${key} = #{val} and id <> #{id}")
	Integer check2(@Param("key") String key, @Param("val") String val, @Param("id") String id);
	
	/**
	 * 修改
	 * 
	 * @param meterFactoryBean
	 * @return
	 */
	Integer updateMeterFactory(MeterFactoryBean meterFactoryBean);

}
