package com.koron.zys.baseConfig.impl;


import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.BaseBusinessHallMapper;
import com.koron.zys.baseConfig.vo.BaseBusinessHallVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import java.util.List;


public class BaseBusinessHallList  implements ServerInterface {
    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        MessageBean<List> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", List.class);
        try {
            BaseBusinessHallMapper mapper = factory.getMapper(BaseBusinessHallMapper.class);

            List<BaseBusinessHallVO> list = mapper.selectList();


            info.setData(list);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription("操作失败");
            logger.error("操作失败", e);
        }
        return info;
    }
}
