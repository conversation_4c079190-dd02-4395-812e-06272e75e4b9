package com.koron.zys.baseConfig.impl;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.IncomeBankMapper;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.JsonUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.swan.bean.MessageBean;

import java.util.List;

public class IncomeBankDelete implements ServerInterface {

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {
        try {
            IncomeBankMapper mapper = factory.getMapper(IncomeBankMapper.class);
            List<String> ids = JsonUtils.objectToPojo(req.getData(), List.class);
            mapper.delete(ids);
            MessageBean<?> msg = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", Void.class);
            return msg;
        } catch (Exception e) {
            logger.error("删除进账银行信息失败", e);
            return MessageBean.create(Constant.MESSAGE_INT_FAIL, "删除进账银行信息失败", void.class);
        }
    }
}
