package com.koron.zys.baseConfig.impl;

import java.util.List;

import com.koron.zys.baseConfig.bean.MatrBean;
import com.koron.zys.baseConfig.bean.MatrCodeBean;
import com.koron.zys.serviceManage.mapper.MatrCodeMapper;
import com.koron.zys.serviceManage.queryBean.matrCodeQueryBean;
import com.mysql.cj.util.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.MatrMapper;
import com.koron.zys.baseConfig.queryBean.MatrQueryBean;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.zys.serviceManage.utils.JsonUtils;
import com.koron.util.Constant;

/**
 * 材料价格-列表初始化
 *
 * <AUTHOR>
 */
public class MatrList implements ServerInterface {

    private static Logger logger = LoggerFactory.getLogger(MatrList.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        @SuppressWarnings("rawtypes")
        MessageBean<PageInfo> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", PageInfo.class);
        try {
            MatrQueryBean bean = JsonUtils.objectToPojo(req.getData(), MatrQueryBean.class);
            MatrMapper mapper = factory.getMapper(MatrMapper.class);
            MatrCodeMapper matrCodeMapper = factory.getMapper(MatrCodeMapper.class, "_default");
            matrCodeQueryBean selectBean = new matrCodeQueryBean();
            if (StringUtils.isNullOrEmpty(bean.getMatrNo())) {
                selectBean.setClassCode("");
            }else {
                MatrCodeBean matrCodeBean = matrCodeMapper.selectMatrTemplateByNo(bean.getMatrNo());
                selectBean.setClassCode(matrCodeBean.getClassCode());
            }
            selectBean.setIsLeaf(1);
         // 设置分页参数
         	PageHelper.startPage(bean.getPage(), bean.getPageCount());
            List<MatrCodeBean> matrCodeBeans = matrCodeMapper.selectMatrCodeList(selectBean);
            List<MatrBean> list = mapper.selectMatrPrice();
            for (MatrBean matrBean : list) {
                for (MatrCodeBean codeBean : matrCodeBeans) {
                    if (matrBean.getMatrNo().equals(codeBean.getMatrNo())) {
                        codeBean.setPrice(matrBean.getMatrPrice());
                        codeBean.setMatrPriceId(matrBean.getId());
                        break;
                    }
                }
            }
            info.setData(new PageInfo<>(matrCodeBeans));
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_INT_FAIL);
            info.setDescription(e.getMessage());
            logger.error(e.getMessage(), e);
        }
        return info;
    }
}
