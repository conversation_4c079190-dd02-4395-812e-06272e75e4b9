package com.koron.zys.baseConfig.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.koron.ebs.mybatis.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.swan.bean.MessageBean;

import com.koron.zys.ServerInterface;
import com.koron.zys.baseConfig.mapper.BankMapper;
import com.koron.zys.baseConfig.queryBean.BankQueryBean;
import com.koron.zys.baseConfig.vo.BankVO;
import com.koron.zys.serviceManage.bean.RequestBean;
import com.koron.zys.serviceManage.bean.TreeBean;
import com.koron.zys.serviceManage.bean.UserInfoBean;
import com.koron.util.Constant;
import com.koron.util.DBSourceUtils;
import com.koron.util.JsonUtils;

/**
 * 银行信息-查询树结构
 *
 * <AUTHOR>
 */
public class BankTree implements ServerInterface {
    private static Logger logger = LoggerFactory.getLogger(BankTree.class);

    @Override
    public MessageBean<?> exec(SessionFactory factory, UserInfoBean userInfo, RequestBean req) {

        MessageBean<TreeBean> info = MessageBean.create(Constant.MESSAGE_INT_SUCCESS, "success", TreeBean.class);
        try {
        	if(req.getData()==null || "".equals(req.getData())) {
        		req.setData(new BankQueryBean());
        	}
            BankQueryBean selectBean = JsonUtils.objectToPojo(req.getData(), BankQueryBean.class);
            String companyNo=userInfo.getCurWaterCode();
            if(StringUtils.isEmpty(companyNo)) {
            	companyNo=selectBean.getCompanyNo();
            }
            BankMapper mapper = factory.getMapper(BankMapper.class,DBSourceUtils.getDbEnv(companyNo));
            // TODO 是否只查询启用状态的银行信息
            selectBean.setStatus(1);//只查询启用的
            selectBean.setBankNo("all");
//            selectBean.setIsLeaf(0);//只查询目录
            List<BankVO> list = mapper.findBank(selectBean);
            TreeBean treeBean = new TreeBean();
            // 创建根目录
            treeBean.setId("0");
            treeBean.setCode("");
            treeBean.setName("根目录");
            treeBean.setParent("");
            treeBean.setIsParent(true);
            // 递归下级目录
            recTree(list, treeBean);
            info.setCode(Constant.MESSAGE_INT_SUCCESS);
            info.setDescription("success");
            info.setData(treeBean);
        } catch (Exception e) {
            info.setCode(Constant.MESSAGE_DBFAIL);
            info.setDescription("数据库异常");
            logger.error("数据库异常", e);
            factory.close(false);
        }
        return info;
    }

    /**
     * 递归查询下级目录树
     *
     * @param list
     * @param treeBean
     */
    private void recTree(List<BankVO> list, TreeBean treeBean) {
        for (BankVO bean : list) {
            // 如果code是以父级开头，且长度多5位，说明这是他的下级
            if (bean.getBankNo().startsWith(treeBean.getCode())
                    && bean.getBankNo().length() == treeBean.getCode().length() + 5) {
                TreeBean b = new TreeBean();
                b.setId(bean.getId() + "");
                b.setCode(bean.getBankNo());
                b.setName(bean.getBankName());
                b.setParent(treeBean.getId() + "");
                b.setIsParent(false);
                treeBean.setIsParent(true);
                treeBean.getChildren().add(b);
                recTree(list, b); // 递归循环下级目录
            }
        }
    }
}
