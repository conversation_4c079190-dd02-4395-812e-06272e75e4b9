package com.koron.util;

public class ColumnInfo {
	
	/** 数据库字段名称 **/
    private String name;
    
    /** 数据库字段类型 **/
    private String type;
    
    /** 数据库字段首字母小写且去掉下划线字符串 **/
    private String fieldName;
    
    /** 数据库字段注释 **/
    private String comment;

	public String getName() {
		return name;
	}

	public String getType() {
		return type;
	}

	public String getFieldName() {
		return fieldName;
	}

	public String getComment() {
		return comment;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setType(String type) {
		this.type = type;
	}

	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}
    
}
