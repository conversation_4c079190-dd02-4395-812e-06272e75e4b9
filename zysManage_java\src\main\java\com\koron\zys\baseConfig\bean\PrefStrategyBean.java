package com.koron.zys.baseConfig.bean;

import java.util.List;

import com.koron.util.Check;

/**
 * 优惠策略实体类
 *
 * <AUTHOR>
 */
public class PrefStrategyBean extends BaseBean {
    /**
     * 策略名称
     */
	@Check(name = "策略名称", notEmpty = true, notNull=true)
    private String strategyName;
    /**
     * 备注
     */
    private String comments;
    /**
     * 状态
     */
    @Check(name = "状态", notEmpty = true, notNull=true)
    private Integer status;
    /**
     * 优惠明细
     */
    @Check(name = "优惠明细", notEmpty = true, notNull=true)
    List<PrefStrategyDetailBean> prefStrategyList;

    public String getStrategyName() {
        return strategyName;
    }

    public void setStrategyName(String strategyName) {
        this.strategyName = strategyName;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<PrefStrategyDetailBean> getPrefStrategyList() {
        return prefStrategyList;
    }

    public void setPrefStrategyList(List<PrefStrategyDetailBean> prefStrategyList) {
        this.prefStrategyList = prefStrategyList;
    }
}
