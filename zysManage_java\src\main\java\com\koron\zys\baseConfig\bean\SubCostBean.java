package com.koron.zys.baseConfig.bean;

public class SubCostBean extends BaseBean{


	private String costNo;  //费用类型
	private String costName; //费用类型名称
	private String subCostNo; //子费用类型编号
	private String subCostName; //子费用类型名称
	private String  isQuota; //是否定额
	private String isQuotaName;
	private String managers; //定额管理员
	private String createTime;
	private String createName;
	private String createAccount;
	private String updateTime;
	private String updateAccount;
	private String updateName;



	public String getIsQuotaName() {
		return isQuotaName;
	}

	public void setIsQuotaName(String isQuotaName) {
		this.isQuotaName = isQuotaName;
	}

	public String getCostNo() {
		return costNo;
	}

	public void setCostNo(String costNo) {
		this.costNo = costNo;
	}

	public String getCostName() {
		return costName;
	}

	public void setCostName(String costName) {
		this.costName = costName;
	}

	public String getSubCostNo() {
		return subCostNo;
	}

	public void setSubCostNo(String subCostNo) {
		this.subCostNo = subCostNo;
	}

	public String getSubCostName() {
		return subCostName;
	}

	public void setSubCostName(String subCostName) {
		this.subCostName = subCostName;
	}

	public String getIsQuota() {
		return isQuota;
	}

	public void setIsQuota(String isQuota) {
		this.isQuota = isQuota;
	}

	public String getManagers() {
		return managers;
	}

	public void setManagers(String managers) {
		this.managers = managers;
	}

	@Override
	public String getCreateTime() {
		return createTime;
	}

	@Override
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	@Override
	public String getCreateName() {
		return createName;
	}

	@Override
	public void setCreateName(String createName) {
		this.createName = createName;
	}

	@Override
	public String getCreateAccount() {
		return createAccount;
	}

	@Override
	public void setCreateAccount(String createAccount) {
		this.createAccount = createAccount;
	}

	@Override
	public String getUpdateTime() {
		return updateTime;
	}

	@Override
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public String getUpdateAccount() {
		return updateAccount;
	}

	@Override
	public void setUpdateAccount(String updateAccount) {
		this.updateAccount = updateAccount;
	}

	@Override
	public String getUpdateName() {
		return updateName;
	}

	@Override
	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}
}
